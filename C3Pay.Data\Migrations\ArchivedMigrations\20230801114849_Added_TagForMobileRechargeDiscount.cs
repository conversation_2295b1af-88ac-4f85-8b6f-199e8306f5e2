﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_TagForMobileRechargeDiscount : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {  
            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[] { "da_mr_tag_7", "en", 99, "50% Off", "da_mr_tag" });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElementTags",
                columns: new[] { "Id", "TextContentCode" },
                values: new object[] { 7, "da_mr_tag_7" });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 671, "en", "50% Off", "da_mr_tag_7" },
                    { 672, "bn", "50% Off", "da_mr_tag_7" },
                    { 673, "hi", "50% Off", "da_mr_tag_7" },
                    { 674, "hi-en", "50% Off", "da_mr_tag_7" },
                    { 675, "ml", "50% Off", "da_mr_tag_7" },
                    { 676, "ta", "50% Off", "da_mr_tag_7" },
                    { 677, "ur-en", "50% Off", "da_mr_tag_7" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElementTags",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 671);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 672);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 673);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 674);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 675);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 676);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 677);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mr_tag_7"); 
        }
    }
}
