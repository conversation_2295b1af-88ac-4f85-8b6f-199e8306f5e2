﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models.DTOs.Payments.Responses;
using C3Pay.Core.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.Services.Payments.Queries
{
    public class GetPaymentAuthRequestQuery : IRequest<Result<PaymentAuthorisationResponseDto>>
    {
        public CancellationToken CancellationToken { get; set; }
        public string UserPhoneNumber { get; set; }
        public string LanguageCode { get; set; }

        public Result IsQueryValid()
        {
            if (string.IsNullOrWhiteSpace(this.UserPhoneNumber))
            {
                return Result.Failure(Errors.InAppAuth.NoPhoneNumberSent);
            }

            return Result.Success();
        }
    }

    public class GetPaymentAuthRequestQueryHandler : IRequestHandler<GetPaymentAuthRequestQuery, Result<PaymentAuthorisationResponseDto>>
    {
        private readonly IFeatureManager _featureManager;
        private readonly ILogger _logger;
        private readonly IUserRepository _userRepository;

        public GetPaymentAuthRequestQueryHandler(IFeatureManager featureManager, ILogger<GetPaymentAuthRequestQueryHandler> logger, IUserRepository userRepository)
        {
            _featureManager = featureManager;
            _logger = logger;
            _userRepository = userRepository;
        }
        public async Task<Result<PaymentAuthorisationResponseDto>> Handle(GetPaymentAuthRequestQuery request, CancellationToken cancellationToken)
        {

            // Check the feature is enabled
            var isFeatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication);
            if (!isFeatureEnabled)
            {
                _logger.LogError(Errors.InAppAuth.PaymentAuthFeatureNotEnabled.Code);
                return Result.Failure<PaymentAuthorisationResponseDto>(Errors.InAppAuth.PaymentAuthFeatureNotEnabled);
            }

            // Validate query.
            var isQueryValid = request.IsQueryValid();
            if (isQueryValid.IsFailure)
            {
                _logger.LogError(isQueryValid.Error.Code);
                return Result.Failure<PaymentAuthorisationResponseDto>(isQueryValid.Error);
            }

            // Prepare request input.
            request.UserPhoneNumber = request.UserPhoneNumber.Trim();
            request.LanguageCode ??= "en";


            // If user is not found, deleted, blocked, exit.
            var user = await _userRepository.GetUserAsync(request.UserPhoneNumber, request.CancellationToken);

            // User checks.
            if (user is null)
            {
                _logger.LogError(Errors.InAppAuth.UserNotFound.Code);
                return Result.Failure<PaymentAuthorisationResponseDto>(Errors.InAppAuth.UserNotFound);
            }

            // Mock data for testing
            var random = new Random();
            var randomDouble = random.NextDouble() * 95 + 5;
            var result = new PaymentAuthorisationResponseDto
            {
                Id = Guid.NewGuid(),
                RequestDate = DateTime.UtcNow,
                ExpiryDate = DateTime.UtcNow.AddMinutes(2),
                Amount = new PaymentAuthAmountDto { Amount = randomDouble.ToString("N2"), CurrencyCode = "AED" },
                Merchant = new PaymentAuthorisationMerchantDto { Name = "Amazon" },
                Status = PaymentAuthRequestStatus.PENDING_USER_APPROVAL
            };



            return Result.Success(result);
        }
    }
}
