﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideoTableChanges_Names : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_VideoTypeId",
                table: "LoginVideoSlots");

            migrationBuilder.RenameColumn(
                name: "VideoTypeId",
                table: "LoginVideoSlots",
                newName: "LoginVideoId");

            migrationBuilder.RenameIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots",
                newName: "IX_LoginVideoSlots_LoginVideoId");

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_LoginVideoId",
                table: "LoginVideoSlots",
                column: "LoginVideoId",
                principalTable: "LoginVideos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_LoginVideoId",
                table: "LoginVideoSlots");

            migrationBuilder.RenameColumn(
                name: "LoginVideoId",
                table: "LoginVideoSlots",
                newName: "VideoTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_LoginVideoSlots_LoginVideoId",
                table: "LoginVideoSlots",
                newName: "IX_LoginVideoSlots_VideoTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId",
                principalTable: "LoginVideos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
