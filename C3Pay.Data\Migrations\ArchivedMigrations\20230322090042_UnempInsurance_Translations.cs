﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnempInsurance_Translations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 658,
                column: "Text",
                value: "বেকারত্ব বীমা");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 661,
                column: "Text",
                value: "തൊഴിലില്ലായ്മ ഇൻഷുറൻസ്");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 662,
                column: "Text",
                value: "வேலையின்மை காப்பீடு திட்டம்");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 658,
                column: "Text",
                value: "Unemployment Insurance");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 661,
                column: "Text",
                value: "Unemployment Insurance");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 662,
                column: "Text",
                value: "Unemployment Insurance");
        }
    }
}
