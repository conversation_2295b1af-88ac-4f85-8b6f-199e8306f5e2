﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_PartnerId_UnempInsurance : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TermsAndConditionsUrl",
                table: "Partners",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 1,
                column: "TermsAndConditionsUrl",
                value: "https://eaec3sharedsp.z1.web.core.windows.net/TermsAndConditions.html");

            migrationBuilder.InsertData(
                table: "Partners",
                columns: new[] { "Id", "BannerUrl", "Code", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "LogoUrl", "TermsAndConditionsUrl", "UpdatedDate" },
                values: new object[] { 5, null, "ORIENT", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, "https://eaec3sharedsp.blob.core.windows.net/exchange-house-logos/orient-exchange-house.png", "https://eaec3sharedsp.z1.web.core.windows.net/OrientTermsAndConditions.html", null });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 1,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 2,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 3,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 4,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 5,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 6,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 7,
                column: "PartnerId",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 8,
                column: "PartnerId",
                value: 1);

            migrationBuilder.InsertData(
                table: "PartnerCorporates",
                columns: new[] { "Id", "CorporateId", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "PartnerId", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, "177", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null },
                    { 2, "296", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null },
                    { 3, "44401", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null },
                    { 4, "4901", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_UnEmpInsurancePaymentOptions_PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                column: "PartnerId");

            migrationBuilder.AddForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                column: "PartnerId",
                principalTable: "Partners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.DropIndex(
                name: "IX_UnEmpInsurancePaymentOptions_PartnerId",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DropColumn(
                name: "PartnerId",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.DropColumn(
                name: "TermsAndConditionsUrl",
                table: "Partners");

        }
    }
}
