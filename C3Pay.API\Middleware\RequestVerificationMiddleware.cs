﻿using C3Pay.Core;
using C3Pay.Core.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.API.Middleware
{
    /// <summary>
    /// Signature Verification Middleware
    /// </summary>
    public class RequestVerificationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly EncryptionSettings encryptionSettings;
        private readonly IFeatureManager _featureManager;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="next"></param>
        /// <param name="_encryptionSettings"></param>
        public RequestVerificationMiddleware(RequestDelegate next, IOptions<EncryptionSettings> _encryptionSettings, IFeatureManager featureManager)
        {
            _next = next;
            //configuration = _configuration;
            encryptionSettings = _encryptionSettings.Value;
            _featureManager = featureManager;
        }

        /// <summary>
        /// Invoke
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task Invoke(HttpContext context)
        {
            var signature = context.Request.Headers["X-Signature"];
            string contentType = context.Request.ContentType;
            var mfaDigitalSignatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MFADigitalSignature);
            
            if (!string.IsNullOrEmpty(signature) && contentType == "application/json" 
                && encryptionSettings.IsActive 
                && mfaDigitalSignatureEnabled)
            {
                using (MemoryStream requestBodyMemoryStream = new MemoryStream())
                {
                    await context.Request.Body.CopyToAsync(requestBodyMemoryStream);
                    requestBodyMemoryStream.Seek(0, SeekOrigin.Begin);
                    string requestBodyJson = await ReadRequestBody(requestBodyMemoryStream); 
                    if (!(DigitalSignatureHelper.VerifyDigitalSignature(requestBodyJson, signature, encryptionSettings.PublicKey)))
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("Invalid");
                        return;
                    }
                    requestBodyMemoryStream.Seek(0, SeekOrigin.Begin);
                    context.Request.Body = requestBodyMemoryStream;
                    await _next(context);
                }
            }
            else
                await _next(context);
        }

        /// <summary>
        /// Private - Read from Request Body
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        private async Task<string> ReadRequestBody(Stream stream)
        {
            MemoryStream copyStream = new MemoryStream();
            await stream.CopyToAsync(copyStream);
            copyStream.Position = 0;
            using (StreamReader reader = new StreamReader(copyStream))
            {
                return await reader.ReadToEndAsync();
            }
        }
    }
}
