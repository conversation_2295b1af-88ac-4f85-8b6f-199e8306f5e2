﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class MobileRechargePromotions : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Features",
                columns: new[] { "Id", "Name" },
                values: new object[] { 10, "MobileRecharge_Promotion" });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 46, 10, null, "en", "PAK", null, 4, "https://eaec3sharedsp.blob.core.windows.net/mobile-recharge-promotions/PakPromo.png" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 46);

            migrationBuilder.DeleteData(
                table: "Features",
                keyColumn: "Id",
                keyValue: 10);
        }
    }
}
