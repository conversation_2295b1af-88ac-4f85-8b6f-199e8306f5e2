﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class ATMPinChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ATMPinBlockEndDate",
                table: "Users",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "Preferences_ATMPinPopupEnabled",
                table: "Users",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastFailedAttemptTimeStamp",
                table: "BlackListedEntities",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ATMPinBlockEndDate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "Preferences_ATMPinPopupEnabled",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "LastFailedAttemptTimeStamp",
                table: "BlackListedEntities");
        }
    }
}
