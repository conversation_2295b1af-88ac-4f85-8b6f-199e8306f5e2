﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SeedMultilanguageData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var query = @"
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'bn', N'BD', N'Bengali', N'বাংলা', N'অ্যাপটি এভাবে দেখানো হবে', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'en', N'GB', N'English', N'English', N'App will be shown this way', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'hi', N'IN', N'Hindi', N'हिन्दी', N'ऐप इस तरह दिखेगा', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'hi-en', N'IN', N'English', N'Hindi / Hinglish', N'App is tarah dikhega', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'ml', N'IN', N'Malayalam', N'മലയാളം', N'ആപ്പ് ഇതുപോലെ കാണിക്കും', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'ta', N'IN', N'Tamil', N'தமிழ்', N'ஆப்ஸ் இப்படிக் காட்டப்படும்', 1)
                            GO
                            INSERT [dbo].[Languages] ([Code], [CountryCode], [Name], [NativeName], [Description], [IsActive]) VALUES (N'ur-en', N'PK', N'English', N'Urdu', N'App is tarah dikhega', 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_1', N'sub_feature', N'en', NULL, 1, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_10', N'sub_feature', N'en', NULL, 10, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_11', N'sub_feature', N'en', NULL, 11, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_12', N'sub_feature', N'en', NULL, 12, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_2', N'sub_feature', N'en', NULL, 2, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_3', N'sub_feature', N'en', NULL, 3, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_4', N'sub_feature', N'en', NULL, 4, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_5', N'sub_feature', N'en', NULL, 5, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_6', N'sub_feature', N'en', NULL, 6, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_7', N'sub_feature', N'en', NULL, 7, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_8', N'sub_feature', N'en', NULL, 8, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'feature_9', N'sub_feature', N'en', NULL, 9, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'mt_re_1', N'mt_reason', N'en', NULL, 1, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'mt_re_2', N'mt_reason', N'en', NULL, 2, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'mt_re_3', N'mt_reason', N'en', NULL, 3, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'mt_re_4', N'mt_reason', N'en', NULL, 4, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sa_cr_1', N'sa_criteria', N'en', N'Your company must be allowed by C3Pay to take advances', 1, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sa_cr_2', N'sa_criteria', N'en', N'You should receive salaries for the last 3 continuous months', 2, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sa_cr_3', N'sa_criteria', N'en', N'Your salary must be between AED 750 and AED 4,999', 3, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sa_cr_4', N'sa_criteria', N'en', N'You should not have received your end of service or gratuity payment', 4, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sa_cr_5', N'sa_criteria', N'en', N'You can only take one advance in a month', 5, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sub_1', N'subscription', N'en', NULL, 1, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sub_2', N'subscription', N'en', NULL, 2, 1)
                            GO
                            INSERT [dbo].[TextContents] ([Code], [Type], [LanguageCode], [Text], [Order], [IsActive]) VALUES (N'sub_3', N'subscription', N'en', NULL, 3, 1)
                            GO
                            SET IDENTITY_INSERT [dbo].[Translations] ON 
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (1, N'sa_cr_1', N'hi', N'आपकी कंपनी को C3Pay द्वारा लोन लेने की अनुमति दी जानी चाहिए')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (2, N'sa_cr_2', N'hi', N'आपको पिछले 3 लगातार महीनों का वेतन मिलना चाहिए')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (3, N'sa_cr_3', N'hi', N'आपका वेतन AED 750 और AED 4,999 के बीच होना चाहिए')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (4, N'sa_cr_4', N'hi', N'आपको अपने रिटायरमेंट या ग्रेच्युटी का पैसा नहीं मिला होना चाहिए')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (5, N'sa_cr_5', N'hi', N'आप एक महीने में सिर्फ़ एक ही लोन ले सकते हैं')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (6, N'sa_cr_1', N'ta', N'கடன் பெற, உங்கள் நிறுவனம் C3Pay ஆல் அனுமதிக்கப்பட வேண்டும்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (7, N'sa_cr_2', N'ta', N'கடந்த 3 மாதங்களாகத் தொடர்ந்து சம்பளம் பெற்றிருக்க வேண்டும்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (8, N'sa_cr_3', N'ta', N'உங்கள் சம்பளம் AED 750 முதல் AED 4,999 வரை இருக்க வேண்டும்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (9, N'sa_cr_4', N'ta', N'உங்கள் சேவையின் முடிவு அல்லது கிராஜுவிட்டி பேமெண்ட்டை நீங்கள் பெற்றிருக்கக்கூடாது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (10, N'sa_cr_5', N'ta', N'ஒரு மாதத்தில் ஒரு கடன் மட்டுமே பெற முடியும்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (11, N'sa_cr_1', N'ml', N'വായ്പകള്‍ എടുക്കാൻ നിങ്ങളുടെ കമ്പനിയെ C3Pay അനുവദിക്കണം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (12, N'sa_cr_2', N'ml', N'കഴിഞ്ഞ തുടർച്ചയായ 3 മാസങ്ങളില്‍ നിങ്ങള്‍ക്ക് ശമ്പളം ലഭിച്ചിരിക്കണം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (13, N'sa_cr_3', N'ml', N'നിങ്ങളുടെ ശമ്പളം എഇഡി 750-നും എഇഡി 4,999-നും ഇടയിലായിരിക്കണം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (14, N'sa_cr_4', N'ml', N'നിങ്ങള്‍ക്ക് എന്‍ഡ് ഓഫ് സര്‍വീസ് അല്ലെങ്കില്‍ ഗ്രാറ്റുവിറ്റി പേയ്‌മെന്‍റ് ലഭിച്ചിരിക്കരുത്')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (15, N'sa_cr_5', N'ml', N'ഒരു മാസത്തിൽ നിങ്ങൾക്ക് ഒരു വായ്പ മാത്രമേ എടുക്കാൻ കഴിയൂ')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (16, N'sa_cr_1', N'hi-en', N'aapki company ko C3pay dwara loan lene ki anumati di jaani chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (17, N'sa_cr_2', N'hi-en', N'Aapko pichle 3 lagatar mahinon ki salary milni chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (18, N'sa_cr_3', N'hi-en', N'Aapki salary AED 750 aur AED 4,999  ke beech honi chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (19, N'sa_cr_4', N'hi-en', N'Aapko apne retirement ya gratuity ka payment nahi mila hona chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (20, N'sa_cr_5', N'hi-en', N'Aap ek mahine men sirf ek hi loan le sakte hain ')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (21, N'sa_cr_1', N'ur-en', N'qarze lene ke liye aapki company ko C3Pay ke zariye manzoor shudah hona chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (22, N'sa_cr_2', N'ur-en', N'Aap ko pichle 3 musalsal mahinon ki tankhwah mili honi chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (23, N'sa_cr_3', N'ur-en', N'Aap ki tankhwah AED 750 aur AED 4,999 ke darmiyan honi chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (24, N'sa_cr_4', N'ur-en', N'Aap ko khidmat ka ikhtitaam ya gratuity ki adaiyigi nahi mili honi chahiye')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (25, N'sa_cr_5', N'ur-en', N'Aap maheene bhar mein sirf ek qarza le sakte hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (26, N'mt_re_1', N'en', N'Family maintenance and savings')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (27, N'mt_re_2', N'en', N'Personal or other travel')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (28, N'mt_re_3', N'en', N'Education services')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (29, N'mt_re_4', N'en', N'Medical or health')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (30, N'mt_re_1', N'hi', N'परिवार का रखरखाव और बचत')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (31, N'mt_re_2', N'hi', N'निजी या किसी और वजह से यात्रा')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (32, N'mt_re_3', N'hi', N'शिक्षा सेवाएं')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (33, N'mt_re_4', N'hi', N'चिकित्सा या स्वास्थ्य')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (34, N'mt_re_1', N'hi-en', N'Family ka maintenance aur savings')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (35, N'mt_re_2', N'hi-en', N'Personal ya kisi aur wajah se travel')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (36, N'mt_re_3', N'hi-en', N'Education services')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (37, N'mt_re_4', N'hi-en', N'Medical ya health')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (38, N'mt_re_1', N'ml', N'കുടുംബച്ചെലവുകള്‍ക്കും സേവിങ്ങുകള്‍ക്കും')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (39, N'mt_re_2', N'ml', N'പേഴ്സണല്‍ അല്ലെങ്കിൽ മറ്റ് യാത്രകൾ')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (40, N'mt_re_3', N'ml', N'വിദ്യാഭ്യാസ സേവനങ്ങൾ')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (41, N'mt_re_4', N'ml', N'മെഡിക്കൽ അല്ലെങ്കിൽ ആരോഗ്യം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (42, N'mt_re_1', N'ta', N'குடும்பச் செலவுகள் மற்றும் சேமிப்பு')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (43, N'mt_re_2', N'ta', N'தனிப்பட்ட அல்லது பிற பயணம்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (44, N'mt_re_3', N'ta', N'கல்விச் சேவைகள்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (45, N'mt_re_4', N'ta', N'மருத்துவம் அல்லது ஆரோக்கியம்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (46, N'mt_re_1', N'ur-en', N'khandaan ki dekhbhaal aur bachat')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (47, N'mt_re_2', N'ur-en', N'Zaati ya deegar safar')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (48, N'mt_re_3', N'ur-en', N'Talimi khidmaat')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (49, N'mt_re_4', N'ur-en', N'Tibbi ya sehat')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (50, N'sub_1', N'en', N'Receive an SMS as soon as your salary is deposited into your account:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (51, N'sub_2', N'en', N'Secure your card from being used without your knowledge. Keep your account safe by getting an SMS whenever:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (52, N'sub_3', N'en', N'View your current balance, save on ATM balance enquiry fees and view your current balance at any time as often as you want on this app')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (53, N'sub_1', N'hi', N'आपके अकाउंट में सेलेरी क्रेडिट होते ही तुरंत SMS पाएं:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (54, N'sub_2', N'hi', N'अपने कार्ड को आपकी जानकारी के बिना इस्तेमाल होने से सुरक्षित रखें कभी भी, SMS पाकर अपने अकाउंट को सुरक्षित रखें:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (55, N'sub_3', N'hi', N'अपना वर्तमान बैलेंस किसी भी समय देखें ,अपना वर्तमान बैलेंस देखें, एटीएम बैलेंस के इंक्वायरी शुल्क से बचें और इस ऐप पर जितनी बार चाहें उतनी बार अपना वर्तमान बैलेंस देखें:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (56, N'sub_1', N'hi-en', N'Aapke account men salary credit hote hi turant SMS paayen:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (57, N'sub_2', N'hi-en', N'Apne card ko aapki jaankaari ke bina istemaal hone se surakshit rakhen  Kbahi bhi, SMS paakar apne account ko safe rakhen:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (58, N'sub_3', N'hi-en', N'Apna current balance kisi bhi samay dekhen, Apna current balance dekhen, ATM balance ki enquiry fees se bachen aur is app par jitni baar chahen utni baar apna current balance dekhen:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (59, N'sub_1', N'ml', N'നിങ്ങളുടെ ശമ്പളം നിങ്ങളുടെ അക്കൗണ്ടിൽ ഡിപ്പോസിറ്റ് ആയാല്‍ ഉടന്‍ ഒരു എസ്എംഎസ് ലഭിക്കും:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (60, N'sub_2', N'ml', N'നിങ്ങള്‍ അറിയാതെ നിങ്ങളുടെ കാർഡ് ഉപയോഗിക്കുന്നതിൽ നിന്ന് സുരക്ഷിതമാക്കാം. ഇനി പറയുന്ന ഏത് ഘട്ടത്തിലും ഒരു എസ്എംഎസ് ലഭിക്കുന്നതിനാല്‍ നിങ്ങളുടെ അക്കൗണ്ട് സുരക്ഷിതമാക്കാം:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (61, N'sub_3', N'ml', N'ഏത് സമയത്തും നിങ്ങളുടെ നിലവിലെ ബാലൻസ് കാണാം, ഈ ആപ്പിൽ നിങ്ങളുടെ നിലവിലെ ബാലൻസ് കാണാം, എടിഎം ബാലൻസ് എന്‍ക്വയറി ഫീസ് ലാഭിക്കാം, ഏതു സമയത്തും എത്ര തവണയും നിങ്ങളുടെ നിലവിലെ ബാലൻസ് കാണാം:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (62, N'sub_1', N'ta', N'உங்கள் சம்பளம் கணக்கில் டெபாசிட் செய்யப்பட்டவுடன் SMS பெறுங்கள்:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (63, N'sub_2', N'ta', N'உங்களுக்குத் தெரியாமல் உங்கள் கார்டை யாரும் பயன்படுத்தாதபடி பாதுகாத்திடுங்கள். எப்போது வேண்டுமானாலும் SMS பெறுவதன் மூலம் உங்கள் கணக்கைப் பாதுகாப்பாக வைத்திருங்கள்:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (64, N'sub_3', N'ta', N'எப்போது வேண்டுமானாலும் உங்கள் தற்போதைய பேலன்ஸைப் பார்க்கலாம், உங்களின் தற்போதைய பேலன்ஸைப் பார்க்கலாம், ஏடிஎம்-இல் பேலன்ஸ் தெரிந்துகொள்வதற்கான கட்டணத்தைச் சேமிக்கலாம். இந்தச் செயலியில் எப்போது வேண்டுமானாலும் உங்கள் தற்போதைய பேலன்ஸைப் பார்க்கலாம்:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (65, N'sub_1', N'ur-en', N'Aapke khaate mein aapki tankhawah jama kiye jaane ke waqt ek SMS wasool karein:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (66, N'sub_2', N'ur-en', N'Apne card ko aap ke Ilm ke begair istemaal hone se mahfuz karein. Ek SMS wasool karte huye apna khata har us waqt mahfuz rakhein jab:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (67, N'sub_3', N'ur-en', N'Kisi bhi waqt apna mujoodah balance dekhein, apna mujoodah balance dekhein, ATM balance keistifsaar ki fees par bachat karein aur is app par jitni baar chaahen kisi bhi waqt apna mujoodah balance dekhein:')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (68, N'feature_1', N'en', N'You received your salary')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (69, N'feature_2', N'en', N'Your card is used online')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (70, N'feature_3', N'en', N'Your card is used at the ATM')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (71, N'feature_4', N'en', N'Your card is used in shops')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (72, N'feature_5', N'en', N'You receive your salary')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (73, N'feature_6', N'en', N'Your card is used at the ATM')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (74, N'feature_7', N'en', N'Your card is used in shops')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (75, N'feature_8', N'en', N'Your card is used online')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (76, N'feature_9', N'en', N'Avoid fees on ATMs')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (77, N'feature_10', N'en', N'View your transactions')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (78, N'feature_11', N'en', N'View your balance')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (79, N'feature_12', N'en', N'Anytime & anywhere')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (80, N'feature_1', N'hi', N'आपने अपना वेतन प्राप्त किया')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (81, N'feature_2', N'hi', N'आपका कार्ड ऑनलाइन इस्तेमाल हुआ है')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (82, N'feature_3', N'hi', N'ATM पर आपका कार्ड इस्तेमाल हुआ है')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (83, N'feature_4', N'hi', N'आपका कार्ड दुकानों में इस्तेमाल हुआ है')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (84, N'feature_5', N'hi', N'आपने अपना वेतन प्राप्त किया')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (85, N'feature_6', N'hi', N'ATM पर आपका कार्ड इस्तेमाल किया गया हैं')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (86, N'feature_7', N'hi', N'आपका कार्ड दुकानों में इस्तेमाल हुआ है')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (87, N'feature_8', N'hi', N'आपका कार्ड ऑनलाइन इस्तेमाल हुआ है')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (88, N'feature_9', N'hi', N'ATM के शुल्क से बचें')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (89, N'feature_10', N'hi', N'अपने लेन-देन (ट्रैन्सैक्शनस) देखें')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (90, N'feature_11', N'hi', N'अपना बैलेंस देखें')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (91, N'feature_12', N'hi', N'कभी भी और कहीं भी')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (92, N'feature_1', N'hi-en', N'Aapki salary credit ho gai hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (93, N'feature_2', N'hi-en', N'Aapka card online use hua hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (94, N'feature_3', N'hi-en', N'ATM par aapka card use hua hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (95, N'feature_4', N'hi-en', N'Shops men aapka card use hua hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (96, N'feature_5', N'hi-en', N'Aapke account men salary credit hui ho')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (97, N'feature_6', N'hi-en', N'ATM par aapka card use kiya gaya hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (98, N'feature_7', N'hi-en', N'Shops men aapka card use kiya gaya hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (99, N'feature_8', N'hi-en', N'Online aapka card use kiya gaya hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (100, N'feature_9', N'hi-en', N'ATM ki fees se bachen')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (101, N'feature_10', N'hi-en', N'Apne transactions dekhein')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (102, N'feature_11', N'hi-en', N'Apna balance dekhen')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (103, N'feature_12', N'hi-en', N'Kabhi bhi aur kahin bhi')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (104, N'feature_1', N'ml', N'നിങ്ങളുടെ ശമ്പളം ലഭിച്ചു')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (105, N'feature_2', N'ml', N'നിങ്ങളുടെ കാർഡ് ഓൺലൈനിൽ ഉപയോഗിച്ചു')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (106, N'feature_3', N'ml', N'നിങ്ങളുടെ കാർഡ് എടിഎമ്മിൽ ഉപയോഗിച്ചു')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (107, N'feature_4', N'ml', N'നിങ്ങളുടെ കാർഡ് ഷോപ്പുകളില്‍ ഉപയോഗിച്ചു')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (108, N'feature_5', N'ml', N'നിങ്ങളുടെ ശമ്പളം നിങ്ങൾക്ക് ലഭിക്കുമ്പോള്‍')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (109, N'feature_6', N'ml', N'നിങ്ങളുടെ കാർഡ് എടിഎമ്മിൽ ഉപയോഗിക്കുമ്പോള്‍')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (110, N'feature_7', N'ml', N'നിങ്ങളുടെ കാർഡ് ഷോപ്പുകളില്‍ ഉപയോഗിക്കുമ്പോള്‍')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (111, N'feature_8', N'ml', N'നിങ്ങളുടെ കാർഡ് ഓൺലൈനിൽ ഉപയോഗിക്കുമ്പോള്‍')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (112, N'feature_9', N'ml', N'എടിഎം ഫീസ് ഒഴിവാക്കാം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (113, N'feature_10', N'ml', N'നിങ്ങളുടെ ട്രാന്‍സാക്ഷനുകള്‍ കാണാം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (114, N'feature_11', N'ml', N'നിങ്ങളുടെ ബാലൻസ് കാണാം')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (115, N'feature_12', N'ml', N'ഏതു സമയത്തും എവിടെ വച്ചും')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (116, N'feature_1', N'ta', N'உங்கள் சம்பளத்தைப் பெறும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (117, N'feature_2', N'ta', N'உங்கள் கார்டு ஆன்லைனில் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (118, N'feature_3', N'ta', N'உங்கள் கார்டு ஏடிஎம்-இல் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (119, N'feature_4', N'ta', N'உங்கள் கார்டு கடைகளில் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (120, N'feature_5', N'ta', N'உங்கள் சம்பளத்தைப் பெறும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (121, N'feature_6', N'ta', N'உங்கள் கார்டு ஏடிஎம்-இல் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (122, N'feature_7', N'ta', N'உங்கள் கார்டு கடைகளில் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (123, N'feature_8', N'ta', N'உங்கள் கார்டு ஆன்லைனில் பயன்படுத்தப்படும்போது')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (124, N'feature_9', N'ta', N'ஏடிஎம் கட்டணங்களைத் தவிருங்கள்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (125, N'feature_10', N'ta', N'உங்கள் பரிவர்த்தனைகளைப் பாருங்கள்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (126, N'feature_11', N'ta', N'உங்கள் பேலன்ஸைப் பாருங்கள்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (127, N'feature_12', N'ta', N'எப்போது வேண்டுமானாலும் எங்கிருந்தும்')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (128, N'feature_1', N'ur-en', N'Aap apni tankhwah wasool kar chuke hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (129, N'feature_2', N'ur-en', N'Aap ka card online istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (130, N'feature_3', N'ur-en', N'Aap ka card ATM mein istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (131, N'feature_4', N'ur-en', N'Aap ka card dukanon mein istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (132, N'feature_5', N'ur-en', N'Aap apni tankhwah wasool karte hain')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (133, N'feature_6', N'ur-en', N'Aap ka card ATM mein istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (134, N'feature_7', N'ur-en', N'Aap ka card dukanon mein istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (135, N'feature_8', N'ur-en', N'Aap ka card online istemaal hota hai')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (136, N'feature_9', N'ur-en', N'ATMs par fees se bachein')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (137, N'feature_10', N'ur-en', N'apni lenden dekhein')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (138, N'feature_11', N'ur-en', N'Apna balance dekhein')
                            GO
                            INSERT [dbo].[Translations] ([Id], [TextContentCode], [LanguageCode], [Text]) VALUES (139, N'feature_12', N'ur-en', N'Kisi bhi waqt aur kahin bhi')
                            GO
                            SET IDENTITY_INSERT [dbo].[Translations] OFF
                            GO

                            update SubscriptionFeatures set TextContentCode='feature_1' where id='5A35E788-6C84-4D40-9D12-03490902A21E'
                            update SubscriptionFeatures set TextContentCode='feature_2' where id='F760A7BF-3F2C-45BE-9F23-0BB3E0D96E50'
                            update SubscriptionFeatures set TextContentCode='feature_3' where id='76990D20-B275-4BA6-8B99-D9CC27205421'
                            update SubscriptionFeatures set TextContentCode='feature_4' where id='7E14E718-7981-4BAE-A08F-F146A5408642'
                            update SubscriptionFeatures set TextContentCode='feature_5' where id='38F76250-0E8B-4985-ADB1-7852368D6884'
                            update SubscriptionFeatures set TextContentCode='feature_6' where id='09FB418D-6219-4C5E-87DF-990C8B050919'
                            update SubscriptionFeatures set TextContentCode='feature_7' where id='0156F81C-A76F-43E8-9A95-B8244C6CD725'
                            update SubscriptionFeatures set TextContentCode='feature_8' where id='36F0989D-DDF5-474C-B18C-E9343D35EDB5'
                            update SubscriptionFeatures set TextContentCode='feature_9' where id='3E569303-038E-413E-B3E2-3DFE3F3B2B76'
                            update SubscriptionFeatures set TextContentCode='feature_10' where id='596A0B4C-9C68-48AB-B574-4A8FC3431EDD'
                            update SubscriptionFeatures set TextContentCode='feature_11' where id='94B6C134-5E52-4019-ACB1-9C0884C28E31'
                            update SubscriptionFeatures set TextContentCode='feature_12' where id='170FD6AE-7C74-4604-BC5E-F0F79EA6398A'
                            
                            update Subscriptions set TextContentCode='sub_1' where id='9A79DD89-2FCC-43B8-801B-8261BE81C97B'
                            update Subscriptions set TextContentCode='sub_2' where id='C5CA013E-54DC-4DE6-B2A4-B567BC0A2AD2'
                            update Subscriptions set TextContentCode='sub_3' where id='30A85D94-3485-4D6F-87DD-E115E67F5770'
                            ";
            migrationBuilder.Sql(query);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
