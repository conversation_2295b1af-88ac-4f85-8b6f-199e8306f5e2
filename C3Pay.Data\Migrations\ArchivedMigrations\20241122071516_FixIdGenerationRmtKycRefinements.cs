﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class FixIdGenerationRmtKycRefinements : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RmtKycRefinement_Users_UserId",
                table: "RmtKycRefinement");

            migrationBuilder.AlterColumn<string>(
                name: "RequiredAction",
                table: "RmtKycRefinement",
                type: "varchar(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Remarks",
                table: "RmtKycRefinement",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EmiratesId",
                table: "RmtKycRefinement",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CardHolderId",
                table: "RmtKycRefinement",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "RmtKycRefinement",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWSEQUENTIALID()",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.CreateIndex(
                name: "IX_RmtKycRefinement_CardHolderId",
                table: "RmtKycRefinement",
                column: "CardHolderId");

            migrationBuilder.CreateIndex(
                name: "IX_RmtKycRefinement_ProcessedDateTime",
                table: "RmtKycRefinement",
                column: "ProcessedDateTime");

            migrationBuilder.AddForeignKey(
                name: "FK_RmtKycRefinement_Users_UserId",
                table: "RmtKycRefinement",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RmtKycRefinement_Users_UserId",
                table: "RmtKycRefinement");

            migrationBuilder.DropIndex(
                name: "IX_RmtKycRefinement_CardHolderId",
                table: "RmtKycRefinement");

            migrationBuilder.DropIndex(
                name: "IX_RmtKycRefinement_ProcessedDateTime",
                table: "RmtKycRefinement");

            migrationBuilder.AlterColumn<string>(
                name: "RequiredAction",
                table: "RmtKycRefinement",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(20)",
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Remarks",
                table: "RmtKycRefinement",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "EmiratesId",
                table: "RmtKycRefinement",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(15)",
                oldMaxLength: 15,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CardHolderId",
                table: "RmtKycRefinement",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(15)",
                oldMaxLength: 15,
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "RmtKycRefinement",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldDefaultValueSql: "NEWSEQUENTIALID()");

            migrationBuilder.AddForeignKey(
                name: "FK_RmtKycRefinement_Users_UserId",
                table: "RmtKycRefinement",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
