﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Extra_Props_ExTransactions_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CustomerAddress",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDExpDate",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDIssueDate",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDPlaceOfIssue",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UniqueIdentificationNo",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CustomerAddress",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDExpDate",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDIssueDate",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDPlaceOfIssue",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "UniqueIdentificationNo",
                table: "MoneyTransferExternalTransactions");
        }
    }
}
