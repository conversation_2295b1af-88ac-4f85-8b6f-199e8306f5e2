﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateExclusion_BaseModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "MissingKycExclusions",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1900, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "MissingKycExclusions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "MissingKycExclusions",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "MissingKycExclusions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "MissingKycExclusions",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "MissingKycExclusions");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "MissingKycExclusions");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "MissingKycExclusions");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "MissingKycExclusions");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "MissingKycExclusions");
        }
    }
}
