﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SpendPolicyModifications : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserSpendPolicy_SpendPolicyId",
                table: "Users");

            migrationBuilder.RenameColumn(
                name: "UserSpendPolicy_SpendPolicyActive",
                table: "Users",
                newName: "SpendPolicy_IsActive");

            migrationBuilder.AddColumn<string>(
                name: "SpendPolicy_PolicyId",
                table: "Users",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SpendPolicy_PolicyId",
                table: "Users");

            migrationBuilder.RenameColumn(
                name: "SpendPolicy_IsActive",
                table: "Users",
                newName: "UserSpendPolicy_SpendPolicyActive");

            migrationBuilder.AddColumn<string>(
                name: "UserSpendPolicy_SpendPolicyId",
                table: "Users",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
