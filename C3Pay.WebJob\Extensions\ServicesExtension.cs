﻿using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Core.Services.C3Pay;
using C3Pay.Core.Services.C3Pay.Membership;
using C3Pay.Services;
using C3Pay.Services.Infobip;
using C3Pay.Services.Membership;
using C3Pay.Services.Validators;
using Edenred.Common.Core;
using FluentValidation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;

namespace C3Pay.WebJob.Extensions
{
    public static class ServicesExtension
    {
        /// <summary>
        /// Injects the validators
        /// </summary>
        /// <param name="services"></param>
        public static void InjectValidators(this IServiceCollection services)
        {
            services.AddScoped<IValidator<Rating>, RatingValidator>();
            services.AddScoped<IValidator<MoneyTransferSuspiciousInformation>, MoneyTransferSuspiciousInformationValidator>();
        }

        public static void AddRewardService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IRewardService, RewardService>();
            if (!services.Any((ServiceDescriptor x) => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            RewardServiceSettings settings = new RewardServiceSettings();
            configuration.GetSection("RewardService").Bind(settings);
            services.Configure<RewardServiceSettings>(configuration.GetSection("RewardService"));

            Uri uri = null;
            TimeSpan timeout = TimeSpan.FromSeconds(10);
            if (Uri.TryCreate(settings.BaseAddress, UriKind.Absolute, out var address))
                uri = address;

            if (settings.Timeout > 0)
                timeout = TimeSpan.FromSeconds(settings.Timeout);

            services.AddHttpClient("RewardService", delegate (HttpClient client)
            {
                client.BaseAddress = uri;
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.Timeout = timeout;
            }).AddHttpMessageHandler<HttpClientLoggingHandler>();
        }
    }
}
