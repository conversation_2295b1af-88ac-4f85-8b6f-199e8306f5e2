﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_IsStillActive_ToC3PayPlusUsers_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "C3PayPlusMembershipUsers",
                newName: "UserHasCanceled");

            migrationBuilder.AddColumn<bool>(
                name: "IsStillActive",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UserCanceledOn",
                table: "C3PayPlusMembershipUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ValidUntill",
                table: "C3PayPlusMembershipUsers",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsStillActive",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "UserCanceledOn",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "ValidUntill",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.RenameColumn(
                name: "UserHasCanceled",
                table: "C3PayPlusMembershipUsers",
                newName: "IsActive");
        }
    }
}
