﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_C3PayPlusUsers_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "EndDate",
                table: "C3PayPlusMembershipUsers",
                newName: "NextBillingDate");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastSuccessfulBillingDate",
                table: "C3PayPlusMembershipUsers",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "LastSuccessfulBillingDate",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.RenameColumn(
                name: "NextBillingDate",
                table: "C3PayPlusMembershipUsers",
                newName: "EndDate");
        }
    }
}
