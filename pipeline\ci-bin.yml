name: $(Date:yyyyMMdd)$(Rev:.r)

pool:  
  vmimage: ubuntu-latest 
  #vmimage: 'windows-latest'

resources:
  - repo: self

trigger:
  branches:
    include:   
    - master
    - Release
    - develop

  paths:
    exclude:
       - pipeline/*
       - infra/*   

variables:
  buildConfiguration: 'Release'  
  webApiArtifactName: 'webapi'
  portalWebApiArtifactName: 'portalwebapi' 
  webJobArtifactName: 'webJob' 
  sqlArtifactName: 'sql'
  identitySqlArtifactName: 'identitySql'
  apiProjectPath: 'C3Pay.API/C3Pay.API.csproj'
  nugetConfigPath: 'NuGet/NuGet.config'
  portalApiProjectPath: 'C3Pay.Portal.API/C3Pay.Portal.API.csproj'
  webJobProjectPath: 'C3Pay.WebJob/C3Pay.WebJob.csproj'
  outputApiFolder: '$(build.artifactstagingdirectory)/api'
  outputPortalApiFolder: '$(build.artifactstagingdirectory)/portalApi'
  outputWebJobFolder: '$(build.artifactstagingdirectory)/webJob'
  WebJobAppFolder: '/App_Data/jobs/Continuous/BackgroungProcessor'
  sqlFile: '$(build.artifactstagingdirectory)/sql/C3PayContext.sql'
  identitySqlFile: '$(build.artifactstagingdirectory)/identitySql/C3PayIdentityDbContext.sql'
  contextName: C3PayContext
  identityContextName: C3PayIdentityDbContext
steps:

  - script: dotnet --info
    displayName: Display information about the dotnet core installation

  - task: UseDotNet@2
    displayName: Install  dotnet core SDK
    inputs:
      version: 8.x

  - template: ci-bin-dotnet-steps.yml
    parameters:                  
      apiProjectPath: $(apiProjectPath)
      nugetConfigPath:  $(nugetConfigPath)
      webJobProjectPath: $(webJobProjectPath)
      portalApiProjectPath: $(portalApiProjectPath)
      buildConfiguration: $(buildConfiguration) 
      apiOutputFolder: $(outputApiFolder)
      webJobOutputFolder: $(outputWebJobFolder)
      outputPortalApiFolder: $(outputPortalApiFolder)
      WebJobAppFolder: $(WebJobAppFolder)

  - template: ci-ef-migration-steps.yml
    parameters:                  
      projectPath: $(apiProjectPath)
      portalApiProjectPath: $(portalApiProjectPath)
      contextName: $(contextName) 
      sqlFile: $(sqlFile)
      identityContextName: $(identityContextName)
      identitySqlFile: $(identitySqlFile)

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact $(webApiArtifactName)'
    inputs:
      targetPath: $(outputApiFolder)
      ArtifactName: '$(webApiArtifactName)'
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release', 'refs/heads/develop'))

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact $(portalWebApiArtifactName)'
    inputs:
      targetPath: $(outputPortalApiFolder)
      ArtifactName: '$(portalWebApiArtifactName)'
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release', 'refs/heads/develop'))

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact $(webJobArtifactName)'
    inputs:
      targetPath: $(outputWebJobFolder)
      ArtifactName: '$(webJobArtifactName)'
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release', 'refs/heads/develop'))

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact $(sqlArtifactName)'
    inputs:
      targetPath: $(sqlFile)
      ArtifactName: '$(sqlArtifactName)'
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release', 'refs/heads/develop')) 

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Artifact $(identitySqlArtifactName)'
    inputs:
      targetPath: $(identitySqlFile)
      ArtifactName: '$(identitySqlArtifactName)'
    condition: and(succeeded(), in(variables['Build.SourceBranch'], 'refs/heads/master', 'refs/heads/release', 'refs/heads/develop'))