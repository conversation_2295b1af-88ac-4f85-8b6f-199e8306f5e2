﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class DropMoneyTransferTransactionHistory : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferTransactionHistory");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferTransactionHistory",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ExternalReferenceNumber = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ExternalStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ExternalStatusDescription = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MoneyTransferTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferTransactionHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferTransactionHistory_MoneyTransferTransactions_MoneyTransferTransactionId",
                        column: x => x.MoneyTransferTransactionId,
                        principalTable: "MoneyTransferTransactions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactionHistory_MoneyTransferTransactionId",
                table: "MoneyTransferTransactionHistory",
                column: "MoneyTransferTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactionHistory_ReferenceNumber",
                table: "MoneyTransferTransactionHistory",
                column: "ReferenceNumber");
        }
    }
}
