parameters:
  - name: azureSubscription
    type: string  
  - name: templateLocation
    type: string
  - name: templateSasToken
    type: string 
  - name: environment
    type: string
  - name: entityCode
    type: string
  - name: appName
    type: string  
  - name: existingAppServiceName
    type: string
  - name: existingWebjobServiceName
    type: string
  - name: location
    type: string
  - name: secondaryLocation
    type: string  
  - name: rgName
    type: string  
  - name: farmSkuName
    type: string
  - name: SparkVMIP
    type: string
  - name: webJobFarmSkuName
    type: string
  - name: farmSkuCapacity
    type: number
  - name: farmSkuCapacityMin
    type: number
    default: 0
  - name: farmSkuCapacityMax
    type: number
    default: 0
  - name: dbName
    type: string
  - name: dbSkuName
    type: string    
  - name: dbWeeklyLtr
    type: string
  - name: dbMonthlyLtr
    type: string
  - name: identityDbSkuName
    type: string
  - name: serviceBusSkuName
    type: string
  - name: infraFile
    type: string
  - name: sqlsrvAdministratorLogin
    type: string
  - name: sqlsrvAdministratorPassword
    type: string
  - name: Dev<PERSON>
    type: string
  - name: DevEmails
    type: string
  - name: SettingApiManagementName
    type: string
  - name: UAENorthAppName
    type: string
    default: "eae-test-c3pay-web-a"
  - name: isSlotDeploymentEnabled
    type: string
  - name: slotDeploymentInstanceName
    type: string
steps:    
  - script: echo "-templatesLocation ${{ parameters.TemplateLocation }} -webjobfarmname $(existingWebjobServiceName) -webjoboldfarmname ${{ parameters.existingWebjobServiceName }}  -sasToken ${{ parameters.templateSasToken }} -environment ${{ parameters.environment }} -entityCode ${{ parameters.entityCode }} -location ${{ parameters.location }} -appName ${{ parameters.appName }} -appShortName ${{ parameters.appName }} -farmSkuName ${{ parameters.farmSkuName }} -dbSkuName ${{ parameters.dbSkuName }} -identityDbSkuName ${{ parameters.identityDbSkuName }} -sqlsrvAdministratorLogin ${{ parameters.sqlsrvAdministratorLogin }} -sqlsrvAdministratorPassword ${{ parameters.sqlsrvAdministratorPassword }} User Id = ${{ parameters.sqlsrvAdministratorLogin }}; password = ${{ parameters.sqlsrvAdministratorPassword }};'"
    displayName: 'Display Deplyoment parameters'
    name: EchoDeploymentParameters

  - task: AzureResourceGroupDeployment@2
    displayName: 'Azure Deployment: Create infra'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      resourceGroupName: ${{ parameters.rgName }}
      location: ${{ parameters.location }}
      csmFile: ${{ parameters.infraFile }}
      overrideParameters: '-templatesLocation ${{ parameters.TemplateLocation }} 
                          -sasToken ${{ parameters.templateSasToken }} 
                          -environment ${{ parameters.environment }} 
                          -entityCode ${{ parameters.entityCode }} 
                          -location ${{ parameters.location }} 
                          -secondaryLocation ${{ parameters.secondaryLocation }} 
                          -appName ${{ parameters.appName }}
                          -existingFarmName ${{ parameters.existingAppServiceName }}
                          -existingWebjobFarmName ${{ parameters.existingWebjobServiceName }}
                          -appShortName ${{ parameters.appName }} 
                          -farmSkuName ${{ parameters.farmSkuName }} 
                          -webJobFarmSkuName ${{ parameters.webJobFarmSkuName }} 
                          -farmSkuCapacity ${{ parameters.farmSkuCapacity }} 
                          -dbName ${{ parameters.dbName }}
                          -dbSkuName ${{ parameters.dbSkuName }} 
                          -identityDbSkuName ${{ parameters.identityDbSkuName }} 
                          -serviceBusSkuName ${{ parameters.serviceBusSkuName }}
                          -sqlsrvAdministratorLogin ${{ parameters.sqlsrvAdministratorLogin }} 
                          -sqlsrvAdministratorPassword ${{ parameters.sqlsrvAdministratorPassword }}'      
      deploymentOutputs: infraOutputs
 
  - task: PowerShell@2    
    displayName: 'Azure Deployment: Parse output parameters'
    inputs:
      targetType: filePath
      filePath: '$(System.DefaultWorkingDirectory)/ArmOutputsParser.ps1'
      arguments: '-armOutputString ''$(infraOutputs)'''   
  
  - task: AzurePowerShell@4
    displayName: 'Key Vault: Configure the Access Policy'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |
        . $(System.DefaultWorkingDirectory)/AzAccessPolicyHelper.ps1
        
        Write-Host "Defines the access Policies for the webapps - Production slots"
        
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webAppName) -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(portalWebAppName) -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webJobName) -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt -PermissionsToCertificates get

        # Adding access policies for staging slots
        Write-Host "Defines the access Policies for the webapps - Staging slots"
        $slotNameWebApp = "$(webAppName)/slots/staging"
        Write-Host "Adding access policy for $slotNameWebApp"
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $slotNameWebApp -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt
        
        $slotNamePortalApp = "$(portalWebAppName)/slots/staging"
        Write-Host "Adding access policy for $slotNamePortalApp"
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $slotNamePortalApp -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt
       
        $slotNameWebJobApp = "$(webJobName)/slots/staging"
        Write-Host "Adding access policy for $slotNameWebJobApp"        
        Allow-AzureWebAppThroughKeyVaultPolicy -ResourceGroupName ${{ parameters.rgName }} -WebAppName $slotNameWebJobApp -VaultName $(keyvaultName) -PermissionsToSecrets get,list -PermissionsToKeys create,get,wrapKey,unwrapKey,sign,verify,list,encrypt,decrypt -PermissionsToCertificates get
        
        Write-Host "Defines the access Policies for the webapp"
      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Add Gateway Access Rule For Web APP'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        Inline: |
            $ruleName = "Gateway"
              
            # Retrieve the current access restriction configuration for the web app
            $accessRestrictions = (Get-AzWebAppAccessRestrictionConfig -ResourceGroupName ${{ parameters.rgName }} -Name $(webAppName)).MainSiteAccessRestrictions
            foreach($result in $accessRestrictions)
            {
                Write-Host $result.RuleName               
                if($result.RuleName -eq $ruleName){
                    $ruleExists = $true;
                    break;
                }
            }
          
            if ($ruleExists -eq $true) {
              Write-Host "Access restriction rule '$ruleName' already exists."
            }
            else {
                $Gateway = Get-AzApiManagement -Name ${{ parameters.SettingApiManagementName }} -ResourceGroupName ${{ parameters.rgName }}
                $GatewayIPAddress = $Gateway.PublicIPAddresses[0];
                Write-Host $GatewayIPAddress 
                Add-AzWebAppAccessRestrictionRule -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webAppName) -Name $ruleName -Priority 120 -Action Allow -IpAddress "$GatewayIPAddress/32" 
                Write-Host "Access restriction rule '$ruleName' created."
            }
          

        azurePowerShellVersion: LatestVersion

# Whitelisted the Spark VM IP since the KYC service communicates directly with the C3Pay backend.
  - task: AzurePowerShell@4
    displayName: 'Add SparkVM Access Rule For Web APP'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        Inline: |
            $ruleName = "SparkVM"
            $sparkVMIP = "${{ parameters.SparkVMIP }}"
              
            # Retrieve the current access restriction configuration for the web app
            $accessRestrictions = (Get-AzWebAppAccessRestrictionConfig -ResourceGroupName ${{ parameters.rgName }} -Name $(webAppName)).MainSiteAccessRestrictions
            foreach($result in $accessRestrictions)
            {
                Write-Host $result.RuleName               
                if($result.RuleName -eq $ruleName){
                    $ruleExists = $true;
                    break;
                }
            }
          
            if ($ruleExists -eq $true) {
              Write-Host "Access restriction rule '$ruleName' already exists."
            }
            else {
                Add-AzWebAppAccessRestrictionRule -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webAppName) -Name $ruleName -Priority 130 -Action Allow -IpAddress $sparkVMIP 
                Write-Host "Access restriction rule '$ruleName' created."
            }
          
        azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Deny Unmatched Rules Access For Web APP'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        Inline: |
             $ResourceWebAPP = Get-AzResource -ResourceType Microsoft.Web/sites -ResourceGroupName ${{ parameters.rgName }} -ResourceName $(webAppName)
             $ResourceWebAPP.Properties.siteConfig.ipSecurityRestrictionsDefaultAction = "Deny"
             $ResourceWebAPP | Set-AzResource -Force
             Write-Host "Denied Unmatched Rules Access."
          
        azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Key Vault: Configure api management Access Policy'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |
        Write-Host "Defines the access Policies for the api management"
        
        $apiManagementPrincipalId = (Get-AzApiManagement -ResourceGroupName ${{ parameters.rgName }} -Name ${{ parameters.SettingApiManagementName }}).Identity.PrincipalId
        Set-AzKeyVaultAccessPolicy -VaultName $(keyvaultName) -ObjectId $apiManagementPrincipalId -PermissionsToSecrets all -PermissionsToKeys all -PermissionsToCertificates all -PassThru -BypassObjectIdValidation

      azurePowerShellVersion: LatestVersion
  
  - task: AzurePowerShell@4
    displayName: 'Key Vault: Configure the network rule'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |
        . $(System.DefaultWorkingDirectory)/AzFirewallHelper.ps1
        
        Write-Host "Defines network rule for the WebApp"
                
        Allow-AzureWebAppThroughKeyVaultFirewall -ResourceGroupName ${{ parameters.rgName }}  -WebAppName $(webAppName) -VaultName $(keyvaultName)
      azurePowerShellVersion: LatestVersion
  
  
  - task: AzurePowerShell@4
    displayName: 'SQL: Configure the network rule'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |
          . $(System.DefaultWorkingDirectory)/AzFirewallHelper.ps1
          
          Write-Host "Defines network rule for the WebApp"
                    
          Allow-AzureWebAppThroughSqlServerFirewall -ResourceGroupName  ${{ parameters.rgName }}  -WebAppName $(webAppName) -ServerName $(sqlName)
          #Allow-AzureWebAppThroughSqlServerFirewall -ResourceGroupName  ${{ parameters.rgName }}  -WebAppName $(webJobName) -ServerName $(sqlName)
          Allow-AzureWebAppThroughSqlServerFirewall -ResourceGroupName  ${{ parameters.rgName }}  -WebAppName $(portalWebAppName) -ServerName $(sqlName)
          
          IF (![string]::IsNullOrWhitespace("${{ parameters.DevIP }}"))
          {
            Write-Host "Defines IP rules for the Dev machines"

            $devIps = "${{ parameters.DevIP }}".split("{;}")
            
            Foreach ($ip in $devIps)
            {
              $ruleName = $($devIps.IndexOf($ip))
              Write-Host $ruleName - $ip
              Add-AzureSqlServerFirewallRule -ResourceGroupName ${{ parameters.rgName }} -ServerName $(sqlName)        -RuleName "DevIP-$($ruleName)" -StartIpAddress $ip -EndIpAddress $ip
            }
          }                     
         
      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'SQL: Configure Long Term Retenion'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |                   
          Write-Host "Defines LTR rules for databases"

          $weeklyRule = "P$(${{ parameters.dbWeeklyLtr }})W"

          Write-Host $weeklyRule

          $monthlyRule = "P$(${{ parameters.dbMonthlyLtr }})M"

          Write-Host $monthlyRule
                    
          Set-AzSqlDatabaseBackupLongTermRetentionPolicy -ServerName $(sqlName) -DatabaseName $(dbName) ` -ResourceGroupName ${{ parameters.rgName }} -WeeklyRetention $weeklyRule -MonthlyRetention $monthlyRule
          Set-AzSqlDatabaseBackupLongTermRetentionPolicy -ServerName $(sqlName) -DatabaseName $(identityDbName) ` -ResourceGroupName ${{ parameters.rgName }} -WeeklyRetention $weeklyRule -MonthlyRetention $monthlyRule
         
      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Storage: Configure the network rule'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |
          . $(System.DefaultWorkingDirectory)/AzFirewallHelper.ps1
          
          Write-Host "Defines network rule for the stroage"
          
          Allow-AzureWebAppThroughStorageFirewall -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webAppName) -StorageAccountName $(storageName) 
          Allow-AzureWebAppThroughStorageFirewall -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(portalWebAppName) -StorageAccountName $(storageName) 
          Allow-AzureWebAppThroughStorageFirewall -ResourceGroupName ${{ parameters.rgName }} -WebAppName $(webJobName) -StorageAccountName $(storageName)                                        
      azurePowerShellVersion: LatestVersion
  
  
  - task: PowerShell@2
    name: setOutputParameters
    displayName: 'PS: Prepare output parameters'
    inputs:
      targetType: 'inline'
      script: |        
        Write-Host "##vso[task.setvariable variable=aiName;isOutput=true]$(aiName)"
        Write-Host "##vso[task.setvariable variable=keyVaultName;isOutput=true]$(keyvaultName)"
        Write-Host "##vso[task.setvariable variable=webAppName;isOutput=true]$(webAppName)"
        Write-Host "##vso[task.setvariable variable=portalWebAppName;isOutput=true]$(portalWebAppName)"
        Write-Host "##vso[task.setvariable variable=webJobName;isOutput=true]$(webJobName)"
        Write-Host "##vso[task.setvariable variable=sqlName;isOutput=true]$(sqlName)"   
        Write-Host "##vso[task.setvariable variable=dbName;isOutput=true]$(dbName)"
        Write-Host "##vso[task.setvariable variable=identityDbName;isOutput=true]$(identityDbName)"
        Write-Host "##vso[task.setvariable variable=serviceBusName;isOutput=true]$(serviceBusName)" 
        Write-Host "##vso[task.setvariable variable=storageName;isOutput=true]$(storageName)"  
        Write-Host "##vso[task.setvariable variable=storageWebJobName;isOutput=true]$(storageWebJobName)"
                                   

