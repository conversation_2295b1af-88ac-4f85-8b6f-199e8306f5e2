﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class CashPickUpNewProperties : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CashPickUpPoint",
                table: "MoneyTransferTransactions",
                type: "nvarchar(150)",
                maxLength: 150,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CashPickupPin",
                table: "MoneyTransferTransactions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CashPickUpPoint",
                table: "MoneyTransferTransactions");

            migrationBuilder.DropColumn(
                name: "CashPickupPin",
                table: "MoneyTransferTransactions");
        }
    }
}
