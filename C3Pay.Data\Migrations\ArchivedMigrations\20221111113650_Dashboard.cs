﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Dashboard : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        { 
            var query = @"   
                IF EXISTS (SELECT 1 
		                FROM INFORMATION_SCHEMA.TABLES 
		                WHERE TABLE_TYPE='BASE TABLE' 
		                AND TABLE_NAME='DashboardElements') 
                BEGIN   
                    delete from Translations where TextContentCode like '%da_%'
                    delete from DashboardQuickActionElements
                    delete from DashboardElements
                    delete from TextContents where Code like '%da_%'  
                    delete from DashboardSections 
                    drop table DashboardQuickActionElements
                    drop table DashboardElements
                    drop table DashboardSections
                END 
            ";
            migrationBuilder.Sql(query);

            migrationBuilder.CreateTable(
                name: "DashboardSections",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardSections", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DashboardElements",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DeepLinkUrl = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    SectionId = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardElements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DashboardElements_DashboardSections_SectionId",
                        column: x => x.SectionId,
                        principalTable: "DashboardSections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "DashboardQuickActionElements",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ElementId = table.Column<int>(type: "int", nullable: false),
                    IconUrl = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    TextContentCode = table.Column<string>(type: "nvarchar(25)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardQuickActionElements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DashboardQuickActionElements_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DashboardQuickActionElements_DashboardElements_ElementId",
                        column: x => x.ElementId,
                        principalTable: "DashboardElements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DashboardQuickActionElements_TextContents_TextContentCode",
                        column: x => x.TextContentCode,
                        principalTable: "TextContents",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Cities",
                columns: new[] { "Id", "CountryCode", "IsStoreEnabled", "Name" },
                values: new object[,]
                {
                    { 6, "AE", true, "Fujairah" },
                    { 7, "AE", true, "Umm Al-Quwain" }
                });

            migrationBuilder.InsertData(
                table: "DashboardSections",
                columns: new[] { "Id", "IsActive", "Name" },
                values: new object[] { 1, true, "Quick Action" });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[,]
                {
                    { "da_qck_act_phl_9", "en", 99, "Pay International Bills", "da_qck_act_phl" },
                    { "da_qck_act_phl_8", "en", 99, "View ATM Pin", "da_qck_act_phl" },
                    { "da_qck_act_phl_7", "en", 99, "History & Settlements", "da_qck_act_phl" },
                    { "da_qck_act_phl_6", "en", 99, "Pay Bills", "da_qck_act_phl" },
                    { "da_qck_act_phl_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_phl" },
                    { "da_qck_act_phl_4", "en", 99, "Get Loan", "da_qck_act_phl" },
                    { "da_qck_act_phl_3", "en", 99, "Recharge International", "da_qck_act_phl" },
                    { "da_qck_act_phl_2", "en", 99, "Recharge Local", "da_qck_act_phl" },
                    { "da_qck_act_phl_1", "en", 99, "Send Money", "da_qck_act_phl" },
                    { "da_qck_act_lka_9", "en", 99, "Pay International Bills", "da_qck_act_lka" },
                    { "da_qck_act_lka_8", "en", 99, "View ATM Pin", "da_qck_act_lka" },
                    { "da_qck_act_lka_7", "en", 99, "History & Settlements", "da_qck_act_lka" },
                    { "da_qck_act_bgd_1", "en", 99, "Send Money", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_2", "en", 99, "Recharge Local", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_3", "en", 99, "Recharge International", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_4", "en", 99, "Get Loan", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_6", "en", 99, "Pay Bills", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_7", "en", 99, "History & Settlements", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_8", "en", 99, "View ATM Pin", "da_qck_act_bgd" },
                    { "da_qck_act_bgd_9", "en", 99, "Pay International Bills", "da_qck_act_bgd" },
                    { "da_qck_act_npl_1", "en", 99, "Send Money", "da_qck_act_npl" },
                    { "da_qck_act_npl_2", "en", 99, "Recharge Local", "da_qck_act_npl" },
                    { "da_qck_act_npl_3", "en", 99, "Recharge International", "da_qck_act_npl" },
                    { "da_qck_act_npl_4", "en", 99, "Get Loan", "da_qck_act_npl" },
                    { "da_qck_act_npl_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_npl" },
                    { "da_qck_act_npl_6", "en", 99, "Pay Bills", "da_qck_act_npl" },
                    { "da_qck_act_npl_7", "en", 99, "History & Settlements", "da_qck_act_npl" },
                    { "da_qck_act_lka_6", "en", 99, "Pay Bills", "da_qck_act_lka" },
                    { "da_qck_act_lka_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_lka" },
                    { "da_qck_act_lka_3", "en", 99, "Recharge International", "da_qck_act_lka" },
                    { "da_qck_act_npl_8", "en", 99, "View ATM Pin", "da_qck_act_npl" },
                    { "da_qck_act_1", "en", 99, "Send Money", "da_qck_act" },
                    { "da_qck_act_2", "en", 99, "Recharge Local", "da_qck_act" },
                    { "da_qck_act_3", "en", 99, "Recharge International", "da_qck_act" },
                    { "da_qck_act_4", "en", 99, "Get Loan", "da_qck_act" },
                    { "da_qck_act_5", "en", 99, "C3Pay to C3Pay", "da_qck_act" },
                    { "da_qck_act_6", "en", 99, "Pay Bills", "da_qck_act" },
                    { "da_qck_act_7", "en", 99, "History & Settlements", "da_qck_act" }
                });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[,]
                {
                    { "da_qck_act_8", "en", 99, "View ATM Pin", "da_qck_act" },
                    { "da_qck_act_9", "en", 99, "Pay International Bills", "da_qck_act" },
                    { "da_qck_act_ind_1", "en", 99, "Send Money", "da_qck_act_ind" },
                    { "da_qck_act_ind_2", "en", 99, "Recharge Local", "da_qck_act_ind" },
                    { "da_qck_act_ind_3", "en", 99, "Recharge International", "da_qck_act_ind" },
                    { "da_qck_act_ind_4", "en", 99, "Get Loan", "da_qck_act_ind" },
                    { "da_qck_act_ind_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_ind" },
                    { "da_qck_act_ind_6", "en", 99, "Pay Bills", "da_qck_act_ind" },
                    { "da_qck_act_ind_7", "en", 99, "History & Settlements", "da_qck_act_ind" },
                    { "da_qck_act_ind_8", "en", 99, "View ATM Pin", "da_qck_act_ind" },
                    { "da_qck_act_ind_9", "en", 99, "Pay International Bills", "da_qck_act_ind" },
                    { "da_qck_act_pak_1", "en", 99, "Send Money", "da_qck_act_pak" },
                    { "da_qck_act_pak_2", "en", 99, "Recharge Local", "da_qck_act_pak" },
                    { "da_qck_act_pak_3", "en", 99, "Recharge International", "da_qck_act_pak" },
                    { "da_qck_act_pak_4", "en", 99, "Get Loan", "da_qck_act_pak" },
                    { "da_qck_act_pak_5", "en", 99, "C3Pay to C3Pay", "da_qck_act_pak" },
                    { "da_qck_act_pak_6", "en", 99, "Pay Bills", "da_qck_act_pak" },
                    { "da_qck_act_pak_7", "en", 99, "History & Settlements", "da_qck_act_pak" },
                    { "da_qck_act_pak_8", "en", 99, "View ATM Pin", "da_qck_act_pak" },
                    { "da_qck_act_pak_9", "en", 99, "Pay International Bills", "da_qck_act_pak" },
                    { "da_qck_act_lka_1", "en", 99, "Send Money", "da_qck_act_lka" },
                    { "da_qck_act_lka_2", "en", 99, "Recharge Local", "da_qck_act_lka" },
                    { "da_qck_act_lka_4", "en", 99, "Get Loan", "da_qck_act_lka" },
                    { "da_qck_act_npl_9", "en", 99, "Pay International Bills", "da_qck_act_npl" }
                });

            migrationBuilder.InsertData(
                table: "DashboardElements",
                columns: new[] { "Id", "CreatedDate", "DeepLinkUrl", "DeletedBy", "DeletedDate", "DisplayOrder", "IsDeleted", "SectionId", "Type", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/moneytransfer", null, null, 1, false, 1, 1, null },
                    { 2, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/mobilerecharge", null, null, 2, false, 1, 1, null },
                    { 3, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/mobilerecharge", null, null, 3, false, 1, 1, null },
                    { 4, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/salaryadvance", null, null, 4, false, 1, 1, null },
                    { 5, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/moneytransfer/c3toc3", null, null, 5, false, 1, 1, null },
                    { 6, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/billpayments", null, null, 6, false, 1, 1, null },
                    { 7, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/transactions", null, null, 7, false, 1, 1, null },
                    { 8, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/pinreveal", null, null, 8, false, 1, 1, null },
                    { 9, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/billpayments", null, null, 6, false, 1, 1, null }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 441, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_phl_6" },
                    { 442, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_phl_6" },
                    { 443, "ur-en", "Pay Local Bills", "da_qck_act_phl_6" },
                    { 486, "en", "History and Statements", "da_qck_act_phl_7" },
                    { 491, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_phl_7" },
                    { 488, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_phl_7" },
                    { 489, "hi-en", "History aur statements", "da_qck_act_phl_7" },
                    { 490, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_phl_7" },
                    { 440, "hi-en", "Local Bills Pay Karein", "da_qck_act_phl_6" },
                    { 487, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_phl_7" },
                    { 439, "hi", "लोकल बिल चुकाए", "da_qck_act_phl_6" },
                    { 392, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_phl_5" },
                    { 437, "en", "Pay Local Bills", "da_qck_act_phl_6" },
                    { 394, "ur-en", "C3Pay to C3Pay", "da_qck_act_phl_5" },
                    { 393, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_phl_5" },
                    { 492, "ur-en", "History and Statements", "da_qck_act_phl_7" },
                    { 391, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_phl_5" },
                    { 390, "hi", "C3Pay से C3Pay", "da_qck_act_phl_5" },
                    { 389, "bn", "C3Pay থেকে C3Pay", "da_qck_act_phl_5" },
                    { 388, "en", "C3Pay to C3Pay", "da_qck_act_phl_5" },
                    { 345, "ur-en", "Get Loan", "da_qck_act_phl_4" },
                    { 344, "ta", "கடன் பெறுங்கள்", "da_qck_act_phl_4" },
                    { 343, "ml", "ലോൺ നേടുക", "da_qck_act_phl_4" },
                    { 438, "bn", "লোকাল বিলাস পে করুন ", "da_qck_act_phl_6" },
                    { 535, "en", "View ATM Pin", "da_qck_act_phl_8" },
                    { 540, "ta", "ATM PIN பார்க்க", "da_qck_act_phl_8" },
                    { 537, "hi", "ATM PIN देखें", "da_qck_act_phl_8" },
                    { 253, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_bgd_2" },
                    { 252, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_bgd_2" },
                    { 251, "hi-en", "Recharge Du Etisalat", "da_qck_act_bgd_2" },
                    { 250, "hi", "रिचार्ज Du Etisalat", "da_qck_act_bgd_2" },
                    { 249, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_bgd_2" },
                    { 248, "en", "Recharge Du Etisalat", "da_qck_act_bgd_2" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 205, "ur-en", "Send Money To Bangladesh", "da_qck_act_bgd_1" },
                    { 204, "ta", "பணம் அனுப்புங்கள் பங்களாதேஷுக்கு", "da_qck_act_bgd_1" },
                    { 203, "ml", "ബംഗ്ലാദേശിലേക്ക് പണം അയയ്ക്കുക", "da_qck_act_bgd_1" },
                    { 202, "hi-en", "Bangladesh Paisa Bhejiye", "da_qck_act_bgd_1" },
                    { 201, "hi", "बांग्लादेश पैसे भेजें", "da_qck_act_bgd_1" },
                    { 536, "bn", "এটিএম পিন দেখুন", "da_qck_act_phl_8" },
                    { 200, "bn", "বাংলাদেশে টাকা পাঠান", "da_qck_act_bgd_1" },
                    { 600, "ur-en", "Pay Philippines Bills", "da_qck_act_phl_9" },
                    { 590, "ta", "பிலிப்பைன்ஸிற்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_phl_9" },
                    { 588, "ml", "ഫിലിപ്പീൻസിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_phl_9" },
                    { 587, "hi-en", "Philippines Ke Bills Pay Karein", "da_qck_act_phl_9" },
                    { 586, "hi", "फिलीपींस के बिल चुकाए", "da_qck_act_phl_9" },
                    { 585, "bn", "ফিলিপিন্স  এ বিল পে করুন", "da_qck_act_phl_9" },
                    { 584, "en", "Pay Philippines Bills", "da_qck_act_phl_9" },
                    { 541, "ur-en", "View ATM Pin", "da_qck_act_phl_8" },
                    { 342, "hi-en", "Loan Paiye", "da_qck_act_phl_4" },
                    { 539, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_phl_8" },
                    { 538, "hi-en", "ATM Pin Dekhein", "da_qck_act_phl_8" },
                    { 199, "en", "Send Money To Bangladesh", "da_qck_act_bgd_1" },
                    { 341, "hi", "लोन पायें", "da_qck_act_phl_4" },
                    { 295, "ta", "பிலிப்பைன்ஸுக்கு ரீசார்ஜ் செய்ய", "da_qck_act_phl_3" },
                    { 339, "en", "Get Loan", "da_qck_act_phl_4" },
                    { 534, "ur-en", "View ATM Pin", "da_qck_act_lka_8" },
                    { 533, "ta", "ATM PIN பார்க்க", "da_qck_act_lka_8" },
                    { 532, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_lka_8" },
                    { 531, "hi-en", "ATM Pin Dekhein", "da_qck_act_lka_8" },
                    { 530, "hi", "ATM PIN देखें", "da_qck_act_lka_8" },
                    { 529, "bn", "এটিএম পিন দেখুন", "da_qck_act_lka_8" },
                    { 528, "en", "View ATM Pin", "da_qck_act_lka_8" },
                    { 485, "ur-en", "History and Statements", "da_qck_act_lka_7" },
                    { 484, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_lka_7" },
                    { 483, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_lka_7" },
                    { 482, "hi-en", "History aur statements", "da_qck_act_lka_7" },
                    { 481, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_lka_7" },
                    { 480, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_lka_7" },
                    { 479, "en", "History and Statements", "da_qck_act_lka_7" },
                    { 436, "ur-en", "Pay Local Bills", "da_qck_act_lka_6" },
                    { 435, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_lka_6" },
                    { 434, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_lka_6" },
                    { 433, "hi-en", "Local Bills Pay Karein", "da_qck_act_lka_6" },
                    { 432, "hi", "लोकल बिल चुकाए", "da_qck_act_lka_6" },
                    { 431, "bn", "লোকাল বিলাস পে করুন ", "da_qck_act_lka_6" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 430, "en", "Pay Local Bills", "da_qck_act_lka_6" },
                    { 387, "ur-en", "C3Pay to C3Pay", "da_qck_act_lka_5" },
                    { 386, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_lka_5" },
                    { 577, "en", "Pay Srilanka Bills", "da_qck_act_lka_9" },
                    { 578, "bn", "শ্রী লঙ্কা এ বিল পে করুন", "da_qck_act_lka_9" },
                    { 579, "hi", "श्री लंका के बिल चुकाए", "da_qck_act_lka_9" },
                    { 580, "hi-en", "Srilanka Ke Bills Pay Karein", "da_qck_act_lka_9" },
                    { 296, "ur-en", "Recharge Philippines", "da_qck_act_phl_3" },
                    { 254, "ur-en", "Recharge Du Etisalat", "da_qck_act_bgd_2" },
                    { 294, "ml", "ഫിലിപ്പീൻസിലേക്ക് റീചാർജ് ചെയ്യുക", "da_qck_act_phl_3" },
                    { 293, "hi-en", "Recharge Philippines", "da_qck_act_phl_3" },
                    { 292, "hi", "रिचार्ज फिलीपींस", "da_qck_act_phl_3" },
                    { 291, "bn", "রিচার্জ ফিলিপনিএস", "da_qck_act_phl_3" },
                    { 290, "en", "Recharge Philippines", "da_qck_act_phl_3" },
                    { 247, "ur-en", "Recharge Du Etisalat", "da_qck_act_phl_2" },
                    { 246, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_phl_2" },
                    { 245, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_phl_2" },
                    { 244, "hi-en", "Recharge Du Etisalat", "da_qck_act_phl_2" },
                    { 340, "bn", "লোন পান", "da_qck_act_phl_4" },
                    { 243, "hi", "रिचार्ज Du Etisalat", "da_qck_act_phl_2" },
                    { 241, "en", "Recharge Du Etisalat", "da_qck_act_phl_2" },
                    { 198, "ur-en", "Send Money To Philippines", "da_qck_act_phl_1" },
                    { 197, "ta", "பணம் அனுப்புங்கள் பிலிப்பைன்ஸுக்கு", "da_qck_act_phl_1" },
                    { 196, "ml", "ഫിലിപ്പീൻസിലേക്ക് പണം അയയ്ക്കുക", "da_qck_act_phl_1" },
                    { 195, "hi-en", "Philippines Paise Bhejiye", "da_qck_act_phl_1" },
                    { 194, "hi", "फिलीपींस पैसे भेजें", "da_qck_act_phl_1" },
                    { 193, "bn", "ফিলিপিন্স টাকা পাঠান", "da_qck_act_phl_1" },
                    { 192, "en", "Send Money To Philippines", "da_qck_act_phl_1" },
                    { 583, "ur-en", "Pay Srilanka Bills", "da_qck_act_lka_9" },
                    { 582, "ta", "இலங்கைக்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_lka_9" },
                    { 581, "ml", "ശ്രീലങ്കയിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_lka_9" },
                    { 242, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_phl_2" },
                    { 297, "en", "Recharge Bangladesh", "da_qck_act_bgd_3" },
                    { 302, "ta", "பங்களாதேஷுக்கு ரீசார்ஜ் செய்ய", "da_qck_act_bgd_3" },
                    { 299, "hi", "रिचार्ज बांग्लादेश", "da_qck_act_bgd_3" },
                    { 406, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_npl_5" },
                    { 405, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_npl_5" },
                    { 404, "hi", "C3Pay से C3Pay", "da_qck_act_npl_5" },
                    { 403, "bn", "C3Pay থেকে C3Pay", "da_qck_act_npl_5" },
                    { 402, "en", "C3Pay to C3Pay", "da_qck_act_npl_5" },
                    { 359, "ur-en", "Get Loan", "da_qck_act_npl_4" },
                    { 358, "ta", "கடன் பெறுங்கள்", "da_qck_act_npl_4" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 357, "ml", "ലോൺ നേടുക", "da_qck_act_npl_4" },
                    { 356, "hi-en", "Loan Paiye", "da_qck_act_npl_4" },
                    { 355, "hi", "लोन पायें", "da_qck_act_npl_4" },
                    { 354, "bn", "লোন পান", "da_qck_act_npl_4" },
                    { 353, "en", "Get Loan", "da_qck_act_npl_4" },
                    { 310, "ur-en", "Recharge Nepal", "da_qck_act_npl_3" },
                    { 309, "ta", "நேபாளத்திற்கு ரீசார்ஜ் செய்ய", "da_qck_act_npl_3" },
                    { 308, "ml", "നേപ്പാളിലേക്ക് റീചാർജ് ചെയ്യുക", "da_qck_act_npl_3" },
                    { 307, "hi-en", "Recharge Nepal", "da_qck_act_npl_3" },
                    { 306, "hi", "रिचार्ज नेपाल", "da_qck_act_npl_3" },
                    { 305, "bn", "রিচার্জ  নেপাল", "da_qck_act_npl_3" },
                    { 304, "en", "Recharge Nepal", "da_qck_act_npl_3" },
                    { 261, "ur-en", "Recharge Du Etisalat", "da_qck_act_npl_2" },
                    { 260, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_npl_2" },
                    { 259, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_npl_2" },
                    { 258, "hi-en", "Recharge Du Etisalat", "da_qck_act_npl_2" },
                    { 407, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_npl_5" },
                    { 408, "ur-en", "C3Pay to C3Pay", "da_qck_act_npl_5" },
                    { 451, "en", "Pay Local Bills", "da_qck_act_npl_6" },
                    { 452, "bn", "লোকাল বিলাস পে করুন ", "da_qck_act_npl_6" },
                    { 612, "ml", "നേപ്പാളിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_npl_9" },
                    { 611, "hi-en", "Nepal Ke Bills Pay Karein", "da_qck_act_npl_9" },
                    { 610, "hi", "नेपाल के बिल चुकाए", "da_qck_act_npl_9" },
                    { 609, "bn", "নেপাল এ বিল পে করুন", "da_qck_act_npl_9" },
                    { 608, "en", "Pay Nepal Bills", "da_qck_act_npl_9" },
                    { 555, "ur-en", "View ATM Pin", "da_qck_act_npl_8" },
                    { 554, "ta", "ATM PIN பார்க்க", "da_qck_act_npl_8" },
                    { 553, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_npl_8" },
                    { 552, "hi-en", "ATM Pin Dekhein", "da_qck_act_npl_8" },
                    { 551, "hi", "ATM PIN देखें", "da_qck_act_npl_8" },
                    { 550, "bn", "এটিএম পিন দেখুন", "da_qck_act_npl_8" },
                    { 257, "hi", "रिचार्ज Du Etisalat", "da_qck_act_npl_2" },
                    { 549, "en", "View ATM Pin", "da_qck_act_npl_8" },
                    { 505, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_npl_7" },
                    { 504, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_npl_7" },
                    { 503, "hi-en", "History aur statements", "da_qck_act_npl_7" },
                    { 502, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_npl_7" },
                    { 501, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_npl_7" },
                    { 500, "en", "History and Statements", "da_qck_act_npl_7" },
                    { 457, "ur-en", "Pay Local Bills", "da_qck_act_npl_6" },
                    { 456, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_npl_6" },
                    { 455, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_npl_6" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 454, "hi-en", "Local Bills Pay Karein", "da_qck_act_npl_6" },
                    { 453, "hi", "लोकल बिल चुकाए", "da_qck_act_npl_6" },
                    { 506, "ur-en", "History and Statements", "da_qck_act_npl_7" },
                    { 256, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_npl_2" },
                    { 255, "en", "Recharge Du Etisalat", "da_qck_act_npl_2" },
                    { 212, "ur-en", "Send Money To Nepal", "da_qck_act_npl_1" },
                    { 449, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_bgd_6" },
                    { 448, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_bgd_6" },
                    { 447, "hi-en", "Local Bills Pay Karein", "da_qck_act_bgd_6" },
                    { 446, "hi", "लोकल बिल चुकाए", "da_qck_act_bgd_6" },
                    { 445, "bn", "লোকাল বিলাস পে করুন ", "da_qck_act_bgd_6" },
                    { 444, "en", "Pay Local Bills", "da_qck_act_bgd_6" },
                    { 401, "ur-en", "C3Pay to C3Pay", "da_qck_act_bgd_5" },
                    { 400, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_bgd_5" },
                    { 399, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_bgd_5" },
                    { 398, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_bgd_5" },
                    { 397, "hi", "C3Pay से C3Pay", "da_qck_act_bgd_5" },
                    { 450, "ur-en", "Pay Local Bills", "da_qck_act_bgd_6" },
                    { 396, "bn", "C3Pay থেকে C3Pay", "da_qck_act_bgd_5" },
                    { 352, "ur-en", "Get Loan", "da_qck_act_bgd_4" },
                    { 351, "ta", "கடன் பெறுங்கள்", "da_qck_act_bgd_4" },
                    { 350, "ml", "ലോൺ നേടുക", "da_qck_act_bgd_4" },
                    { 349, "hi-en", "Loan Paiye", "da_qck_act_bgd_4" },
                    { 348, "hi", "लोन पायें", "da_qck_act_bgd_4" },
                    { 347, "bn", "লোন পান", "da_qck_act_bgd_4" },
                    { 346, "en", "Get Loan", "da_qck_act_bgd_4" },
                    { 303, "ur-en", "Recharge Bangladesh", "da_qck_act_bgd_3" },
                    { 385, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_lka_5" },
                    { 301, "ml", "ബംഗ്ലാദേശിലേക്ക് റീചാർജ് ചെയ്യുക", "da_qck_act_bgd_3" },
                    { 300, "hi-en", "Recharge Bangladesh", "da_qck_act_bgd_3" },
                    { 395, "en", "C3Pay to C3Pay", "da_qck_act_bgd_5" },
                    { 298, "bn", "রিচার্জ  বাংলাদেশ", "da_qck_act_bgd_3" },
                    { 493, "en", "History and Statements", "da_qck_act_bgd_7" },
                    { 495, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_bgd_7" },
                    { 211, "ta", "பணம் அனுப்புங்கள் நேபாளத்திற்கு", "da_qck_act_npl_1" },
                    { 210, "ml", "നേപ്പാളിലേക്ക് പണം അയയ്ക്കുക", "da_qck_act_npl_1" },
                    { 209, "hi-en", "Nepal Paise Bhejiye", "da_qck_act_npl_1" },
                    { 208, "hi", "नेपाल पैसे भेजें", "da_qck_act_npl_1" },
                    { 207, "bn", "নেপালে  টাকা পাঠান", "da_qck_act_npl_1" },
                    { 206, "en", "Send Money To Nepal", "da_qck_act_npl_1" },
                    { 607, "ur-en", "Pay Bangladesh Bills", "da_qck_act_bgd_9" },
                    { 606, "ta", "பங்களாதேஷிற்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_bgd_9" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 605, "ml", "ബംഗ്ലാദേശിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_bgd_9" },
                    { 604, "hi-en", "Bangladesh Ke Bills Pay Karein", "da_qck_act_bgd_9" },
                    { 603, "hi", "बांग्लादेश के बिल चुकाए", "da_qck_act_bgd_9" },
                    { 494, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_bgd_7" },
                    { 602, "bn", "বাংলাদেশ এ বিল পে করুন", "da_qck_act_bgd_9" },
                    { 548, "ur-en", "View ATM Pin", "da_qck_act_bgd_8" },
                    { 547, "ta", "ATM PIN பார்க்க", "da_qck_act_bgd_8" },
                    { 546, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_bgd_8" },
                    { 545, "hi-en", "ATM Pin Dekhein", "da_qck_act_bgd_8" },
                    { 544, "hi", "ATM PIN देखें", "da_qck_act_bgd_8" },
                    { 543, "bn", "এটিএম পিন দেখুন", "da_qck_act_bgd_8" },
                    { 542, "en", "View ATM Pin", "da_qck_act_bgd_8" },
                    { 499, "ur-en", "History and Statements", "da_qck_act_bgd_7" },
                    { 498, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_bgd_7" },
                    { 497, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_bgd_7" },
                    { 496, "hi-en", "History aur statements", "da_qck_act_bgd_7" },
                    { 601, "en", "Pay Bangladesh Bills", "da_qck_act_bgd_9" },
                    { 384, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_lka_5" },
                    { 337, "ta", "கடன் பெறுங்கள்", "da_qck_act_lka_4" },
                    { 382, "bn", "C3Pay থেকে C3Pay", "da_qck_act_lka_5" },
                    { 270, "bn", "রিচার্জ ভারত", "da_qck_act_ind_3" },
                    { 269, "en", "Recharge India", "da_qck_act_ind_3" },
                    { 226, "ur-en", "Recharge Du Etisalat", "da_qck_act_ind_2" },
                    { 225, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_ind_2" },
                    { 224, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_ind_2" },
                    { 223, "hi-en", "Recharge Du Etisalat", "da_qck_act_ind_2" },
                    { 222, "hi", "रिचार्ज Du Etisalat", "da_qck_act_ind_2" },
                    { 221, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_ind_2" },
                    { 220, "en", "Recharge Du Etisalat", "da_qck_act_ind_2" },
                    { 177, "ur-en", "Send Money To India", "da_qck_act_ind_1" },
                    { 176, "ta", "பணம் அனுப்புங்கள் இந்தியாவிற்கு", "da_qck_act_ind_1" },
                    { 175, "ml", "ഇന്ത്യയിലേക്ക് പണം അയയ്ക്കുക", "da_qck_act_ind_1" },
                    { 174, "hi-en", "India Paisa Bhejiye", "da_qck_act_ind_1" },
                    { 173, "hi", "इंडिया पैसे भेजें", "da_qck_act_ind_1" },
                    { 172, "bn", "নেপালে  টাকা পাঠান", "da_qck_act_ind_1" },
                    { 171, "en", "Send Money To India", "da_qck_act_ind_1" },
                    { 562, "ur-en", "Pay International Bill", "da_qck_act_9" },
                    { 561, "ta", "சர்வதேச பில்களை செலுத்துங்கள்", "da_qck_act_9" },
                    { 560, "ml", "അന്താരാഷ്ട്ര ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_9" },
                    { 559, "hi-en", "International Bills Pay Karein", "da_qck_act_9" },
                    { 558, "hi", "अंतर्राष्ट्रीय बिल चुकाए", "da_qck_act_9" },
                    { 557, "bn", "ইন্টারন্যাশনাল বিল পে করুন", "da_qck_act_9" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 556, "en", "Pay International Bill", "da_qck_act_9" },
                    { 271, "hi", "रिचार्ज इंडिया", "da_qck_act_ind_3" },
                    { 272, "hi-en", "Recharge India", "da_qck_act_ind_3" },
                    { 273, "ml", "ഇന്ത്യയിലേക്ക്  റീചാർജ് ചെയ്യുക", "da_qck_act_ind_3" },
                    { 274, "ta", "இந்தியாவிற்கு ரீசார்ஜ் செய்ய", "da_qck_act_ind_3" },
                    { 466, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_ind_7" },
                    { 465, "en", "History and Statements", "da_qck_act_ind_7" },
                    { 422, "ur-en", "Pay Local Bills", "da_qck_act_ind_6" },
                    { 421, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_ind_6" },
                    { 420, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_ind_6" },
                    { 419, "hi-en", "Local Bills Pay Karein", "da_qck_act_ind_6" },
                    { 418, "hi", "लोकल बिल चुकाए", "da_qck_act_ind_6" },
                    { 417, "bn", "লোকাল বিলাস পে করুন", "da_qck_act_ind_6" },
                    { 416, "en", "Pay Local Bills", "da_qck_act_ind_6" },
                    { 373, "ur-en", "C3Pay to C3Pay", "da_qck_act_ind_5" },
                    { 372, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_ind_5" },
                    { 513, "ur-en", "View ATM Pin", "da_qck_act_8" },
                    { 371, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_ind_5" },
                    { 369, "hi", "C3Pay से C3Pay", "da_qck_act_ind_5" },
                    { 368, "bn", "C3Pay থেকে C3Pay", "da_qck_act_ind_5" },
                    { 367, "en", "C3Pay to C3Pay", "da_qck_act_ind_5" },
                    { 324, "ur-en", "Get Loan", "da_qck_act_ind_4" },
                    { 323, "ta", "கடன் பெறுங்கள்", "da_qck_act_ind_4" },
                    { 322, "ml", "ലോൺ നേടുക", "da_qck_act_ind_4" },
                    { 321, "hi-en", "Loan Paiye", "da_qck_act_ind_4" },
                    { 320, "hi", "लोन पायें", "da_qck_act_ind_4" },
                    { 319, "bn", "লোন পান", "da_qck_act_ind_4" },
                    { 318, "en", "Get Loan", "da_qck_act_ind_4" },
                    { 275, "ur-en", "Recharge India", "da_qck_act_ind_3" },
                    { 370, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_ind_5" },
                    { 512, "ta", "ATM PIN பார்க்க", "da_qck_act_8" },
                    { 511, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_8" },
                    { 510, "hi-en", "ATM Pin Dekhein", "da_qck_act_8" },
                    { 313, "hi", "लोन पायें", "da_qck_act_4" },
                    { 312, "bn", "লোন পান", "da_qck_act_4" },
                    { 311, "en", "Get Loan", "da_qck_act_4" },
                    { 268, "ur-en", "Recharge International", "da_qck_act_3" },
                    { 267, "ta", "சர்வதேச ரீசார்ஜ்", "da_qck_act_3" },
                    { 266, "ml", "അന്താരാഷ്ട്ര റീചാർജ്", "da_qck_act_3" },
                    { 265, "hi-en", "Recharge International", "da_qck_act_3" },
                    { 264, "hi", "अंतर्राष्ट्रीय रिचार्ज", "da_qck_act_3" },
                    { 263, "bn", "ইন্টারন্যাশনাল রিচার্জ", "da_qck_act_3" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 262, "en", "Recharge International", "da_qck_act_3" },
                    { 219, "ur-en", "Recharge Du Etisalat", "da_qck_act_2" },
                    { 314, "hi-en", "Loan Paiye", "da_qck_act_4" },
                    { 218, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_2" },
                    { 216, "hi-en", "Recharge Du Etisalat", "da_qck_act_2" },
                    { 215, "hi", "रिचार्ज Du Etisalat", "da_qck_act_2" },
                    { 214, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_2" },
                    { 213, "en", "Recharge Du Etisalat", "da_qck_act_2" },
                    { 170, "ur-en", "Send Money", "da_qck_act_1" },
                    { 169, "ta", "பணம் அனுப்புங்கள்", "da_qck_act_1" },
                    { 168, "ml", "പണം അയക്കുക", "da_qck_act_1" },
                    { 167, "hi-en", "Ghar Paisa bhejiye", "da_qck_act_1" },
                    { 166, "hi", "घर पैसे भेजें", "da_qck_act_1" },
                    { 165, "bn", "টাকা পাঠান", "da_qck_act_1" },
                    { 164, "en", "Send Money", "da_qck_act_1" },
                    { 217, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_2" },
                    { 467, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_ind_7" },
                    { 315, "ml", "ലോൺ നേടുക", "da_qck_act_4" },
                    { 317, "ur-en", "Get Loan", "da_qck_act_4" },
                    { 509, "hi", "ATM PIN देखें", "da_qck_act_8" },
                    { 508, "bn", "এটিএম পিন দেখুন", "da_qck_act_8" },
                    { 507, "en", "View ATM Pin", "da_qck_act_8" },
                    { 464, "ur-en", "History and Statements", "da_qck_act_7" },
                    { 463, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_7" },
                    { 462, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_7" },
                    { 461, "hi-en", "History aur statements", "da_qck_act_7" },
                    { 460, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_7" },
                    { 459, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_7" },
                    { 458, "en", "History and Statements", "da_qck_act_7" },
                    { 415, "ur-en", "Pay Bills", "da_qck_act_6" },
                    { 316, "ta", "கடன் பெறுங்கள்", "da_qck_act_4" },
                    { 414, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_6" },
                    { 412, "hi-en", "Local Bills Pay Karein", "da_qck_act_6" },
                    { 411, "hi", "अंतर्राष्ट्रीय बिल चुकाए", "da_qck_act_6" },
                    { 410, "bn", "লোকাল বিলাস পে করুন", "da_qck_act_6" },
                    { 409, "en", "Pay Bills", "da_qck_act_6" },
                    { 366, "ur-en", "C3Pay to C3Pay", "da_qck_act_5" },
                    { 365, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_5" },
                    { 364, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_5" },
                    { 363, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_5" },
                    { 362, "hi", "C3Pay से C3Pay", "da_qck_act_5" },
                    { 361, "bn", "C3Pay থেকে C3Pay", "da_qck_act_5" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 360, "en", "C3Pay to C3Pay", "da_qck_act_5" },
                    { 413, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_6" },
                    { 383, "hi", "C3Pay से C3Pay", "da_qck_act_lka_5" },
                    { 468, "hi-en", "History aur statements", "da_qck_act_ind_7" },
                    { 470, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_ind_7" },
                    { 185, "en", "Send Money To SriLanka", "da_qck_act_lka_1" },
                    { 576, "ur-en", "Pay Pakistan Bills", "da_qck_act_pak_9" },
                    { 575, "ta", "பாகிஸ்தானுக்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_pak_9" },
                    { 574, "ml", "പാകിസ്ഥാനിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_pak_9" },
                    { 573, "hi-en", "Pakistan Ke Bills Pay Karein", "da_qck_act_pak_9" },
                    { 572, "hi", "पाकिस्तान के बिल चुकाए", "da_qck_act_pak_9" },
                    { 571, "bn", "পাকিস্তান এ বিল পে করুন", "da_qck_act_pak_9" },
                    { 570, "en", "Pay Pakistan Bills", "da_qck_act_pak_9" },
                    { 527, "ur-en", "View ATM Pin", "da_qck_act_pak_8" },
                    { 526, "ta", "ATM PIN பார்க்க", "da_qck_act_pak_8" },
                    { 525, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_pak_8" },
                    { 524, "hi-en", "ATM Pin Dekhein", "da_qck_act_pak_8" },
                    { 523, "hi", "ATM PIN देखें", "da_qck_act_pak_8" },
                    { 522, "bn", "এটিএম পিন দেখুন", "da_qck_act_pak_8" },
                    { 521, "en", "View ATM Pin", "da_qck_act_pak_8" },
                    { 478, "ur-en", "History and Statements", "da_qck_act_pak_7" },
                    { 477, "ta", "வரலாறு மற்றும் அறிக்கைகள்", "da_qck_act_pak_7" },
                    { 476, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_pak_7" },
                    { 475, "hi-en", "History aur statements", "da_qck_act_pak_7" },
                    { 474, "hi", "हिस्ट्री और स्टेटमेंट्स", "da_qck_act_pak_7" },
                    { 473, "bn", "হিস্ট্রি বা স্টেটমেন্টস", "da_qck_act_pak_7" },
                    { 472, "en", "History and Statements", "da_qck_act_pak_7" },
                    { 429, "ur-en", "Pay Local Bills", "da_qck_act_pak_6" },
                    { 186, "bn", "শ্রীলংকা  টাকা পাঠান", "da_qck_act_lka_1" },
                    { 187, "hi", "श्री लंका पैसे भेजें", "da_qck_act_lka_1" },
                    { 188, "hi-en", "Sri Lanka Paisa Bhejiye", "da_qck_act_lka_1" },
                    { 189, "ml", "ശ്രീലങ്കയിലേക്ക് പണം അയയ്ക്കുക", "da_qck_act_lka_1" },
                    { 381, "en", "C3Pay to C3Pay", "da_qck_act_lka_5" },
                    { 338, "ur-en", "Get Loan", "da_qck_act_lka_4" },
                    { 613, "ta", "நேபாளத்திற்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_npl_9" },
                    { 336, "ml", "ലോൺ നേടുക", "da_qck_act_lka_4" },
                    { 335, "hi-en", "Loan Paiye", "da_qck_act_lka_4" },
                    { 334, "hi", "लोन पायें", "da_qck_act_lka_4" },
                    { 333, "bn", "লোন পান", "da_qck_act_lka_4" },
                    { 332, "en", "Get Loan", "da_qck_act_lka_4" },
                    { 289, "ur-en", "Recharge SriLanka", "da_qck_act_lka_3" },
                    { 288, "ta", "இலங்கைக்கு ரீசார்ஜ் செய்ய", "da_qck_act_lka_3" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 287, "ml", "ശ്രീലങ്കയിലേക്ക് റീചാർജ് ചെയ്യുക", "da_qck_act_lka_3" },
                    { 428, "ta", "உள்ளூர் பில்களை செலுத்துங்கள்", "da_qck_act_pak_6" },
                    { 286, "hi-en", "Recharge SriLanka", "da_qck_act_lka_3" },
                    { 284, "bn", "রিচার্জ শ্রীলঙ্কা", "da_qck_act_lka_3" },
                    { 283, "en", "Recharge SriLanka", "da_qck_act_lka_3" },
                    { 240, "ur-en", "Recharge Du Etisalat", "da_qck_act_lka_2" },
                    { 239, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_lka_2" },
                    { 238, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_lka_2" },
                    { 237, "hi-en", "Recharge Du Etisalat", "da_qck_act_lka_2" },
                    { 236, "hi", "रिचार्ज Du Etisalat", "da_qck_act_lka_2" },
                    { 235, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_lka_2" },
                    { 234, "en", "Recharge Du Etisalat", "da_qck_act_lka_2" },
                    { 191, "ur-en", "Send Money To SriLanka", "da_qck_act_lka_1" },
                    { 190, "ta", "பணம் அனுப்புங்கள் இலங்கைக்கு", "da_qck_act_lka_1" },
                    { 285, "hi", "रिचार्ज श्री लंका", "da_qck_act_lka_3" },
                    { 427, "ml", "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_pak_6" },
                    { 426, "hi-en", "Local Bills Pay Karein", "da_qck_act_pak_6" },
                    { 425, "hi", "लोकल बिल चुकाए", "da_qck_act_pak_6" },
                    { 228, "bn", "দু  এটিসালাত রিচার্জ করুন", "da_qck_act_pak_2" },
                    { 227, "en", "Recharge Du Etisalat", "da_qck_act_pak_2" },
                    { 184, "ur-en", "Send Money To Pakistan", "da_qck_act_pak_1" },
                    { 183, "ta", "பணம் அனுப்புங்கள் பாகிஸ்தானுக்கு", "da_qck_act_pak_1" },
                    { 182, "ml", "പാക്കിസ്ഥാനിലേക്ക് പണം അയയ്ക്കു", "da_qck_act_pak_1" },
                    { 181, "hi-en", "Pakistan Paise Bhejiye", "da_qck_act_pak_1" },
                    { 180, "hi", "पाकिस्तान पैसे भेजें", "da_qck_act_pak_1" },
                    { 179, "bn", "পাকিস্তানে টাকা পাঠান", "da_qck_act_pak_1" },
                    { 178, "en", "Send Money To Pakistan", "da_qck_act_pak_1" },
                    { 569, "ur-en", "Pay India Bills", "da_qck_act_ind_9" },
                    { 568, "ta", "இந்தியாவுக்கான உங்கள் பில்களை செலுத்துங்கள்", "da_qck_act_ind_9" },
                    { 229, "hi", "रिचार्ज Du Etisalat", "da_qck_act_pak_2" },
                    { 567, "ml", "ഇന്ത്യയിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക", "da_qck_act_ind_9" },
                    { 565, "hi", "इंडिया के बिल चुकाए", "da_qck_act_ind_9" },
                    { 564, "bn", "ভারত এ বিল পে করুন", "da_qck_act_ind_9" },
                    { 563, "en", "Pay India Bills", "da_qck_act_ind_9" },
                    { 520, "ur-en", "View ATM Pin", "da_qck_act_ind_8" },
                    { 519, "ta", "ATM PIN பார்க்க", "da_qck_act_ind_8" },
                    { 518, "ml", "എടിഎം പിൻ കാണുക", "da_qck_act_ind_8" },
                    { 517, "hi-en", "ATM Pin Dekhein", "da_qck_act_ind_8" },
                    { 516, "hi", "ATM PIN देखें", "da_qck_act_ind_8" },
                    { 515, "bn", "এটিএম পিন দেখুন", "da_qck_act_ind_8" },
                    { 514, "en", "View ATM Pin", "da_qck_act_ind_8" },
                    { 471, "ur-en", "History and Statements", "da_qck_act_ind_7" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 566, "hi-en", "India Ke Bills Pay Karein", "da_qck_act_ind_9" },
                    { 469, "ml", "വിശദാംശങ്ങളും , പ്രസ്താവനകളും", "da_qck_act_ind_7" },
                    { 230, "hi-en", "Recharge Du Etisalat", "da_qck_act_pak_2" },
                    { 232, "ta", "Du Etisalat  ரீசார்ஜ் செய்ய", "da_qck_act_pak_2" },
                    { 424, "bn", "লোকাল বিলাস পে করুন ", "da_qck_act_pak_6" },
                    { 423, "en", "Pay Local Bills", "da_qck_act_pak_6" },
                    { 380, "ur-en", "C3Pay to C3Pay", "da_qck_act_pak_5" },
                    { 379, "ta", "C3Pay இல் இருந்து C3Pay இற்கு", "da_qck_act_pak_5" },
                    { 378, "ml", "C3Pay മുതൽ C3Pay വരെ", "da_qck_act_pak_5" },
                    { 377, "hi-en", "C3 Pay se C3 Pay", "da_qck_act_pak_5" },
                    { 376, "hi", "C3Pay से C3Pay", "da_qck_act_pak_5" },
                    { 375, "bn", "C3Pay থেকে C3Pay", "da_qck_act_pak_5" },
                    { 374, "en", "C3Pay to C3Pay", "da_qck_act_pak_5" },
                    { 331, "ur-en", "Get Loan", "da_qck_act_pak_4" },
                    { 330, "ta", "கடன் பெறுங்கள்", "da_qck_act_pak_4" },
                    { 231, "ml", "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക", "da_qck_act_pak_2" },
                    { 329, "ml", "ലോൺ നേടുക", "da_qck_act_pak_4" },
                    { 327, "hi", "लोन पायें", "da_qck_act_pak_4" },
                    { 326, "bn", "লোন পান", "da_qck_act_pak_4" },
                    { 325, "en", "Get Loan", "da_qck_act_pak_4" },
                    { 282, "ur-en", "Recharge Pakistan", "da_qck_act_pak_3" },
                    { 281, "ta", "பாகிஸ்தானுக்கு ரீசார்ஜ் செய்ய", "da_qck_act_pak_3" },
                    { 280, "ml", "പാകിസ്ഥാനിലേക്ക് റീചാർജ് ചെയ്യുക", "da_qck_act_pak_3" },
                    { 279, "hi-en", "Recharge Pakistan", "da_qck_act_pak_3" },
                    { 278, "hi", "रिचार्ज पाकिस्तान", "da_qck_act_pak_3" },
                    { 277, "bn", "রিচার্জ পাকিস্তান", "da_qck_act_pak_3" },
                    { 276, "en", "Recharge Pakistan", "da_qck_act_pak_3" },
                    { 233, "ur-en", "Recharge Du Etisalat", "da_qck_act_pak_2" },
                    { 328, "hi-en", "Loan Paiye", "da_qck_act_pak_4" },
                    { 614, "ur-en", "Pay Nepal Bills", "da_qck_act_npl_9" }
                });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElements",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "ElementId", "IconUrl", "IsActive", "IsDeleted", "TextContentCode", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(2251), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_1.png", true, false, "da_qck_act_1", null },
                    { 34, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5400), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_5.png", true, false, "da_qck_act_bgd_5", null },
                    { 35, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5417), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_5.png", true, false, "da_qck_act_npl_5", null },
                    { 36, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5422), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_6.png", true, false, "da_qck_act_6", null },
                    { 37, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5424), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_6.png", true, false, "da_qck_act_ind_6", null },
                    { 38, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5427), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_6.png", true, false, "da_qck_act_pak_6", null },
                    { 39, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5430), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_6.png", true, false, "da_qck_act_lka_6", null },
                    { 40, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5433), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_6.png", true, false, "da_qck_act_phl_6", null },
                    { 41, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5435), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_6.png", true, false, "da_qck_act_bgd_6", null },
                    { 42, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5438), null, null, 6, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_6.png", true, false, "da_qck_act_npl_6", null },
                    { 43, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5440), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", true, false, "da_qck_act_7", null },
                    { 44, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5443), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_7.png", true, false, "da_qck_act_ind_7", null },
                    { 45, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5445), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", false, false, "da_qck_act_pak_7", null },
                    { 46, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5448), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", false, false, "da_qck_act_lka_7", null },
                    { 33, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5397), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_5.png", true, false, "da_qck_act_phl_5", null },
                    { 47, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5452), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", false, false, "da_qck_act_phl_7", null },
                    { 49, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5457), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", false, false, "da_qck_act_npl_7", null },
                    { 50, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5460), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_8.png", true, false, "da_qck_act_8", null },
                    { 51, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5462), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_8.png", true, false, "da_qck_act_ind_8", null },
                    { 52, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5465), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_8.png", true, false, "da_qck_act_pak_8", null },
                    { 53, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5468), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_8.png", true, false, "da_qck_act_lka_8", null },
                    { 54, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5470), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_8.png", true, false, "da_qck_act_phl_8", null },
                    { 55, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5473), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_8.png", true, false, "da_qck_act_bgd_8", null },
                    { 56, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5475), null, null, 8, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_8.png", true, false, "da_qck_act_npl_8", null },
                    { 57, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5478), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_9.png", false, false, "da_qck_act_9", null },
                    { 58, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5481), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_action_9.png", false, false, "da_qck_act_ind_9", null },
                    { 59, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5483), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_9.png", true, false, "da_qck_act_pak_9", null },
                    { 60, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5486), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_9.png", true, false, "da_qck_act_lka_9", null },
                    { 61, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5488), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_9.png", true, false, "da_qck_act_phl_9", null },
                    { 48, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5455), null, null, 7, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png", false, false, "da_qck_act_bgd_7", null },
                    { 62, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5492), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_9.png", true, false, "da_qck_act_bgd_9", null },
                    { 32, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5395), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_5.png", true, false, "da_qck_act_lka_5", null },
                    { 30, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5390), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_5.png", true, false, "da_qck_act_ind_5", null },
                    { 2, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5205), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_1.png", true, false, "da_qck_act_ind_1", null },
                    { 3, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5316), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_1.png", true, false, "da_qck_act_pak_1", null },
                    { 4, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5320), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_1.png", true, false, "da_qck_act_lka_1", null },
                    { 5, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5323), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_1.png", true, false, "da_qck_act_phl_1", null },
                    { 6, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5326), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_1.png", true, false, "da_qck_act_bgd_1", null },
                    { 7, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5328), null, null, 1, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_1.png", true, false, "da_qck_act_npl_1", null },
                    { 8, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5331), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_2.png", true, false, "da_qck_act_2", null },
                    { 9, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5335), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_2.png", true, false, "da_qck_act_ind_2", null },
                    { 10, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5337), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_2.png", true, false, "da_qck_act_pak_2", null }
                });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElements",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "ElementId", "IconUrl", "IsActive", "IsDeleted", "TextContentCode", "UpdatedDate" },
                values: new object[,]
                {
                    { 11, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5340), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_2.png", true, false, "da_qck_act_lka_2", null },
                    { 12, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5343), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_2.png", true, false, "da_qck_act_phl_2", null },
                    { 13, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5345), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_2.png", true, false, "da_qck_act_bgd_2", null },
                    { 14, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5348), null, null, 2, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_2.png", true, false, "da_qck_act_npl_2", null },
                    { 31, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5392), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_5.png", true, false, "da_qck_act_pak_5", null },
                    { 15, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5350), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_3.png", true, false, "da_qck_act_3", null },
                    { 17, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5355), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_3.png", true, false, "da_qck_act_pak_3", null },
                    { 18, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5358), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_3.png", true, false, "da_qck_act_lka_3", null },
                    { 19, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5360), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_3.png", true, false, "da_qck_act_phl_3", null },
                    { 20, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5363), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_3.png", true, false, "da_qck_act_bgd_3", null },
                    { 21, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5365), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_3.png", true, false, "da_qck_act_npl_3", null },
                    { 22, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5368), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_4.png", true, false, "da_qck_act_4", null },
                    { 23, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5370), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_4.png", true, false, "da_qck_act_ind_4", null },
                    { 24, "PK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5373), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_4.png", true, false, "da_qck_act_pak_4", null },
                    { 25, "LK", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5375), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_4.png", true, false, "da_qck_act_lka_4", null },
                    { 26, "PH", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5378), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_4.png", true, false, "da_qck_act_phl_4", null },
                    { 27, "BD", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5382), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_4.png", true, false, "da_qck_act_bgd_4", null },
                    { 28, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5385), null, null, 4, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_4.png", true, false, "da_qck_act_npl_4", null },
                    { 29, null, new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5387), null, null, 5, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_5.png", true, false, "da_qck_act_5", null },
                    { 16, "IN", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5353), null, null, 3, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_3.png", true, false, "da_qck_act_ind_3", null },
                    { 63, "NP", new DateTime(2022, 11, 11, 15, 36, 46, 78, DateTimeKind.Local).AddTicks(5494), null, null, 9, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_9.png", true, false, "da_qck_act_npl_9", null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_DashboardElements_SectionId",
                table: "DashboardElements",
                column: "SectionId");

            migrationBuilder.CreateIndex(
                name: "IX_DashboardQuickActionElements_CountryCode",
                table: "DashboardQuickActionElements",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_DashboardQuickActionElements_ElementId",
                table: "DashboardQuickActionElements",
                column: "ElementId");

            migrationBuilder.CreateIndex(
                name: "IX_DashboardQuickActionElements_TextContentCode",
                table: "DashboardQuickActionElements",
                column: "TextContentCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DashboardQuickActionElements");

            migrationBuilder.DropTable(
                name: "DashboardElements");

            migrationBuilder.DropTable(
                name: "DashboardSections");

            migrationBuilder.DeleteData(
                table: "Cities",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "Cities",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 164);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 165);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 166);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 167);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 168);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 169);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 170);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 171);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 172);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 173);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 174);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 175);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 176);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 177);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 178);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 179);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 180);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 181);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 182);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 183);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 184);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 185);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 186);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 187);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 188);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 189);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 190);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 191);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 192);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 193);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 194);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 195);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 196);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 197);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 198);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 199);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 200);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 201);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 202);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 203);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 204);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 205);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 206);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 207);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 208);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 209);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 210);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 211);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 212);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 213);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 214);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 215);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 216);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 217);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 218);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 219);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 220);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 221);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 222);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 223);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 224);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 225);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 226);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 227);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 228);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 229);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 230);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 231);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 232);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 233);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 234);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 235);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 236);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 237);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 238);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 239);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 240);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 241);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 242);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 243);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 244);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 245);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 246);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 247);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 248);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 249);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 250);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 251);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 252);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 253);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 254);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 255);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 256);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 257);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 258);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 259);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 260);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 261);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 262);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 263);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 264);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 265);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 266);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 267);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 268);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 269);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 270);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 271);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 272);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 273);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 274);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 275);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 276);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 277);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 278);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 279);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 280);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 281);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 282);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 283);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 284);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 285);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 286);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 287);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 288);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 289);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 290);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 291);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 292);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 293);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 294);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 295);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 296);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 297);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 298);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 299);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 300);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 301);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 302);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 303);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 304);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 305);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 306);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 307);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 308);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 309);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 310);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 311);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 312);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 313);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 314);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 315);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 316);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 317);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 318);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 319);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 320);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 321);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 322);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 323);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 324);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 325);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 326);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 327);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 328);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 329);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 330);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 331);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 332);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 333);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 334);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 335);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 336);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 337);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 338);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 339);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 340);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 341);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 342);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 343);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 344);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 345);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 346);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 347);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 348);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 349);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 350);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 351);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 352);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 353);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 354);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 355);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 356);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 357);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 358);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 359);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 360);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 361);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 362);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 363);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 364);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 365);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 366);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 367);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 368);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 369);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 370);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 371);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 372);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 373);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 374);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 375);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 376);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 377);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 378);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 379);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 380);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 381);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 382);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 383);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 384);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 385);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 386);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 387);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 388);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 389);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 390);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 391);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 392);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 393);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 394);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 395);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 396);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 397);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 398);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 399);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 400);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 401);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 402);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 403);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 404);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 405);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 406);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 407);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 408);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 409);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 410);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 411);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 412);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 413);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 414);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 415);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 416);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 417);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 418);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 419);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 420);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 421);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 422);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 423);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 424);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 425);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 426);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 427);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 428);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 429);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 430);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 431);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 432);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 433);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 434);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 435);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 436);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 437);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 438);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 439);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 440);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 441);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 442);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 443);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 444);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 445);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 446);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 447);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 448);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 449);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 450);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 451);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 452);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 453);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 454);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 455);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 456);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 457);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 458);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 459);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 460);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 461);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 462);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 463);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 464);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 465);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 466);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 467);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 468);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 469);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 470);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 471);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 472);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 473);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 474);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 475);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 476);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 477);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 478);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 479);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 480);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 481);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 482);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 483);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 484);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 485);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 486);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 487);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 488);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 489);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 490);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 491);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 492);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 493);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 494);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 495);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 496);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 497);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 498);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 499);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 500);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 501);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 502);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 503);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 504);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 505);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 506);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 507);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 508);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 509);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 510);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 511);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 512);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 513);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 514);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 515);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 516);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 517);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 518);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 519);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 520);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 521);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 522);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 523);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 524);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 525);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 526);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 527);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 528);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 529);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 530);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 531);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 532);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 533);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 534);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 535);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 536);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 537);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 538);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 539);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 540);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 541);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 542);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 543);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 544);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 545);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 546);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 547);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 548);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 549);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 550);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 551);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 552);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 553);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 554);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 555);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 556);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 557);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 558);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 559);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 560);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 561);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 562);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 563);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 564);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 565);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 566);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 567);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 568);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 569);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 570);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 571);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 572);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 573);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 574);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 575);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 576);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 577);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 578);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 579);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 580);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 581);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 582);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 583);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 584);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 585);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 586);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 587);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 588);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 590);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 600);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 601);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 602);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 603);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 604);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 605);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 606);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 607);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 608);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 609);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 610);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 611);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 612);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 613);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 614);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_bgd_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_ind_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_lka_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_npl_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_pak_9");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_5");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_6");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_7");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_8");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_phl_9");
        }
    }
}
