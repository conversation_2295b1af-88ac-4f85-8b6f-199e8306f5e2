﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Extra_Id_Validations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsExpiryDateGreaterThan2000",
                table: "EmiratesIdLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsIdNumber4thDigitValid",
                table: "EmiratesIdLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsOlderThan15YearsOld",
                table: "EmiratesIdLogs",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsExpiryDateGreaterThan2000",
                table: "EmiratesIdLogs");

            migrationBuilder.DropColumn(
                name: "IsIdNumber4thDigitValid",
                table: "EmiratesIdLogs");

            migrationBuilder.DropColumn(
                name: "IsOlderThan15YearsOld",
                table: "EmiratesIdLogs");
        }
    }
}
