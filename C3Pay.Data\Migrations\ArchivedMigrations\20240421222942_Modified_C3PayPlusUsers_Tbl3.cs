﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_C3PayPlusUsers_Tbl3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "LastSuccessfulBillingDate",
                table: "C3PayPlusMembershipUsers",
                newName: "LastBillingDate");

            migrationBuilder.RenameColumn(
                name: "IsStillActive",
                table: "C3PayPlusMembershipUsers",
                newName: "IsActive");

            migrationBuilder.AlterColumn<bool>(
                name: "UserHasCanceled",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.AddColumn<string>(
                name: "LastBillingTransactionNumber",
                table: "C3PayPlusMembershipUsers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Remarks",
                table: "C3PayPlusMembershipUsers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Remarks",
                table: "C3PayPlusMembershipTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransactionPayedForThisPeriod",
                table: "C3PayPlusMembershipTransactions",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "LastBillingTransactionNumber",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "Remarks",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "Remarks",
                table: "C3PayPlusMembershipTransactions");

            migrationBuilder.DropColumn(
                name: "TransactionPayedForThisPeriod",
                table: "C3PayPlusMembershipTransactions");

            migrationBuilder.RenameColumn(
                name: "LastBillingDate",
                table: "C3PayPlusMembershipUsers",
                newName: "LastSuccessfulBillingDate");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "C3PayPlusMembershipUsers",
                newName: "IsStillActive");

            migrationBuilder.AlterColumn<bool>(
                name: "UserHasCanceled",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldNullable: true);
        }
    }
}
