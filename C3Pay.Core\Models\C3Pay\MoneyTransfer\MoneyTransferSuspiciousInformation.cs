﻿using Edenred.Common.Core;
using System;

namespace C3Pay.Core.Models
{
    public class MoneyTransferSuspiciousInformation : BaseModel
    {
        public Guid Id { get; set; }
        public Guid MoneyTransferBeneficiaryId { get; set; }
        public string NationalIdNo { get; set; }
        public string FullName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int CityId { get; set; }
        public bool IsApproved { get; set; }
        public DateTime? ApprovalDateTime { get; set; }

        public MoneyTransferBeneficiary MoneyTransferBeneficiary { get; set; }
        public City City { get; set; }
    }
}
