﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Cashback_Tracker_C3P : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipCashbackClaims",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MembershipUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MembershipStartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MembershipEndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    TransactionsReferenceNumbers = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RefundReferenceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RefundAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ClaimedTriggeredFrom = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipCashbackClaims", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipCashbackClaims");
        }
    }
}
