﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class MiddleNavUpdateForUnEmpInsurance : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 4,
                column: "IsDeleted",
                value: true);

            migrationBuilder.InsertData(
                table: "DashboardElements",
                columns: new[] { "Id", "CreatedDate", "DeepLinkUrl", "DeletedBy", "DeletedDate", "DisplayOrder", "IsDeleted", "SectionId", "Type", "UpdatedDate" },
                values: new object[] { 11, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/iloe", null, null, 4, false, 1, 1, null });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[,]
                {
                    { "da_qck_act_11", "en", 99, "UnEmployment Insurance", "da_qck_act" },
                    { "da_cmn_tag", "en", 99, "New", "da_cmn_tag" }
                });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElementTags",
                columns: new[] { "Id", "TextContentCode" },
                values: new object[] { 6, "da_cmn_tag" });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElements",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "ElementId", "IconUrl", "IsActive", "IsDeleted", "TextContentCode", "UpdatedDate" },
                values: new object[,]
                {
                    { 71, null, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 72, "IN", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 73, "PK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 74, "LK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 75, "PH", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 76, "BD", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null },
                    { 77, "NP", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 11, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_11.png", true, false, "da_qck_act_11", null }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 668, "ml", "New", "da_cmn_tag" },
                    { 667, "hi-en", "New", "da_cmn_tag" },
                    { 666, "hi", "New", "da_cmn_tag" },
                    { 665, "bn", "New", "da_cmn_tag" },
                    { 664, "en", "New", "da_cmn_tag" },
                    { 660, "hi-en", "Unemployment Insurance", "da_qck_act_11" },
                    { 662, "ta", "Unemployment Insurance", "da_qck_act_11" },
                    { 661, "ml", "Unemployment Insurance", "da_qck_act_11" },
                    { 669, "ta", "New", "da_cmn_tag" },
                    { 659, "hi", "अनम्पलॉयमेंट इन्शुरन्स", "da_qck_act_11" },
                    { 658, "bn", "Unemployment Insurance", "da_qck_act_11" },
                    { 657, "en", "Unemployment Insurance", "da_qck_act_11" },
                    { 663, "ur-en", "Unemployment Insurance", "da_qck_act_11" },
                    { 670, "ur-en", "New", "da_cmn_tag" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElementTags",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 71);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 72);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 73);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 74);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 75);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 76);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 77);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 657);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 658);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 659);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 660);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 661);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 662);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 663);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 664);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 665);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 666);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 667);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 668);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 669);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 670);

            migrationBuilder.DeleteData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_cmn_tag");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_11");

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 4,
                column: "IsDeleted",
                value: false);
        }
    }
}
