﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU_Phase2_Renamed_Allowed_Char_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AllowedCharacterType_FieldValidationRules_FieldValidationRuleId",
                table: "AllowedCharacterType");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AllowedCharacterType",
                table: "AllowedCharacterType");

            migrationBuilder.RenameTable(
                name: "AllowedCharacterType",
                newName: "AllowedCharacterTypes");

            migrationBuilder.RenameIndex(
                name: "IX_AllowedCharacterType_FieldValidationRuleId",
                table: "AllowedCharacterTypes",
                newName: "IX_AllowedCharacterTypes_FieldValidationRuleId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AllowedCharacterTypes",
                table: "AllowedCharacterTypes",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AllowedCharacterTypes_FieldValidationRules_FieldValidationRuleId",
                table: "AllowedCharacterTypes",
                column: "FieldValidationRuleId",
                principalTable: "FieldValidationRules",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AllowedCharacterTypes_FieldValidationRules_FieldValidationRuleId",
                table: "AllowedCharacterTypes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_AllowedCharacterTypes",
                table: "AllowedCharacterTypes");

            migrationBuilder.RenameTable(
                name: "AllowedCharacterTypes",
                newName: "AllowedCharacterType");

            migrationBuilder.RenameIndex(
                name: "IX_AllowedCharacterTypes_FieldValidationRuleId",
                table: "AllowedCharacterType",
                newName: "IX_AllowedCharacterType_FieldValidationRuleId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_AllowedCharacterType",
                table: "AllowedCharacterType",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_AllowedCharacterType_FieldValidationRules_FieldValidationRuleId",
                table: "AllowedCharacterType",
                column: "FieldValidationRuleId",
                principalTable: "FieldValidationRules",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
