﻿using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.Firebase.FCM.V2.Requests;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [InputValidation]
    [Authorize]
    public class OverrideController : Controller
    {
        private readonly IPushNotificationService _pushNotificationService;

        public OverrideController(IPushNotificationService pushNotificationService)
        {
            _pushNotificationService = pushNotificationService;
        }

        [HttpPost("send-notification")]
        public async Task<ActionResult> Override_SendNotification(Override_SendNotificationRequest request, CancellationToken ct = default)
        {
            var trySendPushNotificationWithData = await this._pushNotificationService.SendPushNotificationWithData(new FcmNotificationRequestDto()
            {
                Message = new FcmNotificationMessageDto()
                {
                    Token = request.DeviceToken,
                    Apns = new FcmNotificationApns()
                    {
                        Payload = new FcmNotificationPayload()
                        {
                            Aps = new FcmNotificationAps()
                            {
                                ContentAvailable = 1
                            }
                        }
                    },
                    Data = new FcmNotificationData()
                    {
                        Title = request.Title,
                        Body = request.Body,
                        Deeplink = request.DeepLink
                    }
                }
            });

            if (trySendPushNotificationWithData.IsSuccessful == false)
            {
                return BadRequest(trySendPushNotificationWithData.ErrorMessage);
            }

            return Ok();
        }

        [HttpPost("send-notification-json")]
        public async Task<ActionResult> Override_SendNotificationJson([FromBody] JObject body)
        {
            var json = body.Root.ToString();
            var trySendPushNotificationWithData = await this._pushNotificationService.SendPushNotificationWithData(json);

            if (trySendPushNotificationWithData.IsSuccessful == false)
            {
                return BadRequest(trySendPushNotificationWithData.ErrorMessage);
            }

            return Ok();
        }
    }


    public class Override_SendNotificationRequest
    {
        public string DeviceToken { get; set; }
        public string Title { get; set; }
        public string Body { get; set; }
        public string DeepLink { get; set; }
    }
}
