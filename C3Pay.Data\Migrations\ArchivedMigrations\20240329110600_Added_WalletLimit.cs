﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_WalletLimit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "WalletLimit_MaxAmount",
                table: "MoneyTransferLimits",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WalletLimit_MaxMonthlyAmount",
                table: "MoneyTransferLimits",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "WalletLimit_MaxMonthlyCount",
                table: "MoneyTransferLimits",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WalletLimit_MinAmount",
                table: "MoneyTransferLimits",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WalletLimit_MaxAmount",
                table: "MoneyTransferLimits");

            migrationBuilder.DropColumn(
                name: "WalletLimit_MaxMonthlyAmount",
                table: "MoneyTransferLimits");

            migrationBuilder.DropColumn(
                name: "WalletLimit_MaxMonthlyCount",
                table: "MoneyTransferLimits");

            migrationBuilder.DropColumn(
                name: "WalletLimit_MinAmount",
                table: "MoneyTransferLimits");
        }
    }
}
