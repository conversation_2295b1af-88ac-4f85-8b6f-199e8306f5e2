﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_RMTTracker : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferProfileTracker",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CardholderId = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    EmiratesId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    RequestAction = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ExternalRakStatus = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true),
                    IsPositiveStatus = table.Column<bool>(type: "bit", nullable: true),
                    DataFileName = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true),
                    BatchId = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferProfileTracker", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferProfileTracker_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferProfileTracker_UserId",
                table: "MoneyTransferProfileTracker",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferProfileTracker");
        }
    }
}
