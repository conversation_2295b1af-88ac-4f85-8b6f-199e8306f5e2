﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Store : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        { 
            migrationBuilder.CreateSequence<int>(
                name: "OrderNumbers",
                startValue: 1001L);

            migrationBuilder.CreateTable(
                name: "Cities",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    IsStoreEnabled = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Cities_Countries_CountryCode",
                        column: x => x.<PERSON>ode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Products",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    Price = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    AttributesJson = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ProductType = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Products", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StoreExperimentUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StoreExperimentUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OrderAddresses",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    Building = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    RoomNumber = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    FloorNumber = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
                    Area = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    CityId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderAddresses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderAddresses_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Orders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    ProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OrderNumber = table.Column<int>(type: "int", nullable: false, defaultValueSql: "NEXT VALUE FOR OrderNumbers"),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", nullable: true),
                    InstallmentsCount = table.Column<int>(type: "int", nullable: false),
                    Currency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProductPrice = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    InstallmentAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    TransactionAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    DeliveryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OrderAddressId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Orders_OrderAddresses_OrderAddressId",
                        column: x => x.OrderAddressId,
                        principalTable: "OrderAddresses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Orders_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Orders_Transactions_ReferenceNumber",
                        column: x => x.ReferenceNumber,
                        principalTable: "Transactions",
                        principalColumn: "ReferenceNumber",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Orders_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Cities",
                columns: new[] { "Id", "CountryCode", "IsStoreEnabled", "Name" },
                values: new object[,]
                {
                    { 1, "AE", true, "Dubai" },
                    { 2, "AE", true, "Abu Dhabi" },
                    { 3, "AE", true, "Sharjah" },
                    { 4, "AE", true, "Ajman" },
                    { 5, "AE", true, "Ras Al Khaimah" },
                    { 6, "AE", true, "Fujairah" },                    
                    { 7, "AE", true, "Umm Al-Quwain" }
                });

            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "AttributesJson", "CreatedDate", "Currency", "DeletedBy", "DeletedDate", "IsDeleted", "Price", "ProductType", "UpdatedDate" },
                values: new object[,]
                {
                    { new Guid("3f9d0f43-7996-4103-b796-b9054fd47ded"), "{\"Brand\":\"Redmi\",\"Model\":\"Note11 Pro 5G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.67 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro5g.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 1060m, 1, null },
                    { new Guid("fc90b41e-bd5c-4d93-aba5-5414412da7dd"), "{\"Brand\":\"Redmi\",\"Model\":\"Note11 Pro 4G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.67 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro4g.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 960m, 1, null },
                    { new Guid("dd43d743-c359-4ea1-9d8c-4927404db06b"), "{\"Brand\":\"Redmi\",\"Model\":\"Note 11\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.5 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 720m, 1, null },
                    { new Guid("854e5c74-4203-4c17-a760-ddc3a943a2be"), "{\"Brand\":\"Oppo\",\"Model\":\"A77\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.56 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoA77Black.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 780m, 1, null },
                    { new Guid("9d3ba1d9-1c38-4ead-8cfc-8438df74c4ab"), "{\"Brand\":\"Vivo\",\"Model\":\"Y22S\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.55 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Blue\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY22s.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 840m, 1, null },
                    { new Guid("404a3b6f-8181-40e2-90e6-31ef551d115d"), "{\"Brand\":\"Vivo\",\"Model\":\"Y33S\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.58 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Blue\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY33s.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 920m, 1, null },
                    { new Guid("b3f63af3-1394-46b9-be5c-bc50d8dc5865"), "{\"Brand\":\"Oppo\",\"Model\":\"RENO 8Z \",\"BackCamera\":\"64MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Gold\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoReno8z.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 1520m, 1, null },
                    { new Guid("869b6ab6-488b-469a-94a7-33ff84974542"), "{\"Brand\":\"Redmi\",\"Model\":\"Note 11S 4G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S4G.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 840m, 1, null },
                    { new Guid("8c1c9bb2-94c5-4713-99a5-7355eeb32add"), "{\"Brand\":\"Oppo\",\"Model\":\"A74\",\"BackCamera\":\"48MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OPPORenoA74.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 920m, 1, null },
                    { new Guid("ef8eaca8-5878-44b5-bd47-7fb1f63ad3b1"), "{\"Brand\":\"Samsung\",\"Model\":\"A33 \",\"BackCamera\":\"48MP\",\"FrontCamera\":\"13MP\",\"ScreenSize\":\"6.4 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA336GB128GB5g.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 1040m, 1, null },
                    { new Guid("71eb3e4f-c494-4d2d-af71-1003818c54a8"), "{\"Brand\":\"Samsung\",\"Model\":\"A23\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Samsung A23 4GB128GB.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 760m, 1, null },
                    { new Guid("27826ac1-9d05-45df-8076-0579a5885ae0"), "{\"Brand\":\"Samsung\",\"Model\":\"A13\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA134GB 128GB.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 660m, 1, null },
                    { new Guid("43976d37-4cae-4f1f-9c44-5487576d4481"), "{\"Brand\":\"Apple\",\"Model\":\"Iphone 11\",\"BackCamera\":\"12MP\",\"FrontCamera\":\"12MP\",\"ScreenSize\":\"6.1 inches\",\"Sim\":\"4G Single SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Apple11.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 2220m, 1, null },
                    { new Guid("59f640ca-90df-466f-8492-23d258cb3b67"), "{\"Brand\":\"Redmi\",\"Model\":\"Note 11S 5G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"13MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S5g.png\"}", new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), "AED", null, null, false, 1000m, 1, null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Cities_CountryCode",
                table: "Cities",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_OrderAddresses_CityId",
                table: "OrderAddresses",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_OrderAddressId",
                table: "Orders",
                column: "OrderAddressId",
                unique: true,
                filter: "[OrderAddressId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_ProductId",
                table: "Orders",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_ReferenceNumber",
                table: "Orders",
                column: "ReferenceNumber",
                unique: true,
                filter: "[ReferenceNumber] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_UserId",
                table: "Orders",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Orders");

            migrationBuilder.DropTable(
                name: "StoreExperimentUsers");

            migrationBuilder.DropTable(
                name: "OrderAddresses");

            migrationBuilder.DropTable(
                name: "Products");

            migrationBuilder.DropTable(
                name: "Cities");

            migrationBuilder.DropSequence(
                name: "OrderNumbers");
        }
    }
}
