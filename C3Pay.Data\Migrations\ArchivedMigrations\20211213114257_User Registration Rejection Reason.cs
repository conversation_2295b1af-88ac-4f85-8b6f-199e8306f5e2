﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UserRegistrationRejectionReason : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "UserRegistrationRejectionReasonId",
                table: "Identifications",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UserRegistrationRejectionReasons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserRegistrationRejectionReasons", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "UserRegistrationRejectionReasons",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Glare" },
                    { 2, "Unclear image" },
                    { 3, "Not original EID photo" },
                    { 4, "Wearing a mask" },
                    { 5, "Mismatch name" },
                    { 6, "Mismatch selfie" },
                    { 7, "Selfie not clear" },
                    { 8, "Missing images" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Identifications_UserRegistrationRejectionReasonId",
                table: "Identifications",
                column: "UserRegistrationRejectionReasonId");

            migrationBuilder.AddForeignKey(
                name: "FK_Identifications_UserRegistrationRejectionReasons_UserRegistrationRejectionReasonId",
                table: "Identifications",
                column: "UserRegistrationRejectionReasonId",
                principalTable: "UserRegistrationRejectionReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Identifications_UserRegistrationRejectionReasons_UserRegistrationRejectionReasonId",
                table: "Identifications");

            migrationBuilder.DropTable(
                name: "UserRegistrationRejectionReasons");

            migrationBuilder.DropIndex(
                name: "IX_Identifications_UserRegistrationRejectionReasonId",
                table: "Identifications");

            migrationBuilder.DropColumn(
                name: "UserRegistrationRejectionReasonId",
                table: "Identifications");
        }
    }
}
