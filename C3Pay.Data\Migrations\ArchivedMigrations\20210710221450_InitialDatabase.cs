﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class InitialDatabase : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CardHolders",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    EmployeeId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    C3RegistrationId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CardSerialNumber = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    CardNumber = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Birthdate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Gender = table.Column<int>(type: "int", maxLength: 6, nullable: false),
                    Nationality = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    PassportNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EmiratesId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    EmiratesIdExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EmployeeStatus = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    CorporateId = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    CorporateName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    City = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ZipCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    BelongsToExchangeHouse = table.Column<bool>(type: "bit", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CardHolders", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Countries",
                columns: table => new
                {
                    Code = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LongName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Code3 = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    STDCode = table.Column<string>(type: "nvarchar(8)", maxLength: 8, nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    IsPapularCountry = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    MobileRechargeEnabled = table.Column<bool>(type: "bit", nullable: false),
                    MobileRechargeEnabledForPartner = table.Column<bool>(type: "bit", nullable: false),
                    MobileRechargeLastSynchronizedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MoneyTransferEnabled = table.Column<bool>(type: "bit", nullable: false),
                    EligibleForBankTransfer = table.Column<bool>(type: "bit", nullable: false),
                    BankTransferLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    EligibleForCashPickUp = table.Column<bool>(type: "bit", nullable: false),
                    CashPickUpLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    CashPickUpProvider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RatesLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Countries", x => x.Code);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeSupportedCountries",
                columns: table => new
                {
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeSupportedCountries", x => x.CountryCode);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferProfiles",
                columns: table => new
                {
                    EmiratesId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    FileDate = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FullName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Status = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    DataFilename = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    BatchId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferProfiles", x => x.EmiratesId);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferReasons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Reason = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferReasons", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PortalUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RequiresPasswordReset = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PortalUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ReferrerCodes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    Code = table.Column<string>(type: "nvarchar(6)", maxLength: 6, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReferrerCodes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SecurityQuestions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Question = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SecurityQuestions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Subscriptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Title = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ShortDescription = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    FullDescription = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsAvailable = table.Column<bool>(type: "bit", nullable: false),
                    PaymentFrequency = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscriptions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Users",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    ExternalId = table.Column<int>(type: "int", nullable: false),
                    CardHolderId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(14)", maxLength: 14, nullable: true),
                    DeviceToken = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IsVerified = table.Column<bool>(type: "bit", nullable: false),
                    IsBlocked = table.Column<bool>(type: "bit", nullable: false),
                    BlockType = table.Column<int>(type: "int", nullable: true),
                    BlockDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UnblockDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApplicationId = table.Column<int>(type: "int", nullable: false),
                    MoneyTransferProfileStatus = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    RequiresPasswordReset = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Users", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Users_CardHolders_CardHolderId",
                        column: x => x.CardHolderId,
                        principalTable: "CardHolders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "BlackListedEntities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    EntityType = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    Identifier = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BlackListedEntities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BlackListedEntities_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferLimits",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    CashPickUpLimit_MinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpLimit_MaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpLimit_MaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpLimit_MaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    BankTransferLimit_MinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferLimit_MaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferLimit_MaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferLimit_MaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: true, defaultValue: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferLimits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferLimits_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Popups",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    Order = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    Visible = table.Column<bool>(type: "bit", nullable: false),
                    ImageFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Popups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Popups_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeProviders",
                columns: table => new
                {
                    Code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ShortName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ValidationRegex = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CustomerCareNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RegionCodes = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    PaymentTypes = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LogoUrl = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IsProcessingTransfers = table.Column<bool>(type: "bit", nullable: true, defaultValue: true),
                    ProcessStatusMessage = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: true, defaultValue: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeProviders", x => x.Code);
                    table.ForeignKey(
                        name: "FK_MobileRechargeProviders_MobileRechargeSupportedCountries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "MobileRechargeSupportedCountries",
                        principalColumn: "CountryCode",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SubscriptionFeatures",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IsAvailable = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionFeatures", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionFeatures_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Documents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Documents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Documents_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "EmiratesIdUpdates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Birthdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IdNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    CardNumber = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    Nationality = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Gender = table.Column<int>(type: "int", maxLength: 6, nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateType = table.Column<int>(type: "int", nullable: false),
                    FaceMatchPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FaceMatchIsIdeal = table.Column<bool>(type: "bit", nullable: false),
                    NameMatchPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    NameMatchIsIdeal = table.Column<bool>(type: "bit", nullable: false),
                    FrontScanFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    BackScanFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    SelfieFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    VerifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    VerificationStatus = table.Column<int>(type: "int", nullable: false),
                    VerificationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OriginalVerifierId = table.Column<int>(type: "int", nullable: true),
                    Vendor = table.Column<int>(type: "int", nullable: false),
                    ServiceReference = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Posted = table.Column<bool>(type: "bit", nullable: false),
                    PostRemarks = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmiratesIdUpdates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmiratesIdUpdates_PortalUsers_VerifierId",
                        column: x => x.VerifierId,
                        principalTable: "PortalUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_EmiratesIdUpdates_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeBeneficiaries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    NickName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    AccountNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    RechargeType = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeBeneficiaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MobileRechargeBeneficiaries_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MobileRechargeBeneficiaries_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferBeneficiaries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: false),
                    MoneyTransferReasonId = table.Column<int>(type: "int", nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MiddleName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    TransferType = table.Column<int>(type: "int", maxLength: 30, nullable: false),
                    BankName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    BankBranchName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Address1 = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Address2 = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IdentifierCode1 = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IdentifierCode2 = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    AccountNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DocumentType = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    DocumentNumber = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Posted = table.Column<bool>(type: "bit", nullable: false),
                    RequiresApproval = table.Column<bool>(type: "bit", nullable: false),
                    PostDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ApprovalDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferBeneficiaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferBeneficiaries_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferBeneficiaries_MoneyTransferReasons_MoneyTransferReasonId",
                        column: x => x.MoneyTransferReasonId,
                        principalTable: "MoneyTransferReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MoneyTransferBeneficiaries_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "SecretAnswers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SecurityQuestionId = table.Column<int>(type: "int", nullable: false),
                    Answer = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SecretAnswers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SecretAnswers_SecurityQuestions_SecurityQuestionId",
                        column: x => x.SecurityQuestionId,
                        principalTable: "SecurityQuestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SecretAnswers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Transactions",
                columns: table => new
                {
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ServiceProvider = table.Column<int>(type: "int", nullable: false),
                    CardNumber = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    CardSerialNumber = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BillPayType = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    AuthenticationCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    PayeeId = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Origin = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountTerminalId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountTerminalAddress = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    RelativeSequenceNumber = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    MacValue = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EndBalance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Date = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Time = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    StatusCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    StatusDescription = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Reversal_AuthenticationCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Reversal_Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Reversal_EndBalance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Reversal_ReferenceNumber = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    Reversal_Date = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Reversal_StatusCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Reversal_StatusDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transactions", x => x.ReferenceNumber);
                    table.ForeignKey(
                        name: "FK_Transactions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UploadedDocuments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Url = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UploadedDocuments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UploadedDocuments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserSubscriptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PaymentWaved = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserSubscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserSubscriptions_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UserSubscriptions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "VerificationComments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PortalUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    OriginalAdminId = table.Column<int>(type: "int", nullable: true),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VerificationComments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VerificationComments_PortalUsers_PortalUserId",
                        column: x => x.PortalUserId,
                        principalTable: "PortalUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_VerificationComments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeProducts",
                columns: table => new
                {
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ProviderCode = table.Column<string>(type: "nvarchar(20)", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    LocalizationKey = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    MinCustomerFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinDistributorFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinReceiveValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinReceiveCurrencyIso = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MinReceiveValueExcludingTax = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinTaxRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinTaxName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MinTaxCalculation = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    MinSendValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MinSendCurrencyIso = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MaxCustomerFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxDistributorFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxReceiveValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxReceiveCurrencyIso = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MaxReceiveValueExcludingTax = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxTaxRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxTaxName = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MaxTaxCalculation = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: true),
                    MaxSendValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    MaxSendCurrencyIso = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CommissionRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ProcessingMode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    RedemptionMechanism = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    Benefits = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    ValidityPeriodIso = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    UATNumber = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    AdditionalInformation = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DefaultDisplayText = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    RegionCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    LookupBillsRequired = table.Column<bool>(type: "bit", nullable: true),
                    CustomFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: true, defaultValue: true),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeProducts", x => x.Code);
                    table.ForeignKey(
                        name: "FK_MobileRechargeProducts_MobileRechargeProviders_ProviderCode",
                        column: x => x.ProviderCode,
                        principalTable: "MobileRechargeProviders",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MobileRechargeProducts_MobileRechargeSupportedCountries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "MobileRechargeSupportedCountries",
                        principalColumn: "CountryCode",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeBeneficiaryProviders",
                columns: table => new
                {
                    BeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ProviderCode = table.Column<string>(type: "nvarchar(20)", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: true, defaultValue: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeBeneficiaryProviders", x => new { x.BeneficiaryId, x.ProviderCode });
                    table.ForeignKey(
                        name: "FK_MobileRechargeBeneficiaryProviders_MobileRechargeBeneficiaries_BeneficiaryId",
                        column: x => x.BeneficiaryId,
                        principalTable: "MobileRechargeBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MobileRechargeBeneficiaryProviders_MobileRechargeProviders_ProviderCode",
                        column: x => x.ProviderCode,
                        principalTable: "MobileRechargeProviders",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferExternalBeneficiaries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    MoneyTransferBeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalId = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: false),
                    CreationMessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionMessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferExternalBeneficiaries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferExternalBeneficiaries_MoneyTransferBeneficiaries_MoneyTransferBeneficiaryId",
                        column: x => x.MoneyTransferBeneficiaryId,
                        principalTable: "MoneyTransferBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MoneyTransferBeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MoneyTransferReasonId = table.Column<int>(type: "int", nullable: false),
                    TransferType = table.Column<int>(type: "int", nullable: false),
                    SendCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    SendAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ReceiveCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    ReceiveAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ConversionRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: true),
                    ChargesAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    WaivedCharge = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    WaiveType = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    TotalCharges = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    ChargesCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    ReferralCode = table.Column<string>(type: "nvarchar(6)", maxLength: 6, nullable: true),
                    TriesCount = table.Column<int>(type: "int", nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferTransactions_MoneyTransferBeneficiaries_MoneyTransferBeneficiaryId",
                        column: x => x.MoneyTransferBeneficiaryId,
                        principalTable: "MoneyTransferBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferTransactions_MoneyTransferReasons_MoneyTransferReasonId",
                        column: x => x.MoneyTransferReasonId,
                        principalTable: "MoneyTransferReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MoneyTransferTransactions_Transactions_ReferenceNumber",
                        column: x => x.ReferenceNumber,
                        principalTable: "Transactions",
                        principalColumn: "ReferenceNumber",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferTransactions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ProductCode = table.Column<string>(type: "nvarchar(50)", nullable: false),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    AccountNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    RechargeType = table.Column<int>(type: "int", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    SendCurrency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    SendAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ReceiveCurrency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ReceiveAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TriesCount = table.Column<int>(type: "int", nullable: true),
                    Remarks = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MobileRechargeTransactions_MobileRechargeBeneficiaries_BeneficiaryId",
                        column: x => x.BeneficiaryId,
                        principalTable: "MobileRechargeBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MobileRechargeTransactions_MobileRechargeProducts_ProductCode",
                        column: x => x.ProductCode,
                        principalTable: "MobileRechargeProducts",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MobileRechargeTransactions_Transactions_ReferenceNumber",
                        column: x => x.ReferenceNumber,
                        principalTable: "Transactions",
                        principalColumn: "ReferenceNumber",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MobileRechargeTransactions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferExternalTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    MoneyTransferTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalTransactionId = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    MessageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    FxRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: true),
                    ChargesAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    WaivedCharge = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    TotalCharges = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true, defaultValue: 0m),
                    ChargesCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    CreditCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    TotalCreditAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    DebitCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    TotalDebitAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ExternalStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StatusDescription = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    StatusDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastStatusDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferExternalTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferExternalTransactions_MoneyTransferTransactions_MoneyTransferTransactionId",
                        column: x => x.MoneyTransferTransactionId,
                        principalTable: "MoneyTransferTransactions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeExternalTransactions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    MobileRechargeTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExternalTransactionId = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CommisionRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    SendCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    SendAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    CustomerFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    ReceiveCurrency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    ReceiveAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ReceiveAmountWithTax = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    DistributorFee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TaxName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    TaxRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    TaxCalculation = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ExternalStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CallingCardPin = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    StatusDescription = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ReceiptText = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    ReceiptParams = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    AccountNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    StatusDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastStatusDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeExternalTransactions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MobileRechargeExternalTransactions_MobileRechargeTransactions_MobileRechargeTransactionId",
                        column: x => x.MobileRechargeTransactionId,
                        principalTable: "MobileRechargeTransactions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferReasons",
                columns: new[] { "Id", "Reason" },
                values: new object[,]
                {
                    { 8, "Family maintenance and savings" },
                    { 21, "Personal or other travel" },
                    { 22, "Education services" },
                    { 24, "Medical or health" }
                });

            migrationBuilder.InsertData(
                table: "SecurityQuestions",
                columns: new[] { "Id", "Question" },
                values: new object[,]
                {
                    { 15, "What city did you fly for the first time by plane?" },
                    { 14, "What was your father's last job?" },
                    { 13, "What was the name of your favorite school teacher?" },
                    { 12, "What is your youngest brother or sister's name?" },
                    { 11, "What is your eldest brother or sister's name?" },
                    { 10, "What was your first car?" },
                    { 9, "What is your wife or husband's first name?" },
                    { 7, "What is the first name of your best friend in school?" },
                    { 6, "Who was your childhood's sports hero?" },
                    { 5, "In what city or town was your first job?" },
                    { 4, "What is the first name of your first child?" },
                    { 3, "What was the name of your school?" },
                    { 2, "What is your father's first name?" },
                    { 1, "What is your mother's first name?" },
                    { 8, "What was the name of the company where you had your first job?" }
                });

            migrationBuilder.InsertData(
                table: "Subscriptions",
                columns: new[] { "Id", "Code", "CreatedDate", "DeletedBy", "DeletedDate", "DisplayOrder", "Fee", "FullDescription", "IsAvailable", "IsDeleted", "PaymentFrequency", "ShortDescription", "Title", "UpdatedDate" },
                values: new object[,]
                {
                    { new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), "T", new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 2, 3m, "Secure your card from being used without your knowledge. Keep your account safe by getting an SMS whenever:", true, false, 1, "Receive an SMS after every transaction.", "Security Alerts", null },
                    { new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), "BE", new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 1, 1m, "View your current balance, save on ATM balance enquiry fees and view your current balance at any time as often as you want on this app", true, false, 1, "View your current balance at any time.", "Balance Enquiry", null },
                    { new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), "S", new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 3, 0.5m, "Receive an SMS as soon as your salary is deposited into your account:", true, false, 2, "Receive an SMS with your salary.", "SMS Salary Alert", null }
                });

            migrationBuilder.InsertData(
                table: "SubscriptionFeatures",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Description", "IsAvailable", "IsDeleted", "SubscriptionId", "UpdatedDate" },
                values: new object[,]
                {
                    { new Guid("94b6c134-5e52-4019-acb1-9c0884c28e31"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "View your balance", true, false, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null },
                    { new Guid("596a0b4c-9c68-48ab-b574-4a8fc3431edd"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "View your transactions", true, false, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null },
                    { new Guid("170fd6ae-7c74-4604-bc5e-f0f79ea6398a"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Anytime & anywhere", true, false, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null },
                    { new Guid("3e569303-038e-413e-b3e2-3dfe3f3b2b76"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Avoid fees on ATMs", true, false, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null },
                    { new Guid("38f76250-0e8b-4985-adb1-7852368d6884"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "You receive your salary", true, false, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null },
                    { new Guid("09fb418d-6219-4c5e-87df-990c8b050919"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used at the ATM", true, false, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null },
                    { new Guid("0156f81c-a76f-43e8-9a95-b8244c6cd725"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used in shops", true, false, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null },
                    { new Guid("36f0989d-ddf5-474c-b18c-e9343d35edb5"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used online", true, false, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null },
                    { new Guid("5a35e788-6c84-4d40-9d12-03490902a21e"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "You receive your salary", true, false, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null },
                    { new Guid("76990d20-b275-4ba6-8b99-d9cc27205421"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used at the ATM", false, false, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null },
                    { new Guid("7e14e718-7981-4bae-a08f-f146a5408642"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used in shops", false, false, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null },
                    { new Guid("f760a7bf-3f2c-45be-9f23-0bb3e0d96e50"), new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Your card is used online", false, false, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_BlackListedEntities_CountryCode",
                table: "BlackListedEntities",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_CardHolders_CardSerialNumber",
                table: "CardHolders",
                column: "CardSerialNumber");

            migrationBuilder.CreateIndex(
                name: "IX_CardHolders_CorporateId",
                table: "CardHolders",
                column: "CorporateId");

            migrationBuilder.CreateIndex(
                name: "IX_CardHolders_EmiratesId",
                table: "CardHolders",
                column: "EmiratesId");

            migrationBuilder.CreateIndex(
                name: "IX_CardHolders_FirstName",
                table: "CardHolders",
                column: "FirstName");

            migrationBuilder.CreateIndex(
                name: "IX_CardHolders_LastName",
                table: "CardHolders",
                column: "LastName");

            migrationBuilder.CreateIndex(
                name: "IX_Documents_UserId",
                table: "Documents",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_EmiratesIdUpdates_UserId",
                table: "EmiratesIdUpdates",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_EmiratesIdUpdates_VerifierId",
                table: "EmiratesIdUpdates",
                column: "VerifierId");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaries_AccountNumber",
                table: "MobileRechargeBeneficiaries",
                column: "AccountNumber");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaries_CountryCode",
                table: "MobileRechargeBeneficiaries",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaries_CreatedDate",
                table: "MobileRechargeBeneficiaries",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaries_NickName",
                table: "MobileRechargeBeneficiaries",
                column: "NickName");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaries_UserId",
                table: "MobileRechargeBeneficiaries",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeBeneficiaryProviders_ProviderCode",
                table: "MobileRechargeBeneficiaryProviders",
                column: "ProviderCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeExternalTransactions_ExternalTransactionId",
                table: "MobileRechargeExternalTransactions",
                column: "ExternalTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeExternalTransactions_MobileRechargeTransactionId",
                table: "MobileRechargeExternalTransactions",
                column: "MobileRechargeTransactionId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeProducts_CountryCode",
                table: "MobileRechargeProducts",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeProducts_ProviderCode",
                table: "MobileRechargeProducts",
                column: "ProviderCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeProviders_CountryCode",
                table: "MobileRechargeProviders",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeTransactions_BeneficiaryId",
                table: "MobileRechargeTransactions",
                column: "BeneficiaryId");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeTransactions_CreatedDate",
                table: "MobileRechargeTransactions",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeTransactions_ProductCode",
                table: "MobileRechargeTransactions",
                column: "ProductCode");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeTransactions_ReferenceNumber",
                table: "MobileRechargeTransactions",
                column: "ReferenceNumber",
                unique: true,
                filter: "[ReferenceNumber] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeTransactions_UserId",
                table: "MobileRechargeTransactions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_CountryCode",
                table: "MoneyTransferBeneficiaries",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_CreatedDate",
                table: "MoneyTransferBeneficiaries",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_FirstName",
                table: "MoneyTransferBeneficiaries",
                column: "FirstName");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_LastName",
                table: "MoneyTransferBeneficiaries",
                column: "LastName");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries",
                column: "MoneyTransferReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_UserId",
                table: "MoneyTransferBeneficiaries",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferExternalBeneficiaries_MoneyTransferBeneficiaryId",
                table: "MoneyTransferExternalBeneficiaries",
                column: "MoneyTransferBeneficiaryId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferExternalTransactions_ExternalTransactionId",
                table: "MoneyTransferExternalTransactions",
                column: "ExternalTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferExternalTransactions_MoneyTransferTransactionId",
                table: "MoneyTransferExternalTransactions",
                column: "MoneyTransferTransactionId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferLimits_CountryCode",
                table: "MoneyTransferLimits",
                column: "CountryCode",
                unique: true,
                filter: "[CountryCode] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactions_CreatedDate",
                table: "MoneyTransferTransactions",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactions_MoneyTransferBeneficiaryId",
                table: "MoneyTransferTransactions",
                column: "MoneyTransferBeneficiaryId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactions_MoneyTransferReasonId",
                table: "MoneyTransferTransactions",
                column: "MoneyTransferReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactions_ReferenceNumber",
                table: "MoneyTransferTransactions",
                column: "ReferenceNumber",
                unique: true,
                filter: "[ReferenceNumber] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferTransactions_UserId",
                table: "MoneyTransferTransactions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Popups_CountryCode",
                table: "Popups",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_SecretAnswers_SecurityQuestionId",
                table: "SecretAnswers",
                column: "SecurityQuestionId");

            migrationBuilder.CreateIndex(
                name: "IX_SecretAnswers_UserId",
                table: "SecretAnswers",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionFeatures_SubscriptionId",
                table: "SubscriptionFeatures",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_Transactions_UserId",
                table: "Transactions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UploadedDocuments_UserId",
                table: "UploadedDocuments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_CardHolderId",
                table: "Users",
                column: "CardHolderId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_CreatedDate",
                table: "Users",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_Users_ExternalId",
                table: "Users",
                column: "ExternalId");

            migrationBuilder.CreateIndex(
                name: "IX_Users_PhoneNumber",
                table: "Users",
                column: "PhoneNumber");

            migrationBuilder.CreateIndex(
                name: "IX_UserSubscriptions_SubscriptionId",
                table: "UserSubscriptions",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_UserSubscriptions_UserId",
                table: "UserSubscriptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_VerificationComments_PortalUserId",
                table: "VerificationComments",
                column: "PortalUserId");

            migrationBuilder.CreateIndex(
                name: "IX_VerificationComments_UserId",
                table: "VerificationComments",
                column: "UserId");

            var sp = @"INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AD', N'Andorra', N'Principality of Andorra', N'AND', N'376', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112064' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AE', N'United Arab Emirates', N'United Arab Emirates (the)', N'ARE', N'971', N'AED', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112078' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AF', N'Afghanistan', N'Islamic Republic of Afghanistan', N'AFG', N'93', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112080' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AG', N'Antigua and Barbuda', N'Antigua and Barbuda', N'ATG', N'1+268', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112081' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AI', N'Anguilla', N'Anguilla', N'AIA', N'1+264', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112082' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AL', N'Albania', N'Republic of Albania', N'ALB', N'355', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112084' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AM', N'Armenia', N'Republic of Armenia', N'ARM', N'374', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112085' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AO', N'Anla', N'Republic of Anla', N'A', N'244', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112087' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AQ', N'Antarctica', N'Antarctica', N'ATA', N'672', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AR', N'Argentina', N'Argentine Republic', N'ARG', N'54', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112088' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AS', N'American Samoa', N'American Samoa', N'ASM', N'1+684', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112089' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AT', N'Austria', N'Republic of Austria', N'AUT', N'43', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112091' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AU', N'Australia', N'Commonwealth of Australia', N'AUS', N'61', N'AUD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112092' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AW', N'Aruba', N'Aruba', N'ABW', N'297', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112093' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AX', N'Aland Islands', N'&Aring;land Islands', N'ALA', N'358', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'AZ', N'Azerbaijan', N'Republic of Azerbaijan', N'AZE', N'994', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112095' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BA', N'Bosnia and Herzevina', N'Bosnia and Herzevina', N'BIH', N'387', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112096' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BB', N'Barbados', N'Barbados', N'BRB', N'1+246', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112097' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BD', N'Bangladesh', N'Peoples Republic of Bangladesh', N'BGD', N'880', N'BDT', 1, 3, 1, 1, CAST(N'2021-06-09T13:04:15.0112098' AS DateTime2), 1, 1, CAST(23.1530 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, CAST(N'2021-11-08T15:17:41.4335798' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BE', N'Belgium', N'Kingdom of Belgium', N'BEL', N'32', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112099' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 1, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BF', N'Burkina Faso', N'Burkina Faso', N'BFA', N'226', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112101' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BG', N'Bulgaria', N'Republic of Bulgaria', N'BGR', N'359', N'BGN', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112102' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 1, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BH', N'Bahrain', N'Kingdom of Bahrain', N'BHR', N'973', N'BHD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112106' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BI', N'Burundi', N'Republic of Burundi', N'BDI', N'257', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112107' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BJ', N'Benin', N'Republic of Benin', N'BEN', N'229', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112108' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BL', N'Saint Barthelemy', N'Saint Barth&eacute;lemy', N'BLM', N'590', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BM', N'Bermuda', N'Bermuda Islands', N'BMU', N'1+441', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112110' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BN', N'Brunei', N'Brunei Darussalam', N'BRN', N'673', N'BND', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112111' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BO', N'Bolivia', N'Plurinational State of Bolivia', N'BOL', N'591', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112112' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BQ', N'Bonaire, Sint Eustatius and Saba', N'Bonaire, Sint Eustatius and Saba', N'BES', N'599', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112114' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BR', N'Brazil', N'Federative Republic of Brazil', N'BRA', N'55', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112115' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BS', N'Bahamas', N'Bahamas (the)', N'BHS', N'1+242', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112116' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BT', N'Bhutan', N'Kingdom of Bhutan', N'BTN', N'975', N'BTN', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112117' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BV', N'Bouvet Island', N'Bouvet Island', N'BVT', N'NONE', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BW', N'Botswana', N'Republic of Botswana', N'BWA', N'267', N'BWP', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112288' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BY', N'Belarus', N'Republic of Belarus', N'BLR', N'375', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112289' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'BZ', N'Belize', N'Belize', N'BLZ', N'501', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112291' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CA', N'Canada', N'Canada', N'CAN', N'1', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112292' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CC', N'Cocos (Keeling) Islands', N'Cocos (Keeling) Islands (the)', N'CCK', N'61', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CD', N'Democratic Republic of the Con', N'Democratic Republic of the Con', N'COD', N'243', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112294' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CF', N'Central African Republic', N'Central African Republic (the)', N'CAF', N'236', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112295' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CG', N'Con', N'Con (the)', N'COG', N'242', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112297' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CH', N'Switzerland', N'Swiss Confederation', N'CHE', N'41', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112298' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CI', N'Cote divoire (Ivory Coast)', N'Republic of C&ocirc;te DIvoire (Ivory Coast)', N'CIV', N'225', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112300' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CL', N'Chile', N'Republic of Chile', N'CHL', N'56', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112301' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CM', N'Cameroon', N'Republic of Cameroon', N'CMR', N'237', N'XAF', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112303' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CN', N'China', N'Peoples Republic of China', N'CHN', N'86', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112304' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CO', N'Colombia', N'Republic of Colombia', N'COL', N'57', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112306' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CR', N'Costa Rica', N'Republic of Costa Rica', N'CRI', N'506', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112308' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CU', N'Cuba', N'Republic of Cuba', N'CUB', N'53', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112309' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CV', N'Cape Verde', N'Republic of Cape Verde', N'CPV', N'238', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112311' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CW', N'Curacao', N'Cura&ccedil;ao', N'CUW', N'599', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112312' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CX', N'Christmas Island', N'Christmas Island', N'CXR', N'61', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CY', N'Cyprus', N'Republic of Cyprus', N'CYP', N'357', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112313' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'CZ', N'Czech Republic', N'Czech Republic', N'CZE', N'420', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112315' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DE', N'Germany', N'Federal Republic of Germany', N'DEU', N'49', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112317' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DJ', N'Djibouti', N'Republic of Djibouti', N'DJI', N'253', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112318' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DK', N'Denmark', N'Kingdom of Denmark', N'DNK', N'45', N'DKK', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112320' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 1, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DM', N'Dominica', N'Commonwealth of Dominica', N'DMA', N'1+767', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112321' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DO', N'Dominican Republic', N'Dominican Republic (the)', N'DOM', N'1+809, 8', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112323' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'DZ', N'Algeria', N'Peoples Democratic Republic of Algeria', N'DZA', N'213', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112324' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'EC', N'Ecuador', N'Republic of Ecuador', N'ECU', N'593', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112326' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'EE', N'Estonia', N'Republic of Estonia', N'EST', N'372', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112327' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'EG', N'Egypt', N'Arab Republic of Egypt', N'EGY', N'20', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112329' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'EH', N'Western Sahara', N'Western Sahara', N'ESH', N'212', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ER', N'Eritrea', N'State of Eritrea', N'ERI', N'291', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112330' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ES', N'Spain', N'Kingdom of Spain', N'ESP', N'34', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112332' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ET', N'Ethiopia', N'Federal Democratic Republic of Ethiopia', N'ETH', N'251', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112333' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FI', N'Finland', N'Republic of Finland', N'FIN', N'358', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112335' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FJ', N'Fiji', N'Republic of Fiji', N'FJI', N'679', N'FJD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112336' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FK', N'Falkland Islands (Malvinas)', N'Falkland Islands (the) [Malvinas]', N'FLK', N'500', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112338' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FM', N'Micronesia', N'Federated States of Micronesia', N'FSM', N'691', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112339' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FO', N'Faroe Islands', N'Faroe Islands (the)', N'FRO', N'298', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112341' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'FR', N'France', N'French Republic', N'FRA', N'33', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112342' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GA', N'Gabon', N'Gabonese Republic', N'GAB', N'241', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112344' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GB', N'United Kingdom', N'United Kingdom of Great Britain and Northern Ireland (the)', N'GBR', N'44', N'GBP', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112345' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GD', N'Grenada', N'Grenada', N'GRD', N'1+473', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112347' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GE', N'Georgia', N'Georgia', N'GEO', N'995', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112348' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GF', N'French Guiana', N'French Guiana', N'GUF', N'594', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112350' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GG', N'Guernsey', N'Guernsey', N'GGY', N'44', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GH', N'Ghana', N'Republic of Ghana', N'GHA', N'233', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112351' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GI', N'Gibraltar', N'Gibraltar', N'GIB', N'350', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112353' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GL', N'Greenland', N'Greenland', N'GRL', N'299', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112355' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GM', N'Gambia', N'Gambia (the)', N'GMB', N'220', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112356' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GN', N'Guinea', N'Republic of Guinea', N'GIN', N'224', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112357' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GP', N'Guadaloupe', N'Guadeloupe', N'GLP', N'590', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112359' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GQ', N'Equatorial Guinea', N'Republic of Equatorial Guinea', N'GNQ', N'240', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112361' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GR', N'Greece', N'Hellenic Republic', N'GRC', N'30', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112362' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GS', N'South Georgia and the South Sandwich Islands', N'South Georgia and the South Sandwich Islands', N'SGS', N'500', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GT', N'Guatemala', N'Republic of Guatemala', N'GTM', N'502', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112364' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GU', N'Guam', N'Guam', N'GUM', N'1+671', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112366' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GW', N'Guinea-Bissau', N'Republic of Guinea-Bissau', N'GNB', N'245', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112367' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'GY', N'Guyana', N'Co-operative Republic of Guyana', N'GUY', N'592', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112369' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HK', N'Hong Kong', N'Hong Kong', N'HKG', N'852', N'HKD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112370' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HM', N'Heard Island and McDonald Islands', N'Heard Island and McDonald Islands', N'HMD', N'NONE', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HN', N'Honduras', N'Republic of Honduras', N'HND', N'504', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112371' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HR', N'Croatia', N'Republic of Croatia', N'HRV', N'385', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112373' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HT', N'Haiti', N'Republic of Haiti', N'HTI', N'509', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112374' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'HU', N'Hungary', N'Hungary', N'HUN', N'36', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112553' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ID', N'Indonesia', N'Republic of Indonesia', N'IDN', N'62', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112555' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IE', N'Ireland', N'Ireland', N'IRL', N'353', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112557' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IL', N'Israel', N'State of Israel', N'ISR', N'972', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112558' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IM', N'Isle of Man', N'Isle of Man', N'IMN', N'44', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IN', N'India', N'Republic of India', N'IND', N'91', N'INR', 1, 1, 1, 1, CAST(N'2021-06-09T13:04:15.0112560' AS DateTime2), 1, 1, CAST(20.1410 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, CAST(N'2021-11-08T15:17:42.7686171' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IO', N'British Indian Ocean Territory', N'British Indian Ocean Territory (the)', N'IOT', N'246', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112561' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IQ', N'Iraq', N'Republic of Iraq', N'IRQ', N'964', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112563' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IR', N'Iran', N'Iran (Islamic Republic of)', N'IRN', N'98', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112564' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IS', N'Iceland', N'Republic of Iceland', N'ISL', N'354', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112566' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'IT', N'Italy', N'Italian Republic', N'ITA', N'39', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112567' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'JE', N'Jersey', N'The Bailiwick of Jersey', N'JEY', N'44', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'JM', N'Jamaica', N'Jamaica', N'JAM', N'1+876', N'JMD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112569' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'JO', N'Jordan', N'Hashemite Kingdom of Jordan', N'JOR', N'962', N'JOD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112570' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'JP', N'Japan', N'Japan', N'JPN', N'81', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112571' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KE', N'Kenya', N'Republic of Kenya', N'KEN', N'254', N'KES', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112573' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KG', N'Kyrgyzstan', N'Kyrgyz Republic', N'KGZ', N'996', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112575' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KH', N'Cambodia', N'Kingdom of Cambodia', N'KHM', N'855', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112576' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KI', N'Kiribati', N'Republic of Kiribati', N'KIR', N'686', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112578' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KM', N'Comoros', N'Comoros (the)', N'COM', N'269', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112579' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KN', N'Saint Kitts and Nevis', N'Federation of Saint Christopher and Nevis', N'KNA', N'1+869', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112581' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KP', N'North Korea', N'Democratic Peoples Republic of Korea', N'PRK', N'850', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112582' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KR', N'South Korea', N'Republic of Korea', N'KOR', N'82', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112584' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KW', N'Kuwait', N'State of Kuwait', N'KWT', N'965', N'KWD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112585' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KY', N'Cayman Islands', N'Cayman Islands (the)', N'CYM', N'1+345', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112587' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'KZ', N'Kazakhstan', N'Republic of Kazakhstan', N'KAZ', N'7', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112589' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LA', N'Laos', N'Lao Peoples Democratic Republic', N'LAO', N'856', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112590' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LB', N'Lebanon', N'Republic of Lebanon', N'LBN', N'961', N'LBP', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112591' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LC', N'Saint Lucia', N'Saint Lucia', N'LCA', N'1+758', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112593' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LI', N'Liechtenstein', N'Principality of Liechtenstein', N'LIE', N'423', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112595' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LK', N'Sri Lanka', N'Democratic Socialist Republic of Sri Lanka', N'LKA', N'94', N'LKR', 1, 6, 1, 1, CAST(N'2021-06-09T13:04:15.0112597' AS DateTime2), 1, 1, CAST(55.0900 AS Decimal(18, 4)), 1, CAST(55.0900 AS Decimal(18, 4)), N'Cargills Br & Food City Outlet', CAST(N'2021-11-08T15:17:44.8485908' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LR', N'Liberia', N'Republic of Liberia', N'LBR', N'231', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112598' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LS', N'Lesotho', N'Kingdom of Lesotho', N'LSO', N'266', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112600' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LT', N'Lithuania', N'Republic of Lithuania', N'LTU', N'370', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112601' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LU', N'Luxembourg', N'Grand Duchy of Luxembourg', N'LUX', N'352', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112603' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LV', N'Latvia', N'Republic of Latvia', N'LVA', N'371', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112604' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'LY', N'Libya', N'Libya', N'LBY', N'218', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112606' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MA', N'Morocco', N'Kingdom of Morocco', N'MAR', N'212', N'MAD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112607' AS DateTime2), 1, 1, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MC', N'Monaco', N'Principality of Monaco', N'MCO', N'377', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112609' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MD', N'Moldava', N'Republic of Moldova', N'MDA', N'373', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112610' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ME', N'Montenegro', N'Montenegro', N'MNE', N'382', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112612' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MF', N'Saint Martin', N'Saint Martin', N'MAF', N'590', N'', 0, 20, 0, 1, CAST(N'2021-07-14T20:16:10.5375220' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MG', N'Madagascar', N'Republic of Madagascar', N'MDG', N'261', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112614' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MH', N'Marshall Islands', N'Marshall Islands (the)', N'MHL', N'692', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112615' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MK', N'Macedonia', N'The Former Yuslav Republic of Macedonia', N'MKD', N'389', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112616' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ML', N'Mali', N'Republic of Mali', N'MLI', N'223', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112618' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MM', N'Myanmar (Burma)', N'Republic of the Union of Myanmar', N'MMR', N'95', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112620' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MN', N'Monlia', N'Monlia', N'MNG', N'976', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112621' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MO', N'Macao', N'The Macao Special Administrative Region', N'MAC', N'853', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112622' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MP', N'Northern Mariana Islands', N'Northern Mariana Islands (the)', N'MNP', N'1+670', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112624' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MQ', N'Martinique', N'Martinique', N'MTQ', N'596', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112626' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MR', N'Mauritania', N'Islamic Republic of Mauritania', N'MRT', N'222', N'MRU', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112627' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MS', N'Montserrat', N'Montserrat', N'MSR', N'1+664', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112629' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MT', N'Malta', N'Republic of Malta', N'MLT', N'356', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112630' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MU', N'Mauritius', N'Republic of Mauritius', N'MUS', N'230', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112632' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MV', N'Maldives', N'Republic of Maldives', N'MDV', N'960', N'MVR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112633' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MW', N'Malawi', N'Republic of Malawi', N'MWI', N'265', N'MWK', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112634' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MX', N'Mexico', N'United Mexican States', N'MEX', N'52', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112636' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MY', N'Malaysia', N'Malaysia', N'MYS', N'60', N'MYR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112638' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'MZ', N'Mozambique', N'Republic of Mozambique', N'MOZ', N'258', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112639' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NA', N'Namibia', N'Republic of Namibia', N'NAM', N'264', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112641' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NC', N'New Caledonia', N'New Caledonia', N'NCL', N'687', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112642' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NE', N'Niger', N'Niger (the)', N'NER', N'227', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112644' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NF', N'Norfolk Island', N'Norfolk Island', N'NFK', N'672', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112645' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NG', N'Nigeria', N'Federal Republic of Nigeria', N'NGA', N'234', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112647' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NI', N'Nicaragua', N'Republic of Nicaragua', N'NIC', N'505', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112649' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NL', N'Netherlands', N'Netherlands (the)', N'NLD', N'31', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112650' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NO', N'Norway', N'Kingdom of Norway', N'NOR', N'47', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112652' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NP', N'Nepal', N'Federal Democratic Republic of Nepal', N'NPL', N'977', N'NPR', 1, 4, 1, 1, CAST(N'2021-06-09T13:04:15.0112654' AS DateTime2), 1, 1, CAST(32.3210 AS Decimal(18, 4)), 1, CAST(32.3210 AS Decimal(18, 4)), N'Prabhu Bank', CAST(N'2021-11-08T15:17:46.1586405' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NR', N'Nauru', N'Republic of Nauru', N'NRU', N'674', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112655' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'NZ', N'New Zealand', N'New Zealand', N'NZL', N'64', N'NZD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112657' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'OM', N'Oman', N'Sultanate of Oman', N'OMN', N'968', N'OMR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112658' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PA', N'Panama', N'Republic of Panama', N'PAN', N'507', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112660' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PE', N'Peru', N'Republic of Peru', N'PER', N'51', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112661' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PF', N'French Polynesia', N'French Polynesia', N'PYF', N'689', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112663' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PG', N'Papua New Guinea', N'Independent State of Papua New Guinea', N'PNG', N'675', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112664' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PH', N'Philippines', N'Philippines (the)', N'PHL', N'63', N'PHP', 1, 5, 1, 1, CAST(N'2021-06-09T13:04:15.0112666' AS DateTime2), 1, 1, CAST(13.5800 AS Decimal(18, 4)), 1, CAST(13.5800 AS Decimal(18, 4)), N'Cebuana Lhuillier', CAST(N'2021-11-08T15:17:47.5913007' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PK', N'Pakistan', N'Islamic Republic of Pakistan', N'PAK', N'92', N'PKR', 1, 2, 1, 1, CAST(N'2021-06-09T13:04:15.0112667' AS DateTime2), 1, 1, CAST(43.8330 AS Decimal(18, 4)), 1, CAST(43.8330 AS Decimal(18, 4)), N'Al Fallah Bank', CAST(N'2021-11-08T15:17:48.9120107' AS DateTime2))
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PL', N'Poland', N'Republic of Poland', N'POL', N'48', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112669' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PM', N'Saint Pierre and Miquelon', N'Saint Pierre and Miquelon', N'SPM', N'508', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112670' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PN', N'Pitcairn', N'Pitcairn', N'PCN', N'NONE', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PR', N'Puerto Rico', N'Commonwealth of Puerto Rico', N'PRI', N'1+939', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112672' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PT', N'Portugal', N'Portuguese Republic', N'PRT', N'351', N'EUR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112673' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PW', N'Palau', N'Republic of Palau', N'PLW', N'680', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112675' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'PY', N'Paraguay', N'Republic of Paraguay', N'PRY', N'595', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112676' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'QA', N'Qatar', N'State of Qatar', N'QAT', N'974', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112678' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'RE', N'Reunion', N'R&eacute;union', N'REU', N'262', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112679' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'RO', N'Romania', N'Romania', N'ROU', N'40', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112681' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'RS', N'Serbia', N'Republic of Serbia', N'SRB', N'381', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112682' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'RU', N'Russia', N'Russian Federation (the)', N'RUS', N'7', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112684' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'RW', N'Rwanda', N'Republic of Rwanda', N'RWA', N'250', N'RWF', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112685' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SA', N'Saudi Arabia', N'Kingdom of Saudi Arabia', N'SAU', N'966', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112687' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SB', N'Solomon Islands', N'Solomon Islands', N'SLB', N'677', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112689' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SC', N'Seychelles', N'Republic of Seychelles', N'SYC', N'248', N'SCR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112690' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SD', N'Sudan', N'Sudan (the)', N'SDN', N'249', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112692' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SE', N'Sweden', N'Kingdom of Sweden', N'SWE', N'46', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112693' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SG', N'Singapore', N'Republic of Singapore', N'SGP', N'65', N'SGD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112694' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SH', N'Saint Helena', N'Saint Helena, Ascension and Tristan da Cunha', N'SHN', N'290', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112696' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SI', N'Slovenia', N'Republic of Slovenia', N'SVN', N'386', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112697' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SJ', N'Svalbard and Jan Mayen', N'Svalbard and Jan Mayen', N'SJM', N'47', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SK', N'Slovakia', N'Slovak Republic', N'SVK', N'421', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112699' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SL', N'Sierra Leone', N'Republic of Sierra Leone', N'SLE', N'232', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112701' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SM', N'San Marino', N'Republic of San Marino', N'SMR', N'378', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112702' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SN', N'Senegal', N'Republic of Senegal', N'SEN', N'221', N'XOF', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112704' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SO', N'Somalia', N'Somali Republic', N'SOM', N'252', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112705' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SR', N'Suriname', N'Republic of Suriname', N'SUR', N'597', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112707' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SS', N'South Sudan', N'Republic of South Sudan', N'SSD', N'211', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112708' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ST', N'Sao Tome and Principe', N'Democratic Republic of S&atilde;o Tom&eacute; and Pr&iacute;ncipe', N'STP', N'239', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112709' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SV', N'El Salvador', N'Republic of El Salvador', N'SLV', N'503', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112711' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SX', N'Sint Maarten', N'Sint Maarten', N'SXM', N'1+721', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112713' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SY', N'Syria', N'Syrian Arab Republic', N'SYR', N'963', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112714' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'SZ', N'Swaziland', N'Kingdom of Swaziland', N'SWZ', N'268', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112716' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TC', N'Turks and Caicos Islands', N'Turks and Caicos Islands (the)', N'TCA', N'1+649', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112717' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TD', N'Chad', N'Republic of Chad', N'TCD', N'235', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112719' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TF', N'French Southern Territories', N'French Southern Territories (the)', N'ATF', N'262', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TG', N'To', N'Tolese Republic', N'T', N'228', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112720' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TH', N'Thailand', N'Kingdom of Thailand', N'THA', N'66', N'THB', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112722' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TJ', N'Tajikistan', N'Republic of Tajikistan', N'TJK', N'992', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112724' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TK', N'Tokelau', N'Tokelau', N'TKL', N'690', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112725' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TL', N'Timor Leste (East Timor)', N'Democratic Republic of Timor-Leste', N'TLS', N'670', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112727' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TM', N'Turkmenistan', N'Turkmenistan', N'TKM', N'993', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112728' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TN', N'Tunisia', N'Republic of Tunisia', N'TUN', N'216', N'TND', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112730' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TO', N'Tonga', N'Kingdom of Tonga', N'TON', N'676', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112731' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TR', N'Turkey', N'Republic of Turkey', N'TUR', N'90', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112733' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TT', N'Trinidad and Toba', N'Republic of Trinidad and Toba', N'TTO', N'1+868', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112735' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TV', N'Tuvalu', N'Tuvalu', N'TUV', N'688', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112736' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'TZ', N'Tanzania', N'United Republic of Tanzania', N'TZA', N'255', N'TZS', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112737' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'UA', N'Ukraine', N'Ukraine', N'UKR', N'380', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112739' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'UG', N'Uganda', N'Republic of Uganda', N'UGA', N'256', N'UGX', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112741' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'UM', N'United States Minor Outlying Islands', N'United States Minor Outlying Islands (the)', N'UMI', N'NONE', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'US', N'United States', N'United States of America (the)', N'USA', N'1', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112742' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'UY', N'Uruguay', N'Eastern Republic of Uruguay', N'URY', N'598', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112744' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'UZ', N'Uzbekistan', N'Republic of Uzbekistan', N'UZB', N'998', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112745' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VA', N'Vatican City', N'Holy See (the)', N'VAT', N'39', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VC', N'Saint Vincent and the Grenadines', N'Saint Vincent and the Grenadines', N'VCT', N'1+784', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112747' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VE', N'Venezuela', N'Bolivarian Republic of Venezuela', N'VEN', N'58', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112748' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VG', N'Virgin Islands, British', N'British Virgin Islands', N'VGB', N'1+284', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112750' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VI', N'Virgin Islands, US', N'Virgin Islands of the United States', N'VIR', N'1+340', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112751' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VN', N'Vietnam', N'Socialist Republic of Vietnam', N'VNM', N'84', N'VND', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112753' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'VU', N'Vanuatu', N'Republic of Vanuatu', N'VUT', N'678', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112754' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'WF', N'Wallis and Futuna', N'Wallis and Futuna', N'WLF', N'681', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112756' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'WS', N'Samoa', N'Independent State of Samoa', N'WSM', N'685', N'USD', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112758' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 1, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'YE', N'Yemen', N'Republic of Yemen', N'YEM', N'967', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112759' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'YT', N'Mayotte', N'Mayotte', N'MYT', N'262', N'', 0, 20, 0, 0, NULL, 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ZA', N'South Africa', N'Republic of South Africa', N'ZAF', N'27', N'ZAR', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112760' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ZM', N'Zambia', N'Republic of Zambia', N'ZMB', N'260', N'ZMW', 0, 20, 1, 1, CAST(N'2021-06-09T13:04:15.0112762' AS DateTime2), 1, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO
                        INSERT [dbo].[Countries] ([Code], [Name], [LongName], [Code3], [STDCode], [Currency], [IsPapularCountry], [DisplayOrder], [MobileRechargeEnabled], [MobileRechargeEnabledForPartner], [MobileRechargeLastSynchronizedDate], [MoneyTransferEnabled], [EligibleForBankTransfer], [BankTransferLatestRate], [EligibleForCashPickUp], [CashPickUpLatestRate], [CashPickUpProvider], [RatesLastUpdatedDate]) VALUES (N'ZW', N'Zimbabwe', N'Republic of Zimbabwe', N'ZWE', N'263', N'', 0, 20, 0, 1, CAST(N'2021-06-09T13:04:15.0112764' AS DateTime2), 0, 0, CAST(0.0000 AS Decimal(18, 4)), 0, CAST(0.0000 AS Decimal(18, 4)), NULL, NULL)
                        GO";
            migrationBuilder.Sql(sp);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BlackListedEntities");

            migrationBuilder.DropTable(
                name: "Documents");

            migrationBuilder.DropTable(
                name: "EmiratesIdUpdates");

            migrationBuilder.DropTable(
                name: "MobileRechargeBeneficiaryProviders");

            migrationBuilder.DropTable(
                name: "MobileRechargeExternalTransactions");

            migrationBuilder.DropTable(
                name: "MoneyTransferExternalBeneficiaries");

            migrationBuilder.DropTable(
                name: "MoneyTransferExternalTransactions");

            migrationBuilder.DropTable(
                name: "MoneyTransferLimits");

            migrationBuilder.DropTable(
                name: "MoneyTransferProfiles");

            migrationBuilder.DropTable(
                name: "Popups");

            migrationBuilder.DropTable(
                name: "ReferrerCodes");

            migrationBuilder.DropTable(
                name: "SecretAnswers");

            migrationBuilder.DropTable(
                name: "SubscriptionFeatures");

            migrationBuilder.DropTable(
                name: "UploadedDocuments");

            migrationBuilder.DropTable(
                name: "UserSubscriptions");

            migrationBuilder.DropTable(
                name: "VerificationComments");

            migrationBuilder.DropTable(
                name: "MobileRechargeTransactions");

            migrationBuilder.DropTable(
                name: "MoneyTransferTransactions");

            migrationBuilder.DropTable(
                name: "SecurityQuestions");

            migrationBuilder.DropTable(
                name: "Subscriptions");

            migrationBuilder.DropTable(
                name: "PortalUsers");

            migrationBuilder.DropTable(
                name: "MobileRechargeBeneficiaries");

            migrationBuilder.DropTable(
                name: "MobileRechargeProducts");

            migrationBuilder.DropTable(
                name: "MoneyTransferBeneficiaries");

            migrationBuilder.DropTable(
                name: "Transactions");

            migrationBuilder.DropTable(
                name: "MobileRechargeProviders");

            migrationBuilder.DropTable(
                name: "Countries");

            migrationBuilder.DropTable(
                name: "MoneyTransferReasons");

            migrationBuilder.DropTable(
                name: "Users");

            migrationBuilder.DropTable(
                name: "MobileRechargeSupportedCountries");

            migrationBuilder.DropTable(
                name: "CardHolders");
        }
    }
}
