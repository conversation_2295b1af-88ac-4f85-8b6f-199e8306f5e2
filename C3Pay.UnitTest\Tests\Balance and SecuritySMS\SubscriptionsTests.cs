﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Microsoft.EntityFrameworkCore.Storage;
using Moq;
using Xunit;

namespace C3Pay.Services.Tests
{
    // ---------------------------------------------------------
    // Minimal supporting classes and interfaces (STUBS)
    // ---------------------------------------------------------
    public static class ConstantParam
    {
        public const string UserAlreadySubscribed = "User already subscribed.";
        public const string UserNotSubscribed = "User not subscribed.";
    }

    public class ServiceResponse
    {
        public bool IsSuccessful { get; set; }
        public string Message { get; set; }

        public ServiceResponse() => IsSuccessful = true;

        public ServiceResponse(bool isSuccessful, string message = null)
        {
            IsSuccessful = isSuccessful;
            Message = message;
        }
    }

    public class ServiceResponse<T> : ServiceResponse
    {
        public T Data { get; set; }

        public ServiceResponse() : base() { }

        public ServiceResponse(T data)
        {
            Data = data;
            IsSuccessful = true;
        }

        public ServiceResponse(bool isSuccessful, string message = null)
            : base(isSuccessful, message) { }

        public ServiceResponse(bool isSuccessful, string message, T data)
            : base(isSuccessful, message)
        {
            Data = data;
        }
    }

    public class UserSubscription
    {
        public Guid UserId { get; set; }
        public Guid SubscriptionId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class OutboxMessage
    {
        public Guid Id { get; set; }
        public string Type { get; set; }
        public string Data { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    // Minimal repository interface with the necessary members.
    public interface IRepository<T>
    {
        IQueryable<T> AsQueryable();
        Task AddAsync(T entity);
        Task<T> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    }

    // Minimal unit-of-work interface.
    public interface IUnitOfWork
    {
        IRepository<UserSubscription> UserSubscriptions { get; }
        IRepository<OutboxMessage> OutboxMessages { get; }
        Task<int> CommitAsync();
    }

    // Minimal context stub.
    public class C3PayContext { }

    // Stub interfaces for additional dependencies.
    public interface ILookupService { }
    public interface IAuditTrailService { }

    // ---------------------------------------------------------
    // The SubscriptionService Implementation
    // ---------------------------------------------------------
    public class SubscriptionService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILookupService _lookupService;
        private readonly IAuditTrailService _auditTrailService;
        private readonly C3PayContext _context;

        public SubscriptionService(
            IUnitOfWork unitOfWork,
            ILookupService lookupService,
            IAuditTrailService auditTrailService,
            C3PayContext context)
        {
            _unitOfWork = unitOfWork;
            _lookupService = lookupService;
            _auditTrailService = auditTrailService;
            _context = context;
        }

        /// <summary>
        /// Checks if a user has an active subscription (EndDate == null).
        /// </summary>
        public async Task<ServiceResponse<bool>> UserHasActiveSubscription(
            Guid userId,
            Guid subscriptionId,
            CancellationToken cancellationToken = default)
        {
            bool isActive = await _unitOfWork.UserSubscriptions
                .AsQueryable()
                .AnyAsync(s =>
                    s.UserId == userId &&
                    s.SubscriptionId == subscriptionId &&
                    s.EndDate == null,
                    cancellationToken);
            return new ServiceResponse<bool>(isActive);
        }

        /// <summary>
        /// Subscribes a user if not already subscribed.
        /// </summary>
        public async Task<ServiceResponse> Subscribe(
            Guid userId,
            Guid subscriptionId,
            Guid? portalUserId = null,
            bool? addOutboxMessage = null)
        {
            var hasActiveSub = await UserHasActiveSubscription(userId, subscriptionId);
            if (hasActiveSub.Data)
            {
                return new ServiceResponse(false, ConstantParam.UserAlreadySubscribed);
            }

            var newSub = new UserSubscription
            {
                UserId = userId,
                SubscriptionId = subscriptionId,
                StartDate = DateTime.Now
            };

            await _unitOfWork.UserSubscriptions.AddAsync(newSub);
            await _unitOfWork.CommitAsync();

            // Optionally, add outbox message logic here

            return new ServiceResponse(true);
        }

        /// <summary>
        /// Unsubscribes a user if an active subscription is found.
        /// </summary>
        public async Task<ServiceResponse> Unsubscribe(
            Guid userId,
            Guid subscriptionId,
            Guid? portalUserId = null,
            bool? addOutboxMessage = null)
        {
            var sub = await _unitOfWork.UserSubscriptions.FirstOrDefaultAsync(
                s => s.UserId == userId &&
                     s.SubscriptionId == subscriptionId &&
                     s.EndDate == null);

            if (sub == null)
            {
                return new ServiceResponse(false, ConstantParam.UserNotSubscribed);
            }

            sub.EndDate = DateTime.Now;

            if (addOutboxMessage.HasValue && addOutboxMessage.Value)
            {
                var outboxMsg = new OutboxMessage
                {
                    Id = Guid.NewGuid(),
                    Type = "SomeEventType",
                    Data = "Some event data",
                    CreatedDate = DateTime.UtcNow
                };
                await _unitOfWork.OutboxMessages.AddAsync(outboxMsg);
            }

            await _unitOfWork.CommitAsync();
            return new ServiceResponse(true);
        }
    }

    // ---------------------------------------------------------
    // Helper Classes to support async queries in tests
    // ---------------------------------------------------------

    // Implementation of IAsyncQueryProvider that wraps synchronous execution in a Task.
    public class TestAsyncQueryProvider<TEntity> : IAsyncQueryProvider
    {
        private readonly IQueryProvider _inner;
        public TestAsyncQueryProvider(IQueryProvider inner)
        {
            _inner = inner;
        }

        public IQueryable CreateQuery(Expression expression)
        {
            return new TestAsyncEnumerable<TEntity>(expression);
        }

        public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
        {
            return new TestAsyncEnumerable<TElement>(expression);
        }

        public object Execute(Expression expression)
        {
            return _inner.Execute(expression);
        }

        public TResult Execute<TResult>(Expression expression)
        {
            return _inner.Execute<TResult>(expression);
        }

        public IAsyncEnumerable<TResult> ExecuteAsync<TResult>(Expression expression)
        {
            return new TestAsyncEnumerable<TResult>(expression);
        }

        public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken)
        {
            // This method should return a Task<T>, so we wrap the synchronous result.
            var resultType = typeof(TResult).GetGenericArguments()[0];
            var executionResult = Execute(expression);
            var taskFromResultMethod = typeof(Task).GetMethod(nameof(Task.FromResult))
                .MakeGenericMethod(resultType);
            return (TResult)taskFromResultMethod.Invoke(null, new[] { executionResult });
        }
    }

    // Enumerable that implements IAsyncEnumerable.
    public class TestAsyncEnumerable<T> : EnumerableQuery<T>, IAsyncEnumerable<T>, IQueryable<T>
    {
        public TestAsyncEnumerable(IEnumerable<T> enumerable)
            : base(enumerable)
        { }
        public TestAsyncEnumerable(Expression expression)
            : base(expression)
        { }

        public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
        {
            return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
        }

        IQueryProvider IQueryable.Provider => new TestAsyncQueryProvider<T>(this);
    }

    public class TestAsyncEnumerator<T> : IAsyncEnumerator<T>
    {
        private readonly IEnumerator<T> _inner;
        public TestAsyncEnumerator(IEnumerator<T> inner)
        {
            _inner = inner;
        }

        public T Current => _inner.Current;

        public ValueTask DisposeAsync()
        {
            _inner.Dispose();
            return default;
        }

        public ValueTask<bool> MoveNextAsync()
        {
            return new ValueTask<bool>(_inner.MoveNext());
        }
    }

    // ---------------------------------------------------------
    // The xUnit Test Class using Moq
    // ---------------------------------------------------------
    public class SubscriptionServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<ILookupService> _mockLookupService;
        private readonly Mock<IAuditTrailService> _mockAuditTrailService;
        private readonly Mock<C3PayContext> _mockContext;
        private readonly SubscriptionService _subscriptionService;

        public SubscriptionServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLookupService = new Mock<ILookupService>();
            _mockAuditTrailService = new Mock<IAuditTrailService>();
            _mockContext = new Mock<C3PayContext>();

            // No DatabaseFacade mocking is needed.
            _subscriptionService = new SubscriptionService(
                _mockUnitOfWork.Object,
                _mockLookupService.Object,
                _mockAuditTrailService.Object,
                _mockContext.Object);
        }

        #region UserHasActiveSubscription Tests

        [Fact]
        public async Task UserHasActiveSubscription_ReturnsTrue_WhenActiveSubscriptionExists()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var subscriptionsList = new List<UserSubscription>
            {
                new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = subscriptionId,
                    StartDate = DateTime.Now.AddDays(-1),
                    EndDate = null
                }
            };

            // Wrap the list in a TestAsyncEnumerable to support async operations.
            var asyncData = new TestAsyncEnumerable<UserSubscription>(subscriptionsList);
            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(r => r.AsQueryable()).Returns(asyncData);
            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);

            // Act
            var result = await _subscriptionService.UserHasActiveSubscription(userId, subscriptionId);

            // Assert
            Assert.True(result.Data);
        }

        [Fact]
        public async Task UserHasActiveSubscription_ReturnsFalse_WhenNoActiveSubscriptionExists()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var subscriptionsList = new List<UserSubscription>(); // empty list
            var asyncData = new TestAsyncEnumerable<UserSubscription>(subscriptionsList);
            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(r => r.AsQueryable()).Returns(asyncData);
            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);

            // Act
            var result = await _subscriptionService.UserHasActiveSubscription(userId, subscriptionId);

            // Assert
            Assert.False(result.Data);
        }

        #endregion

        #region Subscribe Tests

        [Fact]
        public async Task Subscribe_ReturnsError_WhenUserAlreadySubscribed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var subscriptionsList = new List<UserSubscription>
            {
                new UserSubscription
                {
                    UserId = userId,
                    SubscriptionId = subscriptionId,
                    StartDate = DateTime.Now.AddDays(-1),
                    EndDate = null
                }
            };
            var asyncData = new TestAsyncEnumerable<UserSubscription>(subscriptionsList);
            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(r => r.AsQueryable()).Returns(asyncData);
            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);

            // Act
            var response = await _subscriptionService.Subscribe(userId, subscriptionId);

            // Assert
            Assert.False(response.IsSuccessful);
            Assert.Equal(ConstantParam.UserAlreadySubscribed, response.Message);
        }

        [Fact]
        public async Task Subscribe_AddsNewSubscription_WhenUserNotSubscribed()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var subscriptionsList = new List<UserSubscription>(); // empty list
            var asyncData = new TestAsyncEnumerable<UserSubscription>(subscriptionsList);
            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(r => r.AsQueryable()).Returns(asyncData);

            // Expect an AddAsync call.
            mockUserSubscriptionsRepo.Setup(repo => repo.AddAsync(It.IsAny<UserSubscription>()))
                .Returns(Task.CompletedTask)
                .Verifiable();

            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);
            _mockUnitOfWork.Setup(u => u.CommitAsync()).ReturnsAsync(1).Verifiable();

            // Act
            var response = await _subscriptionService.Subscribe(userId, subscriptionId, null, false);

            // Assert
            Assert.True(response.IsSuccessful);
            mockUserSubscriptionsRepo.Verify(repo => repo.AddAsync(
                It.Is<UserSubscription>(us => us.UserId == userId && us.SubscriptionId == subscriptionId)),
                Times.Once);
            _mockUnitOfWork.Verify(u => u.CommitAsync(), Times.AtLeastOnce);
        }

        #endregion

        #region Unsubscribe Tests

        [Fact]
        public async Task Unsubscribe_ReturnsError_WhenNoActiveSubscriptionFound()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();

            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(repo => repo.FirstOrDefaultAsync(
                    It.IsAny<Expression<Func<UserSubscription, bool>>>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync((UserSubscription)null);
            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);

            // Act
            var response = await _subscriptionService.Unsubscribe(userId, subscriptionId, null, false);

            // Assert
            Assert.False(response.IsSuccessful);
            Assert.Equal(ConstantParam.UserNotSubscribed, response.Message);
        }

        [Fact]
        public async Task Unsubscribe_UpdatesSubscriptionAndAddsOutboxMessage_WhenActiveSubscriptionExists()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var subscriptionId = Guid.NewGuid();
            var activeSubscription = new UserSubscription
            {
                UserId = userId,
                SubscriptionId = subscriptionId,
                StartDate = DateTime.Now.AddDays(-5),
                EndDate = null
            };

            var mockUserSubscriptionsRepo = new Mock<IRepository<UserSubscription>>();
            mockUserSubscriptionsRepo.Setup(repo => repo.FirstOrDefaultAsync(
                    It.IsAny<Expression<Func<UserSubscription, bool>>>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(activeSubscription);

            var mockOutboxRepo = new Mock<IRepository<OutboxMessage>>();
            mockOutboxRepo.Setup(repo => repo.AddAsync(It.IsAny<OutboxMessage>()))
                .Returns(Task.CompletedTask)
                .Verifiable();

            _mockUnitOfWork.Setup(u => u.UserSubscriptions).Returns(mockUserSubscriptionsRepo.Object);
            _mockUnitOfWork.Setup(u => u.OutboxMessages).Returns(mockOutboxRepo.Object);
            _mockUnitOfWork.Setup(u => u.CommitAsync()).ReturnsAsync(1).Verifiable();

            // Act
            var response = await _subscriptionService.Unsubscribe(userId, subscriptionId, null, true);

            // Assert
            Assert.True(response.IsSuccessful);
            Assert.NotNull(activeSubscription.EndDate);
            mockOutboxRepo.Verify(repo => repo.AddAsync(It.IsAny<OutboxMessage>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.CommitAsync(), Times.Once);
        }

        #endregion
    }
}
