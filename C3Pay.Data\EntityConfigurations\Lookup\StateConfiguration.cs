﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.EntityConfigurations.Lookup
{
    public class StateConfiguration : IEntityTypeConfiguration<State>
    {
        public void Configure(EntityTypeBuilder<State> builder)
        {
            builder.ToTable("States");

            builder.<PERSON><PERSON>ey(c => c.Id);

            builder.Property(c => c.Id).ValueGeneratedOnAdd();

            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(150);

            builder.Property(c => c.CountryCode)
             .HasMaxLength(2);

            builder
               .HasMany(c => c.Cities)
               .WithOne(u => u.State)
               .HasForeignKey(u => u.StateId)
               .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
