﻿using System;
using System.Collections.Generic;
using System.Text;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.Structs
{
    public struct MoneyTransferTransactionStruct
    {
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreatedDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public MoneyTransferType TransferType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Country { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Status Status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PhoneNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FullName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CardHolderName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal Sent { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? Fee { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string C3REF { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string RAKREF { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public WaiveType WaiveType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BeneficiaryPhoneNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReferralCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Remarks { get; set; }
    }
}
