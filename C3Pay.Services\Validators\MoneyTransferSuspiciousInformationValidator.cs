﻿using C3Pay.Core.Models;
using FluentValidation;

namespace C3Pay.Services.Validators
{
    public class MoneyTransferSuspiciousInformationValidator : AbstractValidator<MoneyTransferSuspiciousInformation>
    {
        public MoneyTransferSuspiciousInformationValidator()
        {
            RuleFor(x => x.MoneyTransferBeneficiaryId).NotNull();
            RuleFor(x => x.NationalIdNo).NotNull();
            RuleFor(x => x.FullName).NotNull();
            RuleFor(x => x.DateOfBirth).NotNull();

            RuleFor(x => x.CityId).GreaterThan(0);
            RuleFor(x => x.FullName).Length(1, 100);
        }
    }
}
