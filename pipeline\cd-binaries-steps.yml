﻿parameters:
  - name: azureSubscription
    type: string
  - name: rgName
    type: string
  - name: webAppName
    type: string 
  - name: portalWebAppName
    type: string 
  - name: webJobName
    type: string 
  - name: sqlName
    type: string  
  - name: dbName
    type: string 
  - name: identityDbName
    type: string
  - name: aiName
    type: string 
  - name: keyVaultName
    type: string 
  - name: serviceBusName
    type: string 
  - name: storageName
    type: string
  - name: storageWebJobName
    type: string 
  - name: webAppBinariesFile    
    type: string
  - name: portalWebAppBinariesFile    
    type: string
  - name: webJobBinariesFile    
    type: string
  - name: sqlFile
    type: string
  - name: identitySqlFile
    type: string
  - name: sqlsrvAdministratorLogin
    type: string
  - name: sqlsrvAdministratorPassword
    type: string
  - name: rakCertificateName
    type: string
  - name: rakCertificatePassword
    type: string  
  - name: rakPrivateKeyName
    type: string
  - name: rakPublicKeyName
    type: string
  - name: SettingEDCAuthority
    type: string    
  - name: SettingEDCApiName
    type: string
  - name: SettingEDCApiSecret
    type: string  
  - name: SettingPortalEDCAuthority
    type: string    
  - name: SettingPortalEDCApiName
    type: string
  - name: SettingPortalEDCApiSecret
    type: string  
  - name: SettingAADAuthority
    type: string    
  - name: SettingAADAudience
    type: string
  - name: SettingAADAllowedClientIds
    type: string  
  - name: SettingKeyName
    type: string  
  - name: SettingSignzyURL
    type: string  
  - name: SettingSignzyFileExchangeAddress
    type: string  
  - name: SettingSignzyId
    type: string  
  - name: SettingSignzyUserId
    type: string 
  - name: SettingSignzyImageQualityTimeout
    type: string
  - name: SettingSignzyFaceMatchTimeout
    type: string
  - name: SettingSignzyReadDocumentTimeout
    type: string
  - name: SettingAzureOCREndpoint
    type: string
  - name: SettingAzureOCRKey
    type: string
  - name: SettingAzureOCRWaitTimeInMilliSeconds
    type: string
  - name: SettingAzureFaceEndpoint
    type: string
  - name: SettingAzureFaceKey
    type: string
  - name: SettingAzureIdentificationServiceUseAzureOCR
    type: string
  - name: SettingAzureIdentificationServiceUseAzureFace
    type: string
  - name: SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck
    type: string
  - name: SettingKycExpiryCheckStartDate
    type: string
  - name: SettingKycExpiryValidateEmiratesIdExpiryScheduler
    type: string
  - name: SettingKycExpiryValidateAdditionKYCScheduler
    type: string
  - name: SettingTransactionsB2CServiceAuthority
    type: string
  - name: SettingTransactionsB2CServiceClientId
    type: string
  - name: SettingTransactionsB2CServiceScope
    type: string
  - name: SettingTransactionsB2CServiceClientSecret
    type: string
  - name: SettingTransactionsB2CServiceBaseAddress
    type: string
  - name: SettingTransactionsB2CServiceGrantType
    type: string
  - name: SettingTransactionsB2CServiceAPIVersion
    type: string
  - name: SettingTransactionsB2CServiceTimeout
    type: string
  - name: SettingRakBaseURL
    type: string  
  - name: SettingRakClientId
    type: string  
  - name: SettingRakClientSecret
    type: string
  - name: SettingRakBanksMaxRecords
    type: string
  - name: SettingRakMoneyTransferBeneficiaryCount
    type: string
  - name: SettingRakProcessBankTransactionsReportsSchedule
    type: string
  - name: SettingRakUpdatePendingBankTransactionsSchedule
    type: string
  - name: SettingRakReverseFailedBankTransactionsSchedule
    type: string
  - name: SettingRakProcessRakStatementSchedule
    type: string
  - name: SettingRakProcessRmtSubmissionSchedule
    type: string
  - name: SettingRakProcessRmtAcknowledgementSchedule
    type: string
  - name: SettingUploadMissingProfilesImagesSchedule
    type: string
  - name: SettingRakMaxTransactionTriesCount
    type: string
  - name: SettingRakMessageProcessInDelay
    type: string
  - name: SettingRakTransactionUpdateDelay
    type: string
  - name: SettingRakMoneyTransferBeneficiaryDelayInMins
    type: string
  - name: SettingRakRmtProfilePositiveStatus
    type: string
  - name: SettingRakLoyaltyImplementDate
    type: string
  - name: SettingRakLoyaltyLimitCount
    type: string
  - name: SettingRakLoyaltyLimitAmount
    type: string
  - name: SettingRakURLPath
    type: string
  - name: SettingRakSftpEndPoint
    type: string
  - name: SettingRakSftpPort
    type: string
  - name: SettingRakSftpUsername
    type: string
  - name: SettingRakSftpPassword
    type: string
  - name: SettingRAKSFTPInputRootDirectory
    type: string
  - name: SettingRAKSFTPOutputRootDirectory
    type: string
  - name: SettingRAKSFTPTransactionStatusDirectory
    type: string
  - name: SettingRAKSFTPTransactionBlobContainerName
    type: string
  - name: SettingRAKSFTPProfileStatusDirectory
    type: string
  - name: SettingRAKSFTPBranchesDirectory
    type: string
  - name: SettingRAKSFTPBranchesArchiveDirectory
    type: string
  - name: SettingRakSftpReportDirectory
    type: string
  - name: SettingRakSftpRmtAcknowledgementDirectory
    type: string
  - name: SettingRakSftpRmtTempSubmissionDirectory
    type: string
  - name: SettingRakRefreshRatesSchedule
    type: string
  - name: SettingRakRefreshRatesEmiratesId
    type: string
  - name: SettingRakEnableRakTokenCache
    type: string
  - name: SettingPPSWebAuthBaseURL
    type: string
  - name: SettingPPSWebAuthClientId
    type: string
  - name: SettingPPSWebAuthClientSecretkey
    type: string
  - name: SettingPPSEndpointAddress
    type: string
  - name: SettingPPSUsername
    type: string
  - name: SettingPPSPassword
    type: string
  - name: SettingPPSSponsorCode
    type: string
  - name: SettingPPSCustomerCode
    type: string
  - name: SettingPPSSharedSecret
    type: string 
  - name: SettingPPSTimeout
    type: string 
  - name: SettingEtisalatSMSUsername
    type: string
  - name: SettingEtisalatSMSPassword
    type: string
  - name: SettingEtisalatSMSBaseAddress
    type: string
  - name: SettingEtisalatSMSTimeout
    type: string
  - name: SettingEtisalatSMSRetryCount
    type: string
  - name: SettingInfobipSMSUsername
    type: string
  - name: SettingInfobipSMSPassword
    type: string
  - name: SettingInfobipSMSBaseAddress
    type: string
  - name: SettingInfobipSMSAuthKey
    type: string
  - name: SettingInfobipSMSAuthKeyBaseUrl
    type: string
  - name: SettingInfobipSMSSmsMode
    type: string
  - name: SettingInfobipSMSTimeout
    type: string
  - name: SettingInfobipSMSRetryCount
    type: string
  - name: SettingDingBaseURL
    type: string
  - name: SettingDingClientApiKey
    type: string
  - name: SettingSecondaryDingClientApiKey
    type: string
  - name: SettingMobileRechargeSynchronizeWithDingSchedule
    type: string
  - name: SettingMobileRechargeUpdateStatusSchedule
    type: string
  - name: SettingMobileRechargeNickNameLength
    type: string
  - name: SettingMobileRechargeCallingCardAccountNumberLive
    type: string
  - name: SettingMobileRechargeTransactionEnvironment
    type: string
  - name: SettingMobileRechargeC3FeeMode
    type: string
  - name: SettingMobileRechargeMySalaryFeeMode
    type: string
  - name: SettingMobileRechargeSelectedCorporatesWithFee
    type: string
  - name: SettingMobileRechargeFeeAmount
    type: string
  - name: SettingMobileRechargeNonVerifiedLimit
    type: string
  - name: SettingMobileRechargeVerifiedLimit
    type: string
  - name: SettingMobileRechargeCustomCallingCardName
    type: string
  - name: SettingMobileRechargeCustomCallingCardCode
    type: string
  - name: SettingMobileRechargeCustomCallingCardLogoUrl
    type: string
  - name: SettingMobileRechargeCustomCallingCardValidationRegex
    type: string
  - name: SettingMobileRechargeDynamicPackageSeperator
    type: string
  - name: SettingMobileRechargeServiceServiceBusTopicName
    type: string
  - name: SettingMobileRechargeServiceServiceBusSubscriptionName
    type: string
  - name: SettingMobileRechargeServiceRenewalSchedule
    type: string
  - name: SettingMobileRechargeServiceLowBalanceRenewalSchedule
    type: string
  - name: SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays
    type: string
  - name: SettingVpnMembershipRenewalSchedule
    type: string
  - name: SettingMoneyTransferMultimediaURL
    type: string 
  - name: SettingMoneyTransferRefreshBanksAndBranchesSchedule
    type: string
  - name: SettingMoneyTransferReversalStartDate
    type: string
  - name: SettingMoneyTransferReversalMode
    type: string
  - name: SettingMoneyTransferComparisonReceiveAmount
    type: string
  - name: SettingMoneyTransferComparisonEHTransferFee
    type: string
  - name: SettingMoneyTransferComparisonEHRateIncrement
    type: string
  - name: SettingMoneyTransferGWNationalities
    type: string
  - name: SettingMoneyTransferGWLanguages
    type: string
  - name: SettingMoneyTransferGWStartDate
    type: string
  - name: SettingMoneyTransferGeneralDefaultAmount
    type: string
  - name: SettingMoneyTransferGeneralDefaultCurrency
    type: string
  - name: SettingMoneyTransferGeneralDefaultType
    type: string
  - name: SettingMoneyTransferMaxRepeatTransferCount
    type: string
  - name: SettingMoneyTransferMinUserBalanceForRepeatTransfer
    type: string
  - name: SettingMoneyTransferReverseOnHoldSchedule
    type: string
  - name: SettingMoneyTransferReverseOnHoldMinNoOfDays
    type: string
  - name: SettingMoneyTransferRateExpiryInMinutes
    type: string
  - name: SettingMoneyTransferRmtCreationSchedule
    type: string
  - name: SettingMoneyTransferPendingSchedulerMinNoOfDays
    type: string
  - name: SettingMoneyTransferCheckMinSuspiciousDate
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountIND
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountPHL
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountNPL
    type: string
  - name: SettingEdenredIdentityManagerBaseAddress
    type: string
  - name: SettingEdenredIdentityManagerAuthority
    type: string
  - name: SettingEdenredIdentityManagerResourceId
    type: string
  - name: SettingEdenredIdentityManagerClientId
    type: string
  - name: SettingEdenredIdentityManagerClientSecret
    type: string
  - name: SettingFirebaseCloudMessagingBaseAddress
    type: string
  - name: SettingFirebaseCloudMessagingKey
    type: string
  - name: SettingFirebaseCloudMessagingSenderId
    type: string
  - name: SettingFirebaseCloudMessagingRetryCount
    type: string
  - name: SettingFirebaseCloudMessagingTimeout
    type: string
  - name: SettingESMOServiceBaseAddress
    type: string
  - name: SettingESMOServiceClientId
    type: string
  - name: SettingESMOServiceClientSecret
    type: string
  - name: SettingESMOServiceAuthority
    type: string
  - name: SettingESMOServiceScope
    type: string
  - name: SettingESMOServiceTimeout
    type: string
  - name: SettingPaykiiServiceBaseAddress
    type: string
  - name: SettingPaykiiServiceAPIKey
    type: string 
  - name: SettingPaykiiServiceToken
    type: string 
  - name: SettingPaykiiServiceCashierId
    type: string
  - name: SettingPaykiiServiceCustomerId
    type: string
  - name: SettingPaykiiServiceBillerCatalogUrl
    type: string
  - name: SettingPaykiiServiceSKUCatalogUrl
    type: string
  - name: SettingPaykiiServiceIOCatalogUrl
    type: string
  - name: SettingPaykiiServiceAmountDueUrl
    type: string
  - name: SettingPaykiiServiceProcessPaymentUrl
    type: string
  - name: SettingPaykiiServiceVerifyPaymentStatusUrl
    type: string
  - name: SettingPaykiiServiceBalanceUrl
    type: string
  - name: SettingPaykiiServiceBillNotificationUrl
    type: string
  - name: SettingPaykiiServiceMobileCarrierLookupUrl
    type: string
  - name: SettingPaykiiServiceDailyFXRatePerBillerTypeUrl
    type: string
  - name: SettingPaykiiServiceBillerFeesCatalogUrl
    type: string 
  - name: SettingPaykiiServiceRefreshDataIntervalCronExpression
    type: string
  - name: SettingPaykiiServiceLocationId
    type: string
  - name: SettingPaykiiServicePointOfSaleId
    type: string
  - name: SettingBillPaymentPendingBillsRefreshIntervalCronExpression
    type: string
  - name: SettingBillPaymentIconBaseUrl
    type: string
  - name: SettingBillPaymentTransactionEnvironment
    type: string
  - name: SettingBillPaymentFxRateRefreshIntervalCronExpression
    type: string
  - name: SettingBillPaymentGridViewDisplayProviderIds
    type: string
  - name: SettingBillPaymentMaximumLocalBillersLimit
    type: string
  - name: SettingBillPaymentMaximumInternationalBillersLimit
    type: string
  - name: SettingBillPaymentMaximumAllowedBillAmountPerTransaction
    type: string
  - name: SettingBillPaymentMaximumAllowedBillAmountPerMonth
    type: string
  - name: SettingBillPaymentNolProviderCode
    type: string
  - name: SettingBillPaymentAllowedNolAmountForNotVerified
    type: string
  - name: SettingBillPaymentNolRechargeMonthlyLimitForVerified
    type: string
  - name: SettingBillPaymentNolRechargeMonthlyLimitForNotVerified
    type: string 
  - name: SettingBillPaymentIconContainerName
    type: string
  - name: SettingBillPaymentSalikProviderCode
    type: string 
  - name: SettingBillPaymentMockUserId
    type: string 
  - name: SettingBillPaymentAddBillerQueueName
    type: string
  - name: SettingBillPaymentProcessPaymentQueueName
    type: string
  - name: SettingBillPaymentMockAddBillerQueueName
    type: string
  - name: SettingBillPaymentMockProcessPaymentQueueName
    type: string
  - name: SettingBillPaymentAmountDueExpireIntervalCronExpression
    type: string
  - name: SettingBillPaymentAmountDueExpiryHours
    type: string
  - name: SettingGeneralEmiratesIdStorageURL
    type: string  
  - name: SettingGeneralQAUserPhoneNumbers
    type: string 
  - name: SettingGeneralQAAutomationPhoneNumbers
    type: string
  - name: SettingGeneralPrimarySmsProvider
    type: string 
  - name: SettingGeneralIsMiddleNavExperimentActive
    type: string 
  - name: SettingGeneralIsSecuritySMSAwarenessActive
    type: string
  - name: SettingGeneralIsSecuritySMSMigrationActive
    type: string 
  - name: SettingGeneralAuthenticationTokenSecretKey
    type: string 
  - name: SettingGeneralUATPentestPhoneNumbers
    type: string
  - name: SettingGeneralMaxDevicesAllowedForBinding
    type: string 
  - name: SettingGeneralDeviceIdBindingPrefix
    type: string
  - name: SettingKYCBaseAddress
    type: string
  - name: SettingKYCUsername
    type: string
  - name: SettingKYCPassword
    type: string
  - name: SettingKYCUniqueRef
    type: string
  - name: SettingKYCSponsorCode
    type: string
  - name: SettingKYCSharedSecret
    type: string
  - name: SettingMobileAppHashKey
    type: string
  - name: SettingSendGridSenderEmail
    type: string
  - name: SettingSendGridAPIKey
    type: string
  - name: SettingSendGridCardHolderRegistrationRejectedEmailTemplateId
    type: string
  - name: SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId
    type: string
  - name: SettingSendGridStoreOrderPlacedEmailTemplateId
    type: string
  - name: SettingSendGridBankStatementEmailTemplateId
    type: string
  - name: SettingSendGridPortalUserCreatedEmailTemplateId
    type: string
  - name: SettingPortalAdminPasswordResetEmailTemplateId
    type: string
  - name: SettingRedisConnection
    type: string
  - name: SettingServiceBusConnection
    type: string
  - name: SettingRakReadRMTProfileResponsesSchedule
    type: string
  - name: SettingRakSftpRMTProfileResponsesDirectory
    type: string
  - name: SettingRakSftpMissingRakFileAlertPhoneNumbers
    type: string
  - name: SettingFirstBlackV1PlasticCardId
    type: string
  - name: SettingFirstBlackV2PlasticCardId
    type: string
  - name: SettingCleverTapBaseAddress
    type: string
  - name: SettingCleverTapProjectId
    type: string
  - name: SettingCleverTapPassCode
    type: string
  - name: SettingExchangeHouseBaseAddress
    type: string
  - name: SettingExchangeHouseUsername
    type: string
  - name: SettingExchangeHousePassword
    type: string
  - name: SettingExchangeHouseMaxAllowedBeneficiaryCount
    type: string
  - name: SettingExchangeHouseMoneyTransferQueueConnectionString
    type: string
  - name: SettingExchangeHouseMoneyTransferQueueName
    type: string
  - name: SettingReferralProgramMoneyTransferCountThreshold
    type: string
  - name: SettingReferralProgramMoneyTransferAmountThreshold
    type: string
  - name: SettingReferralProgramMoneyTransferRewardAmount
    type: string
  - name: SettingReferralProgramMoneyTransferReferralProgramStartDate
    type: string
  - name: SettingSwaggerUsername
    type: string
  - name: SettingSwaggerPassword
    type: string
  - name: SettingEnableSwagger
    type: string
  - name: SettingPayrollServiceBaseAddress
    type: string
  - name: SettingPayrollServiceAuthority
    type: string
  - name: SettingPayrollServiceClientId
    type: string
  - name: SettingPayrollServiceClientSecret
    type: string
  - name: SettingPayrollServiceScope
    type: string
  - name: SettingHRServiceBaseAddress
    type: string
  - name: SettingHRServiceAuthority
    type: string
  - name: SettingHRServiceClientId
    type: string
  - name: SettingHRServiceClientSecret
    type: string
  - name: SettingHRServiceScope
    type: string
  - name: SettingHRServiceCacheInMinutes
    type: string
  - name: SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds
    type: string 

    # Step 9: Add your keys here like this: "Setting" + <setting name (same as property)> with the type of the key.
  - name: SettingDirectTransferMaxBeneficiariesCount
    type: string
  - name: SettingDirectTransferMinAmountToSend
    type: string
  - name: SettingDirectTransferMaxAmountToSend
    type: string
  - name: SettingDirectTransferMaxAmountToSendPerMonth
    type: string
  - name: SettingDirectTransferFee
    type: string
  - name: SettingDirectTransferVAT
    type: string
  - name: SettingClaimPendingDirectTransfersQueueConnectionString
    type: string
  - name: SettingClaimPendingDirectTransfersQueueName
    type: string
  - name: SettingReversePendingDirectMoneyTransfersSchedule
    type: string
  - name: SettingReversePendingDirectMoneyTransfersDurationInMin
    type: string
  - name: SettingReverseFailedDirectMoneyTransfersSchedule
    type: string

    ###########################################################################################################
    #Mock Keys
  - name: SettingMoneyTransferEnableRakMock
    type: string
  - name: SettingMoneyTransferEnableRakNegativeScenarioMock
    type: string

    ###########################################################################################################

  - name: SettingExchangeHouseUpdateStatusSchedule
    type: string
  - name: SettingExchangeHouseRefreshRatesSchedule
    type: string

  - name: SettingRatingMinimumDaysToShowInApp
    type: string
  - name: SettingRatingMinimumDaysToShowStore
    type: string
  - name: SettingMoneyTransferMonthlyAmountLimit
    type: string
  - name: SettingMoneyTransferMonthlyCountLimit
    type: string
  - name: SettingGeneralTestKey
    type: string
  - name: SettingGeneralEnableRedis
    type: string
  - name: SettingStoreEmailRecepients
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusTopicName
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusSubscriptionName
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusUserTopicName
    type: string

  - name: SettingBalanceEnquirySubscriptionServiceBusTopicName
    type: string
  - name: SettingBalanceEnquirySubscriptionServiceBusSubscriptionName
    type: string
  - name: SettingAuditTrailServiceBusQueueName
    type: string
  - name: SettingMoneyTransferFreeTransferExpiryScheduler
    type: string
  - name: SettingMoneyTransferRetryBeneficiarySchedule
    type: string
  - name: SettingMoneyTransferMaxBeneficiaryRetryLimit
    type: string
  - name: SettingMoneyTransferRetryBeneficiaryDurationInMin
    type: string
  - name: SettingDingServiceRetryCount
    type: string
  - name: SettingDingServiceSleepDuration
    type: string
  - name: SettingDingServiceIsRetryEnabled
    type: string
  - name: SettingTestingMRDynamicPackageTestNepalNumbers
    type: string
  - name: SettingTestingMRInlineFeeCalculationTestNumbers
    type: string   
  - name: SettingRakBankMoneyTransferBaseUrl
    type: string
  - name: SettingRakBankMoneyTransferUrlPath
    type: string
  - name: SettingRakBankMoneyTransferClientId
    type: string
  - name: SettingRakBankMoneyTransferClientSecretkey
    type: string
  - name: SettingRakBankMoneyTransferSslCertificateName
    type: string
  - name: SettingRakBankMoneyTransferSslCertificatePassword
    type: string
  - name: SettingRakBankMoneyTransferPayloadPrivateKey
    type: string
  - name: SettingRakBankMoneyTransferPayloadPublicKey
    type: string
  - name: SettingRakBankMoneyTransferTokenGrantType
    type: string
  - name: SettingRakBankMoneyTransferTokenScope
    type: string
  - name: SettingRakBankMoneyTransferContentType
    type: string
  - name: SettingRakBankMoneyTransferX509Certificate2Bytes
    type: string

    ###########----Encryption Settings----##########################################################################
  - name: SettingEncryptionSettingsIsActive
    type: string
  - name: SettingEncryptionSettingsPrivateKey
    type: string
  - name: SettingEncryptionSettingsPublicKey
    type: string

  - name: SettingMoneyTransferCorridorsCorporateId
    type: string
  - name: SettingGeneralIsDbSaveRetryEnabled
    type: string 
  - name: SettingRenewalCardUpdateServiceBusTopicName
    type: string
  - name: SettingRenewalCardUpdateServiceBusSubscriptionName
    type: string
  - name: SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled
    type: string
  - name: SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays
    type: string 
  - name: SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays
    type: string
  - name: SettingMoneyTransferServiceNonWUCorridors
    type: string
  - name: SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled
    type: string
  - name: SettingMoneyTransferServiceLastRaffleWinnerName
    type: string
  - name: SettingMoneyTransferServiceLastRaffleWinnerTicketNumber
    type: string
  - name: SettingMoneyTransferServiceRaffleDateString
    type: string
  - name: SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule
    type: string 
  - name: SettingC3PayPlusMembershipLuckyDrawSchedule
    type: string
  - name: SettingC3PayPlusMembershipLuckyDrawWinnersCount
    type: string
  - name: SettingC3PayPlusMembershipCdnUrl
    type: string
  - name: SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule
    type: string
  - name: SettingC3PayPlusMembershipGenerateTicketsMaxCount
    type: string
  - name: SettingFirebaseNotificationAuthEndpoint
    type: string
  - name: SettingFirebaseNotificationBaseUrl
    type: string
  - name: SettingFirebaseNotificationSendMethodUrl
    type: string
  - name: SettingGoogleAuthType
    type: string
  - name: SettingGoogleAuthProjectId
    type: string
  - name: SettingGoogleAuthPrivateKeyId
    type: string
  - name: SettingGoogleAuthPrivateKey
    type: string
  - name: SettingGoogleAuthClientEmail
    type: string
  - name: SettingGoogleAuthClientId
    type: string
  - name: SettingGoogleAuthAuthUri
    type: string
  - name: SettingGoogleAuthTokenUri
    type: string
  - name: SettingGoogleAuthAuthProviderX509CertUrl
    type: string
  - name: SettingGoogleAuthClientX509CertUrl
    type: string
  - name: SettingGoogleAuthUniverseDomain
    type: string
  - name: SettingKycBlockExclusionsShouldBeDeletedAfter
    type: string
  - name: SettingKycBlockExclusionsScheduleTime
    type: string
  - name: SettingC3PayPlusMembershipOverrideLuckyDrawDate
    type: string
  - name: SettingC3PayPlusMembershipOverrideLuckyDrawTime
    type: string
  - name: SettingC3PayPlusMembershipRenewalSchedule
    type: string
  - name: SettingC3PayPlusMembershipConfirmFirstDebitSchedule
    type: string
  - name: SettingSanctionScreeningApiAddress
    type: string
  - name: SettingRewardServiceBaseAddress
    type: string
  - name: SettingRewardServiceResendScheduleTime
    type: string
  - name: SettingRewardServiceRetryCount
    type: string
  - name: SettingRewardServiceTimeout
    type: string
  - name: SettingRewardServiceTestAccountUsernames
    type: string
  - name: SettingInfobipVoiceCallSettingsBaseUrl
    type: string
  - name: SettingInfobipVoiceCallSettingsAppKey
    type: string
  - name: SettingInfobipVoiceCallSettingsFromNumber
    type: string
  - name: SettingInfobipVoiceCallSettingsCallbackUrl
    type: string
  - name: SettingInfobipVoiceCallSettingsCallbackSecret
    type: string
  - name: isSlotDeploymentEnabled
    type: string
  - name: slotDeploymentInstanceName
    type: string
  - name: SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule
    type: string
  - name: SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName
    type: string
  - name: SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString
    type: string
  - name: SettingAzureAdInstance
    type: string
  - name: SettingAzureAdTenantId
    type: string
  - name: SettingAzureAdClientId
    type: string
  - name: SettingAzureAdClientSecret
    type: string
  - name: SettingAzureAdCallbackPath
    type: string
  - name: SettingAzureAdAudience
    type: string
  - name: SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck
    type: string
  - name: SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays
    type: string
  - name: SettingC3PayPlusMembershipTargetedDiscountCooldownDays
    type: string
  - name: SettingC3PayPlusMembershipAllowedPhoneNumbers
    type: string
  - name: SettingLoginVideoSlotInterval
    type: string
  - name: SettingSalaryPaidEventTopicName
    type: string
  - name: SettingSalaryPaidEventSubscriptionName
    type: string
  - name: SettingSalaryPaidEventConnectionString
    type: string

steps:
  - task: AzurePowerShell@4
    displayName: 'AI: Retrieve the Instrumentation Key of Application Insight'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |             
        $r = Get-AzApplicationInsights -ResourceGroupName ${{ parameters.rgName }} -Name ${{ parameters.aiName }} -ErrorAction Ignore

        if ($null -ne $r) {
          $instrumentationKey=$r.InstrumentationKey                              
          Write-Host "##vso[task.setvariable variable=instrumentationKey;]$instrumentationKey"
        }
      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'SQL: Retrieve SQL URI'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |        
        $r = Get-AzSqlServer -ResourceGroupName ${{ parameters.rgName }} -ServerName ${{ parameters.sqlName }} -ErrorAction Ignore

        if ($null -ne $r) {
          $sqlUri = $r.FullyQualifiedDomainName
          Write-Host "##vso[task.setvariable variable=sqlUri;]$sqlUri"
        }
      azurePowerShellVersion: LatestVersion
  
  - task: AzurePowerShell@4
    displayName: 'Key Vault: Retrieve KV URI'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |        
        $r = Get-AzKeyVault -ResourceGroupName ${{ parameters.rgName }} -VaultName ${{ parameters.keyVaultName }} -ErrorAction Ignore

        if ($null -ne $r) {
          $keyvaultUri = $r.VaultUri
          Write-Host "##vso[task.setvariable variable=keyvaultUri;]$keyvaultUri"
        }
      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Storage: Retrieve access Key'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |              
        $r = Get-AzStorageAccountKey -ResourceGroupName ${{ parameters.rgName }} -Name ${{ parameters.storageName }}
        
        if ($null -ne $r) {
          $storageKey = $r.Value[0]
          Write-Host "##vso[task.setvariable variable=storageKey;isSecret=true]$storageKey"
        }              

        $r = Get-AzStorageAccountKey -ResourceGroupName ${{ parameters.rgName }} -Name ${{ parameters.storageWebJobName }}
        
        if ($null -ne $r) {
          $webJobStorageKey = $r.Value[0]
          Write-Host "##vso[task.setvariable variable=webJobStorageKey;isSecret=true]$webJobStorageKey"
        }           
      azurePowerShellVersion: LatestVersion
  
  - task: AzurePowerShell@4
    displayName: 'Storage: Create containers'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ScriptType: InlineScript
      Inline: |        
        $context = New-AzStorageContext -StorageAccountName ${{ parameters.storageName }} -StorageAccountKey  $(storageKey)
        $webJobContext = New-AzStorageContext -StorageAccountName ${{ parameters.storageWebJobName }} -StorageAccountKey  $(webJobStorageKey)

        ##Create popups container
        $popupsContainer = "popups"
        $objContainer = Get-AzStorageContainer -Context $context -Name $popupsContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $popupsContainer
        }   

        ##Create dashboard cards container
        $dashboardCardsContainer = "dashboard-cards"
        $objContainer = Get-AzStorageContainer -Context $context -Name $dashboardCardsContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $dashboardCardsContainer
        }   

        ##Create selfies container
        $selfiesContainer = "selfies"
        $objContainer= Get-AzStorageContainer -Context $context -Name $selfiesContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $selfiesContainer
        }   
        
        ##Create emirateId container
        $emiratesIdContainer = "emirates-id"
        $objContainer= Get-AzStorageContainer -Context $context -Name $emiratesIdContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $emiratesIdContainer
        } 
        
         ##Create temp identification container
        $tempIdentificationsContainer = "temp-identifications"
        $objContainer= Get-AzStorageContainer -Context $context -Name $tempIdentificationsContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $tempIdentificationsContainer
        } 

        ##Create RAK Bank transactions container
        $raktransactionContainer = "raktransaction"
        $objContainer= Get-AzStorageContainer -Context $webJobContext -Name $raktransactionContainer -ErrorAction SilentlyContinue
 
        if(!$objContainer)
        {
            new-AzStorageContainer -Context $webJobContext -Name $raktransactionContainer
        }   

        ##Create text message templates container
        $textMessageTemplatesContainer = "text-message-templates"
        $objContainer = Get-AzStorageContainer -Context $context -Name $textMessageTemplatesContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $textMessageTemplatesContainer
        }   
       
         ##Create text message templates container
        $otpTable = "otp"
        $objContainer = Get-AzStorageTable -Context $context -Name $otpTable -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageTable -Context $context -Name $otpTable
        } 

        ##Create kycPortal_Temp container
        $kycPortalTempContainer = "kyc-portal-temp"
        $objContainer= Get-AzStorageContainer -Context $context -Name $kycPortalTempContainer -ErrorAction SilentlyContinue

        if(!$objContainer)
        {
            new-AzStorageContainer -Context $context -Name $kycPortalTempContainer
        }   

      azurePowerShellVersion: LatestVersion

  - task: AzurePowerShell@4
    displayName: 'Service Bus: Configure the SSV queues'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        azurePowerShellVersion: LatestVersion
        Inline: |     
          ############# Create Money Transfer Queue #############      
          $queueName= "moneytransfer"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbMoneyTransferConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbMoneyTransferQueueName;]$queueName"

            ############# Create Bill Payment - Amount Due (Add Biller) Queue #############      
          $queueName= "billpaymentamountdue"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBillPaymentAmountDueConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbBillPaymentAmountDueQueueName;]$queueName"

           ############# Create Bill Payment - Process Payment Queue #############      
          $queueName= "billpaymentprocesspayment"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBillPaymentProcessPaymentConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbBillPaymentProcessPaymentQueueName;]$queueName"

          ############# Set UnEmployment Insurance Connection String #############  
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -TopicName ${{ parameters.SettingUnEmploymentInsuranceServiceBusTopicName }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbUnEmploymentInsuranceConnection;]$sbConnection"

          ############# Set UnEmployment Insurance User Topic Connection String #############
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -TopicName ${{ parameters.SettingUnEmploymentInsuranceServiceBusUserTopicName  }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbUnEmploymentInsuranceUserTopicConnection;]$sbConnection"

          ############# Set Mobile Recharge Topic Connection String #############  
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -TopicName ${{ parameters.SettingMobileRechargeServiceServiceBusTopicName }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbMobileRechargeTopicConnection;]$sbConnection"
          
          ############# Set Surcharge Service Topic Connection String #############  
          Write-Host "##vso[task.setvariable variable=sbSurchargeServiceTopicConnection;]$(SurchargeServiceConnectionString)"
          
          ############# Set Renewal Card Update Topic Connection String #############  
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -TopicName ${{ parameters.SettingRenewalCardUpdateServiceBusTopicName }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbRenewalCardUpdateTopicConnection;]$sbConnection"  

          ############# Create EH Money Transfer Queue #############      
          $queueName= "ehmoneytransfer"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbEHMoneyTransferConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbEHMoneyTransferQueueName;]$queueName"

          ############# Create Money Transfer Mock Queue #############      
          $queueName= "moneytransfermock"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbMoneyTransferMockConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbMoneyTransferMockQueueName;]$queueName"

          ############# Create Beneficiary Queue #############      
          $queueName= "beneficiary"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBeneficiaryConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbBeneficiaryQueueName;]$queueName"

          ############# Create Beneficiary Mock Queue #############      
          $queueName= "beneficiarymock"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBeneficiaryMockConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbBeneficiaryMockQueueName;]$queueName"

          ############# Create Mobile Recharge Queue #############      
          $queueName= "mobilerecharge"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbMobileRechargeConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbMobileRechargeQueueName;]$queueName"

          ############# Create ESMO Service Queue #############      
          $queueName= "esmoservice"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbESMOServiceConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbESMOServiceQueueName;]$queueName"

          ############# Create Card Replacement Queue #############      
          $queueName= "replacementcards"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbCardReplacementConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbCardReplacementQueueName;]$queueName"

          ############# Create Branch Id Update Queue #############      
          $queueName= "branchidupdate"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBranchIdUpdateConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbBranchIdUpdateQueueName;]$queueName"

           ############# Create Referral Program Queue #############      
          $queueName= "referralprogram"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbReferralProgramConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbReferralProgramQueueName;]$queueName"

          ############# Create Salary Processing Service Queue #############      
          $queueName= "salaryprocessing"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbSalaryProcessingConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbSalaryProcessingQueueName;]$queueName"

          ############# Create Registration Status Service Queue #############      
          $queueName= "registrationstatus"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbRegistrationStatusConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbRegistrationStatusQueueName;]$queueName"

          ############# Create Phone Number Queue #############      
          $queueName= "kycservice"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbKYCServiceConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbKYCServiceQueueName;]$queueName"  
          
          ############# Create Direct Transfer Claim Queue #############      
          $queueName= "directtransferclaim"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbClaimPendingDirectTransfersQueueConnectionString;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbClaimPendingDirectTransfersQueueName;]$queueName"

  - task: AzurePowerShell@4
    displayName: 'Service Bus: Configure the SSV queues 1'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        azurePowerShellVersion: LatestVersion
        Inline: |     
          ############# Set Balance Enquiry topic Connection String ############# 
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -TopicName ${{ parameters.SettingBalanceEnquirySubscriptionServiceBusTopicName }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbBalanceEnquirySubscriptionConnection;]$sbConnection"  

          #############  Audit Trail Queue Connection String ###########
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue ${{ parameters.SettingAuditTrailServiceBusQueueName }} -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbAuditTrailConnection;]$sbConnection"

          ############# Create Deleted Card Queue #############      
          $queueName= "delete-card-from-spark"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbDeletedCardConnection;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbDeletedCardQueueName;]$queueName"

          ############# Create Queue: C3Pay Plus Post Subscription #############      
          $queueName= "c3p-post-subscription"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbC3pPlusPostSubscriptionQueueConnectionString;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbC3pPlusPostSubscriptionQueueName;]$queueName"

  - task: AzurePowerShell@4
    displayName: 'Service Bus: Configure the SSV queues 2'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        azurePowerShellVersion: LatestVersion
        Inline: |     
          ############# Create Queue: C3Pay ATM Withdrawal Refunds ############# 
          $queueName= "c3p-atm-withdrawal-refunds"
          $queue=Get-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -ErrorAction Ignore
          if($queue -eq $null)
          {
              New-AzServiceBusQueue -ResourceGroup ${{ parameters.rgName }} -NamespaceName ${{ parameters.serviceBusName }} -QueueName $queueName -EnablePartitioning $True -MaxSizeInMegabytes 5120
              New-AzServiceBusAuthorizationRule -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }}  -Name ListenSendKey -Queue $queueName -Rights @("Listen","Send")
          }
          
          $sbConnection = (Get-AzServiceBusKey -ResourceGroup ${{ parameters.rgName }} -Namespace ${{ parameters.serviceBusName }} -Queue $queueName -Name ListenSendKey).PrimaryConnectionString
          $parts = $sbConnection -split "EntityPath="
          $sbConnection = $parts[0]
          Write-Host "##vso[task.setvariable variable=sbC3PAtmWithdrawalQueueConnectionString;]$sbConnection"
          Write-Host "##vso[task.setvariable variable=sbC3PAtmWithdrawalQueueName;]$queueName"

  - task: DownloadSecureFile@1
    name: rakPrivateKey
    displayName: 'Certifcate: Download Rak Private Key file'
    inputs:
      secureFile: ${{ parameters.rakPrivateKeyName }}

  - task: DownloadSecureFile@1
    name: rakPublicKey
    displayName: 'Certifcate: Download Rak Public Key file'
    inputs:
      secureFile: ${{ parameters.rakPublicKeyName }}
    
  - task: DownloadSecureFile@1
    name: rakSslCertificate
    displayName: 'Certifcate: Download Rak SSL file'
    inputs:
      secureFile: ${{ parameters.rakCertificateName }}

  - template: pipelines/open-firewall-for-steps.yml@devopsTemplates
    parameters:
      azConnection: ${{ parameters.azureSubscription }}
      resourceGroup: ${{ parameters.rgName  }}
      resourceName: ${{ parameters.keyvaultName }}
      resourceType: KeyVault
      steps:
        - task: AzurePowerShell@4
          displayName: 'Key Vault: Set the secrets in the key vault 1'
          continueOnError: true
          inputs:
            azureSubscription: ${{ parameters.azureSubscription }}
            azurePowerShellVersion: LatestVersion
            ScriptType: InlineScript            
            Inline: |            
              ############# Store the DB connection strings in the KV #############    
              $connectionString = "Server=tcp:$(sqlUri),1433;Initial Catalog=${{ parameters.dbName }};Persist Security Info=False;User ID=${{ parameters.sqlsrvAdministratorLogin }};Password=${{ parameters.sqlsrvAdministratorPassword }};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Max Pool Size=200;Column Encryption Setting = enabled;"
              $secretvalue = ConvertTo-SecureString $connectionString -AsPlainText -Force
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--C3PayConnection' -SecretValue $secretvalue

              ############# Store the read only DB connection strings in the KV #############    
              $readOnlyConnectionString = "Server=tcp:$(sqlUri),1433;Initial Catalog=${{ parameters.dbName }};Persist Security Info=False;User ID=${{ parameters.sqlsrvAdministratorLogin }};Password=${{ parameters.sqlsrvAdministratorPassword }};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;ApplicationIntent=ReadOnly;Column Encryption Setting = enabled;"
              $secretvalue = ConvertTo-SecureString $readOnlyConnectionString -AsPlainText -Force
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--C3PayConnectionReadOnly' -SecretValue $secretvalue

              $identityConnectionString = "Server=tcp:$(sqlUri),1433;Initial Catalog=${{ parameters.identityDbName }};Persist Security Info=False;User ID=${{ parameters.sqlsrvAdministratorLogin }};Password=${{ parameters.sqlsrvAdministratorPassword }};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
              $identitysecretvalue = ConvertTo-SecureString $identityConnectionString -AsPlainText -Force
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--IdentityConnection' -SecretValue $identitysecretvalue
              
              ############# Store the REdis connection strings in the KV #############   
              $redisSecretValue = ConvertTo-SecureString "${{ parameters.SettingRedisConnection }}" -AsPlainText -Force
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--RedisConnection' -SecretValue $redisSecretValue
              
              ############# Store the Service bus connection strings in the KV #############   
              $serviceBusSecretValue = ConvertTo-SecureString "${{ parameters.SettingServiceBusConnection }}" -AsPlainText -Force
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--ServiceBusConnection' -SecretValue $serviceBusSecretValue

              ############# Store Mobile app hash key #############    
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingMobileAppHashKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'General--MobileAppHashKey' -SecretValue $secretvalue

              ############# Store the Beneficiary Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbBeneficiaryConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--BeneficiaryQueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbBeneficiaryQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--BeneficiaryQueueName' -SecretValue $secretvalue              

              ############# Store the Money Transfer Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbMoneyTransferConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--MoneyTransferQueueConnectionString' -SecretValue $secretvalue
                                                                                      
              $secretvalue = ConvertTo-SecureString '$(sbMoneyTransferQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--MoneyTransferQueueName' -SecretValue $secretvalue        

              ############# Store the Bill Payment (Amount Due) Queue Name & Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbBillPaymentAmountDueConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BillPayment--AddBillerQueueConnectionString' -SecretValue $secretvalue
                                                                                      
              $secretvalue = ConvertTo-SecureString '$(sbBillPaymentAmountDueQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BillPayment--AddBillerQueueName' -SecretValue $secretvalue         
             
              ############# Store the Bill Payment (ProcessPayment) Queue Name #############  
              $secretvalue = ConvertTo-SecureString '$(sbBillPaymentProcessPaymentConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BillPayment--ProcessPaymentQueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbBillPaymentProcessPaymentQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BillPayment--ProcessPaymentQueueName' -SecretValue $secretvalue      
              
              ############# Store the UnEmployment Service bus connection string to Vault #############  
              $secretvalue = ConvertTo-SecureString '$(sbUnEmploymentInsuranceConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'UnEmploymentInsurance--ServiceBusConnectionString' -SecretValue $secretvalue

              ############# Store the UnEmployment Service User Topic connection string to Vault #############  
              $secretvalue = ConvertTo-SecureString '$(sbUnEmploymentInsuranceUserTopicConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'UnEmploymentInsurance--ServiceBusUserTopicConnectionString' -SecretValue $secretvalue

              ############# Store the Mobile Recharge in the KV #############                  
              $secretvalue = ConvertTo-SecureString '$(sbMobileRechargeConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MobileRechargeService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbMobileRechargeTopicConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MobileRechargeService--ServiceBusConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbMobileRechargeQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MobileRechargeService--QueueName' -SecretValue $secretvalue
              
              ############# Store the Surcharge Service in the KV #############                  
              $secretvalue = ConvertTo-SecureString '$(sbSurchargeServiceTopicConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SurchargeService--TopicConnectionString' -SecretValue $secretvalue   
              
              ############# Store Renewal Card Update Connection String settings in the KV #############      
              $secretvalue = ConvertTo-SecureString '$(sbRenewalCardUpdateTopicConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RenewalCardUpdate--ServiceBusConnectionString' -SecretValue $secretvalue

              ############# Store Ding settings in the KV #############                  

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingDingBaseURL }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'DingService--BaseURL' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingDingClientApiKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'DingService--ClientApiKey' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSecondaryDingClientApiKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'DingService--SecondaryClientApiKey' -SecretValue $secretvalue

              ############# Import Rak SSL certificate in KV#############  
              $rakSSLKvName = "RAKServiceSSL"
              $cert = Get-AzKeyVaultCertificate -VaultName "${{ parameters.keyVaultName }}" -Name $rakSSLKvName

              if ($cert -eq $null)
              {
                $secretvalue = ConvertTo-SecureString ${{ parameters.rakCertificatePassword }} -AsPlainText -Force
                Import-AzKeyVaultCertificate -VaultName ${{ parameters.keyVaultName }} -Name $rakSSLKvName -FilePath "$(rakSslCertificate.secureFilePath)" -Password $secretvalue
              }
          
              ############# Put Rak API secrets in KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakBaseURL }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--BaseURL' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--ClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKService--clientSecretkey' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakRefreshRatesEmiratesId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--RefreshRatesEmiratesId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakSftpEndPoint }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKSFTPConnection--Endpoint' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakSftpPort }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKSFTPConnection--Port' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakSftpUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKSFTPConnection--Username' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingRakSftpPassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'RAKSFTPConnection--Password' -SecretValue $secretvalue

              ############# Put Rak Keys in KV #############                 
              Get-Content "$(rakPrivateKey.secureFilePath)" | Select-Object -Skip 1 | Select-Object -SkipLast 1 | Set-Content 'rak-staging.tmp'
              $content = Get-Content 'rak-staging.tmp' -Raw
              $secretvalue = ConvertTo-SecureString $content -AsPlainText -Force
              set-AzKeyVaultSecret -VaultName  ${{ parameters.keyVaultName }} -Name 'RAKService--PaylaodPrivateKey' -SecretValue $secretvalue

              Get-Content "$(rakPublicKey.secureFilePath)" | Select-Object -Skip 1 | Select-Object -SkipLast 1 | Set-Content 'rak-staging.tmp'
              $content = Get-Content 'rak-staging.tmp' -Raw
              $secretvalue = ConvertTo-SecureString $content -AsPlainText -Force
              set-AzKeyVaultSecret -VaultName  ${{ parameters.keyVaultName }} -Name 'RAKService--PaylaodPublicKey' -SecretValue $secretvalue

              
              ############# Generate encryption key #############              
              $Keyvalue = Get-AzKeyVaultKey -VaultName ${{ parameters.keyVaultName }} -Name ${{ parameters.SettingKeyName }}

              if ($Keyvalue -eq $null)
              {
                  $Keyvalue = Add-AzKeyVaultKey -VaultName ${{ parameters.keyVaultName }} -Name ${{ parameters.SettingKeyName }} -Destination 'Software'
              }
              
              $KeyvalueId =  $Keyvalue.Id
              Write-Host "##vso[task.setvariable variable=KeyvalueId;]$KeyvalueId"
              
              ############# Store the Storage connection string in the KV #############
              $connectionString = "DefaultEndpointsProtocol=https;AccountName=${{ parameters.storageName }};AccountKey=$(storageKey);EndpointSuffix=core.windows.net"
              $secretvalue = ConvertTo-SecureString $connectionString -AsPlainText -Force

              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--AzureBlobStorage' -SecretValue $secretvalue

              $connectionString = "DefaultEndpointsProtocol=https;AccountName=${{ parameters.storageWebJobName }};AccountKey=$(webJobStorageKey);EndpointSuffix=core.windows.net"
              $secretvalue = ConvertTo-SecureString $connectionString -AsPlainText -Force

              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ConnectionStrings--AzureWebJobsStorage' -SecretValue $secretvalue

              ############# Store the EDC settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEDCAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EDConnect--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEDCApiName }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EDConnect--ApiName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEDCApiSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EDConnect--ApiSecret' -SecretValue $secretvalue

                ############# Store the Portal EDC settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPortalEDCAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PortalEDConnect--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPortalEDCApiName }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PortalEDConnect--ApiName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPortalEDCApiSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PortalEDConnect--ApiSecret' -SecretValue $secretvalue

              ############# Store the AAD settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAADAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AADSecurity--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAADAudience }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AADSecurity--Audience' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAADAllowedClientIds }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AADSecurity--AllowedClientIds' -SecretValue $secretvalue

              ############# Store Signy secrets in the KV #############             
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSignzyURL }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SignzyService--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSignzyFileExchangeAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SignzyService--FileExchangeBaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSignzyId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SignzyService--Id' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSignzyUserId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SignzyService--UserId' -SecretValue $secretvalue

              ############# Store PPS secrets in the KV #############    
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSWebAuthBaseURL }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--WebAuthBaseURL' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSWebAuthClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--WebAuthClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSWebAuthClientSecretkey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--WebAuthClientSecretkey' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSEndpointAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--EndpointAddress' -SecretValue $secretvalue
            
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--Username' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSPassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--Password' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSSponsorCode }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--SponsorCode' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSCustomerCode }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--CustomerCode' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPPSSharedSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PPSService--SharedSecret' -SecretValue $secretvalue

        - task: AzurePowerShell@4
          displayName: 'Key Vault: Set the secrets in the key vault 2'
          continueOnError: true
          inputs:
            azureSubscription: ${{ parameters.azureSubscription }}
            azurePowerShellVersion: LatestVersion
            ScriptType: InlineScript            
            Inline: |             
              ############# Store Etisalat SMS settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEtisalatSMSUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EtisalatSMS--Username' -SecretValue $secretvalue
              
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEtisalatSMSPassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EtisalatSMS--Password' -SecretValue $secretvalue      

              ############# Store Infobip SMS settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingInfobipSMSUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'InfobipSMS--Username' -SecretValue $secretvalue
              
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingInfobipSMSPassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'InfobipSMS--Password' -SecretValue $secretvalue      
              
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingInfobipSMSAuthKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'InfobipSMS--AuthKey' -SecretValue $secretvalue      

              ############# Store Edenred Identity Manager settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEdenredIdentityManagerBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EdenredIdentityManager--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEdenredIdentityManagerAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EdenredIdentityManager--Authority' -SecretValue $secretvalue
              
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEdenredIdentityManagerResourceId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EdenredIdentityManager--ResourceId' -SecretValue $secretvalue   

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEdenredIdentityManagerClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EdenredIdentityManager--ClientId' -SecretValue $secretvalue   

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingEdenredIdentityManagerClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'EdenredIdentityManager--ClientSecret' -SecretValue $secretvalue   

              ############# Store firebase cloud messaging settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingFirebaseCloudMessagingKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'FirebaseCloudMessaging--Key' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingFirebaseCloudMessagingSenderId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'FirebaseCloudMessaging--SenderId' -SecretValue $secretvalue

              ############# Store esmo service settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingESMOServiceBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingESMOServiceClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--ClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingESMOServiceClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--ClientSecret' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingESMOServiceAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingESMOServiceScope }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--Scope' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbESMOServiceConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbESMOServiceQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--QueueName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbCardReplacementConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ReplacementCardUpdateService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbCardReplacementQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ReplacementCardUpdateService--QueueName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbDeletedCardConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'DeletedCardUpdateService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbDeletedCardQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'DeletedCardUpdateService--QueueName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbBranchIdUpdateConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BranchIdUpdateService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbBranchIdUpdateQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BranchIdUpdateService--QueueName' -SecretValue $secretvalue           

              $secretvalue = ConvertTo-SecureString '$(sbReferralProgramConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ReferralProgramService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbReferralProgramQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ReferralProgramService--QueueName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbSalaryProcessingConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SalaryProcessingEventService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbSalaryProcessingQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SalaryProcessingEventService--QueueName' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbRegistrationStatusConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--RegistrationStatusConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbRegistrationStatusQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ESMOService--RegistrationStatusQueueName' -SecretValue $secretvalue

              ############# Store transactions b2c service settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingTransactionsB2CServiceAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'TransactionsB2CService--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingTransactionsB2CServiceClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'TransactionsB2CService--ClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingTransactionsB2CServiceScope }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'TransactionsB2CService--Scope' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingTransactionsB2CServiceClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'TransactionsB2CService--ClientSecret' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingTransactionsB2CServiceBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'TransactionsB2CService--BaseAddress' -SecretValue $secretvalue

              ############# Store kyc service settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--Username' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCPassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--Password' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCUniqueRef }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--UniqueRef' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCSponsorCode }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--SponsorCode' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingKYCSharedSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--SharedSecret' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbKYCServiceConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--QueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbKYCServiceQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'KYCService--QueueName' -SecretValue $secretvalue
              
              ############# Store SendGrid settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSendGridAPIKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'SendGrid--APIKey' -SecretValue $secretvalue  
              
              ############# Store CleverTap settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingCleverTapProjectId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'CleverTapService--ProjectId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingCleverTapPassCode }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'CleverTapService--PassCode' -SecretValue $secretvalue 

              #### Mock Queues ####

              ############# Store the Beneficiary Mock Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbBeneficiaryMockConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--BeneficiaryMockQueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbBeneficiaryMockQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--BeneficiaryMockQueueName' -SecretValue $secretvalue              

              ############# Store the Money Transfer Mock Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbMoneyTransferMockConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--MoneyTransferMockQueueConnectionString' -SecretValue $secretvalue
                                                                                      
              $secretvalue = ConvertTo-SecureString '$(sbMoneyTransferMockQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--MoneyTransferMockQueueName' -SecretValue $secretvalue

              ############# Store the Money Transfer Mock Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingMoneyTransferMultimediaURL }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--MultimediaURL' -SecretValue $secretvalue

              
              ############# Store the Exchange House Money Transfer Username in the KV #############    
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingExchangeHouseUsername }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ExchangeHouseSettings--Username' -SecretValue $secretvalue
              
              ############# Store the Exchange House Money Transfer Password in the KV #############    
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingExchangeHousePassword }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ExchangeHouseSettings--Password' -SecretValue $secretvalue
              
               ############# Store the Money Transfer Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbEHMoneyTransferConnection)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ExchangeHouseSettings--MoneyTransferQueueConnectionString' -SecretValue $secretvalue
                           
               $secretvalue = ConvertTo-SecureString '$(sbEHMoneyTransferQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'ExchangeHouseSettings--MoneyTransferQueueName' -SecretValue $secretvalue     

              ############# Store the Direct Transfer Queue Connection string in the KV #############    
              $secretvalue = ConvertTo-SecureString '$(sbClaimPendingDirectTransfersQueueConnectionString)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--ClaimPendingDirectTransfersQueueConnectionString' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '$(sbClaimPendingDirectTransfersQueueName)' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--ClaimPendingDirectTransfersQueueName' -SecretValue $secretvalue
               
              ############# Store Payroll service settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPayrollServiceBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PayrollService--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPayrollServiceAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PayrollService--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPayrollServiceClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PayrollService--ClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPayrollServiceClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PayrollService--ClientSecret' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingPayrollServiceScope }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'PayrollService--Scope' -SecretValue $secretvalue

              ############# Store HR service settings in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingHRServiceBaseAddress }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'HRService--BaseAddress' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingHRServiceAuthority }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'HRService--Authority' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingHRServiceClientId }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'HRService--ClientId' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingHRServiceClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'HRService--ClientSecret' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingHRServiceScope }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'HRService--Scope' -SecretValue $secretvalue


              ############# Store Azure Computer Vision secrets in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAzureOCRKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AzureOCR--Key' -SecretValue $secretvalue

              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAzureFaceKey }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AzureFace--Key' -SecretValue $secretvalue

              ############# Store Azure AD secrets in the KV #############
              $secretvalue = ConvertTo-SecureString '${{ parameters.SettingAzureAdClientSecret }}' -AsPlainText -Force              
              Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AzureAd--ClientSecret' -SecretValue $secretvalue


        - task: AzurePowerShell@4
          displayName: 'Key Vault: Set the secrets in the key vault 3'
          continueOnError: true
          inputs:
                azureSubscription: ${{ parameters.azureSubscription }}
                azurePowerShellVersion: LatestVersion
                ScriptType: InlineScript            
                Inline: |             
                    ############# Store the BalanceEnquirySubscription Service bus connection string to Vault #############  
                    $secretvalue = ConvertTo-SecureString '$(sbBalanceEnquirySubscriptionConnection)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'BalanceEnquirySubscription--ServiceBusConnectionString' -SecretValue $secretvalue

                    ############# Store the UnEmployment Service bus connection string to Vault #############  
                    $secretvalue = ConvertTo-SecureString '$(sbAuditTrailConnection)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'AuditTrail--ServiceBusConnectionString' -SecretValue $secretvalue
                    
                    ############# Store Firebase service settings in the KV #############
                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingFirebaseNotificationAuthEndpoint }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'FirebaseNotification--AuthEndpoint' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingFirebaseNotificationBaseUrl }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'FirebaseNotification--BaseUrl' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingFirebaseNotificationSendMethodUrl }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'FirebaseNotification--SendMethodUrl' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthType }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--Type' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthProjectId }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--ProjectId' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthPrivateKeyId }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--PrivateKeyId' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthPrivateKey }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--PrivateKey' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthClientEmail }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--ClientEmail' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthClientId }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--ClientId' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthAuthUri }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--AuthUri' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthTokenUri }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--TokenUri' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthAuthProviderX509CertUrl }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--AuthProviderX509CertUrl' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthClientX509CertUrl }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--ClientX509CertUrl' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingGoogleAuthUniverseDomain }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'GoogleAuth--UniverseDomain' -SecretValue $secretvalue

                    ############# C3Pay+ Money ATM Withdrawals Refunds #############    
                    $secretvalue = ConvertTo-SecureString '$(sbC3PAtmWithdrawalQueueConnectionString)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'C3PayPlusMembership--ATMWithdrawalRefundsQueueConnectionString' -SecretValue $secretvalue
                                                                                    
                    $secretvalue = ConvertTo-SecureString '$(sbC3PAtmWithdrawalQueueName)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'C3PayPlusMembership--ATMWithdrawalRefundsQueueName' -SecretValue $secretvalue

                    ############# C3Pay+ Post Subscription Queue#############    
                    $secretvalue = ConvertTo-SecureString '$(sbC3pPlusPostSubscriptionQueueConnectionString)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'C3PayPlusMembership--PostSubscriptionQueueConnectionString' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '$(sbC3pPlusPostSubscriptionQueueName)' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'C3PayPlusMembership--PostSubscriptionQueueName' -SecretValue $secretvalue


                    ################# MT: Got Salary Paid Event Topic #############
                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSalaryPaidEventConnectionString }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--SalaryGotPaidTopicConnectionString' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSalaryPaidEventTopicName }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--SalaryGotPaidTopicName' -SecretValue $secretvalue

                    $secretvalue = ConvertTo-SecureString '${{ parameters.SettingSalaryPaidEventSubscriptionName }}' -AsPlainText -Force              
                    Set-AzKeyVaultSecret -VaultName ${{ parameters.keyVaultName }} -Name 'MoneyTransferService--SalaryGotPaidTopicSubscriptionName' -SecretValue $secretvalue


  - task: SqlAzureDacpacDeployment@1
    displayName: 'DB: Upgrade schema'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ServerName: '$(sqlUri)'
      DatabaseName: '${{ parameters.dbName }}'
      SqlUsername: '${{ parameters.sqlsrvAdministratorLogin }}'
      SqlPassword: '${{ parameters.sqlsrvAdministratorPassword }}'
      deployType: SqlTask
      SqlFile: '${{ parameters.sqlFile }}'

  - task: SqlAzureDacpacDeployment@1
    displayName: 'Identity DB: Upgrade schema'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      ServerName: '$(sqlUri)'
      DatabaseName: '${{ parameters.identityDbName }}'
      SqlUsername: '${{ parameters.sqlsrvAdministratorLogin }}'
      SqlPassword: '${{ parameters.sqlsrvAdministratorPassword }}'
      deployType: SqlTask
      SqlFile: '${{ parameters.identitySqlFile }}'

  - task: AzurePowerShell@4
    displayName: 'Add Hosted Agent Access Rule For Web APP'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        Inline: |
            $HostedIPAddress = Invoke-RestMethod http://ipinfo.io/json | Select -exp ip
            $ruleName = $HostedIPAddress
            Write-Host $ruleName 
            
            # Retrieve the current access restriction configuration for the web app
            $accessRestrictions = (Get-AzWebAppAccessRestrictionConfig -ResourceGroupName ${{ parameters.rgName }} -Name ${{ parameters.webAppName }}).MainSiteAccessRestrictions
            foreach($result in $accessRestrictions)
            {
                Write-Host $result.RuleName               
                if($result.RuleName -eq $ruleName){
                    $ruleExists = $true;
                    break;
                }
            }
          
            if ($ruleExists -eq $true) {
              Write-Host "Access restriction rule '$ruleName' already exists."
            }
            else {
            #    Add-AzWebAppAccessRestrictionRule -ResourceGroupName ${{ parameters.rgName }} -WebAppName ${{ parameters.webAppName }} -Name $ruleName -Priority 110 -Action Allow -IpAddress "$HostedIPAddress/16"

                Write-Host "Access restriction rule '$ruleName' created."
            }
          

        azurePowerShellVersion: LatestVersion

  - task: AzureRmWebAppDeployment@4
    displayName: 'WebApp: Deploy Package'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      WebAppName: '${{ parameters.webAppName }}'
      packageForLinux: '${{ parameters.webAppBinariesFile }}'
      ResourceGroupName: '${{ parameters.rgName }}'
      DeployToSlotOrASEFlag : true
      SlotName: 'staging'
      AppSettings: '-APPINSIGHTS_INSTRUMENTATIONKEY $(instrumentationKey)
                    -ASPNETCORE_ENVIRONMENT "Production" 
                    -WEBSITE_HEALTHCHECK_MAXPINGFAILURES 3
                    -ApplicationInsightsAgent_EXTENSION_VERSION ~2
                    -WEBSITE_LOAD_USER_PROFILE 1 
                    -WEBSITE_TIME_ZONE "Arabian Standard Time"
                    -General__EnableSwagger "${{ parameters.SettingEnableSwagger }}"
                    -General__AutoUnblockMaxAttemptCount 5
                    -General__AutoUnblockEnabled true
                    -General__SkipOtp false
                    -General__Environment "UAT" 
                    -PPSService__SpendPolicyLimitAmount "500"
                    -PPSService__SpendPolicyReference "DefaultSpendPolicy"
                    -Multimedia__AutoPlayMoneyTransferVideoForAll false                                   
                    -Multimedia__AutoPlayMoneyTransferVideoCorporateIds "${{ parameters.SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds }}"
                    -KeyVault__Authority $(keyvaultUri)
                    -KeyVault__KeyIdentifier $(KeyvalueId)
                    -EDConnect__EnableCaching true
                    -EDConnect__CacheDurationInMinutes 5
                    -EdenredIdentityManager__Tenant "AE"
                    -EtisalatSMS__BaseAddress "${{ parameters.SettingEtisalatSMSBaseAddress }}"
                    -EtisalatSMS__Timeout "${{ parameters.SettingEtisalatSMSTimeout }}"
                    -EtisalatSMS__RetryCount "${{ parameters.SettingEtisalatSMSRetryCount }}"
                    -EtisalatSMS__SenderName "C3Pay"
                    -EtisalatSMS__ContentType "application/json"
                    -InfobipSMS__BaseAddress "${{ parameters.SettingInfobipSMSBaseAddress }}"
                    -InfobipSMS__Timeout "${{ parameters.SettingInfobipSMSTimeout }}"
                    -InfobipSMS__RetryCount "${{ parameters.SettingInfobipSMSRetryCount }}"
                    -InfobipSMS__SenderName "C3Pay"
                    -InfobipSMS__ContentType "application/json"
                    -InfobipSMS__AuthKeyBaseUrl "${{ parameters.SettingInfobipSMSAuthKeyBaseUrl }}"
                    -InfobipSMS__SmsMode "${{ parameters.SettingInfobipSMSSmsMode }}"
                    -SendGrid__SenderEmail "${{ parameters.SettingSendGridSenderEmail }}"
                    -SendGrid__Templates__0__TemplateId "${{ parameters.SettingSendGridBankStatementEmailTemplateId }}"
                    -SendGrid__Templates__2__TemplateId "${{ parameters.SettingSendGridStoreOrderPlacedEmailTemplateId }}"
                    -FirebaseCloudMessaging__BaseAddress "${{ parameters.SettingFirebaseCloudMessagingBaseAddress }}"                    
                    -FirebaseCloudMessaging__RetryCount "${{ parameters.SettingFirebaseCloudMessagingRetryCount }}"                    
                    -FirebaseCloudMessaging__Timeout "${{ parameters.SettingFirebaseCloudMessagingTimeout }}"                    
                    -SignzyService__AcceptedSelfieScore 0.5
                    -SignzyService__AcceptedEIDScore 0.5
                    -SignzyService__IdealFaceMatchPercentage 60
                    -SignzyService__IdealNameMatchPercentage 75
                    -SignzyService__TTL "1 month"
                    -SignzyService__MimeType "image/png"
                    -SignzyService__ContentType "application/json"
                    -SignzyService__ImageQualityTimeout "${{ parameters.SettingSignzyImageQualityTimeout }}"
                    -SignzyService__FaceMatchTimeout "${{ parameters.SettingSignzyFaceMatchTimeout }}"
                    -SignzyService__ReadDocumentTimeout "${{ parameters.SettingSignzyReadDocumentTimeout }}"
                    -AzureOCR__Endpoint "${{ parameters.SettingAzureOCREndpoint }}"
                    -AzureOCR__WaitTimeInMilliSeconds "${{ parameters.SettingAzureOCRWaitTimeInMilliSeconds }}"
                    -AzureFace__Endpoint "${{ parameters.SettingAzureFaceEndpoint }}"
                    -AzureIdentificationService__UseAzureOCR "${{ parameters.SettingAzureIdentificationServiceUseAzureOCR }}"
                    -AzureIdentificationService__UseAzureFace "${{ parameters.SettingAzureIdentificationServiceUseAzureFace }}"
                    -AzureIdentificationService__MaxDifferenceInDigitsForRecheck "${{ parameters.SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck }}"
                    -KycExpiry__CheckStartDate "${{ parameters.SettingKycExpiryCheckStartDate }}"
                    -KycExpiry__ValidateEmiratesIdExpiryScheduler "${{ parameters.SettingKycExpiryValidateEmiratesIdExpiryScheduler }}"
                    -KycExpiry__ValidateAdditionKYCScheduler "${{ parameters.SettingKycExpiryValidateAdditionKYCScheduler }}"
                    -KYCService__MethodName "ApproveKYC"
                    -PPSService__WebAuthContentType "application/xml"                                    
                    -MobileRechargeService__SynchronizeWithDingSchedule "${{ parameters.SettingMobileRechargeSynchronizeWithDingSchedule }}"
                    -MobileRechargeService__UpdateStatusSchedule "${{ parameters.SettingMobileRechargeUpdateStatusSchedule }}"
                    -MobileRechargeService__NickNameLength "${{ parameters.SettingMobileRechargeNickNameLength }}"
                    -MobileRechargeService__CallingCardAccountNumberLive "${{ parameters.SettingMobileRechargeCallingCardAccountNumberLive }}"
                    -MobileRechargeService__TransactionEnvironment "${{ parameters.SettingMobileRechargeTransactionEnvironment }}"
                    -MobileRechargeService__MonthlyAmountLimitVerified "${{ parameters.SettingMobileRechargeVerifiedLimit }}"
                    -MobileRechargeService__MonthlyAmountLimitNotVerified "${{ parameters.SettingMobileRechargeNonVerifiedLimit }}"
                    -MobileRechargeService__C3FeeMode "${{ parameters.SettingMobileRechargeC3FeeMode }}"
                    -MobileRechargeService__MySalaryFeeMode "${{ parameters.SettingMobileRechargeMySalaryFeeMode }}"
                    -MobileRechargeService__SelectedCorporatesWithFee "${{ parameters.SettingMobileRechargeSelectedCorporatesWithFee }}"
                    -MobileRechargeService__FeeAmount "${{ parameters.SettingMobileRechargeFeeAmount }}"
                    -MobileRechargeService__CustomCallingCardName "${{ parameters.SettingMobileRechargeCustomCallingCardName }}"
                    -MobileRechargeService__CustomCallingCardCode "${{ parameters.SettingMobileRechargeCustomCallingCardCode }}"
                    -MobileRechargeService__CustomCallingCardLogoUrl "${{ parameters.SettingMobileRechargeCustomCallingCardLogoUrl }}"
                    -MobileRechargeService__CustomCallingCardValidationRegex "${{ parameters.SettingMobileRechargeCustomCallingCardValidationRegex }}"
                    -MobileRechargeService__DynamicPackageSeperator "${{ parameters.SettingMobileRechargeDynamicPackageSeperator }}"
                    -MobileRechargeService__ServiceBusTopicName "${{ parameters.SettingMobileRechargeServiceServiceBusTopicName}}" 
                    -MobileRechargeService__ServiceBusSubscriptionName "${{ parameters.SettingMobileRechargeServiceServiceBusSubscriptionName}}"
                    -DingService__ContentType "application/json"
                    -MoneyTransferService__UserMonthlyTransactionAmountLimit "${{ parameters.SettingMoneyTransferMonthlyAmountLimit }}"
                    -MoneyTransferService__UserMonthlyTransactionCountLimit "${{ parameters.SettingMoneyTransferMonthlyCountLimit }}"
                    -MoneyTransferService__EnableBeneficiarySameCountryDelay false
                    -RAKService__ContentType "application/json"
                    -RAKService__TokenContentType "application/x-www-form-urlencoded"
                    -RAKService__TokenScope "utility_other_banks utility_fxrate beneficiary_management send_money fetch_fields"
                    -RAKService__TokenGrantType "client_credentials"
                    -RAKService__SSLCertificateName "RAKServiceSSL"
                    -RAKService__BanksMaxRecords "${{ parameters.SettingRakBanksMaxRecords }}"
                    -RAKService__MaxTransactionTriesCount "${{ parameters.SettingRakMaxTransactionTriesCount }}"
                    -RAKService__MessageProcessInDelay "${{ parameters.SettingRakMessageProcessInDelay }}"
                    -RAKService__MoneyTransferBeneficiaryDelayInMins "${{ parameters.SettingRakMoneyTransferBeneficiaryDelayInMins }}"
                    -RAKService__LoyaltyImplementDate "${{ parameters.SettingRakLoyaltyImplementDate }}"
                    -RAKService__LoyaltyLimitCount "${{ parameters.SettingRakLoyaltyLimitCount }}"
                    -RAKService__LoyaltyLimitAmount "${{ parameters.SettingRakLoyaltyLimitAmount }}"
                    -RAKService__URLPath "${{ parameters.SettingRakURLPath }}"
                    -RAKService__MoneyTransferBeneficiaryCount "${{ parameters.SettingRakMoneyTransferBeneficiaryCount }}"
                    -RAKService__EnableRakTokenCache "${{ parameters.SettingRakEnableRakTokenCache }}"
                    -ReferralProgramService__MoneyTransferCountThreshold "${{ parameters.SettingReferralProgramMoneyTransferCountThreshold }}"
                    -ReferralProgramService__MoneyTransferAmountThreshold "${{ parameters.SettingReferralProgramMoneyTransferAmountThreshold }}"
                    -ReferralProgramService__MoneyTransferRewardAmount "${{ parameters.SettingReferralProgramMoneyTransferRewardAmount }}"
                    -ReferralProgramService__MoneyTransferReferralProgramStartDate "${{ parameters.SettingReferralProgramMoneyTransferReferralProgramStartDate }}"
                    -General__FirstBlackV1PlasticCardId "${{ parameters.SettingFirstBlackV1PlasticCardId }}"
                    -General__FirstBlackV2PlasticCardId "${{ parameters.SettingFirstBlackV2PlasticCardId }}"
                    -CleverTapService__BaseAddress "${{ parameters.SettingCleverTapBaseAddress }}"
                    -ExchangeHouseSettings__BaseAddress "${{ parameters.SettingExchangeHouseBaseAddress }}"
                    -ExchangeHouseSettings__MaxAllowedBeneficiaryCount "${{ parameters.SettingExchangeHouseMaxAllowedBeneficiaryCount }}"
                    -MoneyTransferService__DirectTransferMaxBeneficiariesCount "20"
                    -MoneyTransferService__DirectTransferMinAmountToSend "1"
                    -MoneyTransferService__DirectTransferMaxAmountToSend "3002"
                    -MoneyTransferService__DirectTransferMaxAmountToSendPerMonth "3002"
                    -MoneyTransferService__DirectTransferFee "1.00"
                    -MoneyTransferService__DirectTransferVAT "0.05" 
                    -MoneyTransferService__ReversePendingDirectMoneyTransfersDurationInMin "2"
                    -MoneyTransferService__ReversePendingDirectMoneyTransfersSchedule "00:10:00"
                    -MoneyTransferService__ReverseFailedDirectMoneyTransfersSchedule "01:00:00"
                    -MoneyTransferService__ReversalMode "${{ parameters.SettingMoneyTransferReversalMode }}"
                    -MoneyTransferService__ReversalStartDate "${{ parameters.SettingMoneyTransferReversalStartDate }}"
                    -MoneyTransferService__MaxBeneficiaryRetryLimit "${{ parameters.SettingMoneyTransferMaxBeneficiaryRetryLimit }}"
                    -MoneyTransferService__RetryBeneficiaryDurationInMin "${{ parameters.SettingMoneyTransferRetryBeneficiaryDurationInMin }}"
                    -MoneyTransferService__EnableRakMock "${{ parameters.SettingMoneyTransferEnableRakMock}}"
                    -MoneyTransferService__EnableRakNegativeScenarioMock "${{ parameters.SettingMoneyTransferEnableRakNegativeScenarioMock}}"
                    -MoneyTransferService__ComparisonReceiveAmount "${{ parameters.SettingMoneyTransferComparisonReceiveAmount}}"
                    -MoneyTransferService__ComparisonEHTransferFee "${{ parameters.SettingMoneyTransferComparisonEHTransferFee}}"
                    -MoneyTransferService__ComparisonEHRateIncrement "${{ parameters.SettingMoneyTransferComparisonEHRateIncrement}}"
                    -MoneyTransferService__GWNationalities "${{ parameters.SettingMoneyTransferGWNationalities}}"
                    -MoneyTransferService__GWLanguages "${{ parameters.SettingMoneyTransferGWLanguages}}"
                    -MoneyTransferService__GWStartDate "${{ parameters.SettingMoneyTransferGWStartDate}}"
                    -MoneyTransferService__GeneralDefaultAmount "${{ parameters.SettingMoneyTransferGeneralDefaultAmount}}"
                    -MoneyTransferService__GeneralDefaultCurrency "${{ parameters.SettingMoneyTransferGeneralDefaultCurrency}}"
                    -MoneyTransferService__GeneralDefaultType "${{ parameters.SettingMoneyTransferGeneralDefaultType}}"
                    -MoneyTransferService__MaxRepeatTransferCount "${{ parameters.SettingMoneyTransferMaxRepeatTransferCount}}"
                    -MoneyTransferService__MinUserBalanceForRepeatTransfer "${{ parameters.SettingMoneyTransferMinUserBalanceForRepeatTransfer}}"
                    -MoneyTransferService__CorridorsCorporateId "${{ parameters.SettingMoneyTransferCorridorsCorporateId}}"
                    -MoneyTransferService__RateExpiryInMinutes "${{ parameters.SettingMoneyTransferRateExpiryInMinutes }}"
                    -MoneyTransferService__CheckMinSuspiciousDate "${{ parameters.SettingMoneyTransferCheckMinSuspiciousDate}}"
                    -Experiment__NoLoyaltyMaxCountIND "${{ parameters.SettingExperimentNoLoyaltyMaxCountIND}}"
                    -Experiment__NoLoyaltyMaxCountPHL "${{ parameters.SettingExperimentNoLoyaltyMaxCountPHL}}"
                    -Experiment__NoLoyaltyMaxCountNPL "${{ parameters.SettingExperimentNoLoyaltyMaxCountNPL}}"
                    -PaykiiService__BaseAddress "${{ parameters.SettingPaykiiServiceBaseAddress}}"
                    -PaykiiService__APIKey "${{ parameters.SettingPaykiiServiceAPIKey}}"
                    -PaykiiService__Token "${{ parameters.SettingPaykiiServiceToken}}"
                    -PaykiiService__CashierId "${{ parameters.SettingPaykiiServiceCashierId}}"
                    -PaykiiService__CustomerId "${{ parameters.SettingPaykiiServiceCustomerId}}" 
                    -PaykiiService__Urls__BillerCatalogUrl "${{ parameters.SettingPaykiiServiceBillerCatalogUrl}}" 
                    -PaykiiService__Urls__SKUCatalogUrl "${{ parameters.SettingPaykiiServiceSKUCatalogUrl}}" 
                    -PaykiiService__Urls__IOCatalogUrl "${{ parameters.SettingPaykiiServiceIOCatalogUrl}}"
                    -PaykiiService__Urls__AmountDueUrl "${{ parameters.SettingPaykiiServiceAmountDueUrl}}"
                    -PaykiiService__Urls__ProcessPaymentUrl "${{ parameters.SettingPaykiiServiceProcessPaymentUrl}}"
                    -PaykiiService__Urls__VerifyPaymentStatusUrl "${{ parameters.SettingPaykiiServiceVerifyPaymentStatusUrl}}"
                    -PaykiiService__Urls__BalanceUrl "${{ parameters.SettingPaykiiServiceBalanceUrl}}"
                    -PaykiiService__Urls__BillNotificationUrl "${{ parameters.SettingPaykiiServiceBillNotificationUrl}}"
                    -PaykiiService__Urls__MobileCarrierLookupUrl "${{ parameters.SettingPaykiiServiceMobileCarrierLookupUrl}}"
                    -PaykiiService__Urls__DailyFXRatePerBillerTypeUrl "${{ parameters.SettingPaykiiServiceDailyFXRatePerBillerTypeUrl}}"
                    -PaykiiService__Urls__BillerFeesCatalogUrl "${{ parameters.SettingPaykiiServiceBillerFeesCatalogUrl}}"
                    -PaykiiService__LocationId "${{ parameters.SettingPaykiiServiceLocationId}}"
                    -PaykiiService__PointOfSaleId "${{ parameters.SettingPaykiiServicePointOfSaleId}}"
                    -BillPayment__IconBaseUrl "${{ parameters.SettingBillPaymentIconBaseUrl}}"
                    -BillPayment__TransactionEnvironment "${{ parameters.SettingBillPaymentTransactionEnvironment}}"
                    -BillPayment__GridViewDisplayProviderIds "${{ parameters.SettingBillPaymentGridViewDisplayProviderIds}}" 
                    -BillPayment__MaximumLocalBillersLimit "${{ parameters.SettingBillPaymentMaximumLocalBillersLimit}}" 
                    -BillPayment__MaximumInternationalBillersLimit "${{ parameters.SettingBillPaymentMaximumInternationalBillersLimit}}" 
                    -BillPayment__MaximumAllowedBillAmountPerTransaction "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerTransaction}}" 
                    -BillPayment__MaximumAllowedBillAmountPerMonth "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerMonth}}"
                    -BillPayment__NolProviderCode "${{ parameters.SettingBillPaymentNolProviderCode}}" 
                    -BillPayment__AllowedNolAmountForNotVerified "${{ parameters.SettingBillPaymentAllowedNolAmountForNotVerified}}"
                    -BillPayment__IconContainerName "${{ parameters.SettingBillPaymentIconContainerName}}"
                    -BillPayment__AddBillerQueueName "${{ parameters.SettingBillPaymentAddBillerQueueName}}" 
                    -BillPayment__ProcessPaymentQueueName "${{ parameters.SettingBillPaymentProcessPaymentQueueName}}" 
                    -BillPayment__MockAddBillerQueueName "${{ parameters.SettingBillPaymentMockAddBillerQueueName}}" 
                    -BillPayment__MockProcessPaymentQueueName "${{ parameters.SettingBillPaymentMockProcessPaymentQueueName}}" 
                    -BillPayment__AmountDueExpiryHours "${{ parameters.SettingBillPaymentAmountDueExpiryHours}}"
                    -General__QAUserPhoneNumbers "${{ parameters.SettingGeneralQAUserPhoneNumbers}}" 
                    -General__IsMiddleNavExperimentActive "${{ parameters.SettingGeneralIsMiddleNavExperimentActive}}"
                    -General__IsSecuritySMSAwarenessActive "${{ parameters.SettingGeneralIsSecuritySMSAwarenessActive}}"
                    -General__AuthenticationTokenSecretKey "${{ parameters.SettingGeneralAuthenticationTokenSecretKey}}"
                    -General__UATPentestPhoneNumbers "${{ parameters.SettingGeneralUATPentestPhoneNumbers}}"
                    -General__IsDbSaveRetryEnabled "${{ parameters.SettingGeneralIsDbSaveRetryEnabled}}"
                    -MoneyTransferService__WUTransactionMinLimitValidationEnabled "${{ parameters.SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled}}"
                    -MoneyTransferService__NonWUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__WUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__NonWUCorridors "${{ parameters.SettingMoneyTransferServiceNonWUCorridors}}"
                    -MoneyTransferService__RMTStatusFromCreatedToPendingEnabled "${{ parameters.SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled}}"
                    -MoneyTransferService__LastRaffleWinnerName "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerName}}"
                    -MoneyTransferService__LastRaffleWinnerTicketNumber "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerTicketNumber}}"
                    -MoneyTransferService__RaffleDateString "${{ parameters.SettingMoneyTransferServiceRaffleDateString}}"
                    -General__IsSecuritySMSMigrationActive "${{ parameters.SettingGeneralIsSecuritySMSMigrationActive}}"
                    -General__MaxDevicesAllowedForBinding "${{ parameters.SettingGeneralMaxDevicesAllowedForBinding}}" 
                    -General__DeviceIdBindingPrefix "${{ parameters.SettingGeneralDeviceIdBindingPrefix}}"  
                    -General__QAAutomationPhoneNumbers "${{ parameters.SettingGeneralQAAutomationPhoneNumbers}}" 
                    -General__PrimarySmsProvider "${{ parameters.SettingGeneralPrimarySmsProvider}}"
                    -General__TestKey "${{ parameters.SettingGeneralTestKey}}"
                    -General__EnableRedis "${{ parameters.SettingGeneralEnableRedis}}"
                    -BillPayment__RemoveCountrywiseFilter false
                    -Swagger__Username "${{ parameters.SettingSwaggerUsername }}"
                    -Swagger__Password "${{ parameters.SettingSwaggerPassword }}" 
                    -TransactionsB2CService__GrantType "${{ parameters.SettingTransactionsB2CServiceGrantType }}"
                    -TransactionsB2CService__APIVersion "${{ parameters.SettingTransactionsB2CServiceAPIVersion }}"
                    -TransactionsB2CService__Timeout "${{ parameters.SettingTransactionsB2CServiceTimeout }}"
                    -PPSService__Timeout "${{ parameters.SettingPPSTimeout }}"
                    -ESMOService__Timeout "${{ parameters.SettingESMOServiceTimeout }}"
                    -Language__AudioLinks__0__URL "https://cdn.edenred.ae/salary-advance-audios/eligibility-hindi-audio-v1.mp3"
                    -Language__AudioLinks__0__Nationality "IND"
                    -Language__AudioLinks__1__URL "https://cdn.edenred.ae/salary-advance-audios/eligibility-hindi-audio-v1.mp3"
                    -Language__AudioLinks__1__Nationality "PAK"
                    -Rating__MinimumDaysToShowInApp "${{ parameters.SettingRatingMinimumDaysToShowInApp}}"
                    -Rating__MinimumDaysToShowStore "${{ parameters.SettingRatingMinimumDaysToShowStore}}" 
                    -HRService__CacheInMinutes "${{ parameters.SettingHRServiceCacheInMinutes}}"
                    -Store__EmailRecepients "${{ parameters.SettingStoreEmailRecepients}}" 
                    -UnEmploymentInsurance__ServiceBusTopicName "${{ parameters.SettingUnEmploymentInsuranceServiceBusTopicName}}"
                    -UnEmploymentInsurance__ServiceBusSubscriptionName "${{ parameters.SettingUnEmploymentInsuranceServiceBusSubscriptionName}}"
                    -DingService__RetryCount "${{ parameters.SettingDingServiceRetryCount }}"
                    -DingService__SleepDuration "${{ parameters.SettingDingServiceSleepDuration }}"
                    -DingService__IsRetryEnabled "${{ parameters.SettingDingServiceIsRetryEnabled }}"
                    -Testing__MRDynamicPackageTestNepalNumbers "${{ parameters.SettingTestingMRDynamicPackageTestNepalNumbers }}"
                    -Testing__MRInlineFeeCalculationTestNumbers "${{ parameters.SettingTestingMRInlineFeeCalculationTestNumbers }}"
                    -RakBankMoneyTransfer__BaseUrl "${{ parameters.SettingRakBankMoneyTransferBaseUrl }}"
                    -RakBankMoneyTransfer__UrlPath "${{ parameters.SettingRakBankMoneyTransferUrlPath }}"
                    -RakBankMoneyTransfer__ClientId "${{ parameters.SettingRakBankMoneyTransferClientId }}"
                    -RakBankMoneyTransfer__ClientSecretkey "${{ parameters.SettingRakBankMoneyTransferClientSecretkey }}"
                    -RakBankMoneyTransfer__SslCertificateName "${{ parameters.SettingRakBankMoneyTransferSslCertificateName }}"
                    -RakBankMoneyTransfer__SslCertificatePassword "${{ parameters.SettingRakBankMoneyTransferSslCertificatePassword }}"
                    -RakBankMoneyTransfer__PayloadPrivateKey "${{ parameters.SettingRakBankMoneyTransferPayloadPrivateKey }}"
                    -RakBankMoneyTransfer__PayloadPublicKey "${{ parameters.SettingRakBankMoneyTransferPayloadPublicKey }}"
                    -RakBankMoneyTransfer__TokenGrantType "${{ parameters.SettingRakBankMoneyTransferTokenGrantType }}"
                    -RakBankMoneyTransfer__TokenScope "${{ parameters.SettingRakBankMoneyTransferTokenScope }}"
                    -RakBankMoneyTransfer__ContentType "${{ parameters.SettingRakBankMoneyTransferContentType }}"
                    -RakBankMoneyTransfer__X509Certificate2Bytes "${{ parameters.SettingRakBankMoneyTransferX509Certificate2Bytes }}"
                    -EncryptionSettings__IsActive "${{ parameters.SettingEncryptionSettingsIsActive }}"
                    -EncryptionSettings__PrivateKey "${{ parameters.SettingEncryptionSettingsPrivateKey }}"
                    -EncryptionSettings__PublicKey "${{ parameters.SettingEncryptionSettingsPublicKey }}"
                    -RenewalCardUpdate__ServiceBusTopicName "${{ parameters.SettingRenewalCardUpdateServiceBusTopicName}}" 
                    -RenewalCardUpdate__ServiceBusSubscriptionName "${{ parameters.SettingRenewalCardUpdateServiceBusSubscriptionName}}"
                    -C3PayPlusMembership__LuckyDrawSchedule "${{ parameters.SettingC3PayPlusMembershipLuckyDrawSchedule }}"
                    -C3PayPlusMembership__RenewalSchedule "${{ parameters.SettingC3PayPlusMembershipRenewalSchedule }}"
                    -C3PayPlusMembership__AllowedPhoneNumbers "${{ parameters.SettingC3PayPlusMembershipAllowedPhoneNumbers }}"
                    -C3PayPlusMembership__TargetedDiscountCooldownDays "${{ parameters.SettingC3PayPlusMembershipTargetedDiscountCooldownDays }}"
                    -SanctionScreeningApi__Address "${{ parameters.SettingSanctionScreeningApiAddress }}"
                    -RewardService__BaseAddress "${{ parameters.SettingRewardServiceBaseAddress }}"
                    -RewardService__Timeout "${{ parameters.SettingRewardServiceTimeout }}"
                    -RewardService__TestAccountUsernames "${{ parameters.SettingRewardServiceTestAccountUsernames }}""'

      enableXmlVariableSubstitution: true

  - task: AzureRmWebAppDeployment@4
    displayName: 'Portal WebApp: Deploy Package'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      WebAppName: '${{ parameters.portalWebAppName }}'
      packageForLinux: '${{ parameters.portalWebAppBinariesFile }}'
      ResourceGroupName: '${{ parameters.rgName }}'
      DeployToSlotOrASEFlag : true
      SlotName: 'staging'
      AppSettings: '-APPINSIGHTS_INSTRUMENTATIONKEY $(instrumentationKey)
                    -ASPNETCORE_ENVIRONMENT "Production" 
                    -WEBSITE_HEALTHCHECK_MAXPINGFAILURES 3
                    -ApplicationInsightsAgent_EXTENSION_VERSION ~2
                    -WEBSITE_LOAD_USER_PROFILE 1
                    -WEBSITE_TIME_ZONE "Arabian Standard Time"
                    -General__EnableSwagger "${{ parameters.SettingEnableSwagger }}"  
                    -KeyVault__Authority $(keyvaultUri)
                    -KeyVault__KeyIdentifier $(KeyvalueId)
                    -EdenredIdentityManager__Tenant "AE"
                    -PortalEDConnect__EnableCaching true
                    -PortalEDConnect__CacheDurationInMinutes 60
                    -PPSService__WebAuthContentType "application/xml"
                    -EtisalatSMS__BaseAddress "${{ parameters.SettingEtisalatSMSBaseAddress }}"
                    -EtisalatSMS__Timeout "${{ parameters.SettingEtisalatSMSTimeout }}"
                    -EtisalatSMS__RetryCount "${{ parameters.SettingEtisalatSMSRetryCount }}"
                    -EtisalatSMS__SenderName "C3Pay"
                    -EtisalatSMS__ContentType "application/json"
                    -InfobipSMS__BaseAddress "${{ parameters.SettingInfobipSMSBaseAddress }}"
                    -InfobipSMS__Timeout "${{ parameters.SettingInfobipSMSTimeout }}"
                    -InfobipSMS__RetryCount "${{ parameters.SettingInfobipSMSRetryCount }}"
                    -InfobipSMS__SenderName "C3Pay"
                    -InfobipSMS__ContentType "application/json"
                    -InfobipSMS__AuthKeyBaseUrl "${{ parameters.SettingInfobipSMSAuthKeyBaseUrl }}"
                    -InfobipSMS__SmsMode "${{ parameters.SettingInfobipSMSSmsMode }}"
                    -KYCService__MethodName "ApproveKYC" 
                    -FirebaseCloudMessaging__BaseAddress "${{ parameters.SettingFirebaseCloudMessagingBaseAddress }}"
                    -FirebaseCloudMessaging__RetryCount "${{ parameters.SettingFirebaseCloudMessagingRetryCount }}"                    
                    -FirebaseCloudMessaging__Timeout "${{ parameters.SettingFirebaseCloudMessagingTimeout }}"                                       
                    -SignzyService__AcceptedSelfieScore 0.5
                    -SignzyService__AcceptedEIDScore 0.5
                    -SignzyService__IdealFaceMatchPercentage 60
                    -SignzyService__IdealNameMatchPercentage 75
                    -SignzyService__TTL "1 month"
                    -SignzyService__MimeType "image/png"
                    -SignzyService__ContentType "application/json"
                    -SignzyService__ImageQualityTimeout "${{ parameters.SettingSignzyImageQualityTimeout }}"
                    -SignzyService__FaceMatchTimeout "${{ parameters.SettingSignzyFaceMatchTimeout }}"
                    -SignzyService__ReadDocumentTimeout "${{ parameters.SettingSignzyReadDocumentTimeout }}"
                    -AzureOCR__Endpoint "${{ parameters.SettingAzureOCREndpoint }}"
                    -AzureOCR__WaitTimeInMilliSeconds "${{ parameters.SettingAzureOCRWaitTimeInMilliSeconds }}"
                    -AzureFace__Endpoint "${{ parameters.SettingAzureFaceEndpoint }}"
                    -AzureIdentificationService__UseAzureOCR "${{ parameters.SettingAzureIdentificationServiceUseAzureOCR }}"
                    -AzureIdentificationService__UseAzureFace "${{ parameters.SettingAzureIdentificationServiceUseAzureFace }}"
                    -AzureIdentificationService__MaxDifferenceInDigitsForRecheck "${{ parameters.SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck }}"
                    -KycExpiry__CheckStartDate "${{ parameters.SettingKycExpiryCheckStartDate }}"
                    -KycExpiry__ValidateEmiratesIdExpiryScheduler "${{ parameters.SettingKycExpiryValidateEmiratesIdExpiryScheduler }}"
                    -KycExpiry__ValidateAdditionKYCScheduler "${{ parameters.SettingKycExpiryValidateAdditionKYCScheduler }}"
                    -MoneyTransferService__EnableBeneficiarySameCountryDelay false
                    -SendGrid__SenderEmail "${{ parameters.SettingSendGridSenderEmail }}"
                    -SendGrid__Templates__2__TemplateId "${{ parameters.SettingPortalAdminPasswordResetEmailTemplateId }}"
                    -SendGrid__Templates__1__TemplateId "${{ parameters.SettingSendGridPortalUserCreatedEmailTemplateId }}"
                    -SendGrid__Templates__0__TemplateId "${{ parameters.SettingSendGridCardHolderRegistrationRejectedEmailTemplateId }}"
                    -RAKService__ContentType "application/json"
                    -RAKService__TokenContentType "application/x-www-form-urlencoded"
                    -RAKService__TokenScope "utility_other_banks utility_fxrate beneficiary_management send_money fetch_fields"
                    -RAKService__TokenGrantType "client_credentials"
                    -RAKService__SSLCertificateName "RAKServiceSSL"
                    -RAKService__BanksMaxRecords "${{ parameters.SettingRakBanksMaxRecords }}"
                    -RAKService__MaxTransactionTriesCount "${{ parameters.SettingRakMaxTransactionTriesCount }}"
                    -RAKService__MessageProcessInDelay "${{ parameters.SettingRakMessageProcessInDelay }}"
                    -RAKService__MoneyTransferBeneficiaryDelayInMins "${{ parameters.SettingRakMoneyTransferBeneficiaryDelayInMins }}"
                    -RAKService__LoyaltyImplementDate "${{ parameters.SettingRakLoyaltyImplementDate }}"
                    -RAKService__LoyaltyLimitCount "${{ parameters.SettingRakLoyaltyLimitCount }}"
                    -RAKService__LoyaltyLimitAmount "${{ parameters.SettingRakLoyaltyLimitAmount }}"
                    -RAKService__URLPath "${{ parameters.SettingRakURLPath }}"
                    -RAKService__MoneyTransferBeneficiaryCount "${{ parameters.SettingRakMoneyTransferBeneficiaryCount }}"
                    -RAKService__EnableRakTokenCache "${{ parameters.SettingRakEnableRakTokenCache }}"
                    -ReferralProgramService__MoneyTransferCountThreshold "${{ parameters.SettingReferralProgramMoneyTransferCountThreshold }}"
                    -ReferralProgramService__MoneyTransferAmountThreshold "${{ parameters.SettingReferralProgramMoneyTransferAmountThreshold }}"
                    -ReferralProgramService__MoneyTransferRewardAmount "${{ parameters.SettingReferralProgramMoneyTransferRewardAmount }}"
                    -ReferralProgramService__MoneyTransferReferralProgramStartDate "${{ parameters.SettingReferralProgramMoneyTransferReferralProgramStartDate }}"
                    -General__FirstBlackV1PlasticCardId "${{ parameters.SettingFirstBlackV1PlasticCardId }}"
                    -General__FirstBlackV2PlasticCardId "${{ parameters.SettingFirstBlackV2PlasticCardId }}"
                    -CleverTapService__BaseAddress "${{ parameters.SettingCleverTapBaseAddress }}"
                    -MoneyTransferService__DirectTransferMaxBeneficiariesCount "20"
                    -MoneyTransferService__DirectTransferMinAmountToSend "1"
                    -MoneyTransferService__DirectTransferMaxAmountToSend "3002"
                    -MoneyTransferService__DirectTransferMaxAmountToSendPerMonth "3002"
                    -MoneyTransferService__DirectTransferFee "1.00"
                    -MoneyTransferService__DirectTransferVAT "0.05"
                    -MoneyTransferService__ReversalMode "${{ parameters.SettingMoneyTransferReversalMode }}"
                    -MoneyTransferService__ReversalStartDate "${{ parameters.SettingMoneyTransferReversalStartDate }}"
                    -MoneyTransferService__ReversePendingDirectMoneyTransfersDurationInMin "2"
                    -MoneyTransferService__RateExpiryInMinutes "${{ parameters.SettingMoneyTransferRateExpiryInMinutes }}"
                    -MoneyTransferService__CheckMinSuspiciousDate "${{ parameters.SettingMoneyTransferCheckMinSuspiciousDate}}"
                    -MoneyTransferService__EnableRakMock "${{ parameters.SettingMoneyTransferEnableRakMock}}"
                    -MoneyTransferService__EnableRakNegativeScenarioMock "${{ parameters.SettingMoneyTransferEnableRakNegativeScenarioMock}}"
                    -Experiment__NoLoyaltyMaxCountIND "${{ parameters.SettingExperimentNoLoyaltyMaxCountIND}}"
                    -Experiment__NoLoyaltyMaxCountPHL "${{ parameters.SettingExperimentNoLoyaltyMaxCountPHL}}"
                    -Experiment__NoLoyaltyMaxCountNPL "${{ parameters.SettingExperimentNoLoyaltyMaxCountNPL}}"
                    -PaykiiService__BaseAddress "${{ parameters.SettingPaykiiServiceBaseAddress}}"
                    -PaykiiService__APIKey "${{ parameters.SettingPaykiiServiceAPIKey}}"
                    -PaykiiService__Token "${{ parameters.SettingPaykiiServiceToken}}"
                    -PaykiiService__CashierId "${{ parameters.SettingPaykiiServiceCashierId}}"
                    -PaykiiService__CustomerId "${{ parameters.SettingPaykiiServiceCustomerId}}" 
                    -PaykiiService__Urls__BillerCatalogUrl "${{ parameters.SettingPaykiiServiceBillerCatalogUrl}}" 
                    -PaykiiService__Urls__SKUCatalogUrl "${{ parameters.SettingPaykiiServiceSKUCatalogUrl}}" 
                    -PaykiiService__Urls__IOCatalogUrl "${{ parameters.SettingPaykiiServiceIOCatalogUrl}}"
                    -PaykiiService__Urls__AmountDueUrl "${{ parameters.SettingPaykiiServiceAmountDueUrl}}"
                    -PaykiiService__Urls__ProcessPaymentUrl "${{ parameters.SettingPaykiiServiceProcessPaymentUrl}}"
                    -PaykiiService__Urls__VerifyPaymentStatusUrl "${{ parameters.SettingPaykiiServiceVerifyPaymentStatusUrl}}"
                    -PaykiiService__Urls__BalanceUrl "${{ parameters.SettingPaykiiServiceBalanceUrl}}"
                    -PaykiiService__Urls__BillNotificationUrl "${{ parameters.SettingPaykiiServiceBillNotificationUrl}}"
                    -PaykiiService__Urls__MobileCarrierLookupUrl "${{ parameters.SettingPaykiiServiceMobileCarrierLookupUrl}}"
                    -PaykiiService__Urls__DailyFXRatePerBillerTypeUrl "${{ parameters.SettingPaykiiServiceDailyFXRatePerBillerTypeUrl}}"
                    -PaykiiService__Urls__BillerFeesCatalogUrl "${{ parameters.SettingPaykiiServiceBillerFeesCatalogUrl}}"
                    -PaykiiService__RefreshDataIntervalCronExpression "${{ parameters.SettingPaykiiServiceRefreshDataIntervalCronExpression}}"
                    -PaykiiService__LocationId "${{ parameters.SettingPaykiiServiceLocationId}}"
                    -PaykiiService__PointOfSaleId "${{ parameters.SettingPaykiiServicePointOfSaleId}}"
                    -BillPayment__IconBaseUrl "${{ parameters.SettingBillPaymentIconBaseUrl}}"
                    -BillPayment__PendingBillsRefreshIntervalCronExpression "${{ parameters.SettingBillPaymentPendingBillsRefreshIntervalCronExpression}}"
                    -BillPayment__TransactionEnvironment "${{ parameters.SettingBillPaymentTransactionEnvironment}}"
                    -BillPayment__FxRateRefreshIntervalCronExpression "${{ parameters.SettingBillPaymentFxRateRefreshIntervalCronExpression}}" 
                    -BillPayment__GridViewDisplayProviderIds "${{ parameters.SettingBillPaymentGridViewDisplayProviderIds}}"  
                    -BillPayment__MaximumLocalBillersLimit "${{ parameters.SettingBillPaymentMaximumLocalBillersLimit}}" 
                    -BillPayment__MaximumInternationalBillersLimit "${{ parameters.SettingBillPaymentMaximumInternationalBillersLimit}}" 
                    -BillPayment__MaximumAllowedBillAmountPerTransaction "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerTransaction}}" 
                    -BillPayment__MaximumAllowedBillAmountPerMonth "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerMonth}}"
                    -BillPayment__NolProviderCode "${{ parameters.SettingBillPaymentNolProviderCode}}"  
                    -BillPayment__AllowedNolAmountForNotVerified "${{ parameters.SettingBillPaymentAllowedNolAmountForNotVerified}}"
                    -BillPayment__IconContainerName "${{ parameters.SettingBillPaymentIconContainerName}}" 
                    -BillPayment__AddBillerQueueName "${{ parameters.SettingBillPaymentAddBillerQueueName}}" 
                    -BillPayment__ProcessPaymentQueueName "${{ parameters.SettingBillPaymentProcessPaymentQueueName}}" 
                    -BillPayment__MockAddBillerQueueName "${{ parameters.SettingBillPaymentMockAddBillerQueueName}}" 
                    -BillPayment__MockProcessPaymentQueueName "${{ parameters.SettingBillPaymentMockProcessPaymentQueueName}}" 
                    -BillPayment__AmountDueExpireIntervalCronExpression "${{ parameters.SettingBillPaymentAmountDueExpireIntervalCronExpression}}" 
                    -BillPayment__AmountDueExpiryHours "${{ parameters.SettingBillPaymentAmountDueExpiryHours}}"  
                    -General__QAUserPhoneNumbers "${{ parameters.SettingGeneralQAUserPhoneNumbers}}" 
                    -General__PrimarySmsProvider "${{ parameters.SettingGeneralPrimarySmsProvider}}" 
                    -General__TestKey "${{ parameters.SettingGeneralTestKey}}"
                    -General__EnableRedis "${{ parameters.SettingGeneralEnableRedis}}"
                    -General__IsDbSaveRetryEnabled "${{ parameters.SettingGeneralIsDbSaveRetryEnabled}}" 
                    -MoneyTransferService__WUTransactionMinLimitValidationEnabled "${{ parameters.SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled}}"
                    -MoneyTransferService__NonWUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__WUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__NonWUCorridors "${{ parameters.SettingMoneyTransferServiceNonWUCorridors}}"
                    -MoneyTransferService__RMTStatusFromCreatedToPendingEnabled "${{ parameters.SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled}}"
                    -MoneyTransferService__LastRaffleWinnerName "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerName}}"
                    -MoneyTransferService__LastRaffleWinnerTicketNumber "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerTicketNumber}}"
                    -MoneyTransferService__RaffleDateString "${{ parameters.SettingMoneyTransferServiceRaffleDateString}}"
                    -BillPayment__RemoveCountrywiseFilter false
                    -Swagger__Username "${{ parameters.SettingSwaggerUsername }}"
                    -Swagger__Password "${{ parameters.SettingSwaggerPassword }}"
                    -RakBankMoneyTransfer__BaseUrl "${{ parameters.SettingRakBankMoneyTransferBaseUrl }}"
                    -RakBankMoneyTransfer__UrlPath "${{ parameters.SettingRakBankMoneyTransferUrlPath }}"
                    -RakBankMoneyTransfer__ClientId "${{ parameters.SettingRakBankMoneyTransferClientId }}"
                    -RakBankMoneyTransfer__ClientSecretkey "${{ parameters.SettingRakBankMoneyTransferClientSecretkey }}"
                    -RakBankMoneyTransfer__SslCertificateName "${{ parameters.SettingRakBankMoneyTransferSslCertificateName }}"
                    -RakBankMoneyTransfer__SslCertificatePassword "${{ parameters.SettingRakBankMoneyTransferSslCertificatePassword }}"
                    -RakBankMoneyTransfer__PayloadPrivateKey "${{ parameters.SettingRakBankMoneyTransferPayloadPrivateKey }}"
                    -RakBankMoneyTransfer__PayloadPublicKey "${{ parameters.SettingRakBankMoneyTransferPayloadPublicKey }}"
                    -RakBankMoneyTransfer__TokenGrantType "${{ parameters.SettingRakBankMoneyTransferTokenGrantType }}"
                    -RakBankMoneyTransfer__TokenScope "${{ parameters.SettingRakBankMoneyTransferTokenScope }}"
                    -RakBankMoneyTransfer__ContentType "${{ parameters.SettingRakBankMoneyTransferContentType }}"
                    -RakBankMoneyTransfer__X509Certificate2Bytes "${{ parameters.SettingRakBankMoneyTransferX509Certificate2Bytes }}"
                    -C3PayPlusMembership__LuckyDrawSchedule "${{ parameters.SettingC3PayPlusMembershipLuckyDrawSchedule }}"
                    -C3PayPlusMembership__RenewalSchedule "${{ parameters.SettingC3PayPlusMembershipRenewalSchedule }}"
                    -C3PayPlusMembership__AllowedPhoneNumbers "${{ parameters.SettingC3PayPlusMembershipAllowedPhoneNumbers }}"
                    -C3PayPlusMembership__TargetedDiscountCooldownDays "${{ parameters.SettingC3PayPlusMembershipTargetedDiscountCooldownDays }}"
                    -SanctionScreeningApi__Address "${{ parameters.SettingSanctionScreeningApiAddress }}"
                    -RewardService__BaseAddress "${{ parameters.SettingRewardServiceBaseAddress }}"
                    -RewardService__Timeout "${{ parameters.SettingRewardServiceTimeout }}"
                    -RewardService__TestAccountUsernames "${{ parameters.SettingRewardServiceTestAccountUsernames }}"'
    
      enableXmlVariableSubstitution: true

  - task: AzureRmWebAppDeployment@4
    displayName: 'WebJob: Deploy Package'
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      WebAppName: '${{ parameters.webJobName }}'
      packageForLinux: '${{ parameters.webJobBinariesFile }}'
      ResourceGroupName: '${{ parameters.rgName }}'
      DeployToSlotOrASEFlag : true
      SlotName: 'staging'
      AppSettings: '-APPINSIGHTS_INSTRUMENTATIONKEY $(instrumentationKey) 
                    -ASPNETCORE_ENVIRONMENT "Production"
                    -ApplicationInsightsAgent_EXTENSION_VERSION ~2
                    -WEBSITE_LOAD_USER_PROFILE 1
                    -WEBSITE_TIME_ZONE "Arabian Standard Time"
                    -General__EnableSwagger "${{ parameters.SettingEnableSwagger }}"  
                    -KeyVault__Authority $(keyvaultUri)
                    -KeyVault__KeyIdentifier $(KeyvalueId)
                    -EdenredIdentityManager__Tenant "AE"
                    -EtisalatSMS__BaseAddress "${{ parameters.SettingEtisalatSMSBaseAddress }}"
                    -EtisalatSMS__Timeout "${{ parameters.SettingEtisalatSMSTimeout }}"
                    -EtisalatSMS__RetryCount "${{ parameters.SettingEtisalatSMSRetryCount }}"
                    -EtisalatSMS__SenderName "C3Pay"
                    -EtisalatSMS__ContentType "application/json"
                    -InfobipSMS__BaseAddress "${{ parameters.SettingInfobipSMSBaseAddress }}"
                    -InfobipSMS__Timeout "${{ parameters.SettingInfobipSMSTimeout }}"
                    -InfobipSMS__RetryCount "${{ parameters.SettingInfobipSMSRetryCount }}"
                    -InfobipSMS__SenderName "C3Pay"
                    -InfobipSMS__ContentType "application/json"
                    -InfobipSMS__AuthKeyBaseUrl "${{ parameters.SettingInfobipSMSAuthKeyBaseUrl }}"
                    -InfobipSMS__SmsMode "${{ parameters.SettingInfobipSMSSmsMode }}"
                    -KYCService__MethodName "ApproveKYC"
                    -PPSService__WebAuthContentType "application/xml"
                    -MobileRechargeService__SynchronizeWithDingSchedule "${{ parameters.SettingMobileRechargeSynchronizeWithDingSchedule }}"
                    -MobileRechargeService__UpdateStatusSchedule "${{ parameters.SettingMobileRechargeUpdateStatusSchedule }}"
                    -MobileRechargeService__NickNameLength "${{ parameters.SettingMobileRechargeNickNameLength }}"
                    -MobileRechargeService__CallingCardAccountNumberLive "${{ parameters.SettingMobileRechargeCallingCardAccountNumberLive }}"
                    -MobileRechargeService__TransactionEnvironment "${{ parameters.SettingMobileRechargeTransactionEnvironment }}"
                    -MobileRechargeService__C3FeeMode "${{ parameters.SettingMobileRechargeC3FeeMode }}"
                    -MobileRechargeService__MySalaryFeeMode "${{ parameters.SettingMobileRechargeMySalaryFeeMode }}"
                    -MobileRechargeService__SelectedCorporatesWithFee "${{ parameters.SettingMobileRechargeSelectedCorporatesWithFee }}"
                    -MobileRechargeService__FeeAmount "${{ parameters.SettingMobileRechargeFeeAmount }}"
                    -MobileRechargeService__CustomCallingCardName "${{ parameters.SettingMobileRechargeCustomCallingCardName }}"
                    -MobileRechargeService__CustomCallingCardCode "${{ parameters.SettingMobileRechargeCustomCallingCardCode }}"
                    -MobileRechargeService__CustomCallingCardLogoUrl "${{ parameters.SettingMobileRechargeCustomCallingCardLogoUrl }}"
                    -MobileRechargeService__CustomCallingCardValidationRegex "${{ parameters.SettingMobileRechargeCustomCallingCardValidationRegex }}"
                    -MobileRechargeService__MonthlyAmountLimitVerified "${{ parameters.SettingMobileRechargeVerifiedLimit }}"
                    -MobileRechargeService__MonthlyAmountLimitNotVerified "${{ parameters.SettingMobileRechargeNonVerifiedLimit }}"
                    -MobileRechargeService__DynamicPackageSeperator "${{ parameters.SettingMobileRechargeDynamicPackageSeperator }}"
                    -MobileRechargeService__ServiceBusTopicName "${{ parameters.SettingMobileRechargeServiceServiceBusTopicName}}" 
                    -MobileRechargeService__ServiceBusSubscriptionName "${{ parameters.SettingMobileRechargeServiceServiceBusSubscriptionName}}"
                    -MobileRechargeService__RenewalSchedule "${{ parameters.SettingMobileRechargeServiceRenewalSchedule}}"
                    -VpnMembershipService__RenewalSchedule "${{ parameters.SettingVpnMembershipRenewalSchedule}}"
                    -KycExpiry__CheckStartDate "${{ parameters.SettingKycExpiryCheckStartDate }}"
                    -KycExpiry__ValidateEmiratesIdExpiryScheduler "${{ parameters.SettingKycExpiryValidateEmiratesIdExpiryScheduler }}"
                    -KycExpiry__ValidateAdditionKYCScheduler "${{ parameters.SettingKycExpiryValidateAdditionKYCScheduler }}"
                    -MoneyTransferService__EnableBeneficiarySameCountryDelay false
                    -MoneyTransferService__RefreshBanksAndBranchesSchedule "${{ parameters.SettingMoneyTransferRefreshBanksAndBranchesSchedule }}"
                    -SendGrid__SenderEmail "${{ parameters.SettingSendGridSenderEmail }}"
                    -SendGrid__Templates__0__TemplateId "${{ parameters.SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId }}"
                    -FirebaseCloudMessaging__BaseAddress "${{ parameters.SettingFirebaseCloudMessagingBaseAddress }}"
                    -FirebaseCloudMessaging__RetryCount "${{ parameters.SettingFirebaseCloudMessagingRetryCount }}"                    
                    -FirebaseCloudMessaging__Timeout "${{ parameters.SettingFirebaseCloudMessagingTimeout }}"                    
                    -DingService__ContentType "application/json"
                    -RAKService__ContentType "application/json"
                    -RAKService__TokenContentType "application/x-www-form-urlencoded"
                    -RAKService__TokenScope "utility_other_banks utility_fxrate beneficiary_management send_money fetch_fields"
                    -RAKService__TokenGrantType "client_credentials"
                    -RAKService__SSLCertificateName "RAKServiceSSL"                    
                    -RAKService__ProcessBankTransactionsReportsSchedule "${{ parameters.SettingRakProcessBankTransactionsReportsSchedule }}"
                    -RAKService__UpdatePendingBankTransactionsSchedule "${{ parameters.SettingRakUpdatePendingBankTransactionsSchedule }}"
                    -RAKService__ReverseFailedBankTransactionsSchedule "${{ parameters.SettingRakReverseFailedBankTransactionsSchedule }}"
                    -RAKService__ProcessRakStatementSchedule "${{ parameters.SettingRakProcessRakStatementSchedule }}"
                    -RAKService__ProcessRmtSubmissionSchedule "${{ parameters.SettingRakProcessRmtSubmissionSchedule }}"
                    -RAKService__ProcessRmtAcknowledgementSchedule "${{ parameters.SettingRakProcessRmtAcknowledgementSchedule }}"
                    -RAKService__UploadMissingProfilesImagesSchedule "${{ parameters.SettingUploadMissingProfilesImagesSchedule }}"
                    -RAKService__BanksMaxRecords "${{ parameters.SettingRakBanksMaxRecords }}"
                    -RAKService__MaxTransactionTriesCount "${{ parameters.SettingRakMaxTransactionTriesCount }}"
                    -RAKService__MessageProcessInDelay "${{ parameters.SettingRakMessageProcessInDelay }}"
                    -RAKService__TransactionUpdateDelay "${{ parameters.SettingRakTransactionUpdateDelay }}"
                    -RAKService__MoneyTransferBeneficiaryDelayInMins "${{ parameters.SettingRakMoneyTransferBeneficiaryDelayInMins }}"
                    -RAKService__RmtProfilePositiveStatus "${{ parameters.SettingRakRmtProfilePositiveStatus }}"
                    -RAKService__LoyaltyImplementDate "${{ parameters.SettingRakLoyaltyImplementDate }}"
                    -RAKService__LoyaltyLimitCount "${{ parameters.SettingRakLoyaltyLimitCount }}"
                    -RAKService__LoyaltyLimitAmount "${{ parameters.SettingRakLoyaltyLimitAmount }}"
                    -RAKService__URLPath "${{ parameters.SettingRakURLPath }}"
                    -RAKService__MoneyTransferBeneficiaryCount "${{ parameters.SettingRakMoneyTransferBeneficiaryCount }}"
                    -RAKService__EnableRakTokenCache "${{ parameters.SettingRakEnableRakTokenCache }}"
                    -RAKService__RefreshRatesSchedule "${{ parameters.SettingRakRefreshRatesSchedule }}"    
                    -ReferralProgramService__MoneyTransferCountThreshold "${{ parameters.SettingReferralProgramMoneyTransferCountThreshold }}"
                    -ReferralProgramService__MoneyTransferAmountThreshold "${{ parameters.SettingReferralProgramMoneyTransferAmountThreshold }}"
                    -ReferralProgramService__MoneyTransferRewardAmount "${{ parameters.SettingReferralProgramMoneyTransferRewardAmount }}"
                    -ReferralProgramService__MoneyTransferReferralProgramStartDate "${{ parameters.SettingReferralProgramMoneyTransferReferralProgramStartDate }}"
                    -ExchangeHouseSettings__UpdateStatusSchedule "${{ parameters.SettingExchangeHouseUpdateStatusSchedule }}"                    
                    -ExchangeHouseSettings__RefreshRatesSchedule "${{ parameters.SettingExchangeHouseRefreshRatesSchedule }}"    
                    -RAKService__ReadRMTProfileResponsesSchedule "${{ parameters.SettingRakReadRMTProfileResponsesSchedule }}"
                    -RAKSFTPConnection__InputRootDirectory "${{ parameters.SettingRAKSFTPInputRootDirectory }}"
                    -RAKSFTPConnection__OutputRootDirectory "${{ parameters.SettingRAKSFTPOutputRootDirectory }}"
                    -RAKSFTPConnection__TransactionStatusDirectory "${{ parameters.SettingRAKSFTPTransactionStatusDirectory }}"
                    -RAKSFTPConnection__TransactionBlobContainerName "${{ parameters.SettingRAKSFTPTransactionBlobContainerName }}"
                    -RAKSFTPConnection__ProfileStatusDirectory "${{ parameters.SettingRAKSFTPProfileStatusDirectory }}"
                    -RAKSFTPConnection__RMTProfileResponsesDirectory "${{ parameters.SettingRakSftpRMTProfileResponsesDirectory }}"
                    -RAKSFTPConnection__MissingRakFileAlertPhoneNumbers "${{ parameters.SettingRakSftpMissingRakFileAlertPhoneNumbers }}"
                    -RAKSFTPConnection__BranchesDirectory "${{ parameters.SettingRAKSFTPBranchesDirectory }}"
                    -RAKSFTPConnection__BranchesArchiveDirectory "${{ parameters.SettingRAKSFTPBranchesArchiveDirectory }}"
                    -RAKSFTPConnection__ReportDirectory "${{ parameters.SettingRakSftpReportDirectory }}"
                    -RAKSFTPConnection__RmtAcknowledgementDirectory "${{ parameters.SettingRakSftpRmtAcknowledgementDirectory }}"
                    -RAKSFTPConnection__RmtTempSubmissionDirectory "${{ parameters.SettingRakSftpRmtTempSubmissionDirectory }}"
                    -General__FirstBlackV1PlasticCardId "${{ parameters.SettingFirstBlackV1PlasticCardId }}"
                    -General__FirstBlackV2PlasticCardId "${{ parameters.SettingFirstBlackV2PlasticCardId }}"
                    -CleverTapService__BaseAddress "${{ parameters.SettingCleverTapBaseAddress }}"
                    -ExchangeHouseSettings__BaseAddress "${{ parameters.SettingExchangeHouseBaseAddress }}"
                    -ExchangeHouseSettings__MaxAllowedBeneficiaryCount "${{ parameters.SettingExchangeHouseMaxAllowedBeneficiaryCount }}"
                    -MoneyTransferService__DirectTransferMaxBeneficiariesCount "20"
                    -MoneyTransferService__DirectTransferMinAmountToSend "1"
                    -MoneyTransferService__DirectTransferMaxAmountToSend "3002"
                    -MoneyTransferService__DirectTransferMaxAmountToSendPerMonth "3002"
                    -MoneyTransferService__DirectTransferFee "1.00"
                    -MoneyTransferService__DirectTransferVAT "0.05"
                    -MoneyTransferService__ReversePendingDirectMoneyTransfersDurationInMin "2"
                    -MoneyTransferService__ReversePendingDirectMoneyTransfersSchedule "00:10:00"
                    -MoneyTransferService__ReverseFailedDirectMoneyTransfersSchedule "01:00:00"
                    -MoneyTransferService__ReversalMode "${{ parameters.SettingMoneyTransferReversalMode }}"
                    -MoneyTransferService__ReversalStartDate "${{ parameters.SettingMoneyTransferReversalStartDate }}"
                    -MoneyTransferService__ReverseOnHoldSchedule "${{ parameters.SettingMoneyTransferReverseOnHoldSchedule }}"
                    -MoneyTransferService__ReverseOnHoldMinNoOfDays "${{ parameters.SettingMoneyTransferReverseOnHoldMinNoOfDays }}"
                    -MoneyTransferService__RateExpiryInMinutes "${{ parameters.SettingMoneyTransferRateExpiryInMinutes }}"
                    -MoneyTransferService__RmtCreationSchedule "${{ parameters.SettingMoneyTransferRmtCreationSchedule }}"
                    -MoneyTransferService__PendingSchedulerMinNoOfDays "${{ parameters.SettingMoneyTransferPendingSchedulerMinNoOfDays}}"
                    -MoneyTransferService__CheckMinSuspiciousDate "${{ parameters.SettingMoneyTransferCheckMinSuspiciousDate}}"
                    -MoneyTransferService__FreeTransferExpiryScheduler "${{ parameters.SettingMoneyTransferFreeTransferExpiryScheduler }}"
                    -MoneyTransferService__RetryBeneficiarySchedule "${{ parameters.SettingMoneyTransferRetryBeneficiarySchedule }}"
                    -MoneyTransferService__MaxBeneficiaryRetryLimit "${{ parameters.SettingMoneyTransferMaxBeneficiaryRetryLimit }}"
                    -MoneyTransferService__RetryBeneficiaryDurationInMin "${{ parameters.SettingMoneyTransferRetryBeneficiaryDurationInMin }}"
                    -MoneyTransferService__EnableRakMock "${{ parameters.SettingMoneyTransferEnableRakMock}}"
                    -MoneyTransferService__EnableRakNegativeScenarioMock "${{ parameters.SettingMoneyTransferEnableRakNegativeScenarioMock}}" 
                    -MoneyTransferService__AutoDeleteProfileAndSendUAfterRSchedule "${{ parameters.SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule }}"
                    -PaykiiService__BaseAddress "${{ parameters.SettingPaykiiServiceBaseAddress}}"
                    -PaykiiService__APIKey "${{ parameters.SettingPaykiiServiceAPIKey}}"
                    -PaykiiService__Token "${{ parameters.SettingPaykiiServiceToken}}"
                    -PaykiiService__CashierId "${{ parameters.SettingPaykiiServiceCashierId}}"
                    -PaykiiService__CustomerId "${{ parameters.SettingPaykiiServiceCustomerId}}" 
                    -PaykiiService__Urls__BillerCatalogUrl "${{ parameters.SettingPaykiiServiceBillerCatalogUrl}}" 
                    -PaykiiService__Urls__SKUCatalogUrl "${{ parameters.SettingPaykiiServiceSKUCatalogUrl}}" 
                    -PaykiiService__Urls__IOCatalogUrl "${{ parameters.SettingPaykiiServiceIOCatalogUrl}}"
                    -PaykiiService__Urls__AmountDueUrl "${{ parameters.SettingPaykiiServiceAmountDueUrl}}"
                    -PaykiiService__Urls__ProcessPaymentUrl "${{ parameters.SettingPaykiiServiceProcessPaymentUrl}}"
                    -PaykiiService__Urls__VerifyPaymentStatusUrl "${{ parameters.SettingPaykiiServiceVerifyPaymentStatusUrl}}"
                    -PaykiiService__Urls__BalanceUrl "${{ parameters.SettingPaykiiServiceBalanceUrl}}"
                    -PaykiiService__Urls__BillNotificationUrl "${{ parameters.SettingPaykiiServiceBillNotificationUrl}}"
                    -PaykiiService__Urls__MobileCarrierLookupUrl "${{ parameters.SettingPaykiiServiceMobileCarrierLookupUrl}}"
                    -PaykiiService__Urls__DailyFXRatePerBillerTypeUrl "${{ parameters.SettingPaykiiServiceDailyFXRatePerBillerTypeUrl}}"
                    -PaykiiService__Urls__BillerFeesCatalogUrl "${{ parameters.SettingPaykiiServiceBillerFeesCatalogUrl}}"
                    -PaykiiService__RefreshDataIntervalCronExpression "${{ parameters.SettingPaykiiServiceRefreshDataIntervalCronExpression}}"
                    -PaykiiService__LocationId "${{ parameters.SettingPaykiiServiceLocationId}}"
                    -PaykiiService__PointOfSaleId "${{ parameters.SettingPaykiiServicePointOfSaleId}}"
                    -MoneyTransferService__EnableRakMock "${{ parameters.SettingMoneyTransferEnableRakMock}}"
                    -MoneyTransferService__EnableRakNegativeScenarioMock "${{ parameters.SettingMoneyTransferEnableRakNegativeScenarioMock}}"
                    -BillPayment__IconBaseUrl "${{ parameters.SettingBillPaymentIconBaseUrl}}"
                    -BillPayment__PendingBillsRefreshIntervalCronExpression "${{ parameters.SettingBillPaymentPendingBillsRefreshIntervalCronExpression}}"
                    -BillPayment__TransactionEnvironment "${{ parameters.SettingBillPaymentTransactionEnvironment}}"
                    -BillPayment__FxRateRefreshIntervalCronExpression "${{ parameters.SettingBillPaymentFxRateRefreshIntervalCronExpression}}"
                    -BillPayment__GridViewDisplayProviderIds "${{ parameters.SettingBillPaymentGridViewDisplayProviderIds}}" 
                    -BillPayment__MaximumLocalBillersLimit "${{ parameters.SettingBillPaymentMaximumLocalBillersLimit}}" 
                    -BillPayment__MaximumInternationalBillersLimit "${{ parameters.SettingBillPaymentMaximumInternationalBillersLimit}}" 
                    -BillPayment__MaximumAllowedBillAmountPerTransaction "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerTransaction}}" 
                    -BillPayment__MaximumAllowedBillAmountPerMonth "${{ parameters.SettingBillPaymentMaximumAllowedBillAmountPerMonth}}" 
                    -BillPayment__NolProviderCode "${{ parameters.SettingBillPaymentNolProviderCode}}"
                    -BillPayment__AllowedNolAmountForNotVerified "${{ parameters.SettingBillPaymentAllowedNolAmountForNotVerified}}"
                    -BillPayment__IconContainerName "${{ parameters.SettingBillPaymentIconContainerName}}" 
                    -BillPayment__AddBillerQueueName "${{ parameters.SettingBillPaymentAddBillerQueueName}}" 
                    -BillPayment__ProcessPaymentQueueName "${{ parameters.SettingBillPaymentProcessPaymentQueueName}}" 
                    -BillPayment__MockAddBillerQueueName "${{ parameters.SettingBillPaymentMockAddBillerQueueName}}" 
                    -BillPayment__MockProcessPaymentQueueName "${{ parameters.SettingBillPaymentMockProcessPaymentQueueName}}" 
                    -BillPayment__AmountDueExpireIntervalCronExpression "${{ parameters.SettingBillPaymentAmountDueExpireIntervalCronExpression}}" 
                    -BillPayment__AmountDueExpiryHours "${{ parameters.SettingBillPaymentAmountDueExpiryHours}}"
                    -General__QAUserPhoneNumbers "${{ parameters.SettingGeneralQAUserPhoneNumbers}}" 
                    -General__PrimarySmsProvider "${{ parameters.SettingGeneralPrimarySmsProvider}}"
                    -General__TestKey "${{ parameters.SettingGeneralTestKey}}"
                    -General__EnableRedis "${{ parameters.SettingGeneralEnableRedis}}" 
                    -General__IsDbSaveRetryEnabled "${{ parameters.SettingGeneralIsDbSaveRetryEnabled}}"
                    -General__EmiratesIdStorageURL "${{ parameters.SettingGeneralEmiratesIdStorageURL}}" 
                    -MoneyTransferService__WUTransactionMinLimitValidationEnabled "${{ parameters.SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled}}"
                    -MoneyTransferService__NonWUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__WUEmiratesIdExpiryGracePeriodDays "${{ parameters.SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays}}"
                    -MoneyTransferService__NonWUCorridors "${{ parameters.SettingMoneyTransferServiceNonWUCorridors}}"
                    -MoneyTransferService__RMTStatusFromCreatedToPendingEnabled "${{ parameters.SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled}}"
                    -MoneyTransferService__LastRaffleWinnerName "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerName}}"
                    -MoneyTransferService__LastRaffleWinnerTicketNumber "${{ parameters.SettingMoneyTransferServiceLastRaffleWinnerTicketNumber}}"
                    -MoneyTransferService__RaffleDateString "${{ parameters.SettingMoneyTransferServiceRaffleDateString}}"
                    -BillPayment__RemoveCountrywiseFilter false
                    -Swagger__Username "${{ parameters.SettingSwaggerUsername }}"
                    -Swagger__Password "${{ parameters.SettingSwaggerPassword }}"
                    -UnEmploymentInsurance__ServiceBusTopicName "${{ parameters.SettingUnEmploymentInsuranceServiceBusTopicName}}" 
                    -UnEmploymentInsurance__ServiceBusSubscriptionName "${{ parameters.SettingUnEmploymentInsuranceServiceBusSubscriptionName}}"
                    -BalanceEnquirySubscription__ServiceBusTopicName "${{ parameters.SettingBalanceEnquirySubscriptionServiceBusTopicName}}" 
                    -BalanceEnquirySubscription__ServiceBusSubscriptionName "${{ parameters.SettingBalanceEnquirySubscriptionServiceBusSubscriptionName}}" 
                    -AuditTrail__ServiceBusQueueName "${{ parameters.SettingAuditTrailServiceBusQueueName}}" 
                    -Testing__MRDynamicPackageTestNepalNumbers "${{ parameters.SettingTestingMRDynamicPackageTestNepalNumbers }}"
                    -Testing__MRInlineFeeCalculationTestNumbers "${{ parameters.SettingTestingMRInlineFeeCalculationTestNumbers }}"
                    -ExchangeHouseSettings__UpdateStatusSchedule "${{ parameters.SettingExchangeHouseUpdateStatusSchedule }}"                    
                    -ExchangeHouseSettings__RefreshRatesSchedule "${{ parameters.SettingExchangeHouseRefreshRatesSchedule }}"
                    -ExchangeHouseSettings__BaseAddress "${{ parameters.SettingExchangeHouseBaseAddress }}"
                    -ExchangeHouseSettings__MaxAllowedBeneficiaryCount "${{ parameters.SettingExchangeHouseMaxAllowedBeneficiaryCount }}"
                    -RakBankMoneyTransfer__BaseUrl "${{ parameters.SettingRakBankMoneyTransferBaseUrl }}"
                    -RakBankMoneyTransfer__UrlPath "${{ parameters.SettingRakBankMoneyTransferUrlPath }}"
                    -RakBankMoneyTransfer__ClientId "${{ parameters.SettingRakBankMoneyTransferClientId }}"
                    -RakBankMoneyTransfer__ClientSecretkey "${{ parameters.SettingRakBankMoneyTransferClientSecretkey }}"
                    -RakBankMoneyTransfer__SslCertificateName "${{ parameters.SettingRakBankMoneyTransferSslCertificateName }}"
                    -RakBankMoneyTransfer__SslCertificatePassword "${{ parameters.SettingRakBankMoneyTransferSslCertificatePassword }}"
                    -RakBankMoneyTransfer__PayloadPrivateKey "${{ parameters.SettingRakBankMoneyTransferPayloadPrivateKey }}"
                    -RakBankMoneyTransfer__PayloadPublicKey "${{ parameters.SettingRakBankMoneyTransferPayloadPublicKey }}"
                    -RakBankMoneyTransfer__TokenGrantType "${{ parameters.SettingRakBankMoneyTransferTokenGrantType }}"
                    -RakBankMoneyTransfer__TokenScope "${{ parameters.SettingRakBankMoneyTransferTokenScope }}"
                    -RakBankMoneyTransfer__ContentType "${{ parameters.SettingRakBankMoneyTransferContentType }}"
                    -RakBankMoneyTransfer__X509Certificate2Bytes "${{ parameters.SettingRakBankMoneyTransferX509Certificate2Bytes }}"
                    -RenewalCardUpdate__ServiceBusTopicName "${{ parameters.SettingRenewalCardUpdateServiceBusTopicName}}" 
                    -RenewalCardUpdate__ServiceBusSubscriptionName "${{ parameters.SettingRenewalCardUpdateServiceBusSubscriptionName}}"
                    -C3PayPlusMembership__LuckyDrawSchedule "${{ parameters.SettingC3PayPlusMembershipLuckyDrawSchedule }}"
                    -C3PayPlusMembership__RenewalSchedule "${{ parameters.SettingC3PayPlusMembershipRenewalSchedule }}"
                    -KycBlockExclusions__ShouldBeDeletedAfter "${{ parameters.SettingKycBlockExclusionsShouldBeDeletedAfter }}"
                    -KycBlockExclusions__ScheduleTime "${{ parameters.SettingKycBlockExclusionsScheduleTime }}"
                    -SanctionScreeningApi__Address "${{ parameters.SettingSanctionScreeningApiAddress }}"
                    -RewardService__BaseAddress "${{ parameters.SettingRewardServiceBaseAddress }}"
                    -RewardService__ResendScheduleTime "${{ parameters.SettingRewardServiceResendScheduleTime }}"
                    -RewardService__RetryCount "${{ parameters.SettingRewardServiceRetryCount }}"
                    -RewardService__Timeout "${{ parameters.SettingRewardServiceTimeout }}"
                    -RewardService__TestAccountUsernames "${{ parameters.SettingRewardServiceTestAccountUsernames }}"'

      enableXmlVariableSubstitution: true
  
  - task: AzureAppServiceSettings@1
    displayName: Azure App Service Settings
    inputs:
      azureSubscription: ${{ parameters.azureSubscription }}
      appName: '${{ parameters.webJobName }}'
      connectionStrings: |
        [
          {
            "name": "AzureWebJobsStorage",
            "value": "DefaultEndpointsProtocol=https;AccountName=${{ parameters.storageWebJobName }};AccountKey=$(webJobStorageKey);EndpointSuffix=core.windows.net",
            "type": "Custom",
            "slotSetting": false
          },
          {
            "name": "AzureWebJobsDashboard",
            "value": "DefaultEndpointsProtocol=https;AccountName=${{ parameters.storageWebJobName }};AccountKey=$(webJobStorageKey);EndpointSuffix=core.windows.net",
            "type": "Custom",
            "slotSetting": false
          }
        ]

  - task: AzurePowerShell@4
    displayName: 'Remove Hosted Pipeline Access Rule'
    inputs:
        azureSubscription: ${{ parameters.azureSubscription }}
        ScriptType: InlineScript
        Inline: |
             $HostedIPAddress = Invoke-RestMethod http://ipinfo.io/json | Select -exp ip
             $ruleName = $HostedIPAddress
             Write-Host "Remove Access restriction rule '$ruleName'"

             Remove-AzWebAppAccessRestrictionRule -ResourceGroupName ${{ parameters.rgName }} -WebAppName ${{ parameters.webAppName }} -Name $ruleName
        azurePowerShellVersion: LatestVersion
