﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Edenred.Common.Core
{
    public static class SystemMessages
    {
        #region Response Message

        public const string DocumentNotFound = "Document: {0} Was Not Found";
        public const string UnableToCreateBlobClient = "Unable to create the Blob Client.";
        public const string UnableToDeleteBlob = "Unable to delete the Blob.";
        public const string ContainerNotFound = "Container Was Not Found";
        public const string FileUploadFailed = "Unable to Upload File: {0}";
        public const string DefaultCurrency = "AED";

        public const string TableRowNotFound = "Table row not found";

        public const string UnableToConnectToEdenredService = "Unable to Connect to Edenred Service";
        public const string UnableToConnectToKYCService = "Unable to Connect to KYC Service";
        public const string UnableToConnectToIdentityService = "Unable to Connect to Identity Service";
        public const string UnableToConnectToPPSWebAuthService = "Unable to Connect to PPS Web Auth Service";
        public const string UnableToConnectToPPSService = "Unable to connect to PPS service";
        public const string UnableToConnectToEIDService = "Unable to Connect to Emirates Id Service";
        public const string UnableToConnectToIdentificationService = "Unable to Connect to identification Service";
        public const string UnableToConnectToDingService = "Unable to Connect to Ding Service";
        public const string UnableToConnectToFCMService = "Unable to Connect to Firebase Cloud Messaging Service";
        public const string UnableToExtractEmiratesIdDetials = "Unable to extract emirates Id details";
        public const string UnableToExtractPassportIdDetials = "Unable to extract passport details";
        public const string UnableToExtractDocumentDetials = "Unable to extract document details";

        public const string NoDataFound = "No Data Found";
        public const string ExistsBeneficiary = "Beneficiary already exists";
        public const string NotExistsReason = "Money Transfer Reason not exists";
        public const string NotExistsUserId = "User Id not exists";
        public const string NotExistsUser = "User not exists";
        public const string NotExistsRole = "Role not exists";
        public const string LockedUser = "User locked";
        public const string NotAllowedUser = "User not allowed to connect";
        public const string WrongUserCredentials = "Wrong user credentials";

        //Storage Table
        public const string StorageTableNotFound = "Storage Table Not Found, Table Name: {0}";
        public const string StorageTableEntityNotFound = "Storage Table Entity Not Found, Partition Key: {0}, Row Key: {1}";
        public const string InvalidStorageTableEntity = "Invalid Storage Table Entity";
        public const string TableStorageConflictException = "TableStorageConflictException";

        #endregion

        #region Templates

        public const string SignzyReference = "PatronId_{0}-UniqueId_{1}";
        public const string RMTProfileRequestsFileName = "TAB_CUSTOMER_PROFILES_DAILY_{0}";
        public const string RMTProfileAcknowledgementsFileName = "TAB_CUSTOMER_PROFILES_DAILY_{0}";
        public const string RMTProfileResponsesFileName = "RMT_ProfileCreation_Daily_Inc_{0}";

        #endregion

        #region Logging Messages

        //Identity
        public const string CreatingUser = "Creating user identity... Username: {0}";
        public const string CreatingUserFailed = "Failed to create user identity. Username: {0}. Error: {1}";
        public const string UserCreated = "User identity created. Username: {0}";

        public const string UpdatingUser = "Updating user identity... Username: {0}, New Phone Number: {1}, New Email: {2}";
        public const string UpdatingUserFailed = "Failed to update user identity. Username: {0}, New Phone Number: {1}, New Email: {2}. Error: {3}";
        public const string UserUpdated = "User identity updated. Username: {0}, New Phone Number: {1}, New Email: {2}.";

        public const string ValidatingUserPassword = "Validating user password... Username: {0}";
        public const string ValidatingUserPasswordFailed = "Failed to validation user password. Username: {0}";
        public const string UserPasswordValidated = "User password validated. Username: {0}";

        public const string ChangingUsername = "Changing username... Username: {0}, New Username: {1}";
        public const string ChangingUsernameFailed = "Failed to changing username. Username: {0}, New Username: {1}. Error: {2}";
        public const string UsernameChanged = "Username changed. Username: {0}, New Username: {1}";

        public const string ChangingUserPassword = "Changing user password... Username: {0}";
        public const string ChangingUserPasswordFailed = "Failed to changing user password. Username: {0}. Error: {1}";
        public const string UserPasswordChanged = "User password changed. Username: {0}";

        public const string LockingUser = "Locking user... Username: {0}";
        public const string LockingUserFailed = "Failed to lock user. Username: {0}. Error: {1}";
        public const string UserLocked = "User locked. Username: {0}";

        public const string UnlockingUser = "Unlocking user... Username: {0}";
        public const string UnlockingUserFailed = "Failed to unlock user. Username: {0}. Error: {1}";
        public const string UserUnlocked = "User unlocked. Username: {0}";

        public const string RequestingPasswordReset = "Requesting user password reset... Username: {0}";
        public const string RequestingPasswordResetFailed = "Failed to request user password reset. Username: {0}. Error: {1}";
        public const string PasswordResetRequested = "User password reset requested. Username: {0}";

        public const string RequestingPhoneVerification = "Requesting user phone verification... Username: {0}";
        public const string RequestingPhoneVerificationFailed = "Failed to request user phone verification failed. Username: {0}. Error: {1}";
        public const string PhoneVerificationRequested = "User phone verification requested. Username: {0}";

        public const string VerifyingPhone = "Verifying user phone number... Username: {0}";
        public const string VerifyingPhoneFailed = "Failed to verify user phone number. Username: {0}. Error: {1}";
        public const string PhoneVerified = "User phone number verified. Username: {0}";

        public const string ResettingPassword = "Resetting user password... Username: {0}";
        public const string ResettingPasswordFailed = "Failed to reset user password. Username: {0}. Error: {1}";
        public const string PasswordReset = "User password reset. Username: {0}";

        public const string FetchingUserAccount = "Fetching user account... Username: {0}";
        public const string FetchingUserAccountFailed = "Failed to fetch user account. Username: {0}. Error: {1}";
        public const string UserAccountFetched = "User account fetched. Username: {0}";

        public const string DeletingUser = "Deleting user... Username: {0}";
        public const string DeletingUserFailed = "Failed to delete user. Username: {0}. Error: {1}";
        public const string UserDeleted = "User deleted. Username: {0}";

        public const string UpdatingUserEmail = "Updating user email... Username: {0}, UserId: {1}, Current Email: {2}";
        public const string UpdatingUserEmailFailed = "Failed to update user email. Username: {0}";
        public const string UserEmailUpdated = "User email updated. Username: {0}, UserId: {1}, New Email: {2}";
        public const string SendingEmailFailed = "Failed to send email";

        public const string SendingTextMessage = "Sending text message...";
        public const string TextMessageSent = "Text message sent";
        public const string SendingTextMessageFailed = "Sending text message failed";

        public const string SendingPushNotification = "Sending push notification... UserRegistrationToken: {0}, Message: {1}";
        public const string SendingPushNotificationFailed = "Sending push notification failed. UserRegistrationToken: {0}, Message: {1}";
        public const string PushNotificationSent = "Push notification sent. UserRegistrationToken: {0}, Response: {1}";

        public const string ChangingUserEmail = "Changing user email... Username: {0}";
        public const string ChangingUserEmailFailed = "Failed to changing user email. Username: {0}. Error: {1}";
        public const string UserEmailChanged = "User email changed. Username: {0}";

        public const string RequestingEmailVerification = "Requesting user email verification... Username: {0}";
        public const string RequestingEmailVerificationFailed = "Failed to request user email verification failed. Username: {0}. Error: {1}";
        public const string EmailVerificationRequested = "User email verification requested. Username: {0}";

        public const string VerifyingEmail = "Verifying user email... Username: {0}";
        public const string VerifyingEmailFailed = "Failed to verify user email. Username: {0}. Error: {1}";
        public const string EmailVerified = "User email verified. Username: {0}";
        public const string FetchingTokenFailed = "Token fetching failed for External Microservice :  {0}";


        //Role

        public const string ClaimAssigned = "Claim assignement succeded.";
        public const string ClaimAssigningFailed = "Claim assignement failed.";
        public const string ClaimUnassigned = "Claim unassignement succeded.";
        public const string ClaimUnassigningFailed = "Claim unassignement failed.";

        public const string RoleAssigned = "Role assignement succeded.";
        public const string RoleAssigningFailed = "Role assignement failed.";
        public const string RoleUnassigned = "Role unassignement succeded.";
        public const string RoleUnassigningFailed = "Role unassignement failed.";

        //Signzy

        public const string SelfieQualityResult = "Selfie Quality Result (Passed?): {0}, Score {1}, Url: {2}";
        public const string EmiratesIdQualityResult = "Emirates Id Quality Result (Passed?): {0}, Score {1}, Url: {2}";
        public const string PassportQualityResult = "Passport Quality Result (Passed?): {0}, Score {1}, Url: {2}";

        public const string NameMatchResult = "Name Match Result (Passed?): {0}, Score {1}, Name 1: {2}, Name 2: {3}";
        public const string NameMatchEmiratesIdNameMissing = "Failed to match names: emirates id name missing";
        public const string NameMatchPortalNameMissing = "Failed to match names: portal name missing";
        public const string NameMatchDocumentNameMissing = "Failed to match names: document name missing";

        public const string UploadingImageToSignzy = "Uploading image to signzy... Image Name: {0}";
        public const string UploadingImageToSignzyFailed = "Failed to upload image to signzy. Image Name: {0}";
        public const string ImageUploadedToSignzy = "Image upload to signzy. Image Name: {0}";

        public const string CheckingImageQuality = "Checking image quality... Image Url: {0}";
        public const string CheckingImageQualityFailed = "Failed to check image quality. Image Url: {0}";
        public const string ImageQualityChecked = "Image quality checked: Image Url: {0}";

        public const string ExtractingEmiratesIdData = "Extracting emirates id data... Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}";
        public const string ExtractingEmiratesIdDataFailed = "Failed to extract emirates id data. Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}";
        public const string EmiratesIdDataExtracted = "Emirates id data extracted. Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}, Data: {3}";

        public const string UploadingDocumentToSignzy = "Uploading document to signzy... Document Name: {0}";
        public const string UploadingDocumentToSignzyFailed = "Failed to upload document to signzy. Document Name: {0}";
        public const string DocumentUploadedToSignzy = "Document upload to signzy. Document Name: {0}";

        public const string CheckingDocumentQuality = "Checking document quality... Document Url: {0}";
        public const string CheckingDocumentQualityFailed = "Failed to check document quality. Document Url: {0}";
        public const string DocumentQualityChecked = "Document quality checked: Document Url: {0}";

        public const string ExtractingDocumentData = "Extracting document data... Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}";
        public const string ExtractingDocumentDataFailed = "Failed to extract document data. Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}";
        public const string PassportDataExtracted = "Passport data extracted. Front Scan Url: {0}, Back Scan Url: {1}, Name to Match: {2}, Data: {3}";

        public const string MatchingFace = "Matching face... Front Scan Name: {0}, Selfie Name: {1}";
        public const string MatchingFaceFailed = "Matching face failed. Front Scan Name: {0}, Selfie Name: {1}";
        public const string FaceMatched = "Face matched. Front Scan Name: {0}, Selfie Name: {1}";
        public const string FaceMatchResult = "Face Match Result (Passed?): {0}, Score {1}";

        public const string SavingEmiratesIdDocuments = "Saving emirates id documents... Front Scan Name: {0}, Back Scan Name: {1}, Selfie Name: {2}";
        public const string SavingEmiratesIdDocumentsFailed = "Saving emirates id documents failed. Front Scan Name: {0}, Back Scan Name: {1}, Selfie Name: {2}";
        public const string EmiratesIdDocumentsSaved = "Emirates id documents saved. Front Scan Name: {0}, Back Scan Name: {1}, Selfie Name: {2}";

        //PPS Messages
        public const string ActivatingCard = "Activating card... Card Serial Number: {0}";
        public const string CardActivated = "Successfully activated card. Card Serial Number: {0}";
        public const string ActivatingCardFailed = "Failed to activate card. Card Serial Number: {0}, PPS Error Message: {1}";

        public const string BlockingCard = "Blocking card... Card Serial Number: {0}";
        public const string CardBlocked = "Successfully blocked card. Card Serial Number: {0}";
        public const string BlockingCardFailed = "Failed to block card. Card Serial Number: {0}, PPS Error Message: {1}";

        public const string UnblockingCard = "Unblocking card... Card Serial Number: {0}";
        public const string CardUnblocked = "Successfully unblocked card. Card Serial Number: {0}";
        public const string UnblockingCardFailed = "Failed to unblock card. Card Serial Number: {0}, PPS Error Message: {1}";

        //public const string FetchingCardTransactions = "Fetching account transactions... Card Serial Number: {0}";
        //public const string CardTransactionsFetched = "Successfully fetched account transactions. Card Serial Number: {0}";
        //public const string FetchingCardTransactionsFailed = "Failed to fetch account transactions. Card Serial Number: {0}, Error Message: {1}";

        public const string FetchingCardStatus = "Fetching card status... Card Serial Number: {0}";
        public const string CardStatusFetched = "Successfully fetched card status. Card Serial Number: {0}";
        public const string FetchingCardStatusFailed = "Failed to fetch card status. Card Serial Number: {0}, PPS Error Message: {1}";

        public const string ResumingAccount = "Resuming account... Account Number: {0}";
        public const string AccountResumed = "Successfully resumed account. Account Number: {0}";
        public const string ResumingAccountFailed = "Failed to resume account. Account Number: {0}, PPS Error Message: {1}";

        public const string LoadingCard = "Loading card... Account Number: {0}";
        public const string CardLoaded = "Successfull card loading. Account Number: {0}";
        public const string LoadingCardFailed = "Failed to load card. Account Number: {0}, PPS Error Message: {1}";

        public const string FetchingCardPin = "Fetching card pin... Card Serial Number: {0}";
        public const string CardPinFetched = "Successfully fetched card pin. Card Serial Number: {0}";
        public const string FetchingCardPinFailed = "Failed to fetch card pin. Card Serial Number: {0}, PPS Error Message: {1}";

        public const string SendingCardPin = "Sending card pin... Card Serial Number: {0}";
        public const string CardPinSent = "Successfully sent card pin. Card Serial Number: {0}";
        public const string SendingCardPinFailed = "Failed to send card pin. Card Serial Number: {0}, PPS Error Message: {1}";

        public const string DecreasingBalance = "Decreasing balance... Account Number: {0}";
        public const string DecreasedBalance = "Successfull balance decreasing. Account Number: {0}";
        public const string DecreasingBalanceFailed = "Failed to decrease balance. Account Number: {0}, PPS Error Message: {1}";

        public const string FetchingCardHolder = "Fetching cardholder... Account Number: {0}";
        public const string CardHolderFethced = "Successfully fetched cardholder. Account Number:  {0}";
        public const string FetchingCardHolderFailed = "Failed to fetch cardholder. Account Number: {0}, PPS Error Message: {1}";

        //ESMO Service Messages
        public const string FetchingSMSNotificationSettings = "Fetching sms notification settings... Card Serial Number: {0}, NotificationType: {1}";
        public const string SMSNotificationSettingsFetched = "Successfully fetched sms notification settings. Card Serial Number: {0}, NotificationType: {1}";
        public const string FetchingSMSNotificationSettingsFailed = "Failed to fetch sms notification settings. Card Serial Number: {0}, NotificationType: {1}";

        public const string FetchingCardStatement = "Fetching card statement... Card Serial Number: {0}, Start Date: {1}, End Date: {2}";
        public const string CardStatementFetched = "Successfully fetched card statement. Card Serial Number: {0}, Start Date: {1}, End Date: {2}";
        public const string FetchingCardStatementFailed = "Failed to fetch card statement. Card Serial Number: {0}, Start Date: {1}, End Date: {2}";

        public const string FetchingCardTransactions = "Fetching card transactions... Card Serial Number: {0}, Page: {1}";
        public const string CardTransactionsFetched = "Successfully fetched card transactions. Card Serial Number: {0}, Page: {1}";
        public const string FetchingCardTransactionsFailed = "Failed to fetch card transactions. Card Serial Number: {0}, Page: {1}";

        public const string SubscribingToSMSNotification = "Subscribing to sms notification... Card Serial Number: {0}, Notification Type: {1}";
        public const string SubscribedToSMSNotification = "Successfully subscribed to sms notification. Card Serial Number: {0}, Notification Type: {1}";
        public const string SubscribingToSMSNotificationFailed = "Failed to subscribe to sms notification. Card Serial Number: {0}, Notification Type: {1}";

        public const string UnsubscribingFromSMSNotification = "Unubscribing from sms notification... Card Serial Number: {0}, Notification Type: {1}";
        public const string UnsubscribedFromSMSNotification = "Successfully unsubscribed from sms notification. Card Serial Number: {0}, Notification Type: {1}";
        public const string UnsubscribingFromSMSNotificationFailed = "Failed to unsubscribe from sms notification. Card Serial Number: {0}, Notification Type: {1}";

        public const string FetchingSMSSubscriptionMode = "Fetching sms subscription mode... Card Serial Number: {0}";
        public const string SMSSubscriptionModeFetched = "Successfully fetched sms subscription mode. Card Serial Number: {0}";
        public const string FetchingSMSSubscriptionModeFailed = "Failed to fetch subscription mode. Card Serial Number: {0}";

        //KYC Service Messages
        public const string FetchingRMTProfileStatus = "Fetching rmt profile status... EmiratesId: {0}";
        public const string RMTProfileStatusFetched = "Successfully fetched rmt profile status. EmiratesId: {0}";
        public const string FetchingRMTProfileStatusFailed = "Fetching rmt profile status failed. EmiratesId: {0}";

        public const string FetchingRMTProfileHistory = "Fetching rmt profile history... EmiratesId: {0}";
        public const string RMTProfileHistoryFetched = "Successfully fetched rmt profile history. EmiratesId: {0}";
        public const string FetchingRMTProfileHistoryFailed = "Fetching rmt profile history failed. EmiratesId: {0}";

        public const string UpdatingPhoneNumber = "Updating phone number... CitizenId: {0}, Phone Number: {1}";
        public const string PhoneNumberUpdated = "Successfully updated phone number. CitizenId: {0}, Phone Number: {1}";
        public const string UpdatingPhoneNumberFailed = "Failed to update phone number. CitizenId: {0}, Phone Number: {1}";

        public const string UpdatingEmiratesId = "Updating emirates id... CitizenId: {0}, EmiratesId: {1}, Expiry Date: {2}";
        public const string EmiratesIdUpdated = "Successfully updated emirates id. CitizenId: {0}, EmiratesId: {1}, Expiry Date: {2}";
        public const string UpdatingEmiratesIdFailed = "Failed to update emirates id. CitizenId: {0}, EmiratesId: {1}, Expiry Date: {2}";

        public const string DeactivatingRmt = "Deactivating RMT... CitizenId: {0}, EmiratesId: {1}";
        public const string RmtDeactivated = "RMT Deactivated. CitizenId: {0}, EmiratesId: {1}";
        public const string RmtDeactivationFailed = "RMT Deactivation failed. CitizenId: {0}, EmiratesId: {1}";

        public const string FetchingKYCDocument = "Fetching KYC Documents... CitizenId: {0}";
        public const string FetchingKYCDocumentFailed = "Failed to fetch KYC Document... CitizenId: {0}";
        public const string FetchedKYCDocument = "Successfully fetched KYC Document... CitizenId: {0}";

        public const string SavingKYCDocument = "Saving KYC Document... CitizenId: {0}";
        public const string SavingKYCDocumentFailed = "Failed to save KYC Document... CitizenId: {0}";
        public const string SavedKYCDocument = "Successfully saved KYC Document... CitizenId: {0}";

        ///Ding Messages
        public const string FetchingDingCountriesSuccess = "Successfully fetched supported countries from Ding..";
        public const string FetchingDingCountriesFailed = "Failed to fetch supported countries from Ding. Remarks : {0} ";

        public const string FetchingDingProvidersSuccess = "Successfully fetched providers from Ding..";
        public const string FetchingDingProvidersFailed = "Failed to fetch providers from Ding. Remarks : {0} ";

        public const string FetchingDingRegionSuccess = "Successfully fetched regions from Ding..";
        public const string FetchingDingRegionFailed = "Failed to fetch regions from Ding. Remarks : {0} ";

        public const string FetchingDingProviderStatusSuccess = "Successfully fetched provider status from Ding..";
        public const string FetchingDingProviderStatusFailed = "Failed to fetch provider status from Ding. Remarks : {0} ";

        public const string FetchingDingProductSuccess = "Successfully fetched Product from Ding..";
        public const string FetchingDingProductFailed = "Failed to fetch Product from Ding. Remarks : {0} ";

        public const string FetchingDingAccountLookupSuccess = "Successfully fetched account lookup from Ding..";
        public const string FetchingDingAccountLookupFailed = "Failed to fetch account lookup from Ding. Remarks : {0} ";

        //Web Jobs

        public const string WebJobStarted = "Web Job {0} Started";
        public const string WebJobCompleted = "Web Job {0} Completed";
        public const string WebJobError = "Web Job {0} Error";

        //RAK

        public const string ParsingRMTProfileFile = "Parsing rmt profile file... File: {0}";
        public const string ParsingRMTProfileFailed = "Failed to parse rmt profile file. File: {0}";
        public const string RMTProfileFileParsed = "Successfully parsed rmt profile file. File: {0}";

        public const string FoundNRecordsInRMTProfileFile = "Found {0} records in rmt profile file. File: {1}";

        public const string ParsingRMTProfileFileRecord = "Parsing rmt profile file record {0} out of {1}... File: {2}";
        public const string ParsingRMTProfileFileRecordFailed = "Failed to parse rmt profile file record {0} out of {1}. File: {2}";
        public const string RMTProfileFileRecordParsed = "Successfully parsed rmt profile file record {0} out of {1}. File: {2}";

        public const string AddingRMTProfileRequests = "Adding RMT profile file requests... File: {0}";
        public const string AddingRMTProfileRequestsFailed = "Failed to add RMT profile file requests. File: {0}";
        public const string RMTProfileRequestsAdded = "Successfully added RMT profile file requests. File: {0}";

        public const string AddingRMTProfileAcknowledgement = "Adding RMT profile file record {0} out of {1} acknowledgement... File: {2}";
        public const string AddingRMTProfileAcknowledgementFailed = "Failed to add RMT profile file record {0} out of {1} acknowledgement. File: {2}";
        public const string RMTProfileAcknowledgementAdded = "Successfully added RMT profile file record {0} out of {1} acknowledgement. File: {2}";

        public const string AddingRMTProfileResponse = "Adding RMT profile file record {0} out of {1} response... File: {2}";
        public const string AddingRMTProfileResponseFailed = "Failed to add RMT profile file record {0} out of {1} response. File: {2}";
        public const string RMTProfileResponseAdded = "Successfully added RMT profile file record {0} out of {1} response. File: {2}";

        public const string UploadingRMTProfileFile = "Uploading rmt profile file to storage... File: {0}, Container: {1}";
        public const string UploadingRMTProfileFailed = "Failed to upload rmt profile file to storage. File: {0}, Container: {1}";
        public const string RMTProfileFileUploaded = "Successfully uploadd rmt profile file to storage. File: {0}, Container: {1}";

        //SFTP Service

        public const string SearchingForNewSFTPFiles = "Searching for new sftp files... Source Directory: {0}";
        public const string FilteringSFTPSearchByFileNamePattern = "Filtering search by file name pattern... File Name Pattern: {0}";
        public const string FilteringSFTPSearchByContainer = "Filtering search by container... Container Name: {0}";
        public const string FoundNNewSFTPFiles = "Found {0} new sftp files, Source Directory: {1}";

        public const string DownloadingSFTPFiles = "Downloading sftp files to {0}...";
        public const string DownloadingSFTPFile = "Downloading sftp file {0} out of {1}...";
        public const string SFTPFilesDownloaded = "Downloading {0} sftp files to {1} complete";

        public const string FetchingDingEstimateProductPriceSuccess = "Successfully fetched estimated product price from Ding.";
        public const string FetchingDingEstimateProductPriceFailed = "Failed to fetch estimated product price from Ding. Remarks : {0} ";

        public const string PostTransferToDingSuccess = "Recharge successfully transfered to Ding.";
        public const string PostTransferToDingFailed = "Recharge failed transfer to Ding. Remarks : {0} ";

        public const string FetchingDingTransferSuccess = "Successfully fetched transfer status from Ding.";
        public const string FetchingDingTransferFailed = "Failed to fetch transfer status from Ding. Remarks : {0} ";

        //Clevetap
        public const string FetchingAnalyticsProfile = "Fetching analytics profile... Identity: {0}";
        public const string AnalyticsProfileFetched = "Successfully fetched analytics profile. Identity: {0}";
        public const string FetchingAnalyticsProfileFailed = "Fetching analytics profile failed. Identity: {0}";

        public const string UpdatingAnalyticsProfile = "Updating analytics profile... Identity: {0}";
        public const string AnalyticsProfileUpdated = "Successfully updated analytics profile. Identity: {0}";
        public const string UpdatingAnalyticsProfileFailed = "Updating analytics profile failed. Identity: {0}, Error: {1}";
        public const string UpdatingAnalyticsProfilesFailed = "Updating analytics profile failed, Failed Count: {0}/{1}, Errors: {2}";

        public const string UploadingAnalyticsEvent = "Uploading analytics event... Identity: {0}, EventName: {1}";
        public const string AnalyticsEventUploaded = "Successfully uploaded analytics event. Identity: {0}, EventName: {1}";
        public const string UploadingAnalyticsEventFailed = "Uploading analytics event failed. Identity: {0}, EventName: {1}, Error: {2}";
        public const string UploadingAnalyticsEventsFailed = "Uploading events failed, Failed Count: {0}/{1}, Errors: {2}";


        //B2C Transactions MicroService Messages
        public const string FetchingB2CCardTransactions = "Fetching card transactions... Citizen Id: {0}, Page: {1}";
        public const string B2CCardTransactionsFetched = "Successfully fetched card transactions. Citizen Id: {0}, Page: {1}";
        public const string FetchingB2CCardTransactionsFailed = "Failed to fetch card transactions. Citizen Id: {0}, Page: {1}";
        public const string FetchingB2CCardTransactionsV2Failed = "Failed to fetch card transactions. PPS Account Number: {0}, Page: {1}";
        public const string FetchingB2CCardTransactionsExternalAPIFailed = "Failed while calling external Microservice API. Citizen Id: {0}, Page: {1}";

        //Unemployment Insurance
        public const string InsuranceEnquiryFailed = "Failed to fetch Insurance Subscription Details.";
        public const string InsurancePremiumFailed = "Failed to fetch Premium Details.";
        public const string InsuranceInstallmentFailed = "Failed to Pay Installment.";
        public const string InsurancePurchaseFailed = "Failed to Purchase Insurance.";

        //EtisalatPaymentGateway
        public const string AuthorizationFailed = "Authorization Failed. Customer: {0}, OrderId: {1}";

        public const string CardBalance = "Fetching card balance. Account Number: {0}";
        public const string CardBalanceSuccess = "Card balance. Account Number: {0}, Balance: {0}";
        public const string CardBalanceFailed = "Failed to retrieve card balance. Account Number: {0}, PPS Error Message: {1}";

        #endregion
    }
}
