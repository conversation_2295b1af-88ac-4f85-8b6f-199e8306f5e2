﻿using AutoMapper;
using Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Card;
using EdenredExternalService;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Edenred.Common.Services
{
    public class ESMOWebService : IESMOWebService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly ESMOServiceSettings _settings;
        private readonly PPSServiceSettings _ppsSettings;
        private readonly string _sourceClient = "C3PayBackend";

        private readonly string _getCardTransactionsMethodName = "GetLastTransactionsPerCard";
        private readonly string _getSubscriptionStatusMethodName = "GetSMSNotificationSettings";
        private readonly string _getCardHolderDetailsMethodName = "GetCardHolderDetails";
        private readonly string _getCardHolderDetailsV2MethodName = "GetCardHolderDetailsV2";
        private readonly string _getCardStatementMethodName = "GetCardStatementofAccount";
        private readonly string _subscribeMethodName = "SubscribeToSMSNotification";
        private readonly string _unsubscribeMethodName = "UnSubscribeFromSMSNotification";
        private readonly string _getCardBlockTypeMethodName = "GetCardBlockType";
        private readonly string _markCardHolderAsAppRegisteredMethodName = "UpdateCardholderAppStatus";
        private readonly string _cardReIssueMethodName = "CardReIssue";
        private readonly string _getCardPinMethodName = "GetCardPin";
        private readonly string _getCardDetailsMethodName = "GetCardDetails";
        private readonly string _verifyCvc2MethodName = "VerifyCvc2";
        private readonly string _blockCardMethodName = "BlockCard";
        private readonly string _unblockCardMethodName = "UnBlockCard";
        private readonly string _getEmployeeDetailsMethodName = "GetEmployeeDetails";
        private readonly string _getEmployeeDetailsByCardSerialNumberMethodName = "get-employee-by-card-number";
        private readonly string _getEmployeeDetailsByPPSAccountNoMethodName = "get-employee-by-pps-account-number";
        private readonly string _getCashbackEligibilityMethodName = "cashback-eligibility";
        private readonly string _updateCashBackStatusMethodName = "salary-advance-cashback";
        private readonly string _getPendingEodFiles = "GetPendingEodFiles";
        private readonly string _getSearch = "search";
        private readonly string _getSMSSubscriptionModeMethodName = "GetSMSSubscriptionMode";
        private readonly string _markCardAsKycUnblocked = "MarkCardAsKycUnblocked/{0}";
        private readonly string _gotSalaryInLast3Months = "GotSalaryInLast3Months/{0}";
        private readonly string _markCardAsKycUnblockedV2 = "MarkCardAsKycUnblocked/v2";
        private readonly string _markEmployeeAsDormant = "MarkEmployeeAsDormant";
        private readonly string _getCardsByPassport = "GetCardsByPassport";
        private readonly string _isCardDormant = "IsCardDormant/{0}";
        private readonly string _toggleC3PayPlusSmsSubscription = "ToggleC3PayPlusSmsSubscription";
        private readonly string _rollbackC3PayPlusSmsSubscriptionRequestDto = "ToggleC3PayPlusSmsSubscription";

        public ESMOWebService(IOptions<ESMOServiceSettings> settings,
            System.Net.Http.IHttpClientFactory httpClientFactory,
            ILogger<ESMOWebService> logger,
            IOptions<PPSServiceSettings> ppsSettings,
            IMapper mapper)
        {
            _httpClient = httpClientFactory.CreateClient("ESMOWebService");
            _logger = logger;
            _settings = settings.Value;
            _mapper = mapper;
            _ppsSettings = ppsSettings.Value;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task GenerateAndAssignToken()
        {
            var authenticationContext = new AuthenticationContext(_settings.Authority);
            var clientCredential = new ClientCredential(_settings.ClientId, _settings.ClientSecret);
            var authenticationResult = await authenticationContext.AcquireTokenAsync(_settings.Scope, clientCredential);

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authenticationResult.AccessToken);
        }

        public async Task<ServiceResponse<CardReIssueResponseDto>> CardReIssue(CardReIssueRequestDto reIssueRequest)
        {
            await GenerateAndAssignToken();
            var request = new CardReIssueReqest()
            {
                CorporateId = reIssueRequest.CorporateId,
                EmployeeId = reIssueRequest.EmployeeId,
                ReIssueType = reIssueRequest.ReIssueType,
                Email = reIssueRequest.Email,
                ConfirmReIssue = reIssueRequest.ConfirmReIssue,
                VerifiedStatus = reIssueRequest.VerifiedStatus,
                BlockStatus = reIssueRequest.BlockStatus,
                SourceClient = reIssueRequest.SourceClient,
                CreatedBy = reIssueRequest.CreatedBy
            };

            var response = await this._httpClient.PostAsJsonAsync(_cardReIssueMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<CardReIssueResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<CardReIssueResult>();
            var resultCode = result.ResponseCode;

            Enum.TryParse(resultCode, out Enums.CardReIssueResponse responseCode);

            var mainResponse = new ServiceResponse<CardReIssueResponseDto>(true, EnumUtility.GetDescriptionFromEnumValue(responseCode));
            if (responseCode != Enums.CardReIssueResponse.ESCRI000)
                mainResponse.IsSuccessful = false;

            var mappedResult = this._mapper.Map<CardReIssueResponseDto>(result);

            if (mappedResult != null)
                mainResponse.Data = mappedResult;

            return mainResponse;
        }

        public async Task<ServiceResponse<string>> GetCardBlockType(GetCardBlockTypeRequestDto getCardBlockTypeRequest)
        {
            await GenerateAndAssignToken();

            var request = new GetCardBlockTypeRequest()
            {
                CardSerialNumber = getCardBlockTypeRequest.CardSerialNumber,
                SourceClient = _sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_getCardBlockTypeMethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<string>(false, "");
            }

            var result = await response.Content.ReadAsAsync<GetCardBlockTypeResult>();

            var resultCode = result.ResponseCode;

            Enum.TryParse(resultCode, out Enums.GetCardBlockTypeResponse responseCode);

            if (responseCode != Enums.GetCardBlockTypeResponse.ESGCBS000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse<string>(false, errorMessage);
            }

            return new ServiceResponse<string>(result.BlockType);
        }

        public async Task<ServiceResponse<GetCardDetailsForPackageResponseDto>> GetCardDetailsForPackage(GetCardDetailsForPackageRequestDto getCardDetailsForPackageRequest)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<GetCardHolderDetailsResponseDto>> GetCardHolderDetails(GetCardHolderDetailsRequestDto getCardHolderDetailsRequest)
        {
            await GenerateAndAssignToken();

            var request = new GetCardHolderDetailsRequest()
            {
                CardNumber = getCardHolderDetailsRequest.CardNumber,
                CitizenId = getCardHolderDetailsRequest.CitizenId,
                SearchParameter = (CardHolderDetailsSearchParameter)getCardHolderDetailsRequest.SearchParameter,
                SourceClient = _sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_getCardHolderDetailsMethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardHolderDetailsResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<GetCardHolderDetailsResponseDto>();

            var resultCode = result.ResponseCode;

            Enum.TryParse(resultCode, out Enums.GetCardHolderDetails responseCode);

            if (responseCode != Enums.GetCardHolderDetails.ESGCHDS000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse<GetCardHolderDetailsResponseDto>(false, errorMessage);
            }

            var mappedResult = this._mapper.Map<GetCardHolderDetailsResponseDto>(result);

            return new ServiceResponse<GetCardHolderDetailsResponseDto>(mappedResult);
        }

        public async Task<ServiceResponse<GetCardHolderDetailsResponseDto>> GetCardHolderDetailsV2(GetCardHolderDetailsRequestDto getCardHolderDetailsRequest)
        {
            await GenerateAndAssignToken();

            var request = new GetCardHolderDetailsRequest()
            {
                CardNumber = getCardHolderDetailsRequest.CardNumber,
                CitizenId = getCardHolderDetailsRequest.CitizenId,
                SearchParameter = (CardHolderDetailsSearchParameter)getCardHolderDetailsRequest.SearchParameter,
                SourceClient = _sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_getCardHolderDetailsV2MethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardHolderDetailsResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<GetCardHolderDetailsResponseDto>();

            var resultCode = result.ResponseCode;

            Enum.TryParse(resultCode, out Enums.GetCardHolderDetails responseCode);

            if (responseCode != Enums.GetCardHolderDetails.ESGCHDS000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse<GetCardHolderDetailsResponseDto>(false, errorMessage);
            }

            var mappedResult = this._mapper.Map<GetCardHolderDetailsResponseDto>(result);

            return new ServiceResponse<GetCardHolderDetailsResponseDto>(mappedResult);
        }

        public async Task<ServiceResponse<GetCardPackageDetailsResponseDto>> GetCardPackageDetails(GetCardPackageDetailsRequestDto getCardPackageDetailsRequest)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<GetCardStatementResponseDto>> GetCardStatementData(GetCardStatementRequestDto cardStatementRequest)
        {
            await GenerateAndAssignToken();

            var getCardStatementofAccountRequest = new GetCardStatementofAccountRequest()
            {
                CardserialNo = cardStatementRequest.CardSerialNumber,
                CorporateId = cardStatementRequest.CorporateId,
                FromDate = cardStatementRequest.StartDate,
                Todate = cardStatementRequest.EndDate,
                SourceClient = _sourceClient
            };

            var getCardStatementofAccountResponse = await this._httpClient.PostAsJsonAsync(_getCardStatementMethodName, getCardStatementofAccountRequest);

            if (!getCardStatementofAccountResponse.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardStatementResponseDto>(false, "");
            }

            var result = await getCardStatementofAccountResponse.Content.ReadAsAsync<GetCardStatementofAccountResult>();

            var resultCode = result.ResponseCode;

            var parsingResult = Enum.TryParse(resultCode, out Enums.GetCardStatementResponse responseCode);

            if (responseCode != Enums.GetCardStatementResponse.ESSOA000 || parsingResult == false)
            {

                return new ServiceResponse<GetCardStatementResponseDto>(false, result.ResponseDescription);
            }
            else
            {
                var response = new GetCardStatementResponseDto()
                {
                    Attchement = result.CardSOAFile
                };
                return new ServiceResponse<GetCardStatementResponseDto>(response);
            }

        }

        public async Task<ServiceResponse<GetCardTransactionsResponseDto>> GetCardTransactions(GetCardTransactionsRequestDto cardTransactionsRequest)
        {
            await GenerateAndAssignToken();

            var getTransactionsRequest = new GetLastTransactionPerCardRequest()
            {
                CardSerialNo = cardTransactionsRequest.CardSerialNumber,
                CorporateId = cardTransactionsRequest.CorporateId,
                PageNo = cardTransactionsRequest.PageNumber,
                SourceClient = _sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_getCardTransactionsMethodName, getTransactionsRequest);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardTransactionsResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<GetLastTransactionPerCardResult>();

            var resultCode = result.Code;

            var parsingResult = Enum.TryParse(resultCode, out Enums.GetLastTransactionsPerCardResponse responseCode);

            if (responseCode != Enums.GetLastTransactionsPerCardResponse.ESSOA000 || parsingResult == false)
            {

                return new ServiceResponse<GetCardTransactionsResponseDto>(false, result.Description);
            }


            var mappedResult = this._mapper.Map<GetCardTransactionsResponseDto>(result);

            return new ServiceResponse<GetCardTransactionsResponseDto>(mappedResult);
        }

        public async Task<ServiceResponse<GetFeeDetailsResponseDto>> GetFeeDetails(GetFeeDetailsRequestDto getFeeDetailsRequest)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<bool>> GetSMSSubscriptionStatus(GetSMSSubscriptionStatusRequestDto smsNotificationSettingsRequest)
        {
            await GenerateAndAssignToken();


            var getSubscriptionsRequest = new GetSMSNotificationSettingsRequest()
            {
                CardSerialNo = smsNotificationSettingsRequest.CardSerialNumber,
                CorporateId = smsNotificationSettingsRequest.CorporateId,
                NotificationType = smsNotificationSettingsRequest.NotificationType
            };
            var response = await this._httpClient.PostAsJsonAsync(_getSubscriptionStatusMethodName, getSubscriptionsRequest);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<bool>(false, "");
            }

            var result = await response.Content.ReadAsAsync<GetSMSNotificationSettingsResult>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN900 && responseCode != Enums.SMSNotificationResponse.ESSTN031 && responseCode != Enums.SMSNotificationResponse.ESSTN032 && responseCode != Enums.SMSNotificationResponse.ESSTN033)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);


                return new ServiceResponse<bool>(false, errorMessage);
            }
            else
            {

                return new ServiceResponse<bool>(responseCode == Enums.SMSNotificationResponse.ESSTN900);
            }
        }

        public async Task<ServiceResponse<MarkCardHolderAsAppRegisteredResponseDto>> MarkCardHolderAsAppRegistered(MarkCardHolderAsAppRegisteredRequestDto markCardHolderAsAppRegisteredRequest)
        {
            await GenerateAndAssignToken();

            var request = new UpdateCardholderAppStatusRequest()
            {
                CardSerialNo = markCardHolderAsAppRegisteredRequest.CardSerialNo,
                CorporateId = markCardHolderAsAppRegisteredRequest.CorporateId,
                AppStatus = markCardHolderAsAppRegisteredRequest.AppStatus,
                SourceClient = this._sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_markCardHolderAsAppRegisteredMethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<MarkCardHolderAsAppRegisteredResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<UpdateCardholderAppStatusResult>();

            var resultCode = result.ResponseCode;

            Enum.TryParse(resultCode, out Enums.MarkCardHolderAsAppRegisteredResponse responseCode);

            if (responseCode != Enums.MarkCardHolderAsAppRegisteredResponse.ESUCA000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse<MarkCardHolderAsAppRegisteredResponseDto>(false, errorMessage);
            }

            var mappedResult = this._mapper.Map<MarkCardHolderAsAppRegisteredResponseDto>(result);

            return new ServiceResponse<MarkCardHolderAsAppRegisteredResponseDto>(mappedResult);
        }

        public async Task<ServiceResponse> SubscribeToSMSNotification(SubscribeToSMSNotificationRequestDto subscribeToSMSNotificationRequest)
        {
            await GenerateAndAssignToken();


            var request = new SubscribeToSMSNotificationRequest()
            {
                CardSerialNo = subscribeToSMSNotificationRequest.CardSerialNumber,
                CorporateId = subscribeToSMSNotificationRequest.CorporateId,
                NotificationType = subscribeToSMSNotificationRequest.NotificationType,
                MobileNo = subscribeToSMSNotificationRequest.PhoneNumber.ToZeroPrefixedPhoneNumber(),
                SourceClient = _sourceClient,
                SMSFee = subscribeToSMSNotificationRequest.SMSFee
            };

            var response = await this._httpClient.PostAsJsonAsync(_subscribeMethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, "");
            }

            var result = await response.Content.ReadAsAsync<SubscribeToSMSNotificationResult>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN000 && responseCode != Enums.SMSNotificationResponse.ESSTN011)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse(false, errorMessage);
            }
            else
            {

                return new ServiceResponse();
            }
        }

        public async Task<ServiceResponse> UnsubscribeFromSMSNotification(UnsubscribeFromSMSNotificationRequestDto unsubscribeFromSMSNotificationRequest)
        {
            await GenerateAndAssignToken();

            var request = new UnSubscribeFromSMSNotificationRequest()
            {
                CardSerialNo = unsubscribeFromSMSNotificationRequest.CardSerialNumber,
                CorporateId = unsubscribeFromSMSNotificationRequest.CorporateId,
                NotificationType = unsubscribeFromSMSNotificationRequest.NotificationType,
                SourceClient = _sourceClient
            };

            var response = await this._httpClient.PostAsJsonAsync(_unsubscribeMethodName, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, "");
            }

            var result = await response.Content.ReadAsAsync<UnSubscribeFromSMSNotificationResult>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN000 && responseCode != Enums.SMSNotificationResponse.ESSTN021)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse(false, errorMessage);
            }
            else
            {
                return new ServiceResponse();
            }
        }

        public Task<ServiceResponse<string>> GetCardSerialNumber(string cardNumber)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<GetCardPinResult>> GetCardPin(GetCardPinRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_getCardPinMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardPinResult>(false, "");
            }

            var result = await response.Content.ReadAsAsync<EsmoVerifyCardResult>();

            Enum.TryParse(result.ResultCode, out Enums.PPSGetCardPinResponses statusCode);

            if (statusCode != Enums.PPSGetCardPinResponses.PPS0000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(statusCode);
                return new ServiceResponse<GetCardPinResult>(false, errorMessage);
            }

            var body = new Dictionary<string, string>
            {
                { "customerCode", _ppsSettings.CustomerCode },
                { "cardSerial", request.CardSerialNumber },
                { "token", result.Token },
                { "CVC2", request.Cvc2 },
            };
            var form = new FormUrlEncodedContent(body);

            // Call endpoint to verify CVC2.
            var responseMessage = await this._httpClient.PostAsync(result.Uri, form);
            if (responseMessage.IsSuccessStatusCode == false)
            {
                var json = await responseMessage.Content.ReadAsStringAsync();
                var error = JsonConvert.DeserializeObject<GetCardPinUriErrorResult>(json);
                switch (error?.ErrorMessage)
                {
                    case "INVALID CVC2":
                        return new ServiceResponse<GetCardPinResult>(false, Enums.AtmPinErrors.INVALID_CVC2.ToString());
                    case "MAX PIN TRIES EXCEEDED":
                        return new ServiceResponse<GetCardPinResult>(false, Enums.AtmPinErrors.MAX_PIN_TRIES_EXCEEDED.ToString());
                    default:
                        return new ServiceResponse<GetCardPinResult>(false, Enums.AtmPinErrors.GENERAL_ERROR.ToString());
                }
            }

            var pin = await responseMessage.Content.ReadAsStringAsync();
            return new ServiceResponse<GetCardPinResult>(new GetCardPinResult()
            {
                Pin = pin,
                Status = Enums.PPSGetCardPinResponses.PPS0000
            });

        }

        public async Task<ServiceResponse<GetCardDetailsResult>> GetCardDetails(GetCardDetailsRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_getCardDetailsMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardDetailsResult>(false, "");
            }

            var result = await response.Content.ReadAsAsync<EsmoCardDetailsResult>();

            Enum.TryParse(result.resultCodeField, out Enums.PPSGetCardPinResponses statusCode);

            if (statusCode != Enums.PPSGetCardPinResponses.PPS0000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(statusCode);
                return new ServiceResponse<GetCardDetailsResult>(false, errorMessage);
            }

            var mappedResult = this._mapper.Map<GetCardDetailsResult>(result);

            if (string.IsNullOrEmpty(mappedResult.CardNumber))
            {
                return new ServiceResponse<GetCardDetailsResult>(false, "InvalidCard");
            }

            return new ServiceResponse<GetCardDetailsResult>(mappedResult);
        }

        public async Task<ServiceResponse<VerifyCvc2Result>> VerifyCvc2(VerifyCvc2Request request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_verifyCvc2MethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<VerifyCvc2Result>(false, "");
            }

            var result = await response.Content.ReadAsAsync<EsmoVerifyCardResult>();

            Enum.TryParse(result.ResultCode, out Enums.PPSGetCardPinResponses statusCode);

            if (statusCode != Enums.PPSGetCardPinResponses.PPS0000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(statusCode);
                return new ServiceResponse<VerifyCvc2Result>(false, errorMessage);
            }
            var body = new Dictionary<string, string>
            {
                { "customerCode", _ppsSettings.CustomerCode },
                { "cardSerial", request.CardSerialNumber },
                { "token", result.Token },
                { "CVC2", request.Cvc2 },
            };
            var form = new FormUrlEncodedContent(body);

            // Call endpoint to verify CVC2.
            var responseMessage = await this._httpClient.PostAsync(result.Uri, form);
            if (responseMessage.IsSuccessStatusCode == false)
            {
                var json = await responseMessage.Content.ReadAsStringAsync();
                var error = JsonConvert.DeserializeObject<GetCardPinUriErrorResult>(json);

                switch (error?.ErrorMessage)
                {
                    case "INVALID CVC2":
                        return new ServiceResponse<VerifyCvc2Result>(false, Enums.AtmPinErrors.INVALID_CVC2.ToString());
                    case "MAX PIN TRIES EXCEEDED":
                        return new ServiceResponse<VerifyCvc2Result>(false, Enums.AtmPinErrors.MAX_PIN_TRIES_EXCEEDED.ToString());
                    default:
                        return new ServiceResponse<VerifyCvc2Result>(false, Enums.AtmPinErrors.GENERAL_ERROR.ToString());
                }
            }

            return new ServiceResponse<VerifyCvc2Result>(new VerifyCvc2Result()
            {
                IsValid = true
            });
        }

        public async Task<ServiceResponse> BlockCard(BlockCardRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_blockCardMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, "");
            }

            var result = await response.Content.ReadAsAsync<CashBackUpdateResult>();
            var successResponses = new List<string>
            {
                "SUCCESS",
                "Card and/or account is blocked",
                "CARD/ACCOUNT BLOCKED",
                "Method was successful"
            };

            if (result != null)
            {
                if (successResponses.Contains(result.ResponseDescription))
                {
                    return new ServiceResponse();
                }
                else
                {
                    return new ServiceResponse(false, result.ResponseCode);
                }
            }
            else
            {
                return new ServiceResponse(false, "");
            }
        }

        public async Task<ServiceResponse> UnblockCard(UnblockCardRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_unblockCardMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, "");
            }
            return new ServiceResponse();
        }



        public async Task<ServiceResponse<GetEmployeeDetailsResult>> GetEmployeeDetails(GetEmployeeDetailsRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_getEmployeeDetailsMethodName, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetEmployeeDetailsResult>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<GetEmployeeDetailsResult>();

            Enum.TryParse(result.ResponseCode, out Enums.GetEmployeeDetails statusCode);

            if (statusCode != Enums.GetEmployeeDetails.ESGCEDS000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(statusCode);
                return new ServiceResponse<GetEmployeeDetailsResult>(false, errorMessage);
            }

            return new ServiceResponse<GetEmployeeDetailsResult>(result);
        }

        public async Task<ServiceResponse<GetOrianEmployeeResult>> GetEmployeeDetailsByCardSerialNumber(string CardSerialNumber)
        {
            await GenerateAndAssignToken();

            var request = _getEmployeeDetailsByCardSerialNumberMethodName + "?CardSerialNumber=" + CardSerialNumber;

            var response = await this._httpClient.GetAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetOrianEmployeeResult>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<GetOrianEmployeeResult>();

            return new ServiceResponse<GetOrianEmployeeResult>(result);
        }

        public async Task<ServiceResponse<GetOrianEmployeeResult>> GetEmployeeDetailsByPPSAccountNo(string ppsAccountNo)
        {
            await GenerateAndAssignToken();

            var request = _getEmployeeDetailsByPPSAccountNoMethodName + "?ppsAccountNo=" + ppsAccountNo;

            var response = await this._httpClient.GetAsync(request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetOrianEmployeeResult>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<GetOrianEmployeeResult>();

            return new ServiceResponse<GetOrianEmployeeResult>(result);
        }

        public async Task<ServiceResponse<GetCashBackEligibilityResult>> GetCashbackEligibility(string citizenId)
        {
            await GenerateAndAssignToken();

            var request = _getCashbackEligibilityMethodName + "?citizenId=" + citizenId;

            var response = await this._httpClient.GetAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCashBackEligibilityResult>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<GetCashBackEligibilityResult>();

            return new ServiceResponse<GetCashBackEligibilityResult>(result);

        }

        public async Task<ServiceResponse<CashBackUpdateResult>> UpdateCashBackStatus(bool isCashBacked, string citizenId)
        {
            await GenerateAndAssignToken();

            var request = _updateCashBackStatusMethodName + "?isCashBacked=" + isCashBacked + "&citizenId=" + citizenId;

            var json = string.Empty;

            var httpContent = new StringContent(json.ToString(), Encoding.UTF8, "application/json");

            var response = await this._httpClient.PostAsync(request, httpContent);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<CashBackUpdateResult>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<CashBackUpdateResult>();

            return new ServiceResponse<CashBackUpdateResult>(result);
        }

        public async Task<ServiceResponse<List<PendingEodFile>>> GetPendingEodFiles()
        {
            await GenerateAndAssignToken();

            var pendingFiles = await this._httpClient.GetAsync(_getPendingEodFiles);

            if (pendingFiles.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<List<PendingEodFile>>(false, "Error calling internal API.");
            }

            var files = await pendingFiles.Content.ReadAsAsync<List<PendingEodFile>>();

            return new ServiceResponse<List<PendingEodFile>>(files);
        }

        public async Task<ServiceResponse<List<SearchResult>>> Search(SearchRequest request)
        {
            await GenerateAndAssignToken();

            var response = await this._httpClient.PostAsJsonAsync(_getSearch, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<List<SearchResult>>(false, string.Empty);
            }

            var result = await response.Content.ReadAsAsync<List<SearchResult>>();

            return new ServiceResponse<List<SearchResult>>(result);
        }

        public async Task<ServiceResponse<GetSMSSubscriptionModeResponse>> GetSMSSubscriptionMode(GetSMSSubscriptionModeRequest getSMSSubscriptionModeRequest, CancellationToken cancellationToken = default(CancellationToken))
        {
            await GenerateAndAssignToken();

            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var response = await this._httpClient.PostAsJsonAsync(_getSMSSubscriptionModeMethodName, getSMSSubscriptionModeRequest, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetSMSSubscriptionModeResponse>(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<GetSMSSubscriptionModeResponse>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN000)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                this._logger.LogDebug(string.Format(SystemMessages.FetchingSMSSubscriptionModeFailed, getSMSSubscriptionModeRequest.CardSerialNo, errorMessage));

                return new ServiceResponse<GetSMSSubscriptionModeResponse>(false, errorMessage);
            }
            else
            {

                return new ServiceResponse<GetSMSSubscriptionModeResponse>(result);
            }
        }

        public async Task<ServiceResponse> MarkCardAsKycUnblocked(string citizenId)
        {
            await GenerateAndAssignToken();

            var request = string.Format(_markCardAsKycUnblocked, citizenId);

            var response = await this._httpClient.PostAsync(request, null);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<ServiceResponse>();

            return new ServiceResponse(result.IsSuccessful, result.ErrorMessage);
        }

        public async Task<ServiceResponse> MarkCardAsKycUnblockedV2(MarkUnblockCardRequest request)
        {
            await GenerateAndAssignToken();

            var url = string.Format(_markCardAsKycUnblockedV2);

            var response = await this._httpClient.PostAsJsonAsync(url, request);
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<ServiceResponse>();

            return new ServiceResponse(result.IsSuccessful, result.ErrorMessage);
        }

        public async Task<ServiceResponse<GotSalaryInTheLast3MonthsResponse>> GotSalaryInLast3Months(string citizenId)
        {
            await GenerateAndAssignToken();

            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var url = string.Format(_gotSalaryInLast3Months, citizenId);

            var response = await this._httpClient.PostAsync(url, null);

            if (response.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<GotSalaryInTheLast3MonthsResponse>(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<ServiceResponse<GotSalaryInTheLast3MonthsResponse>>();

            return result;
        }

        public async Task<ServiceResponse> MarkEmployeeAsDormant(MarkEmployeeAsDormantRequest request)
        {
            await GenerateAndAssignToken();

            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var url = string.Format(_markEmployeeAsDormant);

            var response = await this._httpClient.PostAsJsonAsync(url, request);

            if (response.IsSuccessStatusCode == false)
            {
                return new ServiceResponse(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<ServiceResponse>();

            return new ServiceResponse(result.IsSuccessful, result.ErrorMessage);
        }

        public async Task<ServiceResponse<List<CardHolderDetailsDto>>> GetCardsByPassportId(GetCardsByPassportIdRequest request)
        {
            await GenerateAndAssignToken();

            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var url = string.Format(_getCardsByPassport);

            var response = await this._httpClient.PostAsJsonAsync(url, request);

            if (response.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<List<CardHolderDetailsDto>>(false, response.ReasonPhrase);
            }

            var result = await response.Content.ReadAsAsync<ServiceResponse<List<CardHolderDetailsDto>>>();

            return result;
        }

        public async Task<ServiceResponse<IsCardDormantResponse>> IsCardDormant(string registrationId)
        {
            await GenerateAndAssignToken();

            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var url = string.Format(_isCardDormant, registrationId);

            var response = await this._httpClient.PostAsync(url, null);
            var isCardDormant = await response.Content.ReadAsAsync<ServiceResponse<IsCardDormantResponse>>();

            if (response.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<IsCardDormantResponse>(false, isCardDormant.ErrorMessage);
            }

            return isCardDormant;
        }

        public async Task<ServiceResponse> ToggleC3PayPlusSmsSubscription(ToggleC3PayPlusSmsSubscriptionRequestDto toggleC3PayPlusSmsSubscriptionRequest)
        {
            await GenerateAndAssignToken();

            var request = new SubscribeToSMSNotificationRequest()
            {
                CardSerialNo = toggleC3PayPlusSmsSubscriptionRequest.CardSerialNumber,
                CorporateId = toggleC3PayPlusSmsSubscriptionRequest.CorporateId,
                NotificationType = toggleC3PayPlusSmsSubscriptionRequest.NotificationType,
                MobileNo = toggleC3PayPlusSmsSubscriptionRequest.PhoneNumber.ToZeroPrefixedPhoneNumber(),
                SourceClient = _sourceClient,
                SMSFee = toggleC3PayPlusSmsSubscriptionRequest.SMSFee
            };

            var response = await this._httpClient.PostAsJsonAsync(_toggleC3PayPlusSmsSubscription, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse(false, "");
            }

            var result = await response.Content.ReadAsAsync<SubscribeToSMSNotificationResult>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN000 && responseCode != Enums.SMSNotificationResponse.ESSTN011)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse(false, errorMessage);
            }
            else
            {

                return new ServiceResponse();
            }
        }
        public async Task<ServiceResponse<RollbackC3PayPlusSmsSubscriptionResponseDto>> RollbackC3PayPlusSmsSubscription(RollbackC3PayPlusSmsSubscriptionRequestDto rollbackC3PayPlusSmsSubscriptionRequestDto)
        {
            await GenerateAndAssignToken();

            rollbackC3PayPlusSmsSubscriptionRequestDto.SourceClient = _sourceClient;

            var response = await this._httpClient.PostAsJsonAsync(_rollbackC3PayPlusSmsSubscriptionRequestDto, rollbackC3PayPlusSmsSubscriptionRequestDto);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<RollbackC3PayPlusSmsSubscriptionResponseDto>(false, "");
            }

            var result = await response.Content.ReadAsAsync<RollbackC3PayPlusSmsSubscriptionResponseDto>();

            var resultCode = result.Code;

            Enum.TryParse(resultCode, out Enums.SMSNotificationResponse responseCode);

            if (responseCode != Enums.SMSNotificationResponse.ESSTN000 && responseCode != Enums.SMSNotificationResponse.ESSTN011)
            {
                var errorMessage = EnumUtility.GetDescriptionFromEnumValue(responseCode);

                return new ServiceResponse<RollbackC3PayPlusSmsSubscriptionResponseDto>(false, errorMessage);
            }
            else
            {

                return new ServiceResponse<RollbackC3PayPlusSmsSubscriptionResponseDto>();
            }
        }

    }
}
