﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models.DTOs.Payments.Requests;
using C3Pay.Core.Models.DTOs.Payments.Responses;
using C3Pay.Core.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace C3Pay.Services.Payments.Commands
{
    public class UpdatePaymentAuthorisationDecisionCommand : IRequest<Result>
    {
        public PaymentAuthorisationDecisionRequestDto DecisionRequest { get; set; }
        public CancellationToken CancellationToken { get; set; }
        public string UserPhoneNumber { get; set; }
        public string LanguageCode { get; set; }

        public Result IsQueryValid()
        {
            if (string.IsNullOrWhiteSpace(this.UserPhoneNumber))
            {
                return Result.Failure(Errors.InAppAuth.NoPhoneNumberSent);
            }

            if (this.DecisionRequest.Id == null || this.DecisionRequest.Id == Guid.Empty || !Guid.TryParse(this.DecisionRequest.Id.ToString(), out _))
            {
                return Result.Failure(Errors.InAppAuth.PaymentAuthIdIsInvalid);
            }

            return Result.Success();
        }
    }

    public class UpdatePaymentAuthorisationDecisionCommandHandler : IRequestHandler<UpdatePaymentAuthorisationDecisionCommand, Result>
    {
        private readonly IFeatureManager _featureManager;
        private readonly ILogger _logger;
        private readonly IUserRepository _userRepository;

        public UpdatePaymentAuthorisationDecisionCommandHandler(IFeatureManager featureManager, ILogger<UpdatePaymentAuthorisationDecisionCommandHandler> logger, IUserRepository userRepository)
        {
            _featureManager = featureManager;
            _logger = logger;
            _userRepository = userRepository;
        }
        public async Task<Result> Handle(UpdatePaymentAuthorisationDecisionCommand request, CancellationToken cancellationToken)
        {
            // Check the feature is enabled
            var isFeatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication);
            if (!isFeatureEnabled)
            {
                _logger.LogError(Errors.InAppAuth.PaymentAuthFeatureNotEnabled.Code);
                return Result.Failure(Errors.InAppAuth.PaymentAuthFeatureNotEnabled);
            }

            // Validate query.
            var isQueryValid = request.IsQueryValid();
            if (isQueryValid.IsFailure)
            {
                _logger.LogError(isQueryValid.Error.Code);
                return Result.Failure<PaymentAuthorisationResponseDto>(isQueryValid.Error);
            }

            // Prepare request input.
            request.UserPhoneNumber = request.UserPhoneNumber.Trim();
            request.LanguageCode ??= "en";


            // If user is not found, deleted, blocked, exit.
            var user = await _userRepository.GetUserAsync(request.UserPhoneNumber, request.CancellationToken);

            // User checks.
            if (user is null)
            {
                _logger.LogError(Errors.InAppAuth.UserNotFound.Code);
                return Result.Failure<PaymentAuthorisationResponseDto>(Errors.InAppAuth.UserNotFound);
            }

            return Result.Success();
        }
    }
}
