﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_C3PayPlusUsers_Tbl2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "UserHadBalanceEnquirySubscription",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "UserHadSalaryAlertSmsSubscription",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "UserHadSecuritySmsSubscription",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserHadBalanceEnquirySubscription",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "UserHadSalaryAlertSmsSubscription",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "UserHadSecuritySmsSubscription",
                table: "C3PayPlusMembershipUsers");
        }
    }
}
