﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class DashboardSectionUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "DashboardSections",
                columns: new[] { "Id", "IsActive", "Name" },
                values: new object[] { 2, true, "Subscriptions" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DashboardSections",
                keyColumn: "Id",
                keyValue: 2);
        }
    }
}
