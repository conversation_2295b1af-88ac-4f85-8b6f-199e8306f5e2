﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Config_MT_Steps : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferStatusStep_MoneyTransferTransactions_MoneyTransferTransactionId",
                table: "MoneyTransferStatusStep");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MoneyTransferStatusStep",
                table: "MoneyTransferStatusStep");

            migrationBuilder.RenameTable(
                name: "MoneyTransferStatusStep",
                newName: "MoneyTransferStatusSteps");

            migrationBuilder.RenameIndex(
                name: "IX_MoneyTransferStatusStep_MoneyTransferTransactionId",
                table: "MoneyTransferStatusSteps",
                newName: "IX_MoneyTransferStatusSteps_MoneyTransferTransactionId");

            migrationBuilder.AddPrimary<PERSON>ey(
                name: "PK_MoneyTransferStatusSteps",
                table: "MoneyTransferStatusSteps",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferStatusSteps_MoneyTransferTransactions_MoneyTransferTransactionId",
                table: "MoneyTransferStatusSteps",
                column: "MoneyTransferTransactionId",
                principalTable: "MoneyTransferTransactions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferStatusSteps_MoneyTransferTransactions_MoneyTransferTransactionId",
                table: "MoneyTransferStatusSteps");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MoneyTransferStatusSteps",
                table: "MoneyTransferStatusSteps");

            migrationBuilder.RenameTable(
                name: "MoneyTransferStatusSteps",
                newName: "MoneyTransferStatusStep");

            migrationBuilder.RenameIndex(
                name: "IX_MoneyTransferStatusSteps_MoneyTransferTransactionId",
                table: "MoneyTransferStatusStep",
                newName: "IX_MoneyTransferStatusStep_MoneyTransferTransactionId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MoneyTransferStatusStep",
                table: "MoneyTransferStatusStep",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferStatusStep_MoneyTransferTransactions_MoneyTransferTransactionId",
                table: "MoneyTransferStatusStep",
                column: "MoneyTransferTransactionId",
                principalTable: "MoneyTransferTransactions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
