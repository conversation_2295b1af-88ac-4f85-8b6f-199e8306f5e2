﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class unemppaymentoptionpartnerupdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var start = @" 
                Update UnEmpInsurancePaymentOptions set PartnerId = 1 where PartnerId is null; 
            ";
            migrationBuilder.Sql(start);
            migrationBuilder.DropForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.AlterColumn<int>(
                name: "PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                type: "int",
                nullable: false,
                defaultValue: 1,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true); 

            migrationBuilder.AddForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                column: "PartnerId",
                principalTable: "Partners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.AlterColumn<int>(
                name: "PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 1);

            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 1,
                column: "Code",
                value: "DirectClient");

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 9,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 10,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 11,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 12,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 13,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 14,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 15,
                column: "PartnerId",
                value: null);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 16,
                column: "PartnerId",
                value: null);

            migrationBuilder.AddForeignKey(
                name: "FK_UnEmpInsurancePaymentOptions_Partners_PartnerId",
                table: "UnEmpInsurancePaymentOptions",
                column: "PartnerId",
                principalTable: "Partners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
