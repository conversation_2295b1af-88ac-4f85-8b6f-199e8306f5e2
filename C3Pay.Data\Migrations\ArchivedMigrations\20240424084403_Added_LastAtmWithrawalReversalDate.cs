﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_LastAtmWithrawalReversalDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "HasClaimedAtmWithdrawalFeeReversalForThisMonth",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastAtmWithrawalReversalDate",
                table: "C3PayPlusMembershipUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastAtmWithrawalReversalTransactionNumber",
                table: "C3PayPlusMembershipUsers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UserSubscribedOn",
                table: "C3PayPlusMembershipUsers",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "TimeStamp",
                table: "C3PayPlusMembershipTransactions",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "TransactionType",
                table: "C3PayPlusMembershipTransactions",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasClaimedAtmWithdrawalFeeReversalForThisMonth",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "LastAtmWithrawalReversalDate",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "LastAtmWithrawalReversalTransactionNumber",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "UserSubscribedOn",
                table: "C3PayPlusMembershipUsers");

            migrationBuilder.DropColumn(
                name: "TimeStamp",
                table: "C3PayPlusMembershipTransactions");

            migrationBuilder.DropColumn(
                name: "TransactionType",
                table: "C3PayPlusMembershipTransactions");
        }
    }
}
