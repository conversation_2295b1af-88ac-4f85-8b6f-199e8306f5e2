﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_Fields_Tbl2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "MoneyTransferCountryCode",
                table: "MoneyTransferFieldGroups",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId",
                principalTable: "MoneyTransferCountries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.DropColumn(
                name: "MoneyTransferCountryCode",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId",
                principalTable: "MoneyTransferCountries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
