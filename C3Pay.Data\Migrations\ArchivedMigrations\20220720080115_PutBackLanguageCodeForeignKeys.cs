﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class PutBackLanguageCodeForeignKeys : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "TextContentCode",
                table: "Translations",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LanguageCode",
                table: "Translations",
                type: "nvarchar(5)",
                maxLength: 5,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LanguageCode",
                table: "TextContents",
                type: "nvarchar(5)",
                maxLength: 5,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations",
                column: "LanguageCode");

            migrationBuilder.CreateIndex(
                name: "IX_TextContents_LanguageCode",
                table: "TextContents",
                column: "LanguageCode");

            migrationBuilder.AddForeignKey(
                name: "FK_TextContents_Languages_LanguageCode",
                table: "TextContents",
                column: "LanguageCode",
                principalTable: "Languages",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Translations_Languages_LanguageCode",
                table: "Translations",
                column: "LanguageCode",
                principalTable: "Languages",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TextContents_Languages_LanguageCode",
                table: "TextContents");

            migrationBuilder.DropForeignKey(
                name: "FK_Translations_Languages_LanguageCode",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_TextContents_LanguageCode",
                table: "TextContents");

            migrationBuilder.DropColumn(
                name: "LanguageCode",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "LanguageCode",
                table: "TextContents");

            migrationBuilder.AlterColumn<string>(
                name: "TextContentCode",
                table: "Translations",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);
        }
    }
}
