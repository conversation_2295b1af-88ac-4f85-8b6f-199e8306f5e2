﻿using AutoMapper;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Dropdowns;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Fields;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Rates;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Transfers;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Base;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Beneficiaries;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Dropdowns;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Fields;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Rates;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Tokens;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Providers.RakBank.Transfers;
using Edenred.Common.Core.Models.Settings.Integration.MoneyTransfer.RakBank;
using Edenred.Common.Core.Services.Integration.MoneyTransfer;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Edenred.Common.Services.Integration.MoneyTransfer
{
    public class RakBankMoneyTransferService : IExternalProviderMoneyTransferService
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IOptions<RakBankMoneyTransferSettings> _settings;

        /// <summary>
        /// 
        /// </summary>
        private readonly IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// 
        /// </summary>
        private HttpClient _httpClient;

        /// <summary>
        /// 
        /// </summary>
        private readonly IJWEncryptionService _encryptionService;

        /// <summary>
        /// 
        /// </summary>
        private readonly IDistributedCache _cacheService;

        /// <summary>
        /// 
        /// </summary>
        private readonly ILogger _logger;

        /// <summary>
        /// 
        /// </summary>
        private byte[] _payloadJwePrivateKey;

        /// <summary>
        /// 
        /// </summary>
        private byte[] _payloadJwsPublicKey;

        /// <summary>
        /// 
        /// </summary>
        private IMapper _mapper;

        public RakBankMoneyTransferService(IJWEncryptionService encryptionService,
                                           IOptions<RakBankMoneyTransferSettings> settings,
                                           IHttpClientFactory httpClientFactory,
                                           ILogger<RAKService> logger,
                                           IDistributedCache cacheService,
                                           IMapper mapper)
        {
            _httpClientFactory = httpClientFactory;
            _settings = settings;
            _encryptionService = encryptionService;
            _logger = logger;
            _cacheService = cacheService;
            _mapper = mapper;
        }

        private async Task<ServiceResponse<bool>> InitializeService(string emiratesId, string messageId = null)
        {
            // Initialize service.
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            if (_payloadJwePrivateKey == null)
            {
                var payloadPrivateKey = _settings.Value.PayloadPrivateKey.Replace("\n", "");
                _payloadJwePrivateKey ??= Convert.FromBase64String(payloadPrivateKey);
            }

            if (_payloadJwsPublicKey == null)
            {
                var payloadPublicKey = _settings.Value.PayloadPublicKey.Replace("\n", "");
                _payloadJwsPublicKey ??= Convert.FromBase64String(payloadPublicKey);
            }

            _httpClient ??= _httpClientFactory.CreateClient("RakBank");

            if (string.IsNullOrWhiteSpace(emiratesId))
            {
                return new ServiceResponse<bool>(false, "C3_ERR_PROVIDER_EMIRATES_ID_MISSING");
            }

            var token = await this.GetAccessToken(emiratesId);
            if (string.IsNullOrWhiteSpace(token))
            {
                return new ServiceResponse<bool>(false, "C3_ERR_PROVIDER_TOKEN_ERROR");
            }

            // Set header values for the upcoming request.
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            _httpClient.DefaultRequestHeaders.Add("X-IBM-Client-Id", _settings.Value.ClientId);
            _httpClient.DefaultRequestHeaders.Add("MsgId", messageId ?? Guid.NewGuid().ToString());

            return new ServiceResponse<bool>();
        }
        private async Task<string> GetAccessToken(string emiratesId)
        {
            // Get token from cache if found.
            var tokenCacheKey = "C3_C_EmiratesIdToken";
            var token = await this._cacheService.GetRecordAsync<string>($"{tokenCacheKey}:{emiratesId}");
            if (string.IsNullOrWhiteSpace(token) == false)
            {
                return token;
            }

            // Token was not found or has expired, so we have to generate a new one.
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("X-IBM-Client-Id", _settings.Value.ClientId);
            _httpClient.DefaultRequestHeaders.Add("X-IBM-Client-Secret", _settings.Value.ClientSecretkey);
            _httpClient.DefaultRequestHeaders.Add("MsgId", Guid.NewGuid().ToString());

            var requestBody = new GetRakBankTokenRequest
            {
                GrantType = _settings.Value.TokenGrantType,
                Scope = _settings.Value.TokenScope,
                OAuthMetadata = new RakBankOAuthMetadata() { EmiratesId = emiratesId }
            };

            var requestContent = new StringContent(_encryptionService.Encrypt(requestBody, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PostAsync($"{_settings.Value.UrlPath}/v1/partner_auth/token", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                var rakBakToken = _encryptionService.DecryptAndVerify<RakBankToken>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                await this._cacheService.SetRecordAsync($"{tokenCacheKey}:{emiratesId}", rakBakToken.Token, TimeSpan.FromMinutes(59));
                return rakBakToken.Token;
            }

            else
            {
                var errorResponse = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                _logger.LogError($"C3_ERR_PROVIDER_TOKEN_ERROR. Error: {errorResponse.Description}");
                return null;
            }
        }

        #region Beneficiaries
        public async Task<ServiceResponse<Beneficiary>> CreateBeneficiary(CreateBeneficiaryRequest beneficiary)
        {
            // Auth.
            var initializedService = await InitializeService(beneficiary.EmiratesId, beneficiary.MessageId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<Beneficiary>(false, initializedService.ErrorMessage);
            }

            // Mask Emirates ID.
            var emiratesId = TypeUtility.GetMaskedValue(beneficiary.EmiratesId, 4, 3);

            // Map to provider type.
            var rakBankCreateBeneficiaryRequest = _mapper.Map<CreateRakBankBeneficiaryRequest>(beneficiary);

            // Set version.
            rakBankCreateBeneficiaryRequest.ApiVersion = "3.0.0";

            // Encrypt and call API.
            var requestContent = new StringContent(_encryptionService.Encrypt(rakBankCreateBeneficiaryRequest, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PostAsync($"{_settings.Value.UrlPath}/v3/customers/Emirates_ID:{emiratesId}/beneficiaries", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            var msgId = _httpClient.DefaultRequestHeaders.FirstOrDefault(x => x.Key == "MsgId");

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankBeneficiary = _encryptionService.DecryptAndVerify<RakBankBeneficiary>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var externalProviderBeneficiary = _mapper.Map<Beneficiary>(rakBankBeneficiary);

                // Set Msg ID.
                externalProviderBeneficiary.MsgId = msgId.Value.First();

                // Return.
                return new ServiceResponse<Beneficiary>(externalProviderBeneficiary);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<Beneficiary>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        public async Task<ServiceResponse<Beneficiary>> GetBeneficiaryById(GetBeneficiaryRequest criteria)
        {
            // Auth.
            var initializedService = await InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<Beneficiary>(false, initializedService.ErrorMessage);
            }

            // Mask Emirates ID.
            var emiratesId = TypeUtility.GetMaskedValue(criteria.EmiratesId, 4, 3);

            // Call API.
            var responseMessage = await _httpClient.GetAsync($"{_settings.Value.UrlPath}/v3/customers/Emirates_ID:{emiratesId}/beneficiaries/{criteria.BeneficiaryId}");
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankBeneficiary = _encryptionService.DecryptAndVerify<RakBankBeneficiary>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var externalProviderBeneficiary = _mapper.Map<Beneficiary>(rakBankBeneficiary);

                // Return.
                return new ServiceResponse<Beneficiary>(externalProviderBeneficiary);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<Beneficiary>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        public async Task<ServiceResponse<IEnumerable<Beneficiary>>> GetBeneficiaries(GetBeneficiaryRequest criteria)
        {
            // Auth.
            var initializedService = await this.InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<IEnumerable<Beneficiary>>(false, initializedService.ErrorMessage);
            }

            // Mask Emirates ID.
            var emiratesId = TypeUtility.GetMaskedValue(criteria.EmiratesId, 4, 3);

            // Call API.
            var responseMessage = await _httpClient.GetAsync($"{_settings.Value.UrlPath}/v3/customers/Emirates_ID:{emiratesId}/beneficiaries");
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankBeneficiaries = _encryptionService.DecryptAndVerify<List<RakBankBeneficiary>>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var externalProviderBeneficiaries = _mapper.Map<List<Beneficiary>>(rakBankBeneficiaries);

                // Return.
                return new ServiceResponse<IEnumerable<Beneficiary>>(externalProviderBeneficiaries);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<IEnumerable<Beneficiary>>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        public async Task<ServiceResponse> DeleteBeneficiary(GetBeneficiaryRequest criteria)
        {
            // Auth.
            var initializedService = await this.InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse(false, initializedService.ErrorMessage);
            }

            // Mask Emirates ID.
            var emiratesId = TypeUtility.GetMaskedValue(criteria.EmiratesId, 4, 3);

            // Call API.
            var responseMessage = await _httpClient.DeleteAsync($"{_settings.Value.UrlPath}/v3/customers/Emirates_ID:{emiratesId}/beneficiaries/{criteria.BeneficiaryId}");
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Return.
                return new ServiceResponse();
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse(false, $"{error.ResponseCode}-{error.Description}");
            }

        }
        #endregion

        #region FX Rates
        public async Task<ServiceResponse<FxRate>> GetFxRate(GetFxRateRequest criteria)
        {
            // Auth.
            var initializedService = await this.InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<FxRate>(false, initializedService.ErrorMessage);
            }

            // Map to provider type.
            var rakBankFxRateRequest = _mapper.Map<GetRakBankFxRateRequest>(criteria);

            // Set version.
            rakBankFxRateRequest.TransactionType = "RAKMoney";

            // Encrypt and call API.
            var requestContent = new StringContent(_encryptionService.Encrypt(rakBankFxRateRequest, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PostAsync($"{_settings.Value.UrlPath}/v2/fx_rates/query", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankFxRate = _encryptionService.DecryptAndVerify<RakBankFxRate>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var fxRate = _mapper.Map<FxRate>(rakBankFxRate);

                // Return.
                return new ServiceResponse<FxRate>(fxRate);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<FxRate>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }

        #endregion

        #region Money Transfers
        public async Task<ServiceResponse<Transfer>> GetMoneyTransferById(GetTransferRequest criteria)
        {
            // Auth.
            var initializedService = await this.InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<Transfer>(false, initializedService.ErrorMessage);
            }

            // Call API.
            var responseMessage = await _httpClient.GetAsync($"{_settings.Value.UrlPath}/v2/transactions/{criteria.TransactionId}?transaction_type=RAKMoney");
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankTransfer = _encryptionService.DecryptAndVerify<RakBankTransfer>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var externalProviderTransfer = _mapper.Map<Transfer>(rakBankTransfer);

                // Return.
                return new ServiceResponse<Transfer>(externalProviderTransfer);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<Transfer>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        public async Task<ServiceResponse<Transfer>> SendMoneyTransfer(CreateTransferRequest transfer)
        {
            // Auth.
            var initializedService = await InitializeService(transfer.Sender.CustomerIdNumber, transfer.MessageId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<Transfer>(false, initializedService.ErrorMessage);
            }

            // Mask Emirates ID.
            transfer.Sender.CustomerIdNumber = TypeUtility.GetMaskedValue(transfer.Sender.CustomerIdNumber, 4, 3);

            // Map to provider type.
            var rakBankCreateTransferRequest = _mapper.Map<CreateRakBankTransferRequest>(transfer);

            // Set version.
            rakBankCreateTransferRequest.TransactionType = "RAKMoney";
            rakBankCreateTransferRequest.ApiVersion = "2.4.0";
            rakBankCreateTransferRequest.PreValidateFlag = transfer.PreValidateFlag ? "Y" : "N";

            // Encrypt and call API.
            var requestContent = new StringContent(_encryptionService.Encrypt(rakBankCreateTransferRequest, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PutAsync($"{_settings.Value.UrlPath}/v2/transactions/{transfer.C3ReferenceNumber}", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            var msgId = _httpClient.DefaultRequestHeaders.FirstOrDefault(x => x.Key == "MsgId");

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankTransfer = _encryptionService.DecryptAndVerify<RakBankTransfer>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var externalProviderTransfer = _mapper.Map<Transfer>(rakBankTransfer);

                // Set Msg ID.
                externalProviderTransfer.MsgId = msgId.Value.First();

                // Return.
                return new ServiceResponse<Transfer>(externalProviderTransfer);

            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<Transfer>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        #endregion

        #region Fields
        public async Task<ServiceResponse<List<Field>>> GetAdditionalFields(GetFieldRequest criteria)
        {
            // Auth.
            var initializedService = await this.InitializeService(criteria.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<List<Field>>(false, initializedService.ErrorMessage);
            }

            // Map to provider type.
            var rakBankFieldsRequest = _mapper.Map<GetRakBankFieldsRequest>(criteria);

            // Encrypt and call API.
            var requestContent = new StringContent(_encryptionService.Encrypt(rakBankFieldsRequest, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PostAsync($"{_settings.Value.UrlPath}/v1/fetch/field_lists", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankFields = _encryptionService.DecryptAndVerify<List<RakBankField>>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var fields = _mapper.Map<List<Field>>(rakBankFields);

                // Return.
                return new ServiceResponse<List<Field>>(fields);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<List<Field>>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }

        #endregion

        #region Dropdowns
        public async Task<ServiceResponse<List<DropdownOption>>> GetDropdownOptions(GetDropdownOptionsRequest request)
        {
            // Auth.
            var initializedService = await this.InitializeService(request.EmiratesId);
            if (initializedService.IsSuccessful == false)
            {
                return new ServiceResponse<List<DropdownOption>>(false, initializedService.ErrorMessage);
            }

            // Map to provider type.
            var rakBankDropdownsRequest = _mapper.Map<GetRakBankDropdownOptionsRequest>(request);

            // Encrypt and call API.
            var requestContent = new StringContent(_encryptionService.Encrypt(rakBankDropdownsRequest, _payloadJwePrivateKey, _payloadJwsPublicKey), Encoding.UTF8, _settings.Value.ContentType);
            var responseMessage = await _httpClient.PostAsync($"{_settings.Value.UrlPath}/v1/fetch/field_values", requestContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            // Check response.
            if (responseMessage.IsSuccessStatusCode && responseMessage.Content.Headers.ContentType?.MediaType == "application/json")
            {
                // Decrypt.
                var rakBankDropdownOptions = _encryptionService.DecryptAndVerify<List<RakBankDropdownOption>>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);

                // Map back.
                var dropdownOptions = _mapper.Map<List<DropdownOption>>(rakBankDropdownOptions);

                // Returns.
                return new ServiceResponse<List<DropdownOption>>(dropdownOptions);
            }
            else
            {
                var error = _encryptionService.DecryptAndVerify<RakBankBaseResponse>(responseContent, _payloadJwePrivateKey, _payloadJwsPublicKey);
                return new ServiceResponse<List<DropdownOption>>(false, $"{error.ResponseCode}-{error.Description}");
            }
        }
        #endregion
    }
}