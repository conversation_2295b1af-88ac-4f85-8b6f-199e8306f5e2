﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SeedingLoginVideos : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                column: "Metadata",
                value: "{\"cta_primary_style\":\"blue_button\",\"cta_primary_deeplink\":\"/c3payplus/insuranceVideo\",\"cta_primary_label\":\"Ok, GotIt\",\"cta_change_language_label\":\"Change Language\",\"cta_secondary_label\":\"Skip\",\"cta_secondary_deeplink\":\"/dashboard\",\"time_out_seconds\":10,\"show_skip_button\":false,\"show_seek_bar_view\":true,\"show_video_replay_button\":true}");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                column: "Metadata",
                value: null);
        }
    }
}
