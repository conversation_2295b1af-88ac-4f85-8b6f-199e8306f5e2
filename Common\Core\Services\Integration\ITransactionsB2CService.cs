﻿using Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Core.Models.Messages.Integration.Transactions;
using System.Threading;
using System.Threading.Tasks;

namespace Edenred.Common.Core
{
    public interface ITransactionsB2CService
    {
        Task<ServiceResponse<GetCardTransactionsResponseDto>> GetCardTransactions(GetCardTransactionRequestDto cardTransactionsRequest, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<GetCardTransactionsResponseDto>> GetCardTransactionsV2(GetCardTransactionRequestV2Dto cardTransactionsRequest, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse<GetDailySpendAmountResponse>> GetDailySpendAmount(GetDailySpendAmountRequest request);
        Task<ServiceResponse<GetTransactionStatusResponse>> GetTransactionStatusBySerialNumber(string transactionSerialNumber);
        Task<ServiceResponse<GetAtmWithdrawalTransactionResponse>> GetAtmWithdrawalTransaction(GetAtmWithdrawalTransactionRequest request);
        Task<ServiceResponse<GetSubscriptionTransactionsResponse>> GetSubscriptionTransactions(GetSubscriptionTransactionsRequest request);
        Task<ServiceResponse<GetC3PayPlusTransactionsResponse>> GetC3PayPlusTransactions(GetC3PayPlusTransactionsRequest request);
        Task<ServiceResponse<GetMonthlyPosSpendingsResponse>> GetMonthlyPosSpendings(GetMonthlyPosSpendingsRequest request);
        Task<ServiceResponse<GetAtmTransactionDetailsResponse>> GetAtmTransactionDetails(GetAtmTransactionDetailsRequest request);
    }
}
