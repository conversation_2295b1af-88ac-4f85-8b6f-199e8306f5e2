﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_StatementFee_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "StatementFees",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    PartnerId = table.Column<int>(type: "int", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Option = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StatementFees", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StatementFees_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "StatementFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "Option", "PartnerId", "UpdatedDate" },
                values: new object[] { new Guid("b13b85df-fc93-4370-b748-7e4f25eef1f0"), new DateTime(2023, 5, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 26.25m, false, 1, 1, null });

            migrationBuilder.InsertData(
                table: "StatementFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "Option", "PartnerId", "UpdatedDate" },
                values: new object[] { new Guid("b24bdea9-82e5-4482-93f3-03a8f98fa953"), new DateTime(2023, 5, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 26.25m, false, 2, 1, null });

            migrationBuilder.CreateIndex(
                name: "IX_StatementFees_PartnerId",
                table: "StatementFees",
                column: "PartnerId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StatementFees");
        }
    }
}
