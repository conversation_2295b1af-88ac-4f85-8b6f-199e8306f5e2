﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedAutoUnblockComponentToUser : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "AutoUnblock_LastWrongAttemptDate",
                table: "Users",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "AutoUnblock_WrongAttemptsCount",
                table: "Users",
                type: "int",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AutoUnblock_LastWrongAttemptDate",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "AutoUnblock_WrongAttemptsCount",
                table: "Users");
        }
    }
}
