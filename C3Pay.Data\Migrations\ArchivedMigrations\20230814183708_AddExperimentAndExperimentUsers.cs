﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddExperimentAndExperimentUsers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferExperimentUsers");

            migrationBuilder.CreateTable(
                name: "Experiments",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: false),
                    FeatureId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Experiments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Experiments_Features_FeatureId",
                        column: x => x.FeatureId,
                        principalTable: "Features",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ExperimentUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    GroupCode = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false),
                    ExperimentId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExperimentUsers_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ExperimentUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Experiments",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "FeatureId", "IsDeleted", "Name", "UpdatedDate" },
                values: new object[] { 1, new DateTime(2023, 8, 14, 22, 37, 5, 582, DateTimeKind.Local).AddTicks(7819), null, null, 2, false, "MoneyBackGuarantee", null });

            migrationBuilder.InsertData(
                table: "Experiments",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "FeatureId", "IsDeleted", "Name", "UpdatedDate" },
                values: new object[] { 2, new DateTime(2023, 8, 14, 22, 37, 5, 582, DateTimeKind.Local).AddTicks(8735), null, null, 2, false, "SocialProofing", null });

            migrationBuilder.CreateIndex(
                name: "IX_Experiments_FeatureId",
                table: "Experiments",
                column: "FeatureId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentUsers_ExperimentId",
                table: "ExperimentUsers",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentUsers_UserId",
                table: "ExperimentUsers",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExperimentUsers");

            migrationBuilder.DropTable(
                name: "Experiments");

            migrationBuilder.CreateTable(
                name: "MoneyTransferExperimentUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExperimentTypeId = table.Column<int>(type: "int", nullable: false),
                    GroupCode = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferExperimentUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferExperimentUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferExperimentUsers_UserId",
                table: "MoneyTransferExperimentUsers",
                column: "UserId");
        }
    }
}
