﻿using AutoMapper;
using Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.Transactions;
using EdenredExternalService;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Clients.ActiveDirectory;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace Edenred.Common.Services
{
    /// <summary>
    /// Transactions B2C MicroService
    /// </summary>
    public class TransactionsB2CService : ITransactionsB2CService
    {
        private readonly HttpClient _httpClient;
        public readonly TransactionServiceSettings _settings;
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly string _getCardTransactionsMethodName = "/api/v{0}/Transactions/GetLastTransactionsPerCard";
        private readonly string _getCardTransactionsV2MethodName = "/api/v{0}/Transactions/GetLastTransactionsPerCardV2";
        private readonly string _sourceClient = "RAK";
        private readonly string _getDailyAmountSpentUrl = "/api/v{0}/Transactions/GetDailySpendAmount";
        private readonly string _getTransactionStatusBySerialNumberUrl = "/api/v{0}/Transactions/GetTransactionStatusBySerialNumber";
        private readonly string _getGetAtmWithdrawalTransaction = "/api/v{0}/Transactions/GetAtmWithdrawalTransaction";
        private readonly string _getSubscriptionTransactions = "/api/v{0}/Transactions/GetSubscriptionTransactions";
        private readonly string _getC3PayPlusTransactions = "/api/v{0}/Transactions/GetC3PayPlusTransactions";
        private readonly string _getAtmTransactionDetails = "/api/v{0}/Transactions/GetAtmTransactionDetails";


        public TransactionsB2CService(IOptions<TransactionServiceSettings> settings,
            System.Net.Http.IHttpClientFactory httpClientFactory,
            ILogger<TransactionsB2CService> logger,
            IMapper mapper)
        {
            _httpClient = httpClientFactory.CreateClient("TransactionsB2CService");
            _logger = logger;
            _settings = settings.Value;
            _mapper = mapper;
        }

        /// <summary>
        /// Get Card Transactions
        /// </summary>
        /// <param name="cardTransactionsRequest"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<GetCardTransactionsResponseDto>> GetCardTransactions(GetCardTransactionRequestDto cardTransactionsRequest, CancellationToken cancellationToken = default(CancellationToken))
        {
            // Token Generation For external API Call
            await GenerateAndAssignToken();
         
            // External API Call based on Version Passed 
            var getTransactionsRequest = new GetCardTransactionsByPageRequest()
            {
                CitizenId = cardTransactionsRequest.CitizenId,
                PageNo = cardTransactionsRequest.PageNumber,
                SourceClient = _sourceClient
            };
            string apiUrl = $"{string.Format(_getCardTransactionsMethodName, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            var response = await this._httpClient.PostAsJsonAsync(apiUrl, getTransactionsRequest, cancellationToken);

            // If SUCCESS (200 Ok) 
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardTransactionsResponseDto>(false, "");
            }
            var result = await response.Content.ReadAsAsync<GetLastTransactionPerCardResult>();

            // Custom Validation based on "Code" from external service
            var resultCode = result.Code;
            var parsingResult = Enum.TryParse(resultCode, out Enums.GetLastTransactionsPerCardResponse responseCode);
            if (responseCode != Enums.GetLastTransactionsPerCardResponse.ESSOA000 || parsingResult == false)
            {
                this._logger.LogError(SystemMessages.FetchingB2CCardTransactionsFailed, cardTransactionsRequest.CitizenId, cardTransactionsRequest.PageNumber, result.Description);
                return new ServiceResponse<GetCardTransactionsResponseDto>(false, result.Description);
            }

            // Mapping and Returning Result
            var mappedResult = this._mapper.Map<GetCardTransactionsResponseDto>(result);
            return new ServiceResponse<GetCardTransactionsResponseDto>(mappedResult);
        }

        /// <summary>
        /// Get Card Transactions V2
        /// </summary>
        /// <param name="cardTransactionsRequest"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<GetCardTransactionsResponseDto>> GetCardTransactionsV2(GetCardTransactionRequestV2Dto cardTransactionsRequest, CancellationToken cancellationToken = default)
        {
            // Token Generation For external API Call
            await GenerateAndAssignToken();

            // External API Call based on Version Passed 
            var getTransactionsRequest = new GetCardTransactionsByPageV2Request()
            {
                PPSAccountNumber = cardTransactionsRequest.PPSAccountNumber,
                PageNo = cardTransactionsRequest.PageNo
            };
            string apiUrl = $"{string.Format(_getCardTransactionsV2MethodName, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);

            var response = await this._httpClient.PostAsJsonAsync(apiUrl, getTransactionsRequest, cancellationToken);

            // If SUCCESS (200 Ok) 
            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetCardTransactionsResponseDto>(false, "");
            }
            var result = await response.Content.ReadAsAsync<GetLastTransactionPerCardResult>();

            // Custom Validation based on "Code" from external service
            var resultCode = result.Code;
            var parsingResult = Enum.TryParse(resultCode, out Enums.GetLastTransactionsPerCardResponse responseCode);
            if (responseCode != Enums.GetLastTransactionsPerCardResponse.ESSOA000 || parsingResult == false)
            {
                this._logger.LogError(SystemMessages.FetchingB2CCardTransactionsV2Failed, cardTransactionsRequest.PPSAccountNumber, cardTransactionsRequest.PageNo, result.Description);
                return new ServiceResponse<GetCardTransactionsResponseDto>(false, result.Description);
            }

            // Mapping and Returning Result
            var mappedResult = this._mapper.Map<GetCardTransactionsResponseDto>(result);
            return new ServiceResponse<GetCardTransactionsResponseDto>(mappedResult);
        }

        /// <summary>
        /// Generate And Assign Token
        /// </summary>
        /// <returns></returns>
        public async Task GenerateAndAssignToken()
        {
            var authenticationContext = new AuthenticationContext(_settings.Authority);

            var clientCredential = new ClientCredential(_settings.ClientId, _settings.ClientSecret);

            var authenticationResult = await authenticationContext.AcquireTokenAsync(_settings.Scope, clientCredential);

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authenticationResult.AccessToken);
        }

        public async Task<ServiceResponse<GetDailySpendAmountResponse>> GetDailySpendAmount(GetDailySpendAmountRequest request)
        {
            // Token Generation For external API Call
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getDailyAmountSpentUrl, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            var response = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetDailySpendAmountResponse>(false, "");
            }

            var result = await response.Content.ReadAsStringAsync();
            var dailyAmount = Newtonsoft.Json.JsonConvert.DeserializeObject<GetDailySpendAmountResponse>(result);

            return new ServiceResponse<GetDailySpendAmountResponse>(dailyAmount);
        }

        public async Task<ServiceResponse<GetTransactionStatusResponse>> GetTransactionStatusBySerialNumber(string transactionSerialNumber)
        {
            // Token Generation For external API Call
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getTransactionStatusBySerialNumberUrl, _settings.APIVersion)}";
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var request = new GetTransactionStatusRequest()
            {
                TransactionSerialNumber = transactionSerialNumber
            };

            var response = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (!response.IsSuccessStatusCode)
            {
                return new ServiceResponse<GetTransactionStatusResponse>(false, "");
            }

            var result = await response.Content.ReadAsStringAsync();

            var statusResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<GetTransactionStatusResponse>(result);

            return new ServiceResponse<GetTransactionStatusResponse>(statusResponse);
        }

        public async Task<ServiceResponse<GetAtmWithdrawalTransactionResponse>> GetAtmWithdrawalTransaction(GetAtmWithdrawalTransactionRequest request)
        {
            // Token Generation For external API Call
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getGetAtmWithdrawalTransaction, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var tryGetAtmWithdrawalTransaction = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (tryGetAtmWithdrawalTransaction.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<GetAtmWithdrawalTransactionResponse>(false, "");
            }

            var result = await tryGetAtmWithdrawalTransaction.Content.ReadAsStringAsync();
            var atmTransaction = Newtonsoft.Json.JsonConvert.DeserializeObject<GetAtmWithdrawalTransactionResponse>(result);

            return new ServiceResponse<GetAtmWithdrawalTransactionResponse>(atmTransaction);
        }


        public async Task<ServiceResponse<GetSubscriptionTransactionsResponse>> GetSubscriptionTransactions(GetSubscriptionTransactionsRequest request)
        {
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getSubscriptionTransactions, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var tryGetSubscriptionTransactions = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (tryGetSubscriptionTransactions.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<GetSubscriptionTransactionsResponse>(false, tryGetSubscriptionTransactions.ReasonPhrase);
            }

            var result = await tryGetSubscriptionTransactions.Content.ReadAsStringAsync();
            var subscriptionTransactions = Newtonsoft.Json.JsonConvert.DeserializeObject<GetSubscriptionTransactionsResponse>(result);

            return new ServiceResponse<GetSubscriptionTransactionsResponse>(subscriptionTransactions);
        }

        public async Task<ServiceResponse<GetC3PayPlusTransactionsResponse>> GetC3PayPlusTransactions(GetC3PayPlusTransactionsRequest request)
        {
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getC3PayPlusTransactions, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var tryGetC3PayPlusTransactions = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (tryGetC3PayPlusTransactions.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<GetC3PayPlusTransactionsResponse>(false, tryGetC3PayPlusTransactions.ReasonPhrase);
            }

            var result = await tryGetC3PayPlusTransactions.Content.ReadAsStringAsync();
            var c3PayPlusTransactions = Newtonsoft.Json.JsonConvert.DeserializeObject<GetC3PayPlusTransactionsResponse>(result);

            return new ServiceResponse<GetC3PayPlusTransactionsResponse>(c3PayPlusTransactions);
        }

        public Task<ServiceResponse<GetMonthlyPosSpendingsResponse>> GetMonthlyPosSpendings(GetMonthlyPosSpendingsRequest request)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<GetAtmTransactionDetailsResponse>> GetAtmTransactionDetails(GetAtmTransactionDetailsRequest request)
        {
            await GenerateAndAssignToken();

            string apiUrl = $"{string.Format(_getAtmTransactionDetails, _settings.APIVersion)}"; // Appending the Version passed externally
            _settings.Timeout = _settings.Timeout == 0 ? 30 : _settings.Timeout;

            if (_httpClient.Timeout == null)
            {
                _httpClient.Timeout = TimeSpan.FromSeconds(_settings.Timeout);
            }

            var tryGetAtmTransactionDetails = await this._httpClient.PostAsJsonAsync(apiUrl, request);

            if (tryGetAtmTransactionDetails.IsSuccessStatusCode == false)
            {
                return new ServiceResponse<GetAtmTransactionDetailsResponse>(false, tryGetAtmTransactionDetails.ReasonPhrase);
            }

            var result = await tryGetAtmTransactionDetails.Content.ReadAsStringAsync();
            var c3PayPlusTransactions = Newtonsoft.Json.JsonConvert.DeserializeObject<GetAtmTransactionDetailsResponse>(result);

            return new ServiceResponse<GetAtmTransactionDetailsResponse>(c3PayPlusTransactions);
        }
    }
}
