﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_C3PayPlusWinners_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PrizeTypeId",
                table: "C3PayPlusMembershipLuckyDrawWinners",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ProfileImageUrl",
                table: "C3PayPlusMembershipLuckyDrawWinners",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PrizeTypeId",
                table: "C3PayPlusMembershipLuckyDrawWinners");

            migrationBuilder.DropColumn(
                name: "ProfileImageUrl",
                table: "C3PayPlusMembershipLuckyDrawWinners");
        }
    }
}
