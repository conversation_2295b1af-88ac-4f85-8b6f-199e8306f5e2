﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Removed_BaseModel_From_Lookups : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "MoneyTransferPartners");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "MoneyTransferPartners");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "MoneyTransferPartners");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "MoneyTransferPartners");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "MoneyTransferPartners");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "MoneyTransferPartnerReasons");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "MoneyTransferPartnerReasons");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "MoneyTransferPartnerReasons");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "MoneyTransferPartnerReasons");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "MoneyTransferPartnerReasons");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "MoneyTransferCorridors");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "MoneyTransferCorridors");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "MoneyTransferCorridors");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "MoneyTransferCorridors");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "MoneyTransferCorridors");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "MoneyTransferPartners",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "MoneyTransferPartners",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "MoneyTransferPartners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "MoneyTransferPartners",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "MoneyTransferPartners",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "MoneyTransferPartnerReasons",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "MoneyTransferPartnerReasons",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "MoneyTransferPartnerReasons",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "MoneyTransferPartnerReasons",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "MoneyTransferPartnerReasons",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "MoneyTransferCorridors",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "MoneyTransferCorridors",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "MoneyTransferCorridors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "MoneyTransferCorridors",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "MoneyTransferCorridors",
                type: "datetime2",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(4614));

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(7242));

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(8498));

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(8507));

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(8536));

            migrationBuilder.UpdateData(
                table: "MoneyTransferCorridors",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 314, DateTimeKind.Local).AddTicks(8539));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 315, DateTimeKind.Local).AddTicks(9842));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(961));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1000));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1001));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1003));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1004));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 7,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1005));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 8,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1006));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 9,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1008));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 10,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1009));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 11,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1010));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 12,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1011));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 13,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1013));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 14,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1014));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 15,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1015));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 16,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1016));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 17,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1018));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 18,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1019));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 19,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1020));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 20,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1021));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 21,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1022));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 22,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1024));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 23,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1025));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartnerReasons",
                keyColumn: "Id",
                keyValue: 24,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 316, DateTimeKind.Local).AddTicks(1026));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartners",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 312, DateTimeKind.Local).AddTicks(6704));

            migrationBuilder.UpdateData(
                table: "MoneyTransferPartners",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedDate",
                value: new DateTime(2021, 9, 27, 1, 51, 1, 312, DateTimeKind.Local).AddTicks(7465));
        }
    }
}
