﻿using C3Pay.Core;
using C3Pay.Core.Models;
using C3Pay.Services.MoneyTransfer.Commands;
using C3Pay.UnitTest.Tests.MoneyTransfer.Fixtures;
using Edenred.Common.Core;
using Moq;
using System;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.UnitTest.Tests.MoneyTransfer.Commands
{
    public class ApproveBeneficiaryCommandTests : IClassFixture<ApproveBeneficiaryCommandFixture>
    {
        private readonly ApproveBeneficiaryCommandFixture _fixture;
        private readonly ApproveBeneficiaryCommandHandler _handler;
        public ApproveBeneficiaryCommandTests(ApproveBeneficiaryCommandFixture fixture)
        {
            _fixture = fixture;
            _handler = new ApproveBeneficiaryCommandHandler(_fixture.UnitOfWorkMock.Object,
                                                            _fixture.RAKServiceMock.Object,
                                                            _fixture.SettingsMock.Object,
                                                            _fixture.TextMessageSenderServiceMock.Object,
                                                            _fixture.MessagingQueueServiceMock.Object,
                                                            _fixture.RAKSettingsMock.Object,
                                                            _fixture.AuditTrailServiceMock.Object,
                                                            _fixture.CacheServiceMock.Object,
                                                            _fixture.LoggerMock.Object);
        }

        [Fact]
        public async Task Handle_BeneficiaryNotFound_ReturnsFailureResult()
        {
            _fixture.ResetMocks();
            // Arrange
            var command = new ApproveBeneficiaryCommand(Guid.NewGuid());
            _fixture.SetupDatabaseToReturnNull();

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("NotFound", result.Error.Code);
            Assert.Equal(TransferStatusValidationMessage.BeneficiaryNotExists.ToString(), result.Error.Message);
        }


        [Fact]
        public async Task Handle_ExternalBeneficiaryAdded()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid(), Guid.NewGuid());
            _fixture.SetupDatabaseToReturnData();
            _fixture.SetupRakServiceToReturnSuccessfulResponse();
            _fixture.TextMessageSenderServiceMock
                    .Setup(service => service.SendMTBeneficiaryApprovedMessage(It.IsAny<string>(), It.IsAny<string>()))
                    .ReturnsAsync(new ServiceResponse { IsSuccessful = true });
            _fixture.SetupDbToReturnMoneyTransferTransactions();
            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            _fixture.RAKServiceMock.Verify(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()), Times.Once);

            _fixture.UnitOfWorkMock.Verify(uow => uow.CommitAsync(), Times.AtLeastOnce);

            _fixture.TextMessageSenderServiceMock.Verify(service => service.SendMTBeneficiaryApprovedMessage(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

            _fixture.MessagingQueueServiceMock.Verify(service => service.SendAsync(It.IsAny<MoneyTransferMessageDto>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<string>()),
                                                                                   Times.AtLeastOnce);

            Assert.True(result.IsSuccess);
            Assert.Equal(Status.APPROVED, _fixture.DbData.Status);
        }

        [Fact]
        public async Task Handle_ExternalBeneficiaryCreationFails()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid(), Guid.NewGuid());
            var beneficiary = new MoneyTransferBeneficiary
            {
                Id = Guid.NewGuid(),
                Status = Status.PENDING,
                IsDeleted = false,
                User = new User
                {
                    MoneyTransferProfileStatus = MoneyTransferProfileStatus.Created,
                    CardHolder = new CardHolder { EmiratesId = "123456" }
                },
                ExternalBeneficiary = null,
                Remarks = null
            };
            _fixture.SetupDatabaseToReturnData();
            _fixture.SetupRakServiceToReturnFailedlResponse();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            _fixture.UnitOfWorkMock.Verify(repo => repo.MoneyTransferBeneficiaries.FirstOrDefaultAsync(It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                                                                                                      It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()),
                                                                                                      Times.AtLeastOnce);

            _fixture.RAKServiceMock.Verify(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()), Times.Once);

            _fixture.UnitOfWorkMock.Verify(uow => uow.CommitAsync(), Times.AtLeastOnce);

            Assert.True(result.IsSuccess);
            Assert.Equal(Status.PENDING, beneficiary.Status);
        }

        [Fact]
        public async Task Handle_NoExternalBeneficiaryAddition()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid(), Guid.NewGuid());
            var beneficiary = new MoneyTransferBeneficiary
            {
                Id = Guid.NewGuid(),
                Status = Status.APPROVED,
                IsDeleted = false,
                User = new User
                {
                    MoneyTransferProfileStatus = MoneyTransferProfileStatus.Pending,
                    CardHolder = new CardHolder { EmiratesId = "123456" }
                },
                ExternalBeneficiary = null
            };

            _fixture.UnitOfWorkMock.Setup(repo => repo.MoneyTransferBeneficiaries.FirstOrDefaultAsync(It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                                                        It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()))
                                                        .ReturnsAsync(beneficiary);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            _fixture.RAKServiceMock.Verify(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()), Times.Never);

            _fixture.UnitOfWorkMock.Verify(uow => uow.CommitAsync(), Times.Once);

            Assert.True(result.IsSuccess);
        }

        [Fact]
        public async Task Handle_RetryLogic_ExternalBeneficiaryAdditionWithRetry()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid(), Guid.NewGuid());
            var beneficiary = new MoneyTransferBeneficiary
            {
                Id = Guid.NewGuid(),
                Status = Status.PENDING,
                IsDeleted = false,
                RetryCount = 2, // Initial retry count
                User = new User
                {
                    MoneyTransferProfileStatus = MoneyTransferProfileStatus.Created,
                    CardHolder = new CardHolder { EmiratesId = "123456" }
                },
                ExternalBeneficiary = null
            };

            _fixture.UnitOfWorkMock.Setup(repo => repo.MoneyTransferBeneficiaries.FirstOrDefaultAsync(It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                                                      It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()))
                                                      .ReturnsAsync(beneficiary);

            _fixture.SetupRakServiceToReturnFailedlResponse_WithRetryMessage();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            _fixture.RAKServiceMock.Verify(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()), Times.Once);

            Assert.Equal(Status.PENDING, beneficiary.Status);
            Assert.Equal(3, beneficiary.RetryCount);
        }

        [Fact]
        public async Task Handle_RetryLogic_ExternalBeneficiaryAdditionMaxRetriesReached()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid());
            var beneficiary = new MoneyTransferBeneficiary
            {
                Id = Guid.NewGuid(),
                Status = Status.PENDING,
                IsDeleted = false,
                RetryCount = 3,
                User = new User
                {
                    MoneyTransferProfileStatus = MoneyTransferProfileStatus.Created,
                    CardHolder = new CardHolder { EmiratesId = "123456" }
                },
                ExternalBeneficiary = null
            };

            _fixture.UnitOfWorkMock.Setup(repo => repo.MoneyTransferBeneficiaries.FirstOrDefaultAsync(It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                                                     It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()))
                                                     .ReturnsAsync(beneficiary);
            _fixture.SetupRakServiceToReturnFailedlResponse_WithRetryMessage();


            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            _fixture.RAKServiceMock.Verify(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()), Times.Once); // Ensure external service is called

            Assert.Equal(Status.FAILED, beneficiary.Status);

            Assert.Equal(4, beneficiary.RetryCount);
        }

        [Fact]
        public async Task Handle_TransactionProcessingOnHold()
        {
            _fixture.ResetMocks();
            // Arrange
            var request = new ApproveBeneficiaryCommand(Guid.NewGuid());
            _fixture.SetupDatabaseToReturnData();
            _fixture.SetupDbToReturnMoneyTransferTransactions();

            _fixture.MessagingQueueServiceMock
                .Setup(service => service.SendAsync(It.IsAny<MoneyTransferMessageDto>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int?>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);

            _fixture.TextMessageSenderServiceMock
                    .Setup(service => service.SendMTBeneficiaryApprovedMessage(It.IsAny<string>(), It.IsAny<string>()))
                    .ReturnsAsync(new ServiceResponse { IsSuccessful = true });

            _fixture.SetupRakServiceToReturnSuccessfulResponse();

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.Equal(Status.ONHOLD,_fixture.OnHoldTransaction.Status);
            Assert.Empty(_fixture.OnHoldTransaction.MoneyTransferStatusSteps);
            _fixture.UnitOfWorkMock.Verify(uow => uow.CommitAsync(), Times.AtLeastOnce);
        }
    }


}
