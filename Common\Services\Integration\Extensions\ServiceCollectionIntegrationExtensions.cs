﻿using Edenred.Common.Core;
using Edenred.Common.Core.Models.Settings.Integration;
using Edenred.Common.Core.Models.Settings.Integration.ExchangeHouse;
using Edenred.Common.Core.Models.Settings.Integration.Firebase;
using Edenred.Common.Core.Models.Settings.Integration.MoneyTransfer.RakBank;
using Edenred.Common.Core.Services.Integration;
using Edenred.Common.Core.Services.Integration.Mock;
using Edenred.Common.Core.Services.Integration.MoneyTransfer;
using Edenred.Common.Services.Azure;
using Edenred.Common.Services.Helper;
using Edenred.Common.Services.Integration;
using Edenred.Common.Services.Integration.MockService;
using Edenred.Common.Services.Integration.MoneyTransfer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using Polly.Timeout;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Authentication;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using static Edenred.Common.Core.Enums;

namespace Edenred.Common.Services.Extension
{

    /// <summary>
    /// 
    /// </summary>
    public static class ServiceCollectionIntegrationExtensions
    {
        public static TimeSpan defaultTimeout { get; set; } = TimeSpan.FromSeconds(90);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddRakMockService(this IServiceCollection services, IConfiguration configuration, bool isDevelopment)
        {
            services.AddScoped<IRAKMockService, RAKMockService>();

            if (isDevelopment)
                services.Configure<RAKSettings>(options => BindRakSettingForLocalDevAsync(configuration, options).ConfigureAwait(false).GetAwaiter().GetResult());
            else
                services.Configure<RAKSettings>(configuration.GetSection("RAKService"));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddRakService(this IServiceCollection services, IConfiguration configuration, bool isDevelopment)
        {
            services.AddScoped<IRAKService, RAKService>();
            services.AddScoped<IJWEncryptionService, JWEncryptionService>();

            if (isDevelopment)
                services.Configure<RAKSettings>(options => BindRakSettingForLocalDevAsync(configuration, options).ConfigureAwait(false).GetAwaiter().GetResult());
            else
                services.Configure<RAKSettings>(configuration.GetSection("RAKService"));


            services.AddKeyVault(configuration);

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient("RAK", (serviceProvider, client) =>
            {
                IOptions<RAKSettings> rakSettings = serviceProvider.GetRequiredService<IOptions<RAKSettings>>();

                client.BaseAddress = new Uri(rakSettings.Value.BaseURL);

            })
            .ConfigurePrimaryHttpMessageHandler(serviceProvider =>
            {
                IKeyVaultService keyValutService = serviceProvider.GetRequiredService<IKeyVaultService>();
                IOptions<RAKSettings> rakSettings = serviceProvider.GetRequiredService<IOptions<RAKSettings>>();

                //Create the certificate
                byte[] certificateBytes;
                if (rakSettings.Value.SSLCertificateBytes != null)
                {
                    certificateBytes = rakSettings.Value.SSLCertificateBytes;
                }
                else
                {
                    certificateBytes = keyValutService.ReadSecret(rakSettings.Value.SSLCertificateName).ConfigureAwait(false).GetAwaiter().GetResult();
                }

                var cert = new X509Certificate2(certificateBytes, rakSettings.Value.SSLCertificatePassword, X509KeyStorageFlags.MachineKeySet);

                //Create the Http Client Handler
                var handler = new HttpClientHandler();
                handler.ClientCertificateOptions = ClientCertificateOption.Manual;
                handler.SslProtocols = SslProtocols.Tls12;
                handler.ClientCertificates.Add(cert);

                return handler;
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddPPSMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPPSMockService, PPSMockService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddPPSService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPPSService, PPSService>();

            services.Configure<PPSServiceSettings>(configuration.GetSection("PPSService"));

            //services.AddHttpClient("PPSService", client =>
            //{
            //    client.BaseAddress = new Uri("wwww");
            //})
            //.AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddPPSWebAuthMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPPSWebAuthMockService, PPSWebAuthMockService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddPPSWebAuthService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IHasherService, HasherService>();
            services.AddScoped<IPPSWebAuthService, PPSWebAuthService>();

            services.Configure<PPSServiceSettings>(configuration.GetSection("PPSService"));

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }


            services.AddHttpClient("PPSService", (serviceProvider, client) =>
            {
                IOptions<PPSServiceSettings> ppsSettings = serviceProvider.GetRequiredService<IOptions<PPSServiceSettings>>();

                client.BaseAddress = new Uri($"{ppsSettings.Value.WebAuthBaseURL}?client={ppsSettings.Value.WebAuthClientId}");
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(ppsSettings.Value.WebAuthContentType));
                client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", ppsSettings.Value.WebAuthContentType);
            })
           .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddDingMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IDingMockService, DingMockService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddDingService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IDingService, DingService>();

            services.Configure<DingServiceSettings>(configuration.GetSection("DingService"));

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient("DingService", (serviceProvider, client) =>
            {
                IOptions<DingServiceSettings> dingSettings = serviceProvider.GetRequiredService<IOptions<DingServiceSettings>>();

                client.BaseAddress = new Uri($"{dingSettings.Value.BaseURL}");
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(dingSettings.Value.ContentType));
                client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", dingSettings.Value.ContentType);
                client.DefaultRequestHeaders.TryAddWithoutValidation("api_key", dingSettings.Value.ClientApiKey);
            })
           .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddSignzyMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IIdentificationMockService, SignzyMockService>();

        }

        public static void AddSecondaryIndividualIdentificationService(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<AzureFaceSettings>(configuration.GetSection("AzureFace"));
            services.Configure<AzureOCRSettings>(configuration.GetSection("AzureOCR"));
            services.Configure<AzureIdentificationServiceSettings>(configuration.GetSection("AzureIdentificationService"));

            services.AddNameMatchService(configuration);

            services.AddScoped<IFaceService, FaceService>();
            services.AddScoped<IOCRService, OCRService>();
            services.AddScoped<ISecondaryIndividualIdentificationService, AzureIndividualIdentificationService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddSignzyService(this IServiceCollection services, IConfiguration configuration)
        {
            var settingsSectionName = "SignzyService";

            services.Configure<IndividualIdentificationServiceSettings>(configuration.GetSection(settingsSectionName));

            services.AddScoped<IPrimaryIndividualIdentificationService, SignzyService>();


            services.AddNameMatchService(configuration);

            var settings = new IndividualIdentificationServiceSettings();

            configuration.GetSection(settingsSectionName).Bind(settings);

            services.AddHttpClient("SignzyService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue(settings.ContentType));
                client.DefaultRequestHeaders.Add("Authorization", settings.Id);

            }).AddHttpMessageHandler<HttpClientLoggingHandler>();

            services.AddHttpClient("SignzyFileExchangeService", client =>
            {
                client.BaseAddress = new Uri(settings.FileExchangeBaseAddress);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue(settings.ContentType));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddEtisalatSMSMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPrimaryTextMessageMockService, EtisalatSMSMockService>();

            services.Configure<EtisalatSMSSettings>(configuration.GetSection("EtisalatSMS"));

        }

        public static void AddEtisalatSMSService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }
            services.AddHttpClient<IPrimaryTextMessageService, EtisalatSMSService>(client =>
            {
                client.BaseAddress = new Uri(configuration["EtisalatSMS:BaseAddress"]);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue(configuration["EtisalatSMS:ContentType"]));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>()
            .AddPolicyHandler((provider, x) => GetFallbackPolicy(provider, typeof(ILogger<EtisalatSMSService>)))
            .AddPolicyHandler(GetRetryPolicy(configuration.GetSection("EtisalatSMS").GetValue<int>("RetryCount")))
            .AddPolicyHandler(GetTimeoutPolicy(configuration.GetSection("EtisalatSMS").GetValue<int>("Timeout")));

            services.Configure<EtisalatSMSSettings>(configuration.GetSection("EtisalatSMS"));

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddInfobipSMSMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<ISecondaryTextMessageMockService, InfobipSMSMockService>();

            services.Configure<InfobipSMSSettings>(configuration.GetSection("InfobipSMS"));

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddInfobipSMSService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient<ISecondaryTextMessageService, InfobipSMSService>(client =>
                {
                    if (configuration["InfobipSMS:SmsMode"]?.ToLower() == InfobipSMSMode.AuthKey.ToString().ToLower())
                    {
                        client.BaseAddress = new Uri(configuration["InfobipSMS:AuthKeyBaseUrl"]);
                        client.DefaultRequestHeaders
                              .Accept
                              .Add(new MediaTypeWithQualityHeaderValue(configuration["InfobipSMS:ContentType"]));
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("App", configuration["InfobipSMS:AuthKey"]);
                    }
                    else
                    {
                        client.BaseAddress = new Uri(configuration["InfobipSMS:BaseAddress"]);
                        client.DefaultRequestHeaders
                              .Accept
                              .Add(new MediaTypeWithQualityHeaderValue(configuration["InfobipSMS:ContentType"]));
                    }
                })
                .AddHttpMessageHandler<HttpClientLoggingHandler>()
                .AddPolicyHandler((provider, x) => GetFallbackPolicy(provider, typeof(ILogger<InfobipSMSService>)))
                .AddPolicyHandler(GetRetryPolicy(configuration.GetSection("InfobipSMS").GetValue<int>("RetryCount")))
                .AddPolicyHandler(GetTimeoutPolicy(configuration.GetSection("InfobipSMS").GetValue<int>("Timeout")));

            services.Configure<InfobipSMSSettings>(configuration.GetSection("InfobipSMS"));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddESMOService(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<ESMOServiceSettings>(configuration.GetSection("ESMOService"));

            services.AddScoped<IESMOService, ESMOService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddESMOWebService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IESMOWebService, ESMOWebService>();

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var settings = new ESMOServiceSettings();

            configuration.GetSection("ESMOService").Bind(settings);

            services.Configure<ESMOServiceSettings>(configuration.GetSection("ESMOService"));

            services.AddHttpClient("ESMOWebService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddHRService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IHRService, HRService>();

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var settings = new HRServiceSettings();

            configuration.GetSection("HRService").Bind(settings);

            services.Configure<HRServiceSettings>(configuration.GetSection("HRService"));

            services.AddHttpClient("HRService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddPayrollService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IPayrollService, PayrollService>();

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var settings = new PayrollServiceSettings();

            configuration.GetSection("PayrollService").Bind(settings);

            services.Configure<PayrollServiceSettings>(configuration.GetSection("PayrollService"));

            services.AddHttpClient("PayrollService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// Add Transactions Webs Service
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddTransactionsB2CService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<ITransactionsB2CService, TransactionsB2CService>();

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var settings = new TransactionServiceSettings();
            configuration.GetSection("TransactionsB2CService").Bind(settings);
            services.Configure<TransactionServiceSettings>(configuration.GetSection("TransactionsB2CService"));
            services.AddHttpClient("TransactionsB2CService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddKYCService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IKYCService, KYCService>();

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var settings = new KYCServiceSettings();

            configuration.GetSection("KYCService").Bind(settings);

            services.Configure<KYCServiceSettings>(configuration.GetSection("KYCService"));

            var timeStamp = DateTime.UtcNow.ToString("o");

            var mac = TypeUtility.EncryptMAC(string.Concat(settings.MethodName, settings.Username, settings.Password, settings.UniqueRef, timeStamp, settings.SharedSecret));

            services.AddHttpClient("KYCService", client =>
            {
                client.BaseAddress = new Uri(settings.BaseAddress);

                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                client.DefaultRequestHeaders.Add("timestamp", timeStamp);
                client.DefaultRequestHeaders.Add("uniqueref", settings.UniqueRef);
                client.DefaultRequestHeaders.Add("mac", mac);

                var credentials = string.Format("\"username\":\"{0}\",\"password\":\"{1}\",\"sponsorcode\":\"{2}\"", settings.Username, settings.Password, settings.SponsorCode);

                client.DefaultRequestHeaders.Add("credentials", $"{{{credentials}}}");
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="settings"></param>
        /// <returns></returns>
        static private async Task BindRakSettingForLocalDevAsync(IConfiguration configuration, RAKSettings settings)
        {
            configuration.GetSection("RAKService").Bind(settings);

            //laod Rak Private Key
            if (!string.IsNullOrEmpty(configuration["DevLocal:RakPrivateCertificate"]))
            {
                var privateKeyText = await File.ReadAllTextAsync(configuration["DevLocal:RakPrivateCertificate"]);
                var privateKeyBlocks = privateKeyText.Split("-", StringSplitOptions.RemoveEmptyEntries);
                settings.PaylaodPrivateKey = privateKeyBlocks[1];
            }


            //laod Rak Public Key
            if (!string.IsNullOrEmpty(configuration["DevLocal:RakPublicCertificate"]))
            {
                var privateKeyText = await File.ReadAllTextAsync(configuration["DevLocal:RakPublicCertificate"]);
                var privateKeyBlocks = privateKeyText.Split("-", StringSplitOptions.RemoveEmptyEntries);
                settings.PaylaodPublicKey = privateKeyBlocks[1];
            }

            //load
            if (!string.IsNullOrEmpty(configuration["DevLocal:RakSSLCertificate"]))
            {
                settings.SSLCertificateBytes = File.ReadAllBytes(configuration["DevLocal:RakSSLCertificate"]);
            }
        }

        public static void AddFirebaseCloudMessagingService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            // Bind Firebase settings.
            var firebaseSettings = new FirebaseNotificationSettings();
            configuration.GetSection("FirebaseNotification").Bind(firebaseSettings);
            services.Configure<FirebaseNotificationSettings>(configuration.GetSection("FirebaseNotification"));

            // Bind Google Auth settings.
            var googleAuthSettings = new GoogleAuthSettings();
            configuration.GetSection("GoogleAuth").Bind(googleAuthSettings);
            services.Configure<GoogleAuthSettings>(configuration.GetSection("GoogleAuth"));

            services.AddHttpClient<IPushNotificationService, FirebaseCloudMessagingService>((serviceProvider, client) =>
            {

                client.BaseAddress = new Uri(firebaseSettings.BaseUrl);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue("application/json"));

                // These are not needed for the new integration.
                //client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", string.Format("key={0}", configuration["FirebaseCloudMessaging:Key"]));
                //client.DefaultRequestHeaders.TryAddWithoutValidation("Sender", string.Format("id={0}", configuration["FirebaseCloudMessaging:SenderId"]));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>()
            .AddPolicyHandler((provider, x) => GetFallbackPolicy(provider, typeof(ILogger<FirebaseCloudMessagingService>)))
            .AddPolicyHandler(GetRetryPolicy(configuration.GetSection("FirebaseCloudMessaging").GetValue<int>("RetryCount")))
            .AddPolicyHandler(GetTimeoutPolicy(configuration.GetSection("FirebaseCloudMessaging").GetValue<int>("Timeout")));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddExchangeHouseMoneyTransferService(this IServiceCollection services, IConfiguration configuration)
        {
            // Get settings
            services.Configure<ExchangeHouseSettings>(configuration.GetSection("ExchangeHouseSettings"));

            // Get provider.
            services.AddScoped<IExchangeHouseMoneyTransferService, ExchangeHouseMoneyTransferService>();

            // Add logging.
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            // Add HTTP client.
            services.AddHttpClient("ExchangeHouse", client =>
            {
                client.BaseAddress = new Uri(configuration["ExchangeHouseSettings:BaseAddress"]);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue("application/json"));
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// <param name="isDevelopment"></param>
        public static void AddEsmoWebMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IESMOWebMockService, ESMOWebMockService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddCleverTapService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IAnalyticsService, CleverTapService>();
            double timeoutInMilliseconds = configuration["CleverTap_TimeoutInMilliseconds"] == null ? (double)3000 : Convert.ToDouble(configuration["CleverTap_TimeoutInMilliseconds"]);
            services.Configure<CleverTapSettings>(configuration.GetSection("CleverTapService"));

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient("CleverTapService", (serviceProvider, client) =>
            {
                var cleverTapSettings = serviceProvider.GetRequiredService<IOptions<CleverTapSettings>>().Value;

                client.BaseAddress = new Uri($"{cleverTapSettings.BaseAddress}");
                client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json");
                client.DefaultRequestHeaders.TryAddWithoutValidation("X-CleverTap-Account-Id", cleverTapSettings.ProjectId);
                client.DefaultRequestHeaders.TryAddWithoutValidation("X-CleverTap-Passcode", cleverTapSettings.PassCode);
            })
           .AddHttpMessageHandler<HttpClientLoggingHandler>()
           .AddPolicyHandler(Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromMilliseconds(timeoutInMilliseconds)));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddPaykiiService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IBillPaymentsService, PaykiiService>();

            services.Configure<PaykiiServiceSettings>(configuration.GetSection("PaykiiService"));

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient("PaykiiService", (serviceProvider, client) =>
            {
                var paykiiSettings = serviceProvider.GetRequiredService<IOptions<PaykiiServiceSettings>>().Value;

                client.BaseAddress = new Uri(paykiiSettings.BaseAddress);

                client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json");
                client.DefaultRequestHeaders.TryAddWithoutValidation("x-api-key", paykiiSettings.APIKey);
                client.DefaultRequestHeaders.TryAddWithoutValidation("Token", paykiiSettings.Token);
                client.Timeout = defaultTimeout;
            })
           .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }


        public static void AddPaykiiMockService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IBillPaymentMockService, PaykiiMockService>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// 
        public static void AddEtisalatPaymentGatewayService(this IServiceCollection services, IConfiguration configuration)
        {
            //add settings
            services.Configure<EtisalatPaymentGatewaySettings>(configuration.GetSection("EtisalatPaymentGatewaySettings"));

            //add provider
            services.AddScoped<IPaymentGatewayService, EtisalatPaymentGatewayService>();


            // Add logging.
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            // Add HTTP client.
            services.AddHttpClient("EtisalatPaymentGatewayService", client =>
            {
                client.BaseAddress = new Uri(configuration["EtisalatPaymentGatewaySettings:BaseUrl"]);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders
                     .Accept
                     .Add(new MediaTypeWithQualityHeaderValue("*/*"));
                client.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json; charset=utf-8");

            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// 
        public static void AddDubaiInsuranceService(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<DubaiInsuranceSettings>(configuration.GetSection("DubaiInsuranceService"));

            services.AddScoped<IDubaiInsuranceService, DubaiInsuranceService>();

            // Add logging.
            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            var credentials = string.Concat(configuration["DubaiInsuranceService:UserName"], ":", configuration["DubaiInsuranceService:Password"]);

            var auth = Convert.ToBase64String(Encoding.ASCII.GetBytes(credentials));

            // Add HTTP client.
            services.AddHttpClient("DubaiInsuranceService", client =>
            {
                client.BaseAddress = new Uri(configuration["DubaiInsuranceService:BaseUrl"]);
                client.DefaultRequestHeaders
                      .Accept
                      .Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", auth);
                client.Timeout = defaultTimeout + new TimeSpan(0, 0, 30);
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();

        }

        /// <summary>
        /// Injects the Sanction Screening Service.
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        /// 
        public static IServiceCollection AddSanctionScreeningService(this IServiceCollection services, IConfiguration configuration)
        {
            var options = new SanctionScreeningApiSettings();
            configuration.GetSection(SanctionScreeningApiSettings.Key).Bind(options);
            services.Configure<SanctionScreeningApiSettings>(configuration.GetSection(SanctionScreeningApiSettings.Key));
        
            Uri uri = null; 
            if (Uri.TryCreate(options.Address, UriKind.Absolute, out var address))
                uri = address;
            
            services.AddHttpClient(SanctionScreeningApiSettings.Key, client => { client.BaseAddress = uri; });
            services.AddTransient<ISanctionScreeningService,SanctionScreeningService>();
            return services;
        }
        
        #region Policies
        private static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy(int timeout)
        {
            timeout = timeout == 0 ? 10 : timeout; //default value incase there is no setting value
            return Policy.TimeoutAsync<HttpResponseMessage>(timeout);
        }

        static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(int retryCount)
        {
            retryCount = retryCount == 0 ? 2 : retryCount;   //default value incase there is no setting value
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .OrResult(msg => !msg.IsSuccessStatusCode)
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(retryCount, retryAttempt => TimeSpan.FromMilliseconds(Math.Pow(2, retryAttempt) * 50)); // wait for 100 milliseconds, then 200 milliseconds
        }

        private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
        {
            return HttpPolicyExtensions.HandleTransientHttpError()
                .CircuitBreakerAsync(3, TimeSpan.FromSeconds(50), (result, retryAttempt) =>
                {
                    Debug.WriteLine("circuit broken :( ");
                },
                () =>
                {
                    Debug.WriteLine("circuit closed :) ");
                });
        }

        private static IAsyncPolicy<HttpResponseMessage> GetAdvancedCircuitBreakerPolicy()
        {
            var policy = Policy
                .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
                .AdvancedCircuitBreakerAsync(0.5, TimeSpan.FromSeconds(60), 10,
                 TimeSpan.FromSeconds(30), (result, retryAttempt) =>
                 {
                     Debug.WriteLine("circuit broken");
                 },
                 () =>
                 {
                     Debug.WriteLine("circuit closed");
                 });
            return policy;
        }

        private static IAsyncPolicy<HttpResponseMessage> GetFallbackPolicy(IServiceProvider provider, Type serviceType)
        {
            var logger = (ILogger)provider.GetRequiredService(serviceType);

            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .OrResult(msg => !msg.IsSuccessStatusCode)
                .Or<TimeoutRejectedException>()
                .FallbackAsync(
                    new HttpResponseMessage(System.Net.HttpStatusCode.ServiceUnavailable),
                    async r =>
                    {
                        if (r.Result != null && r.Result.Content != null)
                        {
                            var failedResult = await r.Result.Content.ReadAsStringAsync();
                            logger.LogInformation("Failed API Response :" + failedResult);
                        }
                    });
        }

        #endregion

        #region Money Transfer
        public static void AddRakBankMoneyTransferService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddScoped<IExternalProviderMoneyTransferService, RakBankMoneyTransferService>();

            services.AddScoped<IJWEncryptionService, JWEncryptionService>();

            services.Configure<RakBankMoneyTransferSettings>(configuration.GetSection("RakBankMoneyTransfer"));

            services.AddKeyVault(configuration);

            if (!services.Any(x => x.ServiceType == typeof(HttpClientLoggingHandler)))
            {
                services.AddScoped<HttpClientLoggingHandler>();
            }

            services.AddHttpClient("RakBank", (serviceProvider, client) =>
            {
                IOptions<RakBankMoneyTransferSettings> settings = serviceProvider.GetRequiredService<IOptions<RakBankMoneyTransferSettings>>();
                client.BaseAddress = new Uri(settings.Value.BaseUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(serviceProvider =>
            {
                IKeyVaultService keyValutService = serviceProvider.GetRequiredService<IKeyVaultService>();
                IOptions<RakBankMoneyTransferSettings> settings = serviceProvider.GetRequiredService<IOptions<RakBankMoneyTransferSettings>>();
                IOptions<RAKSettings> rakSettings = serviceProvider.GetRequiredService<IOptions<RAKSettings>>();

                // TEMP.
                var X509Certificate2Bytes = @"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";
                var certificateBytes = Convert.FromBase64String(X509Certificate2Bytes.Replace("\n", ""));

                //var certificateBytes = Convert.FromBase64String(settings.Value.X509Certificate2Bytes.Replace("\n", ""));
                var certificate = new X509Certificate2(certificateBytes, string.Empty, X509KeyStorageFlags.MachineKeySet);

                //Create the Http Client Handler
                var handler = new HttpClientHandler
                {
                    ClientCertificateOptions = ClientCertificateOption.Manual,
                    SslProtocols = SslProtocols.Tls12
                };
                handler.ClientCertificates.Add(certificate);

                return handler;
            })
            .AddHttpMessageHandler<HttpClientLoggingHandler>();
        }
        #endregion
    }
}