﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class NomineeRelationshipType_Added : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                columns: new[] { "Id", "IsActive", "Name" },
                values: new object[,]
                {
                    { 7, true, "Father" },
                    { 8, true, "Mother" },
                    { 9, true, "Son" },
                    { 10, true, "Daughter" },
                    { 11, true, "Brother" },
                    { 12, true, "Sister" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 12);
        }
    }
}
