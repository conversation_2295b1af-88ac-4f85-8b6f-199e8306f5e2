﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Gw_Dynamic : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsGwHighlighted",
                table: "NavigationFields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsGwHighlighted",
                table: "Fields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "GwTooltipDescription",
                table: "FieldGroups",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "GwTooltipIndex",
                table: "FieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "GwTooltipPosition",
                table: "FieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "GwTooltipTitle",
                table: "FieldGroups",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsGwHighlighted",
                table: "NavigationFields");

            migrationBuilder.DropColumn(
                name: "IsGwHighlighted",
                table: "Fields");

            migrationBuilder.DropColumn(
                name: "GwTooltipDescription",
                table: "FieldGroups");

            migrationBuilder.DropColumn(
                name: "GwTooltipIndex",
                table: "FieldGroups");

            migrationBuilder.DropColumn(
                name: "GwTooltipPosition",
                table: "FieldGroups");

            migrationBuilder.DropColumn(
                name: "GwTooltipTitle",
                table: "FieldGroups");
        }
    }
}
