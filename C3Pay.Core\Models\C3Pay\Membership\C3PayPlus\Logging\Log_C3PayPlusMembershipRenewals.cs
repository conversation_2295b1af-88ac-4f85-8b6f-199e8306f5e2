﻿using System;

namespace C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logging
{
    public class Log_C3PayPlusMembershipRenewals
    {
        #region Initial Log Details

        /// <summary>
        /// Log ID.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The target renewal date. Example: If today is the 15th, this would represent memberships created on the 15th of any month.
        /// </summary>
        public DateTime RenewingFor { get; set; }

        /// <summary>
        /// The actual date and time when the renewal process started.
        /// </summary>
        public DateTime JobInvokedAt { get; set; }

        #endregion

        #region Time Metrics

        /// <summary>
        /// The exact timestamp when the renewal process started.
        /// </summary>
        public DateTime? JobStartTime { get; set; }

        /// <summary>
        /// The exact timestamp when the renewal process was completed.
        /// </summary>
        public DateTime? JobEndTime { get; set; }

        /// <summary>
        /// The duration of the renewal process in seconds (JobEndTime - JobStartTime).
        /// </summary>
        public int? JobDurationInSeconds { get; set; }

        #endregion

        #region Subscription Processing Counts

        /// <summary>
        /// The number of renewals expected to be processed based on the subscription date.
        /// Example: If today is the 15th, this would represent memberships created on the 15th of any month (excluding the current month).
        /// </summary>
        public int TotalRenewalsExpectedToProcess { get; set; }

        /// <summary>
        /// The number of renewals successfully completed.
        /// </summary>
        public int RenewalsCompletedSuccessfully { get; set; }

        /// <summary>
        /// The number of unsubscriptions successfully processed.
        /// </summary>
        public int SuccessfulUnsubscribes { get; set; }

        /// <summary>
        /// The total number of renewals skipped due to incomplete processing or exceptions.
        /// </summary>
        public int TotalSkippedRenewals { get; set; }

        #endregion

        #region Unsubscription Reasons

        /// <summary>
        /// The number of memberships unsubscribed due to the user being deleted.
        /// </summary>
        public int UnsubscribedDueToDeletedUsers { get; set; }

        /// <summary>
        /// The number of memberships unsubscribed due to the user's decision.
        /// </summary>
        public int UnsubscribedDueToUserDecision { get; set; }

        /// <summary>
        /// The number of memberships unsubscribed due to missing age information.
        /// </summary>
        public int UnsubscribedDueToMissingAgeInformation { get; set; }

        /// <summary>
        /// The number of memberships unsubscribed due to the user exceeding the allowed age limit.
        /// </summary>
        public int UnsubscribedDueToExceedingAgeLimit { get; set; }

        /// <summary>
        /// The number of memberships unsubscribed due to a failed dormant card check.
        /// </summary>
        public int UnsubscribedDueToDormantCardCheckFailure { get; set; }

        /// <summary>
        /// The number of memberships unsubscribed due to the user's card being inactive or dormant.
        /// </summary>
        public int UnsubscribedDueToDormantCard { get; set; }
       
        /// <summary>
        /// The number of memberships unsubscribed due to no salary being credited in the last three months.
        /// </summary>
        public int UnsubscribedDueToNoSalaryCreditedInLast3Months { get; set; }


        /// <summary>
        /// The number of memberships unsubscribed due to other reasons not explicitly listed.
        /// </summary>
        public int UnsubscribedDueToOtherReasons { get; set; }

        #endregion

        #region Errors

        /// <summary>
        /// The number of renewals skipped due to a missing Balance Enquiry subscription.
        /// </summary>
        public int SkippedRenewalsDueToBalanceEnquirySubscriptionNotFound { get; set; }

        /// <summary>
        /// The number of renewals skipped due to a missing security SMS subscription code.
        /// </summary>
        public int SkippedRenewalsDueToMissingSecuritySmsSubscriptionCode { get; set; }

        /// <summary>
        /// The number of renewals skipped due to a missing security SMS subscription fee.
        /// </summary>
        public int SkippedRenewalsDueToMissingSecuritySmsSubscriptionFee { get; set; }

        /// <summary>
        /// The number of renewals skipped due to an issue subscribing back to the security SMS service.
        /// </summary>
        public int SkippedRenewalsDueToIssueSubscribingBackToSecuritySms { get; set; }

        /// <summary>
        /// The number of renewals skipped due to a missing salary alert subscription code.
        /// </summary>
        public int SkippedRenewalsDueToMissingSalaryAlertSubscriptionCode { get; set; }

        /// <summary>
        /// The number of renewals skipped due to a missing salary alert subscription fee.
        /// </summary>
        public int SkippedRenewalsDueToMissingSalaryAlertSubscriptionFee { get; set; }

        /// <summary>
        /// The number of renewals skipped due to an issue subscribing back to the salary alert service.
        /// </summary>
        public int SkippedRenewalsDueToIssueSubscribingBackToSalaryAlert { get; set; }
   
        /// <summary>
        /// The number of renewals skipped due to no billing history being found for the user.
        /// </summary>
        public int SkippedRenewalsDueToNoBillingHistoryFound { get; set; }

        /// <summary>
        /// The number of renewals skipped because billing has already occurred in the same month.
        /// </summary>
        public int SkippedRenewalsDueToBillingInSameMonth { get; set; }

        /// <summary>
        /// The number of renewals skipped due to the inability to retrieve the user's balance.
        /// </summary>
        public int SkippedRenewalsDueToUnableToRetrieveBalance { get; set; }

        /// <summary>
        /// The number of renewals skipped due to the inability to confirm whether the user received their salary.
        /// </summary>
        public int SkippedRenewalsDueToUnableToConfirmSalaryReceived { get; set; }
        
        /// <summary>
        /// The number of renewals skipped due to the inability to debit the user's account.
        /// </summary>
        public int SkippedRenewalsDueToUnableToDebitUser { get; set; }

        #endregion

        #region Process Status

        /// <summary>
        /// The status of the current renewal process.
        /// </summary>
        public C3PayPlusRenewalRunStatus RunStatus { get; set; }

        /// <summary>
        /// Additional remarks for the renewal process.
        /// </summary>
        public string Remarks { get; set; }

        #endregion
    }

}
