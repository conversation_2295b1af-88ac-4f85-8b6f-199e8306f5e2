﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_C3PayPlusMembershipTransactionTbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipTransactions",
                columns: table => new
                {
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ServiceProvider = table.Column<int>(type: "int", nullable: false),
                    CardNumber = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    CardSerialNumber = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BillPayType = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    AuthenticationCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    PayeeId = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Origin = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountTerminalId = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    CardAccountTerminalAddress = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MacValue = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EndBalance = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Date = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Time = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    StatusCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    StatusDescription = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    C3PayPlusMembershipUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipTransactions", x => x.ReferenceNumber);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipTransactions_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                        column: x => x.C3PayPlusMembershipUserId,
                        principalTable: "C3PayPlusMembershipUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipTransactions_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipTransactions_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipTransactions",
                column: "C3PayPlusMembershipUserId");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipTransactions_UserId",
                table: "C3PayPlusMembershipTransactions",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipTransactions");
        }
    }
}
