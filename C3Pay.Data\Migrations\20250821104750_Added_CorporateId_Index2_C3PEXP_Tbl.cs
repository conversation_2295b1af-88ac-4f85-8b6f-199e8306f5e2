﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_CorporateId_Index2_C3PEXP_Tbl : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CorporateId",
                table: "C3PayPlusMembershipExperimentUsers",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipExperimentUsers_CorporateId",
                table: "C3PayPlusMembershipExperimentUsers",
                column: "CorporateId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_C3PayPlusMembershipExperimentUsers_CorporateId",
                table: "C3PayPlusMembershipExperimentUsers");

            migrationBuilder.DropColumn(
                name: "CorporateId",
                table: "C3PayPlusMembershipExperimentUsers");
        }
    }
}
