﻿using Edenred.Common.Core;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;

namespace Edenred.Common.Services
{
    public class OpenTelemetryService : IOpenTelemetryService
    {
        private static readonly ActivitySource ActivitySource = new("C3Pay.Common");
        private static readonly Meter Meter = new("C3Pay.Common");
        private readonly ILogger<OpenTelemetryService> _logger;

        public OpenTelemetryService(ILogger<OpenTelemetryService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Track a custom event using Activity and structured logging
        /// </summary>
        /// <param name="eventName">Name of the event</param>
        /// <param name="userName">User associated with the event</param>
        /// <param name="properties">Additional properties</param>
        public void TrackEvent(string eventName, string userName, IDictionary<string, string> properties)
        {
            using var activity = ActivitySource.StartActivity(eventName);
            
            if (activity != null)
            {
                activity.SetTag("user.id", userName);
                activity.SetTag("event.name", eventName);
                
                if (properties != null)
                {
                    foreach (var prop in properties)
                    {
                        activity.SetTag(prop.Key, prop.Value);
                    }
                }
            }

            // Also log the event
            using var scope = _logger.BeginScope(properties ?? new Dictionary<string, string>());
            _logger.LogInformation("Event: {EventName} for User: {UserName}", eventName, userName);
        }

        /// <summary>
        /// Track an exception using Activity and structured logging
        /// </summary>
        /// <param name="exception">The exception to track</param>
        /// <param name="properties">Additional properties</param>
        public void TrackException(System.Exception exception, IDictionary<string, string> properties)
        {
            using var activity = ActivitySource.StartActivity("Exception");
            
            if (activity != null)
            {
                activity.SetStatus(ActivityStatusCode.Error, exception.Message);
                activity.SetTag("exception.type", exception.GetType().Name);
                activity.SetTag("exception.message", exception.Message);
                activity.SetTag("exception.stacktrace", exception.StackTrace);
                
                if (properties != null)
                {
                    foreach (var prop in properties)
                    {
                        activity.SetTag(prop.Key, prop.Value);
                    }
                }
            }

            // Log the exception
            using var scope = _logger.BeginScope(properties ?? new Dictionary<string, string>());
            _logger.LogError(exception, "Exception occurred: {ExceptionMessage}", exception.Message);
        }

        /// <summary>
        /// Track a custom metric
        /// </summary>
        /// <param name="metricName">Name of the metric</param>
        /// <param name="value">Value of the metric</param>
        /// <param name="properties">Additional properties</param>
        public void TrackMetric(string metricName, double value, IDictionary<string, string> properties)
        {
            var counter = Meter.CreateCounter<double>(metricName);
            
            var tags = new List<KeyValuePair<string, object>>();
            if (properties != null)
            {
                foreach (var prop in properties)
                {
                    tags.Add(new KeyValuePair<string, object>(prop.Key, prop.Value));
                }
            }

            counter.Add(value, tags.ToArray());
            
            // Also log the metric
            using var scope = _logger.BeginScope(properties ?? new Dictionary<string, string>());
            _logger.LogInformation("Metric: {MetricName} = {Value}", metricName, value);
        }
    }
}
