﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddC3PayLifeInsuranceNomineeLookup : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                newName: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId");

            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                column: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes_C3PayPlusMembershipLifeIns~",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                column: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                principalTable: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes_C3PayPlusMembershipLifeIns~",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropIndex(
                name: "IX_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.RenameColumn(
                name: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                newName: "RelationshipTypeId");
        }
    }
}
