﻿using C3Pay.Core.Models;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Edenred.Common.Core.Enums;

namespace C3Pay.Core.Services
{
    public interface IIdentificationMockService
    {
        Task<ServiceResponse<string>> UploadIdentificationDocument(Guid? userId, string content, string documentName, DocumentType documentType);
        Task<ServiceResponse<bool>> CheckIdentificationDocumentQuality(Guid? userId, string fileName, string documentUrl, DocumentType documentType);
        Task<ServiceResponse<object>> ReadIdentificationDocument(Guid? userId, string frontScanFileName, string backScanFileName, string nameToMatch, IdentificationType identificationType);
        Task<ServiceResponse<FaceMatchResultDto>> CheckFaceMatch(Guid? userId, string selfieFileName, string frontScanFileName);
        Task<ServiceResponse> SaveIdentificationDocuments(Guid? userId, List<IdentificationDocumentDto> documents);
        Task<ServiceResponse> AddIdentification(Identification identification);
        Task<ServiceResponse> ApproveIdentification(Guid identificationId, string verifiedBy, Guid? portalUserId = null,string portalEmailId=null);
        Task<ServiceResponse> RejectIdentification(Guid identificationId, string rejectedBy,int userRegistrationRejectionReasonId, Guid? portalUserId = null,string portalEmailId=null);
    }
}
