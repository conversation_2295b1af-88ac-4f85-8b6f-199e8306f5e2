﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateCountryAndExperimentCta : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Corridor",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "NationalityName",
                table: "Countries");

            migrationBuilder.AddColumn<string>(
                name: "CountryCode",
                table: "SocialProofing",
                type: "nvarchar(2)",
                maxLength: 2,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Style",
                table: "ExperimentCtas",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_SocialProofing_CountryCode",
                table: "SocialProofing",
                column: "CountryCode");

            migrationBuilder.AddForeignKey(
                name: "FK_SocialProofing_Countries_CountryCode",
                table: "SocialProofing",
                column: "CountryCode",
                principalTable: "Countries",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SocialProofing_Countries_CountryCode",
                table: "SocialProofing");

            migrationBuilder.DropIndex(
                name: "IX_SocialProofing_CountryCode",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "Style",
                table: "ExperimentCtas");

            migrationBuilder.AddColumn<string>(
                name: "Corridor",
                table: "SocialProofing",
                type: "varchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "SocialProofing",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "SocialProofing",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "SocialProofing",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "SocialProofing",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "SocialProofing",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NationalityName",
                table: "Countries",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true);
        }
    }
}
