using System;
using System.Collections.Generic;

namespace C3Pay.Core.Models.Messages
{
    public class SurchargeTransactionMessage
    {
        public string Id { get; set; }
        public int Version { get; set; }
        public string Product_Class_Id { get; set; }
        public DateTime Created_Date_Time { get; set; }
        public string Txn_Lifecycle_Status { get; set; }
        public TransactionResult Txn_Result { get; set; }
        public Payee Payee { get; set; }
        public Payer Payer { get; set; }
        public SurchargeTransactionType Txn_Type { get; set; }
        public Acceptance Acceptance { get; set; }
        public Clearing Clearing { get; set; }
        public DateTime Completed_Date_Time { get; set; }
        public string Reason_Text { get; set; }
        public List<TransactionLedger> Txn_Ledgers { get; set; }
        public string Scheme_Id { get; set; }
        public string OrianCountryKey { get; set; }
        public string _rid { get; set; }
        public string _self { get; set; }
        public string _etag { get; set; }
        public string _attachments { get; set; }
        public long _ts { get; set; }
    }

    public class TransactionResult
    {
        public string Result { get; set; }
        public string Result_Detail { get; set; }
        public string Result_Code { get; set; }
    }

    public class Payee
    {
        // Empty class as per JSON structure
    }

    public class Payer
    {
        public Account Account { get; set; }
    }

    public class Account
    {
        public string Account_No { get; set; }
        public List<Balance> Balances { get; set; }
        public string Balance_Code { get; set; }
    }

    public class Balance
    {
        public string Type { get; set; }

    }

    public class Currency
    {
        public int Exponent { get; set; }
        public string Code { get; set; }
    }

    public class SurchargeTransactionType
    {
        public string Code { get; set; }
        public string Direction { get; set; }
        public bool Reservation { get; set; }
        public bool Financial { get; set; }
        public bool Activation { get; set; }
        public bool Reversal { get; set; }
    }

    public class Acceptance
    {
        public string Acceptance_Method { get; set; }
    }

    public class Clearing
    {
        public TransactionReference Txn_Reference { get; set; }
        public DateTime Clear_Date_Time { get; set; }
    }

    public class TransactionReference
    {
        public string Scheme_Txn_Reference { get; set; }
    }


    public class TransactionLedger
    {
        public string Txn_Id { get; set; }
        public string Txn_Type { get; set; }
    }
}