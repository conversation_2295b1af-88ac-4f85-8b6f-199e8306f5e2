﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideos : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "LoginVideoTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Type = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LoginVideoSlots",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    VideoTypeId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoSlots", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LoginVideoSlots_LoginVideoTypes_VideoTypeId",
                        column: x => x.VideoTypeId,
                        principalTable: "LoginVideoTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "LoginVideoUrls",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LanguageCode = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VideoTypeId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoUrls", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LoginVideoUrls_LoginVideoTypes_VideoTypeId",
                        column: x => x.VideoTypeId,
                        principalTable: "LoginVideoTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "LoginVideoUserRequests",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RequestCount = table.Column<int>(type: "int", nullable: false),
                    LastDisplayedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    VideoSlotId = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoUserRequests", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LoginVideoUserRequests_LoginVideoSlots_VideoSlotId",
                        column: x => x.VideoSlotId,
                        principalTable: "LoginVideoSlots",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_LoginVideoUserRequests_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoUrls_VideoTypeId",
                table: "LoginVideoUrls",
                column: "VideoTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoUserRequests_UserId",
                table: "LoginVideoUserRequests",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoUserRequests_VideoSlotId",
                table: "LoginVideoUserRequests",
                column: "VideoSlotId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "LoginVideoUrls");

            migrationBuilder.DropTable(
                name: "LoginVideoUserRequests");

            migrationBuilder.DropTable(
                name: "LoginVideoSlots");

            migrationBuilder.DropTable(
                name: "LoginVideoTypes");
        }
    }
}
