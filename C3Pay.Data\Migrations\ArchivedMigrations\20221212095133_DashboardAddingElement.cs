﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class DashboardAddingElement : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "DashboardElements",
                columns: new[] { "Id", "CreatedDate", "DeepLinkUrl", "DeletedBy", "DeletedDate", "DisplayOrder", "IsDeleted", "SectionId", "Type", "UpdatedDate" },
                values: new object[] { 10, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/securityalerts", null, null, 8, false, 1, 1, null });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[] { "da_qck_act_10", "en", 99, "Get Security Alerts", "da_qck_act" });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElements",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "ElementId", "IconUrl", "IsActive", "IsDeleted", "TextContentCode", "UpdatedDate" },
                values: new object[,]
                {
                    { 64, null, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 65, "IN", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 66, "PK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 67, "LK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 68, "PH", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 69, "BD", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null },
                    { 70, "NP", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 10, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png", true, false, "da_qck_act_10", null }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 615, "en", "Get Security Alerts", "da_qck_act_10" },
                    { 616, "bn", "সিকিউরিটি এলার্টস পান", "da_qck_act_10" },
                    { 617, "hi", "सिक्योरिटी अलर्टस पाएं", "da_qck_act_10" },
                    { 618, "hi-en", "Security Alerts Payein", "da_qck_act_10" },
                    { 619, "ml", "സുരക്ഷാ അലേർട്ടുകൾ നേടുക", "da_qck_act_10" },
                    { 620, "ta", "பாதுகாப்பு அலெர்ட் பெறுங்கள்", "da_qck_act_10" },
                    { 621, "ur-en", "Get Security Alerts", "da_qck_act_10" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 64);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 65);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 66);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 67);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 68);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 69);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 70);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 615);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 616);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 617);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 618);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 619);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 620);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 621);

            migrationBuilder.DeleteData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_10");
        }
    }
}
