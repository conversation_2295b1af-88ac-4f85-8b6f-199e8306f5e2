﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddMoneyTransferSmartDefaults : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferSmartDefaults",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    NeverSentMoneyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    NeverSentToBeneficiaryAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "varchar(3)", maxLength: 3, nullable: false),
                    Type = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferSmartDefaults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferSmartDefaults_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferSmartDefaults",
                columns: new[] { "Id", "CountryCode", "Currency", "NeverSentMoneyAmount", "NeverSentToBeneficiaryAmount", "Type" },
                values: new object[] { 1, "IN", "INR", 1000m, 10000m, "Receive" });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferSmartDefaults_CountryCode",
                table: "MoneyTransferSmartDefaults",
                column: "CountryCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferSmartDefaults");
        }
    }
}
