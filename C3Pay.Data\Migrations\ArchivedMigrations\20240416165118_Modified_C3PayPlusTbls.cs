﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_C3PayPlusTbls : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Price",
                table: "C3PayPlusMembershipBenefits",
                newName: "PriceLabel");

            migrationBuilder.AlterColumn<decimal>(
                name: "PriceVatInclusive",
                table: "C3PayPlusMemberships",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Price",
                table: "C3PayPlusMemberships",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "SummarySubtitle",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SummaryTitle",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Price",
                table: "C3PayPlusMemberships");

            migrationBuilder.DropColumn(
                name: "SummarySubtitle",
                table: "C3PayPlusMembershipBenefits");

            migrationBuilder.DropColumn(
                name: "SummaryTitle",
                table: "C3PayPlusMembershipBenefits");

            migrationBuilder.RenameColumn(
                name: "PriceLabel",
                table: "C3PayPlusMembershipBenefits",
                newName: "Price");

            migrationBuilder.AlterColumn<string>(
                name: "PriceVatInclusive",
                table: "C3PayPlusMemberships",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)",
                oldPrecision: 18,
                oldScale: 2);
        }
    }
}
