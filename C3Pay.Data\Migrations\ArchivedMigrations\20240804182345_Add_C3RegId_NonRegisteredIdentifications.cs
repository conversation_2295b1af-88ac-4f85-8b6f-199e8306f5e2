﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Add_C3RegId_NonRegisteredIdentifications : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "C3RegistrationId",
                table: "NonRegisteredIdentification",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "C3RegistrationId",
                table: "NonRegisteredIdentification");

        }
    }
}
