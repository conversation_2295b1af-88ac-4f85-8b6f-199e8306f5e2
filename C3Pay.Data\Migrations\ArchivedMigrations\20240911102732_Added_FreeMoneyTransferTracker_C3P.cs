﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_FreeMoneyTransferTracker_C3P : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "HasClaimedTheFreeMoneyTransferForThisMonth",
                table: "C3PayPlusMembershipUsers",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "HasClaimedTheFreeMoneyTransferForThisMonth",
                table: "C3PayPlusMembershipUsers");
        }
    }
}
