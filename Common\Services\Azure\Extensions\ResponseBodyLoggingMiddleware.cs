﻿using Microsoft.AspNetCore.Http;
using System.IO;
using System.Threading.Tasks;
using System.Diagnostics;

namespace Edenred.Common.Services.Azure.Extensions
{
    public class ResponseBodyLoggingMiddleware : IMiddleware    
    {
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var originalBodyStream = context.Response.Body;

            try
            {
                using var memoryStream = new MemoryStream();
                context.Response.Body = memoryStream;

                await next(context);

                memoryStream.Position = 0;
                var responseBody = await new StreamReader(memoryStream).ReadToEndAsync();

                // Only log failed responses (status codes 4xx and 5xx)
                if (context.Response.StatusCode >= 400)
                {
                    var activity = Activity.Current;
                    if (activity != null && !string.IsNullOrEmpty(responseBody))
                    {
                        activity.SetTag("http.response.body", responseBody);
                        activity.SetTag("http.response.body.size", responseBody.Length);
                        activity.SetTag("http.response.status_code", context.Response.StatusCode);
                    }
                }

                memoryStream.Position = 0;
                await memoryStream.CopyToAsync(originalBodyStream);
            }
            finally
            {
                context.Response.Body = originalBodyStream;
            }
        }
    }
}
