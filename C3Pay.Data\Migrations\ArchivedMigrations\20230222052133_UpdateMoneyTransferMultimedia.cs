﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateMoneyTransferMultimedia : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "Type",
                table: "MultimediaResources",
                type: "int",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "varchar(5)",
                oldMaxLength: 5);

            migrationBuilder.AddColumn<string>(
                name: "Identifier",
                table: "MultimediaResources",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 1,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 2,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 3,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 4,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 5,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 6,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 7,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 8,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 9,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 10,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 11,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 12,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 13,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 14,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 15,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 16,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 17,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 18,
                column: "Type",
                value: 1);

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 19,
                columns: new[] { "ThumbnailUrl", "Type" },
                values: new object[] { null, 1 });

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 20,
                columns: new[] { "ThumbnailUrl", "Type" },
                values: new object[] { null, 1 });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[,]
                {
                    { 41, 2, "AddBeneficiary_SendNow", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image7.png" },
                    { 39, 2, "AddBeneficiary_AccountNumber", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image5.png" },
                    { 22, 2, "AddBeneficiary_PersonalDetail", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-12s.mp3" },
                    { 23, 2, "AddBeneficiary_SelectCountry", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-15s.mp3" },
                    { 24, 2, "AddBeneficiary_IFSC", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-3s.mp3" },
                    { 25, 2, "AddBeneficiary_AccountNumber", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-6s.mp3" },
                    { 26, 2, "AddBeneficiary_Review", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-9s.mp3" },
                    { 27, 2, "AddBeneficiary_SendNow", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample-9s.mp3" },
                    { 28, 2, "AddBeneficiary_Main", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample1.txt" },
                    { 29, 2, "AddBeneficiary_PersonalDetail", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample2.txt" },
                    { 30, 2, "AddBeneficiary_SelectCountry", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample3.txt" },
                    { 31, 2, "AddBeneficiary_IFSC", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample4.txt" },
                    { 32, 2, "AddBeneficiary_AccountNumber", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample5.txt" },
                    { 33, 2, "AddBeneficiary_Review", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample6.txt" },
                    { 34, 2, "AddBeneficiary_SendNow", "hi", "IND", null, 3, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/sample7.txt" },
                    { 35, 2, "AddBeneficiary_Main", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image1.png" },
                    { 36, 2, "AddBeneficiary_PersonalDetail", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image2.png" },
                    { 37, 2, "AddBeneficiary_SelectCountry", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image3.png" },
                    { 38, 2, "AddBeneficiary_IFSC", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image4.png" },
                    { 40, 2, "AddBeneficiary_Review", "hi", "IND", null, 4, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/image6.png" },
                    { 21, 2, "AddBeneficiary_Main", "hi", "IND", null, 2, "https://eaec3sharedsp.blob.core.windows.net/money-transfer/file_example_MP3_700KB.mp3" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 21);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 22);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 23);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 24);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 25);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 26);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 27);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 28);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 29);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 30);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 31);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 32);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 33);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 34);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 35);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 36);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 37);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 38);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 39);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 40);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 41);

            migrationBuilder.DropColumn(
                name: "Identifier",
                table: "MultimediaResources");

            migrationBuilder.AlterColumn<string>(
                name: "Type",
                table: "MultimediaResources",
                type: "varchar(5)",
                maxLength: 5,
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 1,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 2,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 3,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 4,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 5,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 6,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 7,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 8,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 9,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 10,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 11,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 12,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 13,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 14,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 15,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 16,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 17,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 18,
                column: "Type",
                value: "Video");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 19,
                columns: new[] { "ThumbnailUrl", "Type" },
                values: new object[] { "", "Video" });

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 20,
                columns: new[] { "ThumbnailUrl", "Type" },
                values: new object[] { "", "Video" });
        }
    }
}
