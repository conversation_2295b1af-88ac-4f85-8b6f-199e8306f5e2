﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddCreatedDate_IdentificationLogs : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FullName",
                table: "EmiratesIdLogs");

            migrationBuilder.DropColumn(
                name: "Nationality",
                table: "EmiratesIdLogs");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "PassportLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "EmiratesIdLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "PassportLogs");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "EmiratesIdLogs");

            migrationBuilder.AddColumn<string>(
                name: "FullName",
                table: "EmiratesIdLogs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Nationality",
                table: "EmiratesIdLogs",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
