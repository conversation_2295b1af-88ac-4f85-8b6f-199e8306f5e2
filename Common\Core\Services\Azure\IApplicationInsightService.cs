﻿using System.Collections.Generic;

namespace Edenred.Common.Core
{
    /// <summary>
    /// OpenTelemetry activity service for tracking custom events and activities
    /// </summary>
    public interface IOpenTelemetryService
    {
        /// <summary>
        /// Track a custom event with properties
        /// </summary>
        /// <param name="eventName">Name of the event</param>
        /// <param name="userName">User associated with the event</param>
        /// <param name="properties">Additional properties</param>
         void TrackEvent(string eventName, string userName, IDictionary<string, string> properties);

        /// <summary>
        /// Track an exception
        /// </summary>
        /// <param name="exception">The exception to track</param>
        /// <param name="properties">Additional properties</param>
        void TrackException(System.Exception exception, IDictionary<string, string> properties);

        /// <summary>
        /// Track a custom metric
        /// </summary>
        /// <param name="metricName">Name of the metric</param>
        /// <param name="value">Value of the metric</param>
        /// <param name="properties">Additional properties</param>
        void TrackMetric(string metricName, double value, IDictionary<string, string> properties);
    }
}
