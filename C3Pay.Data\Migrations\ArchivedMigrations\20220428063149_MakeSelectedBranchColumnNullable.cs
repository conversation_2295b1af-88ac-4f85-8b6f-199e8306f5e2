﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class MakeSelectedBranchColumnNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SelectedBranch",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.AddColumn<bool>(
                name: "IsBranchSelected",
                table: "MoneyTransferBeneficiaries",
                type: "bit",
                nullable: true,
                defaultValue: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsBranchSelected",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.AddColumn<bool>(
                name: "SelectedBranch",
                table: "MoneyTransferBeneficiaries",
                type: "bit",
                nullable: false,
                defaultValue: true);
        }
    }
}
