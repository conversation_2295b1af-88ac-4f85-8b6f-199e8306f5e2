﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU_Phase2_Modified_RemittanceDestinations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferMethods_Countries_CountryCode",
                table: "MoneyTransferMethods");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferMethods_CountryCode",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "Code2",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "Currency",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "MoneyTransferMethodType",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "Rate",
                table: "MoneyTransferMethods");

            migrationBuilder.DropColumn(
                name: "RateExpiryDate",
                table: "MoneyTransferMethods");

            migrationBuilder.AddColumn<string>(
                name: "CountryCode",
                table: "MoneyTransferProviders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Currency",
                table: "MoneyTransferProviders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferMethodType",
                table: "MoneyTransferProviders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "Rate",
                table: "MoneyTransferProviders",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "RateExpiryDate",
                table: "MoneyTransferProviders",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "Currency",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "MoneyTransferMethodType",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "Rate",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "RateExpiryDate",
                table: "MoneyTransferProviders");

            migrationBuilder.AddColumn<string>(
                name: "Code2",
                table: "MoneyTransferMethods",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryCode",
                table: "MoneyTransferMethods",
                type: "nvarchar(2)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Currency",
                table: "MoneyTransferMethods",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferMethodType",
                table: "MoneyTransferMethods",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "Rate",
                table: "MoneyTransferMethods",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "RateExpiryDate",
                table: "MoneyTransferMethods",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferMethods_CountryCode",
                table: "MoneyTransferMethods",
                column: "CountryCode");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferMethods_Countries_CountryCode",
                table: "MoneyTransferMethods",
                column: "CountryCode",
                principalTable: "Countries",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
