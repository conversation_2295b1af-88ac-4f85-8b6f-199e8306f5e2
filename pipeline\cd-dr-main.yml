﻿name: $(Date:yyyyMMdd)$(Rev:.r)

trigger: none

pool:
  vmimage: windows-latest

resources:
  repositories:
    - repository: devopsTemplates
      type: git
      endpoint: edenered-hq
      name: Architecture/devops-templates
    - repository: automationRegressionArtifacts
      type: git
      name: C3Pay.Backend/C3Pay.Backend
      ref: QA_Automation      

  pipelines:
    - pipeline: binaries
      source: "CI - Binaries"
      trigger:
        enabled: false
        branches:
          include:
            - master
            - Release
            - develop        

    - pipeline: infra
      source: "CI - Infra"
      trigger:
        enabled: false
        branches:
          include:
            - master
            - Release
            - develop        

variables:
  global.location: "uaenorth"
  global.secondaryLocation: "southeastasia"
  global.templateLocation: "https://ehqtemplates.blob.core.windows.net/templates-v2/"
  global.infraFile: "$(Pipeline.Workspace)/infra/infra/master.json"
  global.webAppBinariesFile: "$(Pipeline.Workspace)/binaries/webapi/api.zip"
  global.portalWebAppBinariesFile: "$(Pipeline.Workspace)/binaries/portalwebapi/portalApi.zip"
  global.webJobBinariesFile: "$(Pipeline.Workspace)/binaries/webjob"
  global.SqlFile: "$(Pipeline.Workspace)/binaries/sql/C3PayContext.sql"
  global.IdentitySqlFile: "$(Pipeline.Workspace)/binaries/identitySql/C3PayIdentityDbContext.sql"
  global.RakProcessBankTransactionsReportsSchedule: "03:00:00"
  global.RakUpdatePendingBankTransactionsSchedule: "00:05:00"
  global.RakReverseFailedBankTransactionsSchedule: "00:05:00"
  global.RefreshRatesSchedule: "00:05:00"
  global.RakBanksMaxRecords: 100
  global.MoneyTransferBeneficiaryCount: 20
  global.RakMaxTransactionTriesCount: 1
  global.RakMessageProcessInDelay: 60
  global.RakMoneyTransferBeneficiaryDelayInMins: 60
  global.RakLoyaltyImplementDate: "2020-11-12 00:00:00"
  global.RakLoyaltyLimitCount: 5
  global.RakLoyaltyLimitAmount: 501
  global.RakURLPath: "/rb/api4"
  global.MobileRechargeSynchronizeWithDingSchedule: "04:00:00"
  global.MobileRechargeUpdateStatusSchedule: "04:00:00"
  global.MobileRechargeNickNameLength: 20
  global.MobileRechargeCallingCardAccountNumberLive: "**********"
  global.FirstBlackV1PlasticCardId: "**********"
  global.FirstBlackV2PlasticCardId: "**********"
  global.ReversePendingDirectMoneyTransfersSchedule: "00:10:00"
  global.ReverseFailedDirectMoneyTransfersSchedule: "01:00:00"
  global.MobileRechargeC3FeeMode: "0"
  global.MobileRechargeMySalaryFeeMode: "0"
  global.MobileRechargeSelectedCorporatesWithFee: "25107,12010,12448,51574,34008,60012,34707,12177,34569,60543,12094,33780,35950,60004,34039,33714,12072,33790,34761,35955,12428,13246,34736,32010,12266,34635,13238,60053,51005,12905,13048,12182,34623,13319,13232,34980,34134,23065,12097,14179,13118,13243,12629,33302,12086,12602,13457,35006,51028,12316,14152,13738,13231,12628,12033,13385,60457,34515,23051,13255,51030,12708,13289,12427,13391,51450,12456,60088,35570"
  global.MobileRechargeFeeAmount: "0.5"

stages:
  - stage: Development_EAE
    dependsOn: []
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/develop'))
    variables:
      - name: azureSubscription
        value: "AzureDevops-eae-c3pay-d" #'AzureDevops-eae-c3pay-d-local'
      - name: azureSubscriptionEQ
        value: "AzureDevops-eae-c3pay-d"
      - name: stage.environment
        value: "DEV"
      - name: stage.entityCode
        value: "eae"
      - name: stage.rgName
        value: "c3pay-dr-dev-rg"
      - name: stage.appName
        value: "c3paydr"
      - name: stage.dbName
        value: "C3PayDr"

      - name: stage.farmSkuName
        value: "B1"
      - name: farmSkuCapacity
        value: 1
      - name: farmSkuCapacityMin
        value: 0
      - name: farmSkuCapacityMax
        value: 0
      - name: stage.farmJobSkuName
        value: "B1"
      - name: farmJobSkuCapacity
        value: 1
      - name: stage.farmPortalSkuName
        value: "B1"
      - name: farmPortalSkuCapacity
        value: 1

      - name: stage.dbSkuName
        value: "S0"
      - name: stage.dbWeeklyLtr
        value: "1"
      - name: stage.dbMonthlyLtr
        value: "1"
      - name: stage.identityDbSkuName
        value: "S0"
      - name: stage.serviceBusSkuName
        value: "Basic"
      - name: rakCertificateName
        value: file.rak.pfx
      - name: rakPrivateKeyName
        value: rak-staging.key
      - name: rakPublicKeyName
        value: rakapi-uat2024.pub
      - group: "C3Pay-dev"
      - name: stage.RAKSFTPInputRootDirectory
        value: "//IN//"
      - name: stage.RAKSFTPOutputRootDirectory
        value: "//OUT//"
      - name: stage.RAKSFTPTransactionStatusDirectory
        value: "RMT//C3TxnStatus//dev//"
      - name: stage.RAKSFTPTransactionBlobContainerName
        value: "raktransaction"
      - name: stage.RAKSFTPProfileStatusDirectory
        value: ""
      - name: stage.MobileRechargeTransactionEnvironment
        value: "UAT"
      - name: stage.MobileRechargeNonVerifiedLimit
        value: 100000
      - name: stage.MobileRechargeVerifiedLimit
        value: 100000

    jobs:
      - template: cd-jobs.yml
        parameters:
          azureSubscription: ${{ variables.azureSubscription }}
          azureSubscriptionEQ: ${{ variables.azureSubscriptionEQ }}
          targetenvironment: "Development EAE"
          templateLocation: $(global.templateLocation)
          environment: $(stage.environment)
          entityCode: $(stage.entityCode)
          appName: $(stage.appName)
          location: $(global.location)
          secondaryLocation: $(global.secondaryLocation)
          rgName: $(stage.rgName)
          dbName: $(stage.dbName)

          farmSkuName: $(stage.farmSkuName)
          farmSkuCapacity: ${{ variables.farmSkuCapacity }}
          farmSkuCapacityMin: ${{ variables.farmSkuCapacityMin }}
          farmSkuCapacityMax: ${{ variables.farmSkuCapacityMax }}
          farmJobSkuName: $(stage.farmJobSkuName)
          farmJobSkuCapacity: ${{ variables.farmJobSkuCapacity }}
          farmPortalSkuName: $(stage.farmPortalSkuName)
          farmPortalSkuCapacity: ${{ variables.farmPortalSkuCapacity }}

          dbSkuName: $(stage.dbSkuName)
          dbWeeklyLtr: $(stage.dbWeeklyLtr)
          dbMonthlyLtr: $(stage.dbMonthlyLtr)
          identityDbSkuName: $(stage.identityDbSkuName)
          serviceBusSkuName: $(stage.serviceBusSkuName)
          infraFile: $(global.infraFile)
          webAppBinariesFile: $(global.webAppBinariesFile)
          webJobBinariesFile: $(global.webJobBinariesFile)
          portalWebAppBinariesFile: $(global.portalWebAppBinariesFile)
          sqlFile: $(global.sqlFile)
          identitySqlFile: $(global.identitySqlFile)
          sqlsrvAdministratorLogin: $(sqlsrvAdministratorLogin)
          sqlsrvAdministratorPassword: $(sqlsrvAdministratorPassword)
          DevIP: $(DevIP)
          DevEmails: $(DevEmails)
          rakCertificateName: ${{ variables.rakCertificateName }}
          rakCertificatePassword: $(pfxpassword)
          rakPrivateKeyName: ${{ variables.rakPrivateKeyName }}
          rakPublicKeyName: ${{ variables.rakPublicKeyName }}
          SettingEDCAuthority: $(EDConnectAuthority)
          SettingEDCApiName: $(EDConnectApiName)
          SettingEDCApiSecret: $(EDConnectApiSecret)
          SettingPortalEDCAuthority: $(PortalEDConnectAuthority)
          SettingPortalEDCApiName: $(PortalEDConnectApiName)
          SettingPortalEDCApiSecret: $(PortalEDConnectApiSecret)
          SettingAADAuthority: $(AADAuthority)
          SettingAADAudience: $(AADAudience)
          SettingAADAllowedClientIds: $(AADAllowedClientIds)
          SettingKeyName: $(KeyName)
          SettingSignzyURL: $(SignzyURL)
          SettingSignzyFileExchangeAddress: $(SignzyFileExchangeAddress)
          SettingSignzyId: $(SignzyId)
          SettingSignzyUserId: $(SignzyUserId)
          SettingTransactionsB2CServiceAuthority: $(TransactionsB2CServiceAuthority) 
          SettingTransactionsB2CServiceClientId: $(TransactionsB2CServiceClientId) 
          SettingTransactionsB2CServiceScope: $(TransactionsB2CServiceScope) 
          SettingTransactionsB2CServiceClientSecret: $(TransactionsB2CServiceClientSecret) 
          SettingTransactionsB2CServiceBaseAddress: $(TransactionsB2CServiceBaseAddress) 
          SettingTransactionsB2CServiceGrantType: $(TransactionsB2CServiceGrantType) 
          SettingTransactionsB2CServiceAPIVersion: $(TransactionsB2CServiceAPIVersion) 
          SettingRakBaseURL: $(RakBaseURL)
          SettingRakClientId: $(RakClientId)
          SettingRakClientSecret: $(RakClientSecret)
          SettingRakSftpEndPoint: $(RakSftpEndPoint)
          SettingRakSftpPort: $(RakSftpPort)
          SettingRakProcessBankTransactionsReportsSchedule: $(global.RakProcessBankTransactionsReportsSchedule)
          SettingRakUpdatePendingBankTransactionsSchedule: $(global.RakUpdatePendingBankTransactionsSchedule)
          SettingRakReverseFailedBankTransactionsSchedule: $(global.RakReverseFailedBankTransactionsSchedule)
          SettingRakReadRMTProfileResponsesSchedule: $(RakReadRMTProfileResponsesSchedule)
          SettingRakSftpRMTProfileResponsesDirectory: $(RakSftpRMTProfileResponsesDirectory)
          SettingRakSftpMissingRakFileAlertPhoneNumbers: $(RakSftpMissingRakFileAlertPhoneNumbers)
          SettingRakMoneyTransferBeneficiaryCount: $(global.MoneyTransferBeneficiaryCount)
          SettingRakBanksMaxRecords: $(global.RakBanksMaxRecords)
          SettingRakMaxTransactionTriesCount: $(global.RakMaxTransactionTriesCount)
          SettingRakMessageProcessInDelay: $(global.RakMessageProcessInDelay)
          SettingRakMoneyTransferBeneficiaryDelayInMins: $(global.RakMoneyTransferBeneficiaryDelayInMins)
          SettingRakLoyaltyImplementDate: $(global.RakLoyaltyImplementDate)
          SettingRakLoyaltyLimitCount: $(global.RakLoyaltyLimitCount)
          SettingRakLoyaltyLimitAmount: $(global.RakLoyaltyLimitAmount)
          SettingRakURLPath: $(global.RakURLPath)
          SettingRakSftpUsername: $(RakSftpUsername)
          SettingRakSftpPassword: $(RakSftpPassword)
          SettingRAKSFTPInputRootDirectory: $(stage.RAKSFTPInputRootDirectory)
          SettingRAKSFTPOutputRootDirectory: $(stage.RAKSFTPOutputRootDirectory)
          SettingRAKSFTPTransactionStatusDirectory: $(stage.RAKSFTPTransactionStatusDirectory)
          SettingRAKSFTPTransactionBlobContainerName: $(stage.RAKSFTPTransactionBlobContainerName)
          SettingRAKSFTPProfileStatusDirectory: $(stage.RAKSFTPProfileStatusDirectory)
          SettingRakRefreshRatesSchedule: $(global.RefreshRatesSchedule)
          SettingRakRefreshRatesEmiratesId: $(RakRefreshRatesEmiratesId)
          SettingPPSWebAuthBaseURL: $(PPSWebAuthBaseURL)
          SettingPPSWebAuthClientId: $(PPSWebAuthClientId)
          SettingPPSWebAuthClientSecretkey: $(PPSWebAuthClientSecretkey)
          SettingPPSEndpointAddress: $(PPSEndpointAddress)
          SettingPPSUsername: $(PPSUsername)
          SettingPPSPassword: $(PPSPassword)
          SettingPPSSponsorCode: $(PPSSponsorCode)
          SettingPPSCustomerCode: $(PPSCustomerCode)
          SettingPPSSharedSecret: $(PPSSharedSecret)
          SettingKYCBaseAddress: $(KYCBaseAddress)
          SettingKYCUsername: $(KYCUsername)
          SettingKYCPassword: $(KYCPassword)
          SettingKYCUniqueRef: $(KYCUniqueRef)
          SettingKYCSponsorCode: $(KYCSponsorCode)
          SettingKYCSharedSecret: $(KYCSharedSecret)
          SettingEtisalatSMSUsername: $(EtisalatSMSUsername)
          SettingEtisalatSMSPassword: $(EtisalatSMSPassword)
          SettingEtisalatSMSBaseAddress: $(EtisalatSMSBaseAddress)
          SettingEtisalatSMSTimeout: $(EtisalatSMSTimeout)
          SettingEtisalatSMSRetryCount: $(EtisalatSMSRetryCount)
          SettingInfobipSMSUsername: $(InfobipSMSUsername)
          SettingInfobipSMSPassword: $(InfobipSMSPassword)
          SettingInfobipSMSAuthKey: $(InfobipSMSAuthKey)
          SettingInfobipSMSAuthKeyBaseUrl: $(InfobipSMSAuthKeyBaseUrl)
          SettingInfobipSMSTimeout: $(InfobipSMSTimeout)
          SettingInfobipSMSRetryCount: $(InfobipSMSRetryCount)
          SettingInfobipSMSSmsMode: $(InfobipSMSSmsMode)
          SettingDingBaseURL: $(DingBaseURL)
          SettingDingClientApiKey: $(DingClientApiKey)
          SettingSecondaryDingClientApiKey: $(SecondaryDingClientApiKey)
          SettingMobileRechargeSynchronizeWithDingSchedule: $(global.MobileRechargeSynchronizeWithDingSchedule)
          SettingMobileRechargeUpdateStatusSchedule: $(global.MobileRechargeUpdateStatusSchedule)
          SettingMobileRechargeNickNameLength: $(global.MobileRechargeNickNameLength)
          SettingMobileRechargeC3FeeMode: $(global.MobileRechargeC3FeeMode)
          SettingMobileRechargeMySalaryFeeMode: $(global.MobileRechargeMySalaryFeeMode)
          SettingMobileRechargeSelectedCorporatesWithFee: $(global.MobileRechargeSelectedCorporatesWithFee)
          SettingMobileRechargeFeeAmount: $(global.MobileRechargeFeeAmount)
          SettingMobileRechargeCallingCardAccountNumberLive: $(global.MobileRechargeCallingCardAccountNumberLive)
          SettingMobileRechargeTransactionEnvironment: $(stage.MobileRechargeTransactionEnvironment)
          SettingMobileRechargeCustomCallingCardName: $(MobileRechargeCustomCallingCardName)
          SettingMobileRechargeCustomCallingCardCode: $(MobileRechargeCustomCallingCardCode)
          SettingMobileRechargeCustomCallingCardLogoUrl: $(MobileRechargeCustomCallingCardLogoUrl)
          SettingMobileRechargeCustomCallingCardValidationRegex: $(MobileRechargeCustomCallingCardValidationRegex)
          SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
          SettingMobileRechargeNonVerifiedLimit: $(stage.MobileRechargeNonVerifiedLimit)
          SettingMobileRechargeVerifiedLimit: $(MobileRechargeVerifiedLimit)
          SettingMobileRechargeServiceServiceBusTopicName: $(MobileRechargeServiceServiceBusTopicName) 
          SettingMobileRechargeServiceServiceBusSubscriptionName: $(MobileRechargeServiceServiceBusSubscriptionName)  
          SettingRenewalCardUpdateServiceBusTopicName: $(RenewalCardUpdateServiceBusTopicName) 
          SettingRenewalCardUpdateServiceBusSubscriptionName: $(RenewalCardUpdateServiceBusSubscriptionName) 
          SettingEdenredIdentityManagerBaseAddress: $(EdenredIdentityManagerBaseAddress)
          SettingEdenredIdentityManagerAuthority: $(EdenredIdentityManagerAuthority)
          SettingEdenredIdentityManagerResourceId: $(EdenredIdentityManagerResourceId)
          SettingEdenredIdentityManagerClientId: $(EdenredIdentityManagerClientId)
          SettingEdenredIdentityManagerClientSecret: $(EdenredIdentityManagerClientSecret)
          SettingFirebaseCloudMessagingBaseAddress: $(FirebaseCloudMessagingBaseAddress)
          SettingFirebaseCloudMessagingKey: $(FirebaseCloudMessagingKey)
          SettingFirebaseCloudMessagingSenderId: $(FirebaseCloudMessagingSenderId)
          SettingFirebaseCloudMessagingRetryCount: $(FirebaseCloudMessagingRetryCount)
          SettingFirebaseCloudMessagingTimeout: $(FirebaseCloudMessagingTimeout)
          SettingESMOServiceBaseAddress: $(ESMOBaseAddress)
          SettingESMOServiceClientId: $(ESMOClientId)
          SettingESMOServiceClientSecret: $(ESMOClientSecret)
          SettingESMOServiceAuthority: $(ESMOAuthority)
          SettingESMOServiceScope: $(ESMOScope)
          SettingMobileAppHashKey: $(MobileAppHashKey)
          SettingSendGridSenderEmail: $(SendGridSenderEmail)
          SettingSendGridAPIKey: $(SendGridAPIKey)
          SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: $(SendGridCardHolderRegistrationRejectedEmailTemplateId)
          SettingSendGridPortalUserCreatedEmailTemplateId: $(SendGridPortalUserCreatedEmailTemplateId)
          SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: $(SendGridCardHolderRMTProfileCreatedEmailTemplateId)
          SettingSendGridBankStatementEmailTemplateId: $(SendGridBankStatementEmailTemplateId)
          SettingSendGridStoreOrderPlacedEmailTemplateId: $(SendGridStoreOrderPlacedEmailTemplateId)
          SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
          SettingRedisConnection: $(RedisConnection)
          SettingServiceBusConnection: $(ServiceBusConnection)
          SettingFirstBlackV1PlasticCardId: $(global.FirstBlackV1PlasticCardId)
          SettingFirstBlackV2PlasticCardId: $(global.FirstBlackV2PlasticCardId)
          SettingCleverTapBaseAddress: $(CleverTapBaseAddress)
          SettingCleverTapProjectId: $(CleverTapProjectId)
          SettingCleverTapPassCode: $(CleverTapPassCode)
          SettingMoneyTransferMultimediaURL: $(MoneyTransferMultimediaURL)

          # Step 6: Add your keys here like this: "Setting" + <setting name (same as property)> 
          SettingDirectTransferMaxBeneficiariesCount: $(DirectTransferMaxBeneficiariesCount)
          SettingDirectTransferMinAmountToSend: $(DirectTransferMinAmountToSend)
          SettingDirectTransferMaxAmountToSend: $(DirectTransferMaxAmountToSend)
          SettingDirectTransferMaxAmountToSendPerMonth: $(DirectTransferMaxAmountToSendPerMonth)
          SettingDirectTransferFee: $(DirectTransferFee)
          SettingDirectTransferVAT: $(DirectTransferVAT)
          SettingClaimPendingDirectTransfersQueueConnectionString: $(ClaimPendingDirectTransfersQueueConnectionString)
          SettingClaimPendingDirectTransfersQueueName: $(ClaimPendingDirectTransfersQueueName)     
          SettingReversePendingDirectMoneyTransfersSchedule: $(global.ReversePendingDirectMoneyTransfersSchedule)
          SettingReversePendingDirectMoneyTransfersDurationInMin: $(ReversePendingDirectMoneyTransfersDurationInMin)
          SettingReverseFailedDirectMoneyTransfersSchedule: $(global.ReverseFailedDirectMoneyTransfersSchedule)
          ###########################################################################################################


          SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
          SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
          SettingPaykiiServiceToken: $(PaykiiServiceToken)
          SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
          SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
          SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
          SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
          SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
          SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
          SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
          SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
          SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
          SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
          SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
          SettingPaykiiServiceDailyFXRatePerBillerTypeUrl: $(PaykiiServiceDailyFXRatePerBillerTypeUrl)
          SettingPaykiiServiceBillerFeesCatalogUrl: $(PaykiiServiceBillerFeesCatalogUrl)
          SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression)
          SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
          SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
          SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl)
          SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
          SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
          SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
          SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds)
          SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit)
          SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit)
          SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction)
          SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth)
          SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode)
          SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
          SettingBillPaymentIconContainerName: $(BillPaymentIconContainerName)
          SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode)
          SettingBillPaymentMockUserId: $(BillPaymentMockUserId) 
          SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
          SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
          SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
          SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
          SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
          SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
          SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
          SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
          SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
          SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
          SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
          SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
          SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
          SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceNonWUCorridors: $(MoneyTransferServiceNonWUCorridors)
          SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(MoneyTransferServiceRMTStatusFromCreatedToPendingEnabled)
          SettingMoneyTransferServiceLastRaffleWinnerName: $(MoneyTransferServiceLastRaffleWinnerName)
          SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(MoneyTransferServiceLastRaffleWinnerTicketNumber)
          SettingMoneyTransferServiceRaffleDateString: $(MoneyTransferServiceRaffleDateString)
          SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
          SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
          SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
          SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
          SettingGeneralTestKey: $(GeneralTestKey)
          SettingSwaggerUsername: $(SwaggerUsername)
          SettingSwaggerPassword: $(SwaggerPassword)
          SettingEnableSwagger: $(EnableSwagger)
          SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds: $(MultimediaAutoPlayMoneyTransferVideoCorporateIds)
          SettingRatingMinimumDaysToShowInApp: $(RatingMinimumDaysToShowInApp)
          SettingRatingMinimumDaysToShowStore: $(RatingMinimumDaysToShowStore)
          SettingHRServiceCacheInMinutes: $(HRServiceCacheInMinutes)
          SettingStoreEmailRecepients: $(StoreEmailRecepients)
          SettingUnEmploymentInsuranceServiceBusTopicName: $(UnEmploymentInsuranceServiceBusTopicName)
          SettingUnEmploymentInsuranceServiceBusUserTopicName: $(UnEmploymentInsuranceServiceBusUserTopicName)
          SettingUnEmploymentInsuranceServiceBusSubscriptionName: $(UnEmploymentInsuranceServiceBusSubscriptionName)
          SettingDingServiceRetryCount: $(DingServiceRetryCount)
          SettingDingServiceSleepDuration: $(DingServiceSleepDuration)
          SettingDingServiceIsRetryEnabled: $(DingServiceIsRetryEnabled)
          SettingTestingMRDynamicPackageTestNepalNumbers: $(TestingMRDynamicPackageTestNepalNumbers)
          SettingTestingMRInlineFeeCalculationTestNumbers: $(TestingMRInlineFeeCalculationTestNumbers)
          SettingEncryptionSettingsIsActive: $(EncryptionSettingsIsActive)
          SettingEncryptionSettingsPrivateKey: $(EncryptionSettingsPrivateKey)
          SettingEncryptionSettingsPublicKey: $(EncryptionSettingsPublicKey)
          SettingC3PayPlusMembershipLuckyDrawSchedule: $(C3PayPlusMembershipLuckyDrawSchedule)
          SettingC3PayPlusMembershipLuckyDrawWinnersCount: $(C3PayPlusMembershipLuckyDrawWinnersCount)
          SettingC3PayPlusMembershipCdnUrl: $(C3PayPlusMembershipCdnUrl)
          SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule: $(C3PayPlusMembershipAtmWithdrawalFeeReversalSchedule)
          SettingFirebaseNotificationAuthEndpoint: $(FirebaseNotificationAuthEndpoint)
          SettingFirebaseNotificationBaseUrl: $(FirebaseNotificationBaseUrl)
          SettingFirebaseNotificationSendMethodUrl: $(FirebaseNotificationSendMethodUrl)
          SettingGoogleAuthType: $(GoogleAuthType)
          SettingGoogleAuthProjectId: $(GoogleAuthProjectId)
          SettingGoogleAuthPrivateKeyId: $(GoogleAuthPrivateKeyId)
          SettingGoogleAuthPrivateKey: $(GoogleAuthPrivateKey)
          SettingGoogleAuthClientEmail: $(GoogleAuthClientEmail)
          SettingGoogleAuthClientId: $(GoogleAuthClientId)
          SettingGoogleAuthAuthUri: $(GoogleAuthAuthUri)
          SettingGoogleAuthTokenUri: $(GoogleAuthTokenUri)
          SettingGoogleAuthAuthProviderX509CertUrl: $(GoogleAuthAuthProviderX509CertUrl)
          SettingGoogleAuthClientX509CertUrl: $(GoogleAuthClientX509CertUrl)
          SettingGoogleAuthUniverseDomain: $(GoogleAuthUniverseDomain)
          SettingC3PayPlusMembershipGenerateTicketsMaxCount: $(C3PayPlusMembershipGenerateTicketsMaxCount)
          SettingC3PayPlusMembershipOverrideLuckyDrawDate: $(C3PayPlusMembershipOverrideLuckyDrawDate)
          SettingC3PayPlusMembershipOverrideLuckyDrawTime: $(C3PayPlusMembershipOverrideLuckyDrawTime)
          SettingC3PayPlusMembershipRenewalSchedule: $(C3PayPlusMembershipRenewalSchedule)
          SettingC3PayPlusMembershipConfirmFirstDebitSchedule: $(C3PayPlusMembershipConfirmFirstDebitSchedule)
          SettingSanctionScreeningApiAddress: $(SanctionScreeningApiAddress)
          SettingC3PayAutomatedCallsBaseUrl: $(C3PayAutomatedCallsBaseUrl)
          SettingC3PayAutomatedCallsAppKey: $(C3PayAutomatedCallsAppKey)
          SettingC3PayAutomatedCallsFromNumber: $(C3PayAutomatedCallsFromNumber)
          SettingC3PayAutomatedCallsCallbackUrl: $(C3PayAutomatedCallsCallbackUrl)
          SettingC3PayAutomatedCallsCallbackSecret: $(C3PayAutomatedCallsCallbackSecret)
          SettingC3PayAutomatedCallsOverrideEnqueueDelay: $(C3PayAutomatedCallsOverrideEnqueueDelay)
          SettingC3PayAutomatedCallsWelcomeCallQueueConnectionString: $(C3PayAutomatedCallsWelcomeCallQueueConnectionString)
          SettingC3PayAutomatedCallsWelcomeCallQueueName: $(C3PayAutomatedCallsWelcomeCallQueueName)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString: $(C3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueName: $(C3PayAutomatedCallsPreLuckyDrawCallQueueName)
          SettingC3PayAutomatedCallsPreRenewalCallQueueConnectionString: $(C3PayAutomatedCallsPreRenewalCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreRenewalCallQueueName: $(C3PayAutomatedCallsPreRenewalCallQueueName)
          SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: $(C3PayPlusMembershipFreeMoneyTransferRefundsSchedule)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $(C3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $(C3PayPlusMembershipATMWithdrawalRefundsQueueName)
          SettingAzureAdInstance: $(SettingAzureAdInstance)
          SettingAzureAdTenantId: $(SettingAzureAdTenantId)
          SettingAzureAdClientId: $(SettingAzureAdClientId)
          SettingAzureAdClientSecret: $(SettingAzureAdClientSecret)
          SettingAzureAdCallbackPath: $(SettingAzureAdCallbackPath)
          SettingAzureAdAudience: $(SettingAzureAdAudience)
          SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: $(MoneyTransferServiceC3ToC3MinVersionForOtpCheck)
          SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: $(C3PayPlusMembershipLoginVideoLastSeenIntervalInDays)
          SettingC3PayPlusMembershipTargetedDiscountCooldownDays: $(C3PayPlusMembershipTargetedDiscountCooldownDays)
          SettingC3PayPlusMembershipAllowedPhoneNumbers: $(C3PayPlusMembershipAllowedPhoneNumbers)
          SettingLoginVideoSlotInterval: $(LoginVideoSlotInterval)

  - stage: UAT_EAE
    dependsOn: []
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/release'))
    variables:
      - name: azureSubscription
        value: "AzureDevops-eae-c3pay-a" #'AzureDevops-eae-c3pay-a-local'
      - name: azureSubscriptionEQ
        value: "AzureDevops-eae-c3pay-a"
      - name: stage.environment
        value: "UAT"
      - name: stage.entityCode
        value: "ae"
      - name: stage.rgName
        value: "c3pay-disrec-uat-rg"
      - name: stage.appName
        value: "c3paydr"
      - name: stage.dbName
        value: "C3PayDr"

      - name: stage.farmSkuName
        value: "S1"
      - name: farmSkuCapacity
        value: 1
      - name: farmSkuCapacityMin
        value: 1
      - name: farmSkuCapacityMax
        value: 2
      - name: stage.farmJobSkuName
        value: "S1"
      - name: farmJobSkuCapacity
        value: 1
      - name: stage.farmPortalSkuName
        value: "S1"
      - name: farmPortalSkuCapacity
        value: 1

      - name: stage.dbSkuName
        value: "S0"
      - name: stage.dbWeeklyLtr
        value: "1"
      - name: stage.dbMonthlyLtr
        value: "1"
      - name: stage.identityDbSkuName
        value: "S0"
      - name: stage.serviceBusSkuName
        value: "Basic"
      - name: rakCertificateName
        value: star_myc3card_2021.pfx
      - name: rakPrivateKeyName
        value: star_myc3card_2021.key
      - name: rakPublicKeyName
        value: rakapi-uat2024.pub
      - group: "C3Pay-uat"
      - name: stage.RAKSFTPInputRootDirectory
        value: "//IN//"
      - name: stage.RAKSFTPOutputRootDirectory
        value: "//OUT//"
      - name: stage.RAKSFTPTransactionStatusDirectory
        value: "RMT//C3TxnStatus//"
      - name: stage.RAKSFTPTransactionBlobContainerName
        value: "raktransaction"
      - name: stage.RAKSFTPProfileStatusDirectory
        value: ""
      - name: stage.MobileRechargeTransactionEnvironment
        value: "UAT"


    jobs:
      - template: cd-jobs.yml
        parameters:
          azureSubscription: ${{ variables.azureSubscription }}
          azureSubscriptionEQ: ${{ variables.azureSubscriptionEQ }}
          targetenvironment: "UAT EAE"
          templateLocation: $(global.templateLocation)
          environment: $(stage.environment)
          entityCode: $(stage.entityCode)
          appName: $(stage.appName)
          location: $(global.location)
          secondaryLocation: $(global.secondaryLocation)
          rgName: $(stage.rgName)
          dbName: $(stage.dbName)

          farmSkuName: $(stage.farmSkuName)
          farmSkuCapacity: ${{ variables.farmSkuCapacity }}
          farmSkuCapacityMin: ${{ variables.farmSkuCapacityMin }}
          farmSkuCapacityMax: ${{ variables.farmSkuCapacityMax }}
          farmJobSkuName: $(stage.farmJobSkuName)
          farmJobSkuCapacity: ${{ variables.farmJobSkuCapacity }}
          farmPortalSkuName: $(stage.farmPortalSkuName)
          farmPortalSkuCapacity: ${{ variables.farmPortalSkuCapacity }}

          dbSkuName: $(stage.dbSkuName)
          dbWeeklyLtr: $(stage.dbWeeklyLtr)
          dbMonthlyLtr: $(stage.dbMonthlyLtr)
          identityDbSkuName: $(stage.identityDbSkuName)
          serviceBusSkuName: $(stage.serviceBusSkuName)

          infraFile: $(global.infraFile)
          webAppBinariesFile: $(global.webAppBinariesFile)
          webJobBinariesFile: $(global.webJobBinariesFile)
          portalWebAppBinariesFile: $(global.portalWebAppBinariesFile)
          sqlFile: $(global.sqlFile)
          identitySqlFile: $(global.identitySqlFile)
          sqlsrvAdministratorLogin: $(sqlsrvAdministratorLogin)
          sqlsrvAdministratorPassword: $(sqlsrvAdministratorPassword)
          DevIP: $(DevIP)
          DevEmails: $(DevEmails)
          rakCertificateName: ${{ variables.rakCertificateName }}
          rakCertificatePassword: $(pfxpassword)
          rakPrivateKeyName: ${{ variables.rakPrivateKeyName }}
          rakPublicKeyName: ${{ variables.rakPublicKeyName }}
          SettingEDCAuthority: $(EDConnectAuthority)
          SettingEDCApiName: $(EDConnectApiName)
          SettingEDCApiSecret: $(EDConnectApiSecret)
          SettingPortalEDCAuthority: $(PortalEDConnectAuthority)
          SettingPortalEDCApiName: $(PortalEDConnectApiName)
          SettingPortalEDCApiSecret: $(PortalEDConnectApiSecret)
          SettingAADAuthority: $(AADAuthority)
          SettingAADAudience: $(AADAudience)
          SettingAADAllowedClientIds: $(AADAllowedClientIds)
          SettingKeyName: $(KeyName)
          SettingSignzyURL: $(SignzyURL)
          SettingSignzyFileExchangeAddress: $(SignzyFileExchangeAddress)
          SettingSignzyId: $(SignzyId)
          SettingSignzyUserId: $(SignzyUserId) 
          SettingTransactionsB2CServiceAuthority: $(TransactionsB2CServiceAuthority) 
          SettingTransactionsB2CServiceClientId: $(TransactionsB2CServiceClientId) 
          SettingTransactionsB2CServiceScope: $(TransactionsB2CServiceScope) 
          SettingTransactionsB2CServiceClientSecret: $(TransactionsB2CServiceClientSecret) 
          SettingTransactionsB2CServiceBaseAddress: $(TransactionsB2CServiceBaseAddress) 
          SettingTransactionsB2CServiceGrantType: $(TransactionsB2CServiceGrantType) 
          SettingTransactionsB2CServiceAPIVersion: $(TransactionsB2CServiceAPIVersion) 
          SettingRakBaseURL: $(RakBaseURL)
          SettingRakClientId: $(RakClientId)
          SettingRakClientSecret: $(RakClientSecret)
          SettingRakProcessBankTransactionsReportsSchedule: $(global.RakProcessBankTransactionsReportsSchedule)
          SettingRakUpdatePendingBankTransactionsSchedule: $(global.RakUpdatePendingBankTransactionsSchedule)
          SettingRakReverseFailedBankTransactionsSchedule: $(global.RakReverseFailedBankTransactionsSchedule)
          SettingRakReadRMTProfileResponsesSchedule: $(RakReadRMTProfileResponsesSchedule)
          SettingRakSftpRMTProfileResponsesDirectory: $(RakSftpRMTProfileResponsesDirectory)
          SettingRakSftpMissingRakFileAlertPhoneNumbers: $(RakSftpMissingRakFileAlertPhoneNumbers)
          SettingRakBanksMaxRecords: $(global.RakBanksMaxRecords)
          SettingRakMaxTransactionTriesCount: $(global.RakMaxTransactionTriesCount)
          SettingRakMessageProcessInDelay: $(global.RakMessageProcessInDelay)
          SettingRakMoneyTransferBeneficiaryDelayInMins: $(global.RakMoneyTransferBeneficiaryDelayInMins)
          SettingRakLoyaltyImplementDate: $(global.RakLoyaltyImplementDate)
          SettingRakLoyaltyLimitCount: $(global.RakLoyaltyLimitCount)
          SettingRakLoyaltyLimitAmount: $(global.RakLoyaltyLimitAmount)
          SettingRakURLPath: $(global.RakURLPath)
          SettingRakMoneyTransferBeneficiaryCount: $(global.MoneyTransferBeneficiaryCount)
          SettingRakSftpEndPoint: $(RakSftpEndPoint)
          SettingRakSftpPort: $(RakSftpPort)
          SettingRakSftpUsername: $(RakSftpUsername)
          SettingRakSftpPassword: $(RakSftpPassword)
          SettingRAKSFTPInputRootDirectory: $(stage.RAKSFTPInputRootDirectory)
          SettingRAKSFTPOutputRootDirectory: $(stage.RAKSFTPOutputRootDirectory)
          SettingRAKSFTPTransactionStatusDirectory: $(stage.RAKSFTPTransactionStatusDirectory)
          SettingRAKSFTPTransactionBlobContainerName: $(stage.RAKSFTPTransactionBlobContainerName)
          SettingRAKSFTPProfileStatusDirectory: $(stage.RAKSFTPProfileStatusDirectory)
          SettingRakRefreshRatesSchedule: $(global.RefreshRatesSchedule)
          SettingRakRefreshRatesEmiratesId: $(RakRefreshRatesEmiratesId)
          SettingPPSWebAuthBaseURL: $(PPSWebAuthBaseURL)
          SettingPPSWebAuthClientId: $(PPSWebAuthClientId)
          SettingPPSWebAuthClientSecretkey: $(PPSWebAuthClientSecretkey)
          SettingPPSEndpointAddress: $(PPSEndpointAddress)
          SettingPPSUsername: $(PPSUsername)
          SettingPPSPassword: $(PPSPassword)
          SettingPPSSponsorCode: $(PPSSponsorCode)
          SettingPPSCustomerCode: $(PPSCustomerCode)
          SettingPPSSharedSecret: $(PPSSharedSecret)
          SettingKYCBaseAddress: $(KYCBaseAddress)
          SettingKYCUsername: $(KYCUsername)
          SettingKYCPassword: $(KYCPassword)
          SettingKYCUniqueRef: $(KYCUniqueRef)
          SettingKYCSponsorCode: $(KYCSponsorCode)
          SettingKYCSharedSecret: $(KYCSharedSecret)
          SettingEtisalatSMSUsername: $(EtisalatSMSUsername)
          SettingEtisalatSMSPassword: $(EtisalatSMSPassword)
          SettingEtisalatSMSBaseAddress: $(EtisalatSMSBaseAddress)
          SettingEtisalatSMSTimeout: $(EtisalatSMSTimeout)
          SettingEtisalatSMSRetryCount: $(EtisalatSMSRetryCount)
          SettingInfobipSMSUsername: $(InfobipSMSUsername)
          SettingInfobipSMSPassword: $(InfobipSMSPassword)
          SettingInfobipSMSBaseAddress: $(InfobipSMSBaseAddress)
          SettingInfobipSMSAuthKey: $(InfobipSMSAuthKey)
          SettingInfobipSMSAuthKeyBaseUrl: $(InfobipSMSAuthKeyBaseUrl)
          SettingInfobipSMSTimeout: $(InfobipSMSTimeout)
          SettingInfobipSMSRetryCount: $(InfobipSMSRetryCount)
          SettingInfobipSMSSmsMode: $(InfobipSMSSmsMode)
          SettingDingBaseURL: $(DingBaseURL)
          SettingDingClientApiKey: $(DingClientApiKey)
          SettingSecondaryDingClientApiKey: $(SecondaryDingClientApiKey)
          SettingMobileRechargeSynchronizeWithDingSchedule: $(global.MobileRechargeSynchronizeWithDingSchedule)
          SettingMobileRechargeUpdateStatusSchedule: $(global.MobileRechargeUpdateStatusSchedule)
          SettingMobileRechargeNickNameLength: $(global.MobileRechargeNickNameLength)
          SettingMobileRechargeC3FeeMode: $(global.MobileRechargeC3FeeMode)
          SettingMobileRechargeMySalaryFeeMode: $(global.MobileRechargeMySalaryFeeMode)
          SettingMobileRechargeSelectedCorporatesWithFee: $(global.MobileRechargeSelectedCorporatesWithFee)
          SettingMobileRechargeFeeAmount: $(global.MobileRechargeFeeAmount)
          SettingMobileRechargeCallingCardAccountNumberLive: $(global.MobileRechargeCallingCardAccountNumberLive)
          SettingMobileRechargeTransactionEnvironment: $(stage.MobileRechargeTransactionEnvironment)
          SettingMobileRechargeNonVerifiedLimit: $(stage.MobileRechargeNonVerifiedLimit)
          SettingMobileRechargeVerifiedLimit: $(stage.MobileRechargeVerifiedLimit)
          SettingMobileRechargeCustomCallingCardName: $(MobileRechargeCustomCallingCardName)
          SettingMobileRechargeCustomCallingCardCode: $(MobileRechargeCustomCallingCardCode)
          SettingMobileRechargeCustomCallingCardLogoUrl: $(MobileRechargeCustomCallingCardLogoUrl)
          SettingMobileRechargeCustomCallingCardValidationRegex: $(MobileRechargeCustomCallingCardValidationRegex)
          SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
          SettingMobileRechargeServiceServiceBusTopicName: $(MobileRechargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceServiceBusSubscriptionName: $(MobileRechargeServiceServiceBusSubscriptionName)  
          SettingRenewalCardUpdateServiceBusTopicName: $(RenewalCardUpdateServiceBusTopicName) 
          SettingRenewalCardUpdateServiceBusSubscriptionName: $(RenewalCardUpdateServiceBusSubscriptionName) 
          SettingEdenredIdentityManagerBaseAddress: $(EdenredIdentityManagerBaseAddress)
          SettingEdenredIdentityManagerAuthority: $(EdenredIdentityManagerAuthority)
          SettingEdenredIdentityManagerResourceId: $(EdenredIdentityManagerResourceId)
          SettingEdenredIdentityManagerClientId: $(EdenredIdentityManagerClientId)
          SettingEdenredIdentityManagerClientSecret: $(EdenredIdentityManagerClientSecret)
          SettingFirebaseCloudMessagingBaseAddress: $(FirebaseCloudMessagingBaseAddress)
          SettingFirebaseCloudMessagingKey: $(FirebaseCloudMessagingKey)
          SettingFirebaseCloudMessagingSenderId: $(FirebaseCloudMessagingSenderId)
          SettingFirebaseCloudMessagingRetryCount: $(FirebaseCloudMessagingRetryCount)
          SettingFirebaseCloudMessagingTimeout: $(FirebaseCloudMessagingTimeout)
          SettingESMOServiceBaseAddress: $(ESMOBaseAddress)
          SettingESMOServiceClientId: $(ESMOClientId)
          SettingESMOServiceClientSecret: $(ESMOClientSecret)
          SettingESMOServiceAuthority: $(ESMOAuthority)
          SettingESMOServiceScope: $(ESMOScope)
          SettingMobileAppHashKey: $(MobileAppHashKey)
          SettingSendGridSenderEmail: $(SendGridSenderEmail)
          SettingSendGridAPIKey: $(SendGridAPIKey)
          SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: $(SendGridCardHolderRegistrationRejectedEmailTemplateId)
          SettingSendGridPortalUserCreatedEmailTemplateId: $(SendGridPortalUserCreatedEmailTemplateId)
          SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: $(SendGridCardHolderRMTProfileCreatedEmailTemplateId)
          SettingSendGridBankStatementEmailTemplateId: $(SendGridBankStatementEmailTemplateId)
          SettingSendGridStoreOrderPlacedEmailTemplateId: $(SendGridStoreOrderPlacedEmailTemplateId)
          SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
          SettingRedisConnection: $(RedisConnection)
          SettingServiceBusConnection: $(ServiceBusConnection)
          SettingFirstBlackV1PlasticCardId: $(global.FirstBlackV1PlasticCardId)
          SettingFirstBlackV2PlasticCardId: $(global.FirstBlackV2PlasticCardId)
          SettingCleverTapBaseAddress: $(CleverTapBaseAddress)
          SettingCleverTapProjectId: $(CleverTapProjectId)
          SettingCleverTapPassCode: $(CleverTapPassCode)
          SettingMoneyTransferMultimediaURL: $(MoneyTransferMultimediaURL)

          # Step 7: Add your keys here like this: "Setting" + <setting name (same as property)>
          SettingDirectTransferMaxBeneficiariesCount: $(DirectTransferMaxBeneficiariesCount)
          SettingDirectTransferMinAmountToSend: $(DirectTransferMinAmountToSend)
          SettingDirectTransferMaxAmountToSend: $(DirectTransferMaxAmountToSend)
          SettingDirectTransferMaxAmountToSendPerMonth: $(DirectTransferMaxAmountToSendPerMonth)
          SettingDirectTransferFee: $(DirectTransferFee)
          SettingDirectTransferVAT: $(DirectTransferVAT)
          SettingClaimPendingDirectTransfersQueueConnectionString: $(ClaimPendingDirectTransfersQueueConnectionString)
          SettingClaimPendingDirectTransfersQueueName: $(ClaimPendingDirectTransfersQueueName)     
          SettingReversePendingDirectMoneyTransfersSchedule: $(global.ReversePendingDirectMoneyTransfersSchedule)
          SettingReversePendingDirectMoneyTransfersDurationInMin: $(ReversePendingDirectMoneyTransfersDurationInMin)
          SettingReverseFailedDirectMoneyTransfersSchedule: $(global.ReverseFailedDirectMoneyTransfersSchedule)
          ###########################################################################################################


          SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
          SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
          SettingPaykiiServiceToken: $(PaykiiServiceToken)
          SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
          SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
          SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
          SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
          SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
          SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
          SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
          SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
          SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
          SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
          SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
          SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression)
          SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
          SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
          SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl) 
          SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
          SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
          SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
          SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds)
          SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit)
          SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit)
          SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction)
          SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth)
          SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode) 
          SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
          SettingBillPaymentIconContainerName: $(BillPaymentIconContainerName)
          SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode)
          SettingBillPaymentMockUserId: $(BillPaymentMockUserId) 
          SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
          SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
          SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
          SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
          SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
          SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
          SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
          SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
          SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
          SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
          SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
          SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
          SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
          SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceNonWUCorridors: $(MoneyTransferServiceNonWUCorridors)
          SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(MoneyTransferServiceRMTStatusFromCreatedToPendingEnabled)
          SettingMoneyTransferServiceLastRaffleWinnerName: $(MoneyTransferServiceLastRaffleWinnerName)
          SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(MoneyTransferServiceLastRaffleWinnerTicketNumber)
          SettingMoneyTransferServiceRaffleDateString: $(MoneyTransferServiceRaffleDateString)
          SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
          SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
          SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
          SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
          SettingSwaggerUsername: $(SwaggerUsername)
          SettingSwaggerPassword: $(SwaggerPassword)
          SettingEnableSwagger: $(EnableSwagger)
          SettingRatingMinimumDaysToShowInApp: $(RatingMinimumDaysToShowInApp)
          SettingRatingMinimumDaysToShowStore: $(RatingMinimumDaysToShowStore) 
          SettingHRServiceCacheInMinutes: $(HRServiceCacheInMinutes) 
          SettingStoreEmailRecepients: $(StoreEmailRecepients)
          SettingUnEmploymentInsuranceServiceBusTopicName: $(UnEmploymentInsuranceServiceBusTopicName)
          SettingUnEmploymentInsuranceServiceBusUserTopicName: $(UnEmploymentInsuranceServiceBusUserTopicName)
          SettingUnEmploymentInsuranceServiceBusSubscriptionName: $(UnEmploymentInsuranceServiceBusSubscriptionName)
          SettingDingServiceRetryCount: $(DingServiceRetryCount)
          SettingDingServiceSleepDuration: $(DingServiceSleepDuration)
          SettingDingServiceIsRetryEnabled: $(DingServiceIsRetryEnabled)
          SettingTestingMRDynamicPackageTestNepalNumbers: $(TestingMRDynamicPackageTestNepalNumbers)
          SettingTestingMRInlineFeeCalculationTestNumbers: $(TestingMRInlineFeeCalculationTestNumbers)
          SettingEncryptionSettingsIsActive: $(EncryptionSettingsIsActive)
          SettingEncryptionSettingsPrivateKey: $(EncryptionSettingsPrivateKey)
          SettingEncryptionSettingsPublicKey: $(EncryptionSettingsPublicKey)
          SettingFirebaseNotificationAuthEndpoint: $(FirebaseNotificationAuthEndpoint)
          SettingFirebaseNotificationBaseUrl: $(FirebaseNotificationBaseUrl)
          SettingFirebaseNotificationSendMethodUrl: $(FirebaseNotificationSendMethodUrl)
          SettingGoogleAuthType: $(GoogleAuthType)
          SettingGoogleAuthProjectId: $(GoogleAuthProjectId)
          SettingGoogleAuthPrivateKeyId: $(GoogleAuthPrivateKeyId)
          SettingGoogleAuthPrivateKey: $(GoogleAuthPrivateKey)
          SettingGoogleAuthClientEmail: $(GoogleAuthClientEmail)
          SettingGoogleAuthClientId: $(GoogleAuthClientId)
          SettingGoogleAuthAuthUri: $(GoogleAuthAuthUri)
          SettingGoogleAuthTokenUri: $(GoogleAuthTokenUri)
          SettingGoogleAuthAuthProviderX509CertUrl: $(GoogleAuthAuthProviderX509CertUrl)
          SettingGoogleAuthClientX509CertUrl: $(GoogleAuthClientX509CertUrl)
          SettingGoogleAuthUniverseDomain: $(GoogleAuthUniverseDomain)
          SettingC3PayPlusMembershipOverrideLuckyDrawDate: $(C3PayPlusMembershipOverrideLuckyDrawDate)
          SettingC3PayPlusMembershipOverrideLuckyDrawTime: $(C3PayPlusMembershipOverrideLuckyDrawTime)
          SettingC3PayPlusMembershipRenewalSchedule: $(C3PayPlusMembershipRenewalSchedule)
          SettingC3PayPlusMembershipConfirmFirstDebitSchedule: $(C3PayPlusMembershipConfirmFirstDebitSchedule)
          SettingSanctionScreeningApiAddress: $(SanctionScreeningApiAddress)
          SettingC3PayAutomatedCallsBaseUrl: $(C3PayAutomatedCallsBaseUrl)
          SettingC3PayAutomatedCallsAppKey: $(C3PayAutomatedCallsAppKey)
          SettingC3PayAutomatedCallsFromNumber: $(C3PayAutomatedCallsFromNumber)
          SettingC3PayAutomatedCallsCallbackUrl: $(C3PayAutomatedCallsCallbackUrl)
          SettingC3PayAutomatedCallsCallbackSecret: $(C3PayAutomatedCallsCallbackSecret)
          SettingC3PayAutomatedCallsOverrideEnqueueDelay: $(C3PayAutomatedCallsOverrideEnqueueDelay)
          SettingC3PayAutomatedCallsWelcomeCallQueueConnectionString: $(C3PayAutomatedCallsWelcomeCallQueueConnectionString)
          SettingC3PayAutomatedCallsWelcomeCallQueueName: $(C3PayAutomatedCallsWelcomeCallQueueName)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString: $(C3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueName: $(C3PayAutomatedCallsPreLuckyDrawCallQueueName)
          SettingC3PayAutomatedCallsPreRenewalCallQueueConnectionString: $(C3PayAutomatedCallsPreRenewalCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreRenewalCallQueueName: $(C3PayAutomatedCallsPreRenewalCallQueueName)
          SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: $(C3PayPlusMembershipFreeMoneyTransferRefundsSchedule)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $(C3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $(C3PayPlusMembershipATMWithdrawalRefundsQueueName)
          SettingAzureAdInstance: $(SettingAzureAdInstance)
          SettingAzureAdTenantId: $(SettingAzureAdTenantId)
          SettingAzureAdClientId: $(SettingAzureAdClientId)
          SettingAzureAdClientSecret: $(SettingAzureAdClientSecret)
          SettingAzureAdCallbackPath: $(SettingAzureAdCallbackPath)
          SettingAzureAdAudience: $(SettingAzureAdAudience)
          SettingMobileRechargeServiceLowBalanceRenewalSchedule: $(MobileRechargeServiceLowBalanceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays: $(MobileRechargeServiceLowBalanceRetryMaxThresholdInDays)
          SettingVpnMembershipRenewalSchedule: $(VpnMembershipRenewalSchedule)
          SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: $(MoneyTransferServiceC3ToC3MinVersionForOtpCheck)
          SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: $(C3PayPlusMembershipLoginVideoLastSeenIntervalInDays)
          SettingC3PayPlusMembershipTargetedDiscountCooldownDays: $(C3PayPlusMembershipTargetedDiscountCooldownDays)
          SettingC3PayPlusMembershipAllowedPhoneNumbers: $(C3PayPlusMembershipAllowedPhoneNumbers)
          SettingLoginVideoSlotInterval: $(LoginVideoSlotInterval)


  - stage: PROD_EAE    
    dependsOn: []
    condition: and(succeeded(), eq(variables['build.sourceBranch'], 'refs/heads/master'))
    variables:
      - name: azureSubscription
        value: "AzureDevops-eae-c3pay-p"
      - name: azureSubscriptionEQ
        value: "AzureDevops-eae-c3pay-p"
      - name: stage.environment
        value: "PROD"
      - name: stage.entityCode
        value: "eae"
      - name: stage.rgName
        value: "c3pay-dr-prod-rg"
      - name: stage.appName
        value: "c3pay2dr"
      - name: stage.farmSkuName
        value: "S3"
      - name: farmSkuCapacity
        value: 4
      - name: farmSkuCapacityMin
        value: 4
      - name: farmSkuCapacityMax
        value: 6
      - name: stage.farmJobSkuName
        value: "S2"
      - name: farmJobSkuCapacity
        value: 1
      - name: stage.farmPortalSkuName
        value: "S1"
      - name: farmPortalSkuCapacity
        value: 1
      - name: stage.dbSkuName
        value: "P4"
      - name: stage.dbWeeklyLtr
        value: "4"
      - name: stage.dbMonthlyLtr
        value: "1"
      - name: stage.identityDbSkuName
        value: "S1"
      - name: stage.serviceBusSkuName
        value: "Premium"
      - name: stage.dbName
        value: "C3Pay.New.DR"

      - name: rakCertificateName
        value: file.rak.prd.pfx
      - name: rakPrivateKeyName
        value: myc3cert2019.key
      - name: rakPublicKeyName
        value: RAKpublickey2021.pub
      - group: "C3Pay-prod"
      - name: stage.RAKSFTPInputRootDirectory
        value: "//IN//"
      - name: stage.RAKSFTPOutputRootDirectory
        value: "//OUT//"
      - name: stage.RAKSFTPTransactionStatusDirectory
        value: "RMT//C3TxnStatus//"
      - name: stage.RAKSFTPTransactionBlobContainerName
        value: "raktransaction"
      - name: stage.RAKSFTPProfileStatusDirectory
        value: ""
      - name: stage.MobileRechargeTransactionEnvironment
        value: "LIVE"

    jobs:
      - template: cd-jobs.yml
        parameters:
          azureSubscription: ${{ variables.azureSubscription }}
          azureSubscriptionEQ: ${{ variables.azureSubscriptionEQ }}
          targetenvironment: "PROD EAE"
          templateLocation: $(global.templateLocation)
          environment: $(stage.environment)
          entityCode: $(stage.entityCode)
          appName: $(stage.appName)
          location: $(global.location)
          secondaryLocation: $(global.location)
          rgName: $(stage.rgName)
          dbName: $(stage.dbName)

          farmSkuName: $(stage.farmSkuName)
          farmSkuCapacity: ${{ variables.farmSkuCapacity }}
          farmSkuCapacityMin: ${{ variables.farmSkuCapacityMin }}
          farmSkuCapacityMax: ${{ variables.farmSkuCapacityMax }}
          farmJobSkuName: $(stage.farmJobSkuName)
          farmJobSkuCapacity: ${{ variables.farmJobSkuCapacity }}
          farmPortalSkuName: $(stage.farmPortalSkuName)
          farmPortalSkuCapacity: ${{ variables.farmPortalSkuCapacity }}

          dbSkuName: $(stage.dbSkuName)
          dbWeeklyLtr: $(stage.dbWeeklyLtr)
          dbMonthlyLtr: $(stage.dbMonthlyLtr)
          identityDbSkuName: $(stage.identityDbSkuName)
          serviceBusSkuName: $(stage.serviceBusSkuName)
          infraFile: $(global.infraFile)
          webAppBinariesFile: $(global.webAppBinariesFile)
          webJobBinariesFile: $(global.webJobBinariesFile)
          portalWebAppBinariesFile: $(global.portalWebAppBinariesFile)
          sqlFile: $(global.sqlFile)
          identitySqlFile: $(global.identitySqlFile)
          sqlsrvAdministratorLogin: $(sqlsrvAdministratorLogin)
          sqlsrvAdministratorPassword: $(sqlsrvAdministratorPassword)
          DevIP: $(DevIP)
          DevEmails: $(DevEmails)
          rakCertificateName: ${{ variables.rakCertificateName }}
          rakCertificatePassword: $(pfxpassword)
          rakPrivateKeyName: ${{ variables.rakPrivateKeyName }}
          rakPublicKeyName: ${{ variables.rakPublicKeyName }}
          SettingEDCAuthority: $(EDConnectAuthority)
          SettingEDCApiName: $(EDConnectApiName)
          SettingEDCApiSecret: $(EDConnectApiSecret)
          SettingPortalEDCAuthority: $(PortalEDConnectAuthority)
          SettingPortalEDCApiName: $(PortalEDConnectApiName)
          SettingPortalEDCApiSecret: $(PortalEDConnectApiSecret)
          SettingAADAuthority: $(AADAuthority)
          SettingAADAudience: $(AADAudience)
          SettingAADAllowedClientIds: $(AADAllowedClientIds)
          SettingKeyName: $(KeyName)
          SettingSignzyURL: $(SignzyURL)
          SettingSignzyFileExchangeAddress: $(SignzyFileExchangeAddress)
          SettingSignzyId: $(SignzyId)
          SettingSignzyUserId: $(SignzyUserId) 
          SettingTransactionsB2CServiceAuthority: $(TransactionsB2CServiceAuthority) 
          SettingTransactionsB2CServiceClientId: $(TransactionsB2CServiceClientId) 
          SettingTransactionsB2CServiceScope: $(TransactionsB2CServiceScope) 
          SettingTransactionsB2CServiceClientSecret: $(TransactionsB2CServiceClientSecret) 
          SettingTransactionsB2CServiceBaseAddress: $(TransactionsB2CServiceBaseAddress) 
          SettingTransactionsB2CServiceGrantType: $(TransactionsB2CServiceGrantType) 
          SettingTransactionsB2CServiceAPIVersion: $(TransactionsB2CServiceAPIVersion) 
          SettingRakBaseURL: $(RakBaseURL)
          SettingRakClientId: $(RakClientId)
          SettingRakClientSecret: $(RakClientSecret)
          SettingRakProcessBankTransactionsReportsSchedule: $(global.RakProcessBankTransactionsReportsSchedule)
          SettingRakUpdatePendingBankTransactionsSchedule: $(global.RakUpdatePendingBankTransactionsSchedule)
          SettingRakReverseFailedBankTransactionsSchedule: $(global.RakReverseFailedBankTransactionsSchedule)
          SettingRakReadRMTProfileResponsesSchedule: $(RakReadRMTProfileResponsesSchedule)
          SettingRakSftpRMTProfileResponsesDirectory: $(RakSftpRMTProfileResponsesDirectory)
          SettingRakSftpMissingRakFileAlertPhoneNumbers: $(RakSftpMissingRakFileAlertPhoneNumbers)
          SettingRakBanksMaxRecords: $(global.RakBanksMaxRecords)
          SettingRakMaxTransactionTriesCount: $(global.RakMaxTransactionTriesCount)
          SettingRakMessageProcessInDelay: $(global.RakMessageProcessInDelay)
          SettingRakMoneyTransferBeneficiaryDelayInMins: $(global.RakMoneyTransferBeneficiaryDelayInMins)
          SettingRakLoyaltyImplementDate: $(global.RakLoyaltyImplementDate)
          SettingRakLoyaltyLimitCount: $(global.RakLoyaltyLimitCount)
          SettingRakLoyaltyLimitAmount: $(global.RakLoyaltyLimitAmount)
          SettingRakURLPath: $(global.RakURLPath)
          SettingRakMoneyTransferBeneficiaryCount: $(global.MoneyTransferBeneficiaryCount)
          SettingRakSftpEndPoint: $(RakSftpEndPoint)
          SettingRakSftpPort: $(RakSftpPort)
          SettingRakSftpUsername: $(RakSftpUsername)
          SettingRakSftpPassword: $(RakSftpPassword)
          SettingRAKSFTPInputRootDirectory: $(stage.RAKSFTPInputRootDirectory)
          SettingRAKSFTPOutputRootDirectory: $(stage.RAKSFTPOutputRootDirectory)
          SettingRAKSFTPTransactionStatusDirectory: $(stage.RAKSFTPTransactionStatusDirectory)
          SettingRAKSFTPTransactionBlobContainerName: $(stage.RAKSFTPTransactionBlobContainerName)
          SettingRAKSFTPProfileStatusDirectory: $(stage.RAKSFTPProfileStatusDirectory)
          SettingRakRefreshRatesSchedule: $(global.RefreshRatesSchedule)
          SettingRakRefreshRatesEmiratesId: $(RakRefreshRatesEmiratesId)
          SettingPPSWebAuthBaseURL: $(PPSWebAuthBaseURL)
          SettingPPSWebAuthClientId: $(PPSWebAuthClientId)
          SettingPPSWebAuthClientSecretkey: $(PPSWebAuthClientSecretkey)
          SettingPPSEndpointAddress: $(PPSEndpointAddress)
          SettingPPSUsername: $(PPSUsername)
          SettingPPSPassword: $(PPSPassword)
          SettingPPSSponsorCode: $(PPSSponsorCode)
          SettingPPSCustomerCode: $(PPSCustomerCode)
          SettingPPSSharedSecret: $(PPSSharedSecret)
          SettingKYCBaseAddress: $(KYCBaseAddress)
          SettingKYCUsername: $(KYCUsername)
          SettingKYCPassword: $(KYCPassword)
          SettingKYCUniqueRef: $(KYCUniqueRef)
          SettingKYCSponsorCode: $(KYCSponsorCode)
          SettingKYCSharedSecret: $(KYCSharedSecret)
          SettingEtisalatSMSUsername: $(EtisalatSMSUsername)
          SettingEtisalatSMSPassword: $(EtisalatSMSPassword)
          SettingEtisalatSMSBaseAddress: $(EtisalatSMSBaseAddress)
          SettingInfobipSMSUsername: $(InfobipSMSUsername)
          SettingInfobipSMSPassword: $(InfobipSMSPassword)
          SettingInfobipSMSBaseAddress: $(InfobipSMSBaseAddress)
          SettingInfobipSMSAuthKey: $(InfobipSMSAuthKey)
          SettingInfobipSMSAuthKeyBaseUrl: $(InfobipSMSAuthKeyBaseUrl)
          SettingInfobipSMSTimeout: $(InfobipSMSTimeout)
          SettingInfobipSMSRetryCount: $(InfobipSMSRetryCount)
          SettingInfobipSMSSmsMode: $(InfobipSMSSmsMode)
          SettingDingBaseURL: $(DingBaseURL)
          SettingDingClientApiKey: $(DingClientApiKey)
          SettingSecondaryDingClientApiKey: $(SecondaryDingClientApiKey)
          SettingMobileRechargeSynchronizeWithDingSchedule: $(global.MobileRechargeSynchronizeWithDingSchedule)
          SettingMobileRechargeUpdateStatusSchedule: $(global.MobileRechargeUpdateStatusSchedule)
          SettingMobileRechargeNickNameLength: $(global.MobileRechargeNickNameLength)
          SettingMobileRechargeC3FeeMode: $(global.MobileRechargeC3FeeMode)
          SettingMobileRechargeMySalaryFeeMode: $(global.MobileRechargeMySalaryFeeMode)
          SettingMobileRechargeSelectedCorporatesWithFee: $(global.MobileRechargeSelectedCorporatesWithFee)
          SettingMobileRechargeFeeAmount: $(global.MobileRechargeFeeAmount)
          SettingMobileRechargeCallingCardAccountNumberLive: $(global.MobileRechargeCallingCardAccountNumberLive)
          SettingMobileRechargeTransactionEnvironment: $(stage.MobileRechargeTransactionEnvironment)
          SettingMobileRechargeNonVerifiedLimit: $(stage.MobileRechargeNonVerifiedLimit)
          SettingMobileRechargeVerifiedLimit: $(stage.MobileRechargeVerifiedLimit)
          SettingMobileRechargeCustomCallingCardName: $(MobileRechargeCustomCallingCardName)
          SettingMobileRechargeCustomCallingCardCode: $(MobileRechargeCustomCallingCardCode)
          SettingMobileRechargeCustomCallingCardLogoUrl: $(MobileRechargeCustomCallingCardLogoUrl)
          SettingMobileRechargeCustomCallingCardValidationRegex: $(MobileRechargeCustomCallingCardValidationRegex)
          SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
          SettingMobileRechargeServiceServiceBusTopicName: $(MobileRechargeServiceServiceBusTopicName)
          SettingMobileRechargeServiceServiceBusSubscriptionName: $(MobileRechargeServiceServiceBusSubscriptionName)  
          SettingRenewalCardUpdateServiceBusTopicName: $(RenewalCardUpdateServiceBusTopicName) 
          SettingRenewalCardUpdateServiceBusSubscriptionName: $(RenewalCardUpdateServiceBusSubscriptionName) 
          SettingEdenredIdentityManagerBaseAddress: $(EdenredIdentityManagerBaseAddress)
          SettingEdenredIdentityManagerAuthority: $(EdenredIdentityManagerAuthority)
          SettingEdenredIdentityManagerResourceId: $(EdenredIdentityManagerResourceId)
          SettingEdenredIdentityManagerClientId: $(EdenredIdentityManagerClientId)
          SettingEdenredIdentityManagerClientSecret: $(EdenredIdentityManagerClientSecret)
          SettingFirebaseCloudMessagingBaseAddress: $(FirebaseCloudMessagingBaseAddress)
          SettingFirebaseCloudMessagingKey: $(FirebaseCloudMessagingKey)
          SettingFirebaseCloudMessagingSenderId: $(FirebaseCloudMessagingSenderId)
          SettingFirebaseCloudMessagingRetryCount: $(FirebaseCloudMessagingRetryCount)
          SettingFirebaseCloudMessagingTimeout: $(FirebaseCloudMessagingTimeout)
          SettingESMOServiceBaseAddress: $(ESMOBaseAddress)
          SettingESMOServiceClientId: $(ESMOClientId)
          SettingESMOServiceClientSecret: $(ESMOClientSecret)
          SettingESMOServiceAuthority: $(ESMOAuthority)
          SettingESMOServiceScope: $(ESMOScope)
          SettingMobileAppHashKey: $(MobileAppHashKey)
          SettingSendGridSenderEmail: $(SendGridSenderEmail)
          SettingSendGridAPIKey: $(SendGridAPIKey)
          SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: $(SendGridCardHolderRegistrationRejectedEmailTemplateId)
          SettingSendGridPortalUserCreatedEmailTemplateId: $(SendGridPortalUserCreatedEmailTemplateId)
          SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: $(SendGridCardHolderRMTProfileCreatedEmailTemplateId)
          SettingSendGridBankStatementEmailTemplateId: $(SendGridBankStatementEmailTemplateId)
          SettingSendGridStoreOrderPlacedEmailTemplateId: $(SendGridStoreOrderPlacedEmailTemplateId)
          SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
          SettingRedisConnection: $(RedisConnection)
          SettingServiceBusConnection: $(ServiceBusConnection)
          SettingFirstBlackV1PlasticCardId: $(global.FirstBlackV1PlasticCardId)
          SettingFirstBlackV2PlasticCardId: $(global.FirstBlackV2PlasticCardId)
          SettingCleverTapBaseAddress: $(CleverTapBaseAddress)
          SettingCleverTapProjectId: $(CleverTapProjectId)
          SettingCleverTapPassCode: $(CleverTapPassCode)
          SettingMoneyTransferMultimediaURL: $(MoneyTransferMultimediaURL)

          # Step 8: Add your keys here like this: "Setting" + <setting name (same as property)>
          SettingDirectTransferMaxBeneficiariesCount: $(DirectTransferMaxBeneficiariesCount)
          SettingDirectTransferMinAmountToSend: $(DirectTransferMinAmountToSend)
          SettingDirectTransferMaxAmountToSend: $(DirectTransferMaxAmountToSend)
          SettingDirectTransferMaxAmountToSendPerMonth: $(DirectTransferMaxAmountToSendPerMonth)
          SettingDirectTransferFee: $(DirectTransferFee)
          SettingDirectTransferVAT: $(DirectTransferVAT)
          SettingClaimPendingDirectTransfersQueueConnectionString: $(ClaimPendingDirectTransfersQueueConnectionString)
          SettingClaimPendingDirectTransfersQueueName: $(ClaimPendingDirectTransfersQueueName)      
          SettingReversePendingDirectMoneyTransfersSchedule: $(global.ReversePendingDirectMoneyTransfersSchedule)
          SettingReversePendingDirectMoneyTransfersDurationInMin: $(ReversePendingDirectMoneyTransfersDurationInMin)
          SettingReverseFailedDirectMoneyTransfersSchedule: $(global.ReverseFailedDirectMoneyTransfersSchedule)
          ###########################################################################################################

          SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
          SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
          SettingPaykiiServiceToken: $(PaykiiServiceToken)
          SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
          SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
          SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
          SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
          SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
          SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
          SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
          SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
          SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
          SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
          SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
          SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression)
          SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
          SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
          SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl)
          SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
          SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
          SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
          SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds)
          SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit)
          SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit)
          SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction)
          SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth)
          SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode)
          SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified)
          SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
          SettingBillPaymentIconContainerName: $(BillPaymentIconContainerName)
          SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode)
          SettingBillPaymentMockUserId: $(BillPaymentMockUserId) 
          SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
          SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
          SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
          SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
          SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
          SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
          SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
          SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
          SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
          SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
          SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
          SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
          SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
          SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays)
          SettingMoneyTransferServiceNonWUCorridors: $(MoneyTransferServiceNonWUCorridors)
          SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(MoneyTransferServiceRMTStatusFromCreatedToPendingEnabled)
          SettingMoneyTransferServiceLastRaffleWinnerName: $(MoneyTransferServiceLastRaffleWinnerName)
          SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(MoneyTransferServiceLastRaffleWinnerTicketNumber)
          SettingMoneyTransferServiceRaffleDateString: $(MoneyTransferServiceRaffleDateString)
          SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
          SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
          SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
          SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
          SettingSwaggerUsername: $(SwaggerUsername)
          SettingSwaggerPassword: $(SwaggerPassword)
          SettingEnableSwagger: $(EnableSwagger)
          SettingRatingMinimumDaysToShowInApp: $(RatingMinimumDaysToShowInApp)
          SettingRatingMinimumDaysToShowStore: $(RatingMinimumDaysToShowStore)
          SettingHRServiceCacheInMinutes: $(HRServiceCacheInMinutes) 
          SettingStoreEmailRecepients: $(StoreEmailRecepients)
          SettingUnEmploymentInsuranceServiceBusTopicName: $(UnEmploymentInsuranceServiceBusTopicName)
          SettingUnEmploymentInsuranceServiceBusSubscriptionName: $(UnEmploymentInsuranceServiceBusSubscriptionName)
          SettingDingServiceRetryCount: $(DingServiceRetryCount)
          SettingDingServiceSleepDuration: $(DingServiceSleepDuration) 
          SettingTestingMRDynamicPackageTestNepalNumbers: $(TestingMRDynamicPackageTestNepalNumbers)
          SettingTestingMRInlineFeeCalculationTestNumbers: $(TestingMRInlineFeeCalculationTestNumbers)
          SettingEncryptionSettingsIsActive: $(EncryptionSettingsIsActive)
          SettingEncryptionSettingsPrivateKey: $(EncryptionSettingsPrivateKey)
          SettingEncryptionSettingsPublicKey: $(EncryptionSettingsPublicKey)
          SettingFirebaseNotificationAuthEndpoint: $(FirebaseNotificationAuthEndpoint)
          SettingFirebaseNotificationBaseUrl: $(FirebaseNotificationBaseUrl)
          SettingFirebaseNotificationSendMethodUrl: $(FirebaseNotificationSendMethodUrl)
          SettingGoogleAuthType: $(GoogleAuthType)
          SettingGoogleAuthProjectId: $(GoogleAuthProjectId)
          SettingGoogleAuthPrivateKeyId: $(GoogleAuthPrivateKeyId)
          SettingGoogleAuthPrivateKey: $(GoogleAuthPrivateKey)
          SettingGoogleAuthClientEmail: $(GoogleAuthClientEmail)
          SettingGoogleAuthClientId: $(GoogleAuthClientId)
          SettingGoogleAuthAuthUri: $(GoogleAuthAuthUri)
          SettingGoogleAuthTokenUri: $(GoogleAuthTokenUri)
          SettingGoogleAuthAuthProviderX509CertUrl: $(GoogleAuthAuthProviderX509CertUrl)
          SettingGoogleAuthClientX509CertUrl: $(GoogleAuthClientX509CertUrl)
          SettingGoogleAuthUniverseDomain: $(GoogleAuthUniverseDomain)
          SettingC3PayPlusMembershipOverrideLuckyDrawDate: $(C3PayPlusMembershipOverrideLuckyDrawDate)
          SettingC3PayPlusMembershipOverrideLuckyDrawTime: $(C3PayPlusMembershipOverrideLuckyDrawTime)
          SettingC3PayPlusMembershipRenewalSchedule: $(C3PayPlusMembershipRenewalSchedule)
          SettingC3PayPlusMembershipConfirmFirstDebitSchedule: $(C3PayPlusMembershipConfirmFirstDebitSchedule)
          SettingSanctionScreeningApiAddress: $(SanctionScreeningApiAddress)
          SettingC3PayAutomatedCallsBaseUrl: $(C3PayAutomatedCallsBaseUrl)
          SettingC3PayAutomatedCallsAppKey: $(C3PayAutomatedCallsAppKey)
          SettingC3PayAutomatedCallsFromNumber: $(C3PayAutomatedCallsFromNumber)
          SettingC3PayAutomatedCallsCallbackUrl: $(C3PayAutomatedCallsCallbackUrl)
          SettingC3PayAutomatedCallsCallbackSecret: $(C3PayAutomatedCallsCallbackSecret)
          SettingC3PayAutomatedCallsOverrideEnqueueDelay: $(C3PayAutomatedCallsOverrideEnqueueDelay)
          SettingC3PayAutomatedCallsWelcomeCallQueueConnectionString: $(C3PayAutomatedCallsWelcomeCallQueueConnectionString)
          SettingC3PayAutomatedCallsWelcomeCallQueueName: $(C3PayAutomatedCallsWelcomeCallQueueName)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString: $(C3PayAutomatedCallsPreLuckyDrawCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreLuckyDrawCallQueueName: $(C3PayAutomatedCallsPreLuckyDrawCallQueueName)
          SettingC3PayAutomatedCallsPreRenewalCallQueueConnectionString: $(C3PayAutomatedCallsPreRenewalCallQueueConnectionString)
          SettingC3PayAutomatedCallsPreRenewalCallQueueName: $(C3PayAutomatedCallsPreRenewalCallQueueName)
          SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: $(C3PayPlusMembershipFreeMoneyTransferRefundsSchedule)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $(C3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString)
          SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $(C3PayPlusMembershipATMWithdrawalRefundsQueueName)
          SettingAzureAdInstance: $(SettingAzureAdInstance)
          SettingAzureAdTenantId: $(SettingAzureAdTenantId)
          SettingAzureAdClientId: $(SettingAzureAdClientId)
          SettingAzureAdClientSecret: $(SettingAzureAdClientSecret)
          SettingAzureAdCallbackPath: $(SettingAzureAdCallbackPath)
          SettingAzureAdAudience: $(SettingAzureAdAudience)
          SettingMobileRechargeServiceLowBalanceRenewalSchedule: $(MobileRechargeServiceLowBalanceRenewalSchedule)
          SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays: $(MobileRechargeServiceLowBalanceRetryMaxThresholdInDays)
          SettingVpnMembershipRenewalSchedule: $(VpnMembershipRenewalSchedule)
          SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: $(MoneyTransferServiceC3ToC3MinVersionForOtpCheck)
          SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: $(C3PayPlusMembershipLoginVideoLastSeenIntervalInDays)
          SettingC3PayPlusMembershipTargetedDiscountCooldownDays: $(C3PayPlusMembershipTargetedDiscountCooldownDays)
          SettingC3PayPlusMembershipAllowedPhoneNumbers: $(C3PayPlusMembershipAllowedPhoneNumbers)
          SettingLoginVideoSlotInterval: $(LoginVideoSlotInterval)
