﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateMTransaction : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsRewardAssignmentFailed",
                table: "MoneyTransferTransactions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "RewardAssignmentRetryCount",
                table: "MoneyTransferTransactions",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsRewardAssignmentFailed",
                table: "MoneyTransferTransactions");

            migrationBuilder.DropColumn(
                name: "RewardAssignmentRetryCount",
                table: "MoneyTransferTransactions");
        }
    }
}
