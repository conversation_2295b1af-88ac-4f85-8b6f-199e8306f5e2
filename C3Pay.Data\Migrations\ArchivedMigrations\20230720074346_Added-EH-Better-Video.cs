﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedEHBetterVideo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Features",
                columns: new[] { "Id", "Name" },
                values: new object[] { 11, "EhRatesAreBetter" });

            migrationBuilder.InsertData(
               table: "MultimediaResources",
               columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
               values: new object[] { 47, 11, null, "en", "IND", null, 1, "https://eaec3sharedsp.blob.core.windows.net/money-transfer-videos/eh-rates-are-better-v1.mp4" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Features",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 47);
        }
    }
}
