﻿using Microsoft.Azure.Cosmos.Table;
using Azure.Storage.Blobs;
using Edenred.Common.Core;
using Microsoft.Azure.Storage;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace Edenred.Common.Services.Extension
{

    /// <summary>
    /// 
    /// </summary>
    public static class ServiceCollectionAzureExtensions
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddTableStorageService(this IServiceCollection services, IConfiguration configuration, string connectionStringName)
        {
            if (!services.Any(x => x.ServiceType == typeof(ITableStorageService)))
            {
                var connectionString = configuration.GetConnectionString(connectionStringName);

                services.AddScoped(service => Microsoft.Azure.Cosmos.Table.CloudStorageAccount.Parse(connectionString).CreateCloudTableClient());

                services.AddScoped<ITableStorageService, TableStorageService>();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddBlobStorageService(this IServiceCollection services, IConfiguration configuration, string connectionStringName)
        {
            if (!services.Any(x => x.ServiceType == typeof(IBlobStorageService)))
            {
                var connectionString = configuration.GetConnectionString(connectionStringName);

                services.AddTransient(service => new BlobServiceClient(connectionString));

                var storageAccount = Microsoft.Azure.Storage.CloudStorageAccount.Parse(connectionString);

                services.Configure<BlobStorageSettings>(settings => {
                    settings.AccountName = storageAccount.Credentials.AccountName;
                    settings.AccountKey = storageAccount.Credentials.ExportBase64EncodedKey();
                    settings.BlobEndpoint = storageAccount.BlobEndpoint.AbsoluteUri;
                    settings.BlobStorageUri = storageAccount.BlobStorageUri.PrimaryUri.AbsoluteUri;
                });

                services.AddScoped<IBlobStorageService, BlobStorageService>();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddKeyVault(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(IKeyVaultService)))
            {
                services.AddScoped<IKeyVaultService, KeyVaultService>();
                services.Configure<KeyVaultSettings>(configuration.GetSection("KeyVault"));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddSendGridService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(IEmailService)))
            {
                services.Configure<SendGridSettings>(configuration.GetSection("SendGrid"));
                services.AddScoped<IEmailService, SendGridService>();
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddServiceBusService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(IMessagingQueueService)))
            {
                services.AddScoped<IMessagingQueueService, ServiceBusMessagingService>();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="services"></param>
        /// <param name="configuration"></param>
        public static void AddOpenTelemetryService(this IServiceCollection services, IConfiguration configuration)
        {
            if (!services.Any(x => x.ServiceType == typeof(IOpenTelemetryService)))
            {
                services.AddScoped<IOpenTelemetryService, OpenTelemetryService>();
            }
        }
    }
}