﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Services\GraphicalResources\edenred-logo.png" />
		<None Remove="Services\GraphicalResources\index_logo.png" />
		<None Remove="Services\GraphicalResources\insurance-logo.png" />
		<None Remove="Services\GraphicalResources\logo.png" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="Services\GraphicalResources\edenred-logo.png" />
		<EmbeddedResource Include="Services\GraphicalResources\index_logo.png" />
		<EmbeddedResource Include="Services\GraphicalResources\insurance-logo.png" />
		<EmbeddedResource Include="Services\GraphicalResources\logo.png" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
		<PackageReference Include="iTextSharp" Version="5.5.13.4" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.Azure.Cosmos.Table" Version="1.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.10" />
		<!-- <PackageReference Include="Microsoft.Extensions.Http" Version="5.0.0" />  -->
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Polly" Version="8.4.2" />
		<PackageReference Include="SendGrid" Version="9.29.3" />
		<PackageReference Include="SSH.NET" Version="2024.1.0" />
		<PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.ComputerVision" Version="7.0.1" />
		<PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.Face" Version="2.8.0-preview.2" />
		<PackageReference Include="EFCore.BulkExtensions" Version="8.1.1" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />

		<PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
		<PackageReference Include="Azure.Data.Tables" Version="12.9.1" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.22.2" />
		<PackageReference Include="ClosedXML" Version="0.104.1" />
		<PackageReference Include="FuzzySharp" Version="2.0.2" />
		<PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
		<PackageReference Include="jose-jwt" Version="5.0.0" />
		<PackageReference Include="OpenTelemetry" Version="1.7.0" />
		<PackageReference Include="OpenTelemetry.Api" Version="1.7.0" />
		<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Server.Kestrel.Core" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="2.2.0" />
		<PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
		<PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.1" />
		<PackageReference Include="Microsoft.Azure.Storage.Common" Version="11.2.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="5.0.17" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.10" />
		<PackageReference Include="Renci.SshNet.Async" Version="1.4.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.16" />
		<PackageReference Include="System.Drawing.Common" Version="6.0.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.1.2" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
		<PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
		<PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
		<PackageReference Include="System.Text.Encoding" Version="4.3.0" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
		<PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.ComputerVision" Version="7.0.1" />
		<PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.Face" Version="2.8.0-preview.2" />

	</ItemGroup>

	<ItemGroup>
		<Folder Include="Data\" />
		<Folder Include="Services\" />
	</ItemGroup>

</Project>
