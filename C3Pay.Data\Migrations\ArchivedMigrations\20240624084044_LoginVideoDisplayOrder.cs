﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideoDisplayOrder : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots");

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "LoginVideoSlots",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots");

            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "LoginVideoSlots");

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoSlots_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId",
                unique: true);
        }
    }
}
