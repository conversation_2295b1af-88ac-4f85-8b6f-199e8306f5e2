﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedBeneficiaryForeignKey : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeRenewals_BeneficiaryId",
                table: "MobileRechargeRenewals",
                column: "BeneficiaryId");

            migrationBuilder.AddForeignKey(
                name: "FK_MobileRechargeRenewals_MobileRechargeBeneficiaries_BeneficiaryId",
                table: "MobileRechargeRenewals",
                column: "BeneficiaryId",
                principalTable: "MobileRechargeBeneficiaries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MobileRechargeRenewals_MobileRechargeBeneficiaries_BeneficiaryId",
                table: "MobileRechargeRenewals");

            migrationBuilder.DropIndex(
                name: "IX_MobileRechargeRenewals_BeneficiaryId",
                table: "MobileRechargeRenewals");
        }
    }
}
