﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnEmploymentInsurance_MySalaryUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "UnEmpInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Category", "Description", "Fee", "FeeCurrency", "Frequency", "IsActive", "PartnerCode" },
                values: new object[,]
                {
                    { 9, 5m, "AED", 3m, "AED", 1, "AED 6/Month", 2m, "AED", 1, true, 2 },
                    { 10, 60m, "AED", 3m, "AED", 1, "AED 60 (1 Year)", 10m, "AED", 4, true, 2 },
                    { 11, 10m, "AED", 6m, "AED", 2, "AED 10/Month", 4m, "AED", 1, true, 2 },
                    { 12, 120m, "AED", 6m, "AED", 2, "AED 120 (1 Year)", 20m, "AED", 4, true, 2 }
                });

            migrationBuilder.InsertData(
                table: "UnEmpInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Category", "Description", "Fee", "FeeCurrency", "Frequency", "PartnerCode" },
                values: new object[,]
                {
                    { 13, 15m, "AED", 3m, "AED", 1, "AED 15/Quarter", 2m, "AED", 2, 2 },
                    { 14, 30m, "AED", 3m, "AED", 1, "AED 30 (Semi-Annual)", 2m, "AED", 3, 2 },
                    { 15, 30m, "AED", 6m, "AED", 2, "AED 30/Quarter", 20m, "AED", 2, 2 },
                    { 16, 60m, "AED", 6m, "AED", 2, "AED 60 (Semi-Annual)", 20m, "AED", 3, 2 }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 16);
        }
    }
}
