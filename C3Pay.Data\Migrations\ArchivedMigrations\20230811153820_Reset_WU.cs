﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Reset_WU : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CountryTransferMethods");

            migrationBuilder.DropTable(
                name: "MoneyTransferFields");

            migrationBuilder.DropTable(
                name: "MoneyTransferFieldGroups");

            migrationBuilder.DropTable(
                name: "MoneyTransferCountries");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferCountries",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Alpha2Code = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    Alpha3Code = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsPopular = table.Column<bool>(type: "bit", nullable: false),
                    IsdCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferCountries", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CountryTransferMethods",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DetailsUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    MoneyTransferCountryId = table.Column<int>(type: "int", nullable: false),
                    ProviderLogoUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProviderName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CountryTransferMethods", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CountryTransferMethods_MoneyTransferCountries_MoneyTransferCountryId",
                        column: x => x.MoneyTransferCountryId,
                        principalTable: "MoneyTransferCountries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferFieldGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    MoneyTransferCountryId = table.Column<int>(type: "int", nullable: true),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferFieldGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                        column: x => x.MoneyTransferCountryId,
                        principalTable: "MoneyTransferCountries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    DropdownReference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldType = table.Column<int>(type: "int", nullable: false),
                    Hint = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    MaxLength = table.Column<int>(type: "int", nullable: false),
                    MinLength = table.Column<int>(type: "int", nullable: false),
                    MoneyTransferFieldGroupId = table.Column<int>(type: "int", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferFields_MoneyTransferFieldGroups_MoneyTransferFieldGroupId",
                        column: x => x.MoneyTransferFieldGroupId,
                        principalTable: "MoneyTransferFieldGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferCountries",
                columns: new[] { "Id", "Alpha2Code", "Alpha3Code", "Currency", "DisplayOrder", "IsActive", "IsPopular", "IsdCode", "Name" },
                values: new object[,]
                {
                    { 1, "IN", "IND", "INR", 1, true, true, "91", "India" },
                    { 2, "PK", "PAK", "PKR", 2, true, true, "92", "Pakistan" },
                    { 3, "BD", "BGD", "BDT", 3, true, true, "880", "Bangladesh" },
                    { 4, "LK", "LKA", "LKR", 4, true, true, "94", "Sri Lanka" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_CountryTransferMethods_MoneyTransferCountryId",
                table: "CountryTransferMethods",
                column: "MoneyTransferCountryId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferFieldGroups_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferFields_MoneyTransferFieldGroupId",
                table: "MoneyTransferFields",
                column: "MoneyTransferFieldGroupId");
        }
    }
}
