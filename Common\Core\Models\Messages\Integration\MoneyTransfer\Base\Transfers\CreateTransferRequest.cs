﻿namespace Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Transfers
{
    public class CreateTransferRequest
    {
        public string MessageId { get; set; }
        /// <summary>
        /// Gets or sets a unique reference number associated with the transfer request.
        /// </summary>
        public string C3ReferenceNumber { get; set; }

        /// <summary>
        /// Gets or sets information about the sender of the money transfer.
        /// </summary>
        public MoneyTransferSender Sender { get; set; }

        /// <summary>
        /// Gets or sets information about the receiver of the money transfer.
        /// </summary>
        public MoneyTransferReceiver Receiver { get; set; }

        /// <summary>
        /// Gets or sets the amount of money to be transferred, including currency details.
        /// </summary>
        public MoneyTransferAmount Amount { get; set; }

        /// <summary>
        /// Gets or sets a code or description specifying the purpose of the money transfer.
        /// </summary>
        public string PurposeCode { get; set; }

        /// <summary>
        /// Gets or sets the API version used for interacting with the money transfer service.
        /// </summary>
        public string ApiVersion { get; set; }

        /// <summary>
        /// Gets or sets the type of transaction for the transfer request.
        /// </summary>
        public string TransactionType { get; set; }

        /// <summary>
        /// Gets or sets the pre-validate flag for the transfer request.
        /// </summary>
        public bool PreValidateFlag { get; set; }

        /// <summary>
        /// Gets or sets the temporary host transaction ID for the transfer request.
        /// </summary>
        public string TempHostTxnId { get; set; }
    }
}
