﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Removed_ManuallyUpdatedTracked_C3PayPlus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WasAtmReversalManuallyUpdated",
                table: "C3PayPlusMembershipTransactions");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "WasAtmReversalManuallyUpdated",
                table: "C3PayPlusMembershipTransactions",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
