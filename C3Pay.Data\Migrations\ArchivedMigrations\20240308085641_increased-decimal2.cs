﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class increaseddecimal2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "ConversionRate",
                table: "MoneyTransferTransactions",
                type: "decimal(18,16)",
                precision: 18,
                scale: 16,
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,7)",
                oldPrecision: 18,
                oldScale: 7,
                oldNullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "ConversionRate",
                table: "MoneyTransferTransactions",
                type: "decimal(18,7)",
                precision: 18,
                scale: 7,
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,16)",
                oldPrecision: 18,
                oldScale: 16,
                oldNullable: true);
        }
    }
}
