﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class UnemploymentInsurance : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UnemploymentInsurancePaymentOptions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    AmountVAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountVATCurrency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FeeCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Frequency = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnemploymentInsurancePaymentOptions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UnemploymentInsurancePayments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", nullable: true),
                    PhoneNumber = table.Column<string>(type: "nvarchar(14)", maxLength: 14, nullable: true),
                    DocumentNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    OptionId = table.Column<int>(type: "int", nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    AmountVAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountVATCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FeeCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    TotalAmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnemploymentInsurancePayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnemploymentInsurancePayments_Transactions_ReferenceNumber",
                        column: x => x.ReferenceNumber,
                        principalTable: "Transactions",
                        principalColumn: "ReferenceNumber");
                    table.ForeignKey(
                        name: "FK_UnemploymentInsurancePayments_UnemploymentInsurancePaymentOptions_OptionId",
                        column: x => x.OptionId,
                        principalTable: "UnemploymentInsurancePaymentOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_UnemploymentInsurancePayments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "UnemploymentInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Description", "Fee", "FeeCurrency", "Frequency", "Name" },
                values: new object[,]
                {
                    { 1, 5m, "AED", 0.25m, "AED", "AED 6/Month", 0.75m, "AED", 0, "Monthly" },
                    { 2, 60m, "AED", 3m, "AED", "AED 70 (1 Year)", 7m, "AED", 1, "Full" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_UnemploymentInsurancePayments_CreatedDate",
                table: "UnemploymentInsurancePayments",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_UnemploymentInsurancePayments_OptionId",
                table: "UnemploymentInsurancePayments",
                column: "OptionId");

            migrationBuilder.CreateIndex(
                name: "IX_UnemploymentInsurancePayments_ReferenceNumber",
                table: "UnemploymentInsurancePayments",
                column: "ReferenceNumber",
                unique: true,
                filter: "[ReferenceNumber] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_UnemploymentInsurancePayments_UserId",
                table: "UnemploymentInsurancePayments",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UnemploymentInsurancePayments");

            migrationBuilder.DropTable(
                name: "UnemploymentInsurancePaymentOptions");
        }
    }
}
