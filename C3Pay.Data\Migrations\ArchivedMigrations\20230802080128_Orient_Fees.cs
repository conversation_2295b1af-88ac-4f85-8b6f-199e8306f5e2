﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Orient_Fees : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 1,
                column: "Code",
                value: "DirectClient");

            migrationBuilder.InsertData(
                table: "StatementFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "Option", "PartnerId", "UpdatedDate", "VatPercentage" },
                values: new object[,]
                {
                    { new Guid("b13b85df-fc93-4370-b748-7e4f25eef1f1"), new DateTime(2023, 8, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 25m, false, 1, 5, null, 5m },
                    { new Guid("b24bdea9-82e5-4482-93f3-03a8f98fa952"), new DateTime(2023, 8, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 25m, false, 2, 5, null, 5m }
                });

            migrationBuilder.InsertData(
                table: "SubscriptionFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "PartnerId", "PaymentFrequency", "SubscriptionId", "UpdatedDate" },
                values: new object[,]
                {
                    { new Guid("648ebeef-497e-4ee5-8438-d3fe5b64054a"), new DateTime(2023, 8, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 3m, false, 5, 1, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null },
                    { new Guid("f05df535-6972-4c09-b439-e12547338c61"), new DateTime(2023, 8, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 3m, false, 5, 1, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null },
                    { new Guid("5a41404e-6dec-45e2-94c4-0057a085899d"), new DateTime(2023, 8, 2, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 1m, false, 5, 2, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null }
                });

            migrationBuilder.InsertData(
                table: "UnEmpInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Category", "Description", "Fee", "FeeCurrency", "Frequency", "IsActive", "PartnerCode", "PartnerId" },
                values: new object[,]
                {
                    { 17, 5m, "AED", 3m, "AED", 1, "AED 5/Month", 2m, "AED", 1, true, 1, 5 },
                    { 18, 60m, "AED", 3m, "AED", 1, "AED 60 (1 Year)", 10m, "AED", 4, true, 1, 5 },
                    { 19, 10m, "AED", 6m, "AED", 2, "AED 10/Month", 4m, "AED", 1, true, 1, 5 },
                    { 20, 120m, "AED", 6m, "AED", 2, "AED 120 (1 Year)", 20m, "AED", 4, true, 1, 5 }
                });

            migrationBuilder.InsertData(
                table: "UnEmpInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Category", "Description", "Fee", "FeeCurrency", "Frequency", "PartnerCode", "PartnerId" },
                values: new object[,]
                {
                    { 21, 15m, "AED", 3m, "AED", 1, "AED 15/Quarter", 2m, "AED", 2, 1, 5 },
                    { 22, 30m, "AED", 3m, "AED", 1, "AED 30 (Semi-Annual)", 2m, "AED", 3, 1, 5 },
                    { 23, 30m, "AED", 6m, "AED", 2, "AED 30/Quarter", 20m, "AED", 2, 1, 5 },
                    { 24, 60m, "AED", 6m, "AED", 2, "AED 60 (Semi-Annual)", 20m, "AED", 3, 1, 5 }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b13b85df-fc93-4370-b748-7e4f25eef1f1"));

            migrationBuilder.DeleteData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b24bdea9-82e5-4482-93f3-03a8f98fa952"));

            migrationBuilder.DeleteData(
                table: "SubscriptionFees",
                keyColumn: "Id",
                keyValue: new Guid("5a41404e-6dec-45e2-94c4-0057a085899d"));

            migrationBuilder.DeleteData(
                table: "SubscriptionFees",
                keyColumn: "Id",
                keyValue: new Guid("648ebeef-497e-4ee5-8438-d3fe5b64054a"));

            migrationBuilder.DeleteData(
                table: "SubscriptionFees",
                keyColumn: "Id",
                keyValue: new Guid("f05df535-6972-4c09-b439-e12547338c61"));

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 18);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 19);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 20);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 21);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 22);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 23);

            migrationBuilder.DeleteData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 24);

            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 1,
                column: "Code",
                value: "Default");
        }
    }
}
