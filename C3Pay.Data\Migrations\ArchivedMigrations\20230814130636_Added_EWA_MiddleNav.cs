﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_EWA_MiddleNav : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 5,
                column: "DisplayOrder",
                value: 6);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 6,
                column: "DisplayOrder",
                value: 7);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 7,
                column: "DisplayOrder",
                value: 8);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 8,
                column: "DisplayOrder",
                value: 9);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 9,
                column: "DisplayOrder",
                value: 7);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 10,
                column: "DisplayOrder",
                value: 9);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 11,
                column: "DisplayOrder",
                value: 5);

            migrationBuilder.InsertData(
                table: "DashboardElements",
                columns: new[] { "Id", "CreatedDate", "DeepLinkUrl", "DeletedBy", "DeletedDate", "DisplayOrder", "IsDeleted", "SectionId", "Type", "UpdatedDate" },
                values: new object[] { 12, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), "/ewa", null, null, 4, false, 1, 1, null });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[] { "da_qck_act_12", "en", 99, "Get Salary", "da_qck_act" });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElements",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "ElementId", "IconUrl", "IsActive", "IsDeleted", "TextContentCode", "UpdatedDate" },
                values: new object[,]
                {
                    { 78, null, new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 79, "IN", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 80, "PK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 81, "LK", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 82, "PH", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 83, "BD", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null },
                    { 84, "NP", new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 12, "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_12.png", true, false, "da_qck_act_12", null }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 678, "en", "Get Salary", "da_qck_act_12" },
                    { 679, "bn", "Get Salary", "da_qck_act_12" },
                    { 680, "hi", "Get Salary", "da_qck_act_12" },
                    { 681, "hi-en", "Get Salary", "da_qck_act_12" },
                    { 682, "ml", "Get Salary", "da_qck_act_12" },
                    { 683, "ta", "Get Salary", "da_qck_act_12" },
                    { 684, "ur-en", "Get Salary", "da_qck_act_12" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 78);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 79);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 80);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 81);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 82);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 83);

            migrationBuilder.DeleteData(
                table: "DashboardQuickActionElements",
                keyColumn: "Id",
                keyValue: 84);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 678);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 679);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 680);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 681);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 682);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 683);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 684);

            migrationBuilder.DeleteData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_12");

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 5,
                column: "DisplayOrder",
                value: 5);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 6,
                column: "DisplayOrder",
                value: 6);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 7,
                column: "DisplayOrder",
                value: 7);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 8,
                column: "DisplayOrder",
                value: 8);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 9,
                column: "DisplayOrder",
                value: 6);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 10,
                column: "DisplayOrder",
                value: 8);

            migrationBuilder.UpdateData(
                table: "DashboardElements",
                keyColumn: "Id",
                keyValue: 11,
                column: "DisplayOrder",
                value: 4);
        }
    }
}
