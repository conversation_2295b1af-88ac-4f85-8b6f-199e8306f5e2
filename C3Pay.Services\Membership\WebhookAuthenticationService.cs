﻿using C3Pay.Core.Common;
using C3Pay.Core.Services.C3Pay.Membership;
using Microsoft.Extensions.Options;
using System;
using System.Security.Cryptography;
using System.Text;

namespace C3Pay.Services.Membership
{
    public class WebhookAuthenticationService : IWebhookAuthenticationService
    {
        private readonly string _secretKey;

        public WebhookAuthenticationService(IOptionsSnapshot<InfobipVoiceCallSettings> infobipSettings)
        {
            _secretKey = infobipSettings.Value.CallbackSecret;
        }


        public string GenerateAuthToken()
        {
            var randomBytes = new byte[16];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(randomBytes);
            }
            var cryptoString = Convert.ToBase64String(randomBytes).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            var signature = GenerateSignature(cryptoString);
            return $"{cryptoString}:{signature}";
        }


        public bool ValidateAuthToken(string token)
        {
            var parts = token.Split(':');
            if (parts.Length != 2) return false;

            var tokenToValidate = parts[0];
            var signature = parts[1];

            var expectedSignature = GenerateSignature(tokenToValidate);
            return signature == expectedSignature;
        }


        private string GenerateSignature(string token)
        {
            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_secretKey)))
            {
                var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(token));
                return Convert.ToBase64String(hash).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            }
        }
    }
}
