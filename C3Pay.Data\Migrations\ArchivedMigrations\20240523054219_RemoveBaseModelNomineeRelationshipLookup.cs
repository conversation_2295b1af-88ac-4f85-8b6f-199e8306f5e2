﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class RemoveBaseModelNomineeRelationshipLookup : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "datetime2",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 1,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(138));

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 2,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(566));

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 3,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(584));

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 4,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(586));

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 5,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(587));

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 6,
                column: "CreatedDate",
                value: new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(588));
        }
    }
}
