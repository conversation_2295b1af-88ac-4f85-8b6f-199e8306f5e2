﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnEmploymentInsurance_MySalaryUpdateForVideo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Features",
                columns: new[] { "Id", "Name" },
                values: new object[] { 8, "UnEmpInsurance_MySalary" });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 42, 8, null, "hi", "IND", null, 1, "https://player.vimeo.com/progressive_redirect/playback/803787585/rendition/360p/file.mp4?loc=external&signature=bd613942410360fc89de7f427b757303f3b9046c2a145e5d87eb0cb3555fadec" });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 43, 8, null, "en", "PHL", null, 1, "https://player.vimeo.com/progressive_redirect/playback/803790226/rendition/360p/file.mp4?loc=external&signature=b65a91f6301811778c021b67d980010c1f8ad7a8d3ca5c204e87d27b72432cfc" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 42);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 43);

            migrationBuilder.DeleteData(
                table: "Features",
                keyColumn: "Id",
                keyValue: 8);
        }
    }
}
