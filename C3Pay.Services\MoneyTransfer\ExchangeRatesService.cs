﻿using C3Pay.Core.Models.C3Pay.MoneyTransfer.ExchangeRates;
using C3Pay.Core.Services.C3Pay.MoneyTransfer;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Services.MoneyTransfer
{
    public class ExchangeRatesService : IExchangeRatesService
    {
        public async Task<ServiceResponse<FxRateResponseRakModel>> GetExchangeRates(GetExchangeRatesOptions options)
        {
            if (options == null)
            {
                //return new ServiceResponse<FxRateResponseRakModel>();
            }

            //if (string.IsNullOrEmpty(toCurrency))
            //    return BadRequest(BaseEnums.TransferStatusValidationMessage.InvalidToCurrency.ToString());
            //else if (!string.IsNullOrEmpty(toCurrency) && toCurrency.ToUpper() == "AED")
            //    return BadRequest(BaseEnums.TransferStatusValidationMessage.InvalidToCurrency.ToString());
            //else if (amount <= 0)
            //    return BadRequest(BaseEnums.TransferStatusValidationMessage.AmountNotExists.ToString());
            //else if (!string.IsNullOrEmpty(transferMethod) && transferMethod.ToUpper() != BaseEnums.TransferMethod.BANKTRANSFER.ToString() && transferMethod.ToUpper() != BaseEnums.TransferMethod.CASHPICKUP.ToString())
            //    return BadRequest(BaseEnums.TransferStatusValidationMessage.TransferMethodNotExists.ToString());

            return new ServiceResponse<FxRateResponseRakModel>();
        }
    }
}
