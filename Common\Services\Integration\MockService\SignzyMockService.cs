﻿using Edenred.Common.Core;
using Edenred.Common.Core.Services.Integration;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Edenred.Common.Services.Integration.MockService
{
    public class SignzyMockService : IIdentificationMockService
    {
        readonly HttpClient _httpClient = new HttpClient();
        readonly string apiUrl = "https://eae-c3shared-mockapi-web-a.azurewebsites.net/api/PPSRAKMock";

        public SignzyMockService()
        {
            _httpClient.BaseAddress = new Uri(apiUrl);
        }
        public async Task<ServiceResponse<FaceMatchResultDto>> CheckFaceMatch(string selfieFileName, string frontScanFileName)
        {
            TimeDelay delayDto = new TimeDelay();
            delayDto.Delay = 1;
            var jsonBenificiary = JsonSerializer.Serialize(delayDto);

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(apiUrl),
                Content = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            var FaceMatchResultDto = new FaceMatchResultDto()
            {
                Verified = true,
                Message = "Succcess",
                MatchPercentage = "100",
                IsIdealMatchPercentage = true,
                ServiceReference = "123443",
                ServiceReferenceId = "1242342"
            };
            return new ServiceResponse<FaceMatchResultDto>(FaceMatchResultDto);
        }

        public async Task<ServiceResponse<bool>> CheckDocumentQuality(string fileName, string documentUrl, Enums.DocumentType documentType)
        {
            TimeDelay delayDto = new TimeDelay();
            delayDto.Delay = 1;
            var jsonBenificiary = JsonSerializer.Serialize(delayDto);

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(apiUrl),
                Content = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<object>> ReadDocument(string frontScanUrl, string backScanUrl, Enums.DocumentType documentType, string nameToMatch)
        {
            TimeDelay delayDto = new TimeDelay();
            delayDto.Delay = 1;
            var jsonBenificiary = JsonSerializer.Serialize(delayDto);

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(apiUrl),
                Content = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            return new ServiceResponse<object>(new EmiratesIdDto());
        }

        public async Task<ServiceResponse> SaveDocuments(List<IdentificationDocumentDto> documents)
        {
            TimeDelay delayDto = new TimeDelay();
            delayDto.Delay = 1;
            var jsonBenificiary = JsonSerializer.Serialize(delayDto);

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(apiUrl),
                Content = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            return new ServiceResponse();
        }

        public async Task<ServiceResponse<string>> UploadDocument(string content, string documentName, Enums.IdentificationDocumentMimeType documentMimeType)
        {
            TimeDelay delayDto = new TimeDelay();
            delayDto.Delay = 1;
            var jsonBenificiary = JsonSerializer.Serialize(delayDto);

            var request = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = new Uri(apiUrl),
                Content = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json")
            };

            var response = await _httpClient.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();
            return new ServiceResponse<string>("success");
        }
    }
}
