﻿using C3Pay.API.Models;
using C3Pay.API.Resources;
using C3Pay.Core;
using C3Pay.Core.Models;
using Edenred.Common.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.API.Controllers.QA
{
    [Route("api/[controller]")]
    [ApiController]
    [ApiExplorerSettings(IgnoreApi = false)]
    [Authorize(AuthenticationSchemes = AuthenticationScheme.AzureActiveDirectory, Policy = "ApplicationIdEntry")]
    public class QAAutomationController : ControllerBase
    {
        private readonly IIdentityService _identityService;
        private readonly GeneralSettings _generalSettings;
        private readonly List<string> automationPhoneNumbers;
        private readonly string _tempAccountPrefix = "Temp_";
        private readonly string[] _phoneNumberOTPReasons = new string[]
        {
            OTPVerificationReason.ChangePhoneNumber.ToString(),
            OTPVerificationReason.ForgotPhoneNumber.ToString()
        };

        public QAAutomationController(IIdentityService identityService, IOptions<GeneralSettings> generalSettings)
        {
            this._generalSettings = generalSettings.Value;
            this._identityService = identityService;
            this.automationPhoneNumbers = _generalSettings.QAAutomationPhoneNumbers.Split(";").ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> GetOneTimePassword(GetOTPRequestDto request)
        {
            if (!Enum.IsDefined(typeof(OTPVerificationReason), request.Reason))
            {
                return this.BadRequest(ConstantParam.InvalidOneTimePasswordReason);
            }

            var username = request.PhoneNumber.ToZeroPrefixedPhoneNumber();

            var phoneNumber = username;

            if (!automationPhoneNumbers.Contains(username))
            {
                return this.Unauthorized();
            }

            if (_phoneNumberOTPReasons.Contains(request.Reason))
            {
                username = string.Concat(_tempAccountPrefix, username);

                await this._identityService.DeleteUserAccountAsync(username);
            }

            var userAccountExistsResult = await this._identityService.UserAccountExistsAsync(username);

            if (!userAccountExistsResult.IsSuccessful)
            {
                return this.Ok();
            }

            var userExists = userAccountExistsResult.Data;

            if (!userExists)
            {
                if (request.Reason != BaseEnums.OTPVerificationReason.SignUp.ToString() && !_phoneNumberOTPReasons.Contains(request.Reason))
                {
                    return this.NotFound(string.Format(ConstantParam.UserIdentityNotFound, username));
                }

                var generatePasswordResult = await this._identityService.GeneratePasswordAsync();

                var password = generatePasswordResult.Data;

                await this._identityService.CreateUserAccountAsync(username, password, phoneNumber, null);
            }

            var oneTimePasswordResult = await this._identityService.RequestPhoneVerificationAsync(username);

            if (!oneTimePasswordResult.IsSuccessful)
            {
                return this.BadRequest(oneTimePasswordResult.ErrorMessage);
            }

            var oneTimePassword = oneTimePasswordResult.Data;

            return this.Ok(new GetOTPResponseDto() { OneTimePassword = oneTimePassword });
        }
    }
}
