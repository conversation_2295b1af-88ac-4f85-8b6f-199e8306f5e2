﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class deleteRmtKycRefinementsModel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RmtKycRefinements");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RmtKycRefinements",
                columns: table => new
                {
                    RmtId = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CardHolderId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EmiratesId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    ProcessedDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Remarks = table.Column<string>(type: "varchar(500)", maxLength: 500, nullable: true),
                    RequiredAction = table.Column<string>(type: "varchar(20)", maxLength: 20, nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RmtKycRefinements", x => x.RmtId);
                    table.ForeignKey(
                        name: "FK_RmtKycRefinements_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RmtKycRefinements_CardHolderId",
                table: "RmtKycRefinements",
                column: "CardHolderId");

            migrationBuilder.CreateIndex(
                name: "IX_RmtKycRefinements_ProcessedDateTime",
                table: "RmtKycRefinements",
                column: "ProcessedDateTime");

            migrationBuilder.CreateIndex(
                name: "IX_RmtKycRefinements_UserId",
                table: "RmtKycRefinements",
                column: "UserId");
        }
    }
}
