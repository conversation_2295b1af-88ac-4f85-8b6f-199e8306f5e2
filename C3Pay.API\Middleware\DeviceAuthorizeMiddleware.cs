﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using C3Pay.Services.Helper;
using DocumentFormat.OpenXml.Office2013.Drawing.ChartStyle;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Serilog;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace C3Pay.API
{
    public class DeviceAuthorizeMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly GeneralSettings _generalSettings;
        private readonly IFeatureManager _featureManager;
        private readonly ILogger<DeviceAuthorizeMiddleware> _logger;

        private static readonly Lazy<TelemetryClient> _telemetryClient = new Lazy<TelemetryClient>(() => new TelemetryClient());
        private static TelemetryClient TelemetryClient => _telemetryClient.Value;

        public DeviceAuthorizeMiddleware(RequestDelegate next, IOptions<GeneralSettings> generalSettings, IFeatureManager featureManager, ILogger<DeviceAuthorizeMiddleware> logger)
        {
            _next = next;
            _generalSettings = generalSettings.Value;
            _featureManager = featureManager;
            _logger = logger;
        }

        public async Task Invoke(HttpContext context)
        {
            var mfaDeviceAuthorizeEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MFADeviceAuthorize);
            if (mfaDeviceAuthorizeEnabled)
            {
                var userName = context.User.FindFirst(ConstantParam.Username)?.Value;
                var mfaDeviceAuthorizeTelemetryEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MFADeviceAuthorizeTelemetry);
                if (mfaDeviceAuthorizeTelemetryEnabled)
                {
                    using (var operation = TelemetryClient.StartOperation(new DependencyTelemetry("DeviceAuthorize", "ValidateDeviceIdentifier", "DeviceAuthorize", string.IsNullOrEmpty(userName) ? "No UserName" : userName)))
                    {
                        bool isValid = await ValidateDeviceIdentifier(context);
                        if (!isValid)
                        {
                            return;
                        }
                    }
                }
                else
                {
                    bool isValid = await ValidateDeviceIdentifier(context);
                    if (!isValid)
                    {
                        return;
                    }
                }
            }

            await _next(context);
        }

        private async Task<bool> ValidateDeviceIdentifier(HttpContext context)
        {
            var endpoint = context.GetEndpoint();
            var hasCustomAuthorizationAttribute = endpoint?.Metadata.GetMetadata<DeviceAuthorizeAttribute>() != null;
            if (hasCustomAuthorizationAttribute)
            {
                var userName = context.User.FindFirst(ConstantParam.Username)?.Value;

                bool tokenValidationEnabled = ValidateMFATokenValidation(userName);

                if (tokenValidationEnabled)
                {
                    if (!context.Request.Headers.ContainsKey("X-Identifier"))
                    {
                        _logger.LogError($"DeviceAuthorize: {userName} is unauthorized.");
                        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        return false;
                    }
                    if (string.IsNullOrEmpty(userName))
                    {
                        _logger.LogError($"DeviceAuthorize: {userName} is unauthorized.");
                        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        context.Response.ContentType = "application/json";
                    }
                    var _userService = context.RequestServices.GetService(typeof(IUserService)) as IUserService;
                    var isValidToken = await _userService.ValidateDeviceToken(userName, context.Request.Headers["X-Identifier"]);
                    if (!isValidToken)
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
                        context.Response.ContentType = "application/json";
                        _logger.LogError($"{userName} is unauthorized.");
                        return false;
                    }
                }
            }

            return true;
        }

        private bool ValidateMFATokenValidation(string userName)
        {
            bool validationEnabled = true;

            var _uatPentestPhoneNumbers = _generalSettings.UATPentestPhoneNumbers.Split(";").ToList();

            if (_uatPentestPhoneNumbers.Count() > 0)
            {
                if (_uatPentestPhoneNumbers.Contains(userName))
                {
                    validationEnabled = true;
                }
                else
                {
                    validationEnabled = false;
                }
            }
            return validationEnabled;
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class DeviceAuthorizeAttribute : Attribute
    {
    }
}
