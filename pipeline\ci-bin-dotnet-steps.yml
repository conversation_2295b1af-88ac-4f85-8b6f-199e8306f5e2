parameters:
  - name: apiProjectPath
    type: string
  - name: nugetConfigPath
    type: string
  - name: portalApiProjectPath
    type: string
  - name: webJobProjectPath
    type: string  
  - name: buildConfiguration
    type: string 
  - name: apiOutputFolder
    type: string 
  - name: outputPortalApiFolder
    type: string 
  - name: webJobOutputFolder
    type: string 
  - name: WebJobAppFolder
    type: string 
steps:
  - script: dotnet clean . --configuration ${{ parameters.buildConfiguration}}
    displayName: dotnet clean
  
  - task: UseDotNet@2 
    displayName: ".NET Core 3.1.x"
    inputs:
      version: 8.x
      packageType: sdk

  - task: DotNetCoreCLI@2
    displayName: dotnet restore
    inputs:
      command: restore
      projects: '**/*.csproj'
      feedsToUse: config
      nugetConfigPath: 'NuGet/NuGet.config'
        
  - task: DotNetCoreCLI@2
    displayName: 'Run tests'
    inputs:
      command: 'test'
      arguments: '--logger "trx;LogFileName=testresults.trx"'
      projects: '**/*UnitTest.csproj'

  - task: DotNetCoreCLI@2
    displayName: 'Install dotnet-reportgenerator-globaltool'
    inputs:
      command: custom
      custom: tool
      arguments: 'install --tool-path . dotnet-reportgenerator-globaltool --version 5.1.26'

  # - script: ./reportgenerator -reports:$(Build.SourcesDirectory)/**/coverage.cobertura.xml -targetDir:$(Build.SourcesDirectory)/CodeCoverage -reporttypes:HtmlInline_AzurePipelines
  #   displayName: 'Create Code Coverage Reports'

  # - task: PublishCodeCoverageResults@1
  #   displayName: 'Publish Code Coverage'
  #   inputs:
  #     codeCoverageTool: Cobertura
  #     summaryFileLocation: '$(Build.SourcesDirectory)/**/coverage.cobertura.xml'      
  #     failIfCoverageEmpty: false 
  
  - script: dotnet build . --no-restore --configuration ${{ parameters.buildConfiguration}}
    displayName: Build Code

  - task: DotNetCoreCLI@2
    displayName: 'Create package ${{ parameters.apiProjectPath }}'
    inputs:
      command: publish
      arguments: ' --no-build --configuration ${{ parameters.buildConfiguration}} --output "${{ parameters.apiOutputFolder}}"'
      publishWebProjects: false
      zipAfterPublish: true
      modifyOutputPath: false
      projects: '${{ parameters.apiProjectPath }}' 

  - task: DotNetCoreCLI@2
    displayName: 'Create package ${{ parameters.portalApiProjectPath }}'
    inputs:
      command: publish
      arguments: ' --no-build --configuration ${{ parameters.buildConfiguration}} --output "${{ parameters.outputPortalApiFolder}}"'
      publishWebProjects: false
      zipAfterPublish: true
      modifyOutputPath: false
      projects: '${{ parameters.portalApiProjectPath }}' 
  
  - task: DotNetCoreCLI@2
    displayName: 'Create package ${{ parameters.webJobProjectPath }}'
    inputs:
      command: publish
      arguments: ' --no-build --configuration ${{ parameters.buildConfiguration}} --output "${{ parameters.webJobOutputFolder}}${{ parameters.WebJobAppFolder}}"'
      publishWebProjects: false
      zipAfterPublish: false
      modifyOutputPath: false
      projects: '${{ parameters.webJobProjectPath }}'
  
  - task: PowerShell@2
    displayName: Create run.cmd
    inputs:
      targetType: 'inline'
      script: '"dotnet C3Pay.WebJob.dll" | Out-File -FilePath run.cmd -Encoding ASCII; $LASTEXITCODE'
      workingDirectory: '${{ parameters.webJobOutputFolder}}${{ parameters.WebJobAppFolder}}' 