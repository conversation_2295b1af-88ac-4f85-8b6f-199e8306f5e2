﻿using C3Pay.Core;
using C3Pay.Core.Common;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using System;
using System.IO;
using System.Threading.Tasks;

namespace C3Pay.API.Middleware
{
    /// <summary>
    /// Response Verification Middleware
    /// </summary>
    public class ResponseVerificationMiddleware
    {
        private readonly RequestDelegate _next; 
        private readonly EncryptionSettings encryptionSettings;
        private readonly IFeatureManager _featureManager;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="next"></param>
        /// <param name="_configuration"></param>
        public ResponseVerificationMiddleware(RequestDelegate next, IOptions<EncryptionSettings> _encryptionSettings, IFeatureManager featureManager)
        {
            _next = next;
            encryptionSettings = _encryptionSettings.Value;
            _featureManager = featureManager;
        }

        /// <summary>
        /// Invoke
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task Invoke(HttpContext context)
        {
            var requestSignature = context.Request.Headers["X-Signature"];
            var mfaDigitalSignatureEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MFADigitalSignature);

            if (!string.IsNullOrEmpty(requestSignature) 
                && encryptionSettings.IsActive 
                && mfaDigitalSignatureEnabled)
            {
                var originalResponseBody = context.Response.Body;
                using (var responseBody = new MemoryStream())
                {
                    context.Response.Body = responseBody;
                    await _next(context);
                    responseBody.Seek(0, SeekOrigin.Begin);
                    var responseBodyString = await new StreamReader(responseBody).ReadToEndAsync();
                    var responseSignature = DigitalSignatureHelper.CalculateDigitalSignature(responseBodyString, encryptionSettings.PrivateKey);
                    context.Response.Headers.Add("X-Signature", responseSignature);
                    context.Response.Body = originalResponseBody;
                    responseBody.Seek(0, SeekOrigin.Begin);
                    await responseBody.CopyToAsync(originalResponseBody); 
                }
            }
            else
                await _next(context);
        } 
    }
}
