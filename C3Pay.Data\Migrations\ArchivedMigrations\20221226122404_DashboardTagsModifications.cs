﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class DashboardTagsModifications : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 623,
                column: "Text",
                value: "কোন চার্জ নেই");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 624,
                column: "Text",
                value: "कोई चार्जेस नहीं");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 626,
                column: "Text",
                value: "നിരക്കുകളൊന്നുമില്ല");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 627,
                column: "Text",
                value: "கட்டணங்கள் இல்லை");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 630,
                column: "Text",
                value: "AED5 সে");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 631,
                column: "Text",
                value: "AED 5 से");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 632,
                column: "Text",
                value: "AED5 se");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 633,
                column: "Text",
                value: "AED5 മുതൽ");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 634,
                column: "Text",
                value: "AED5 இல் இலிருந்து");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 637,
                column: "Text",
                value: "2.5% পান");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 638,
                column: "Text",
                value: "पाएं 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 639,
                column: "Text",
                value: "Paaye 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 640,
                column: "Text",
                value: "2.5% നേടുക");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 641,
                column: "Text",
                value: "2.5% பெறுங்கள்");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 644,
                column: "Text",
                value: "0 ছাড়");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 645,
                column: "Text",
                value: "0  कटौती");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 647,
                column: "Text",
                value: "0 കിഴിവ്");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 648,
                column: "Text",
                value: "0  டிடக்ஷான்");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 651,
                column: "Text",
                value: "মাত্র AED10");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 652,
                column: "Text",
                value: "सिर्फ AED10");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 653,
                column: "Text",
                value: "Sirf AED10");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 654,
                column: "Text",
                value: "AED10 മാത്രം");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 655,
                column: "Text",
                value: "AED10 மட்டுமே");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 623,
                column: "Text",
                value: "No Charges");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 624,
                column: "Text",
                value: "No Charges");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 626,
                column: "Text",
                value: "No Charges");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 627,
                column: "Text",
                value: "No Charges");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 630,
                column: "Text",
                value: "From AED 5");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 631,
                column: "Text",
                value: "From AED 5");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 632,
                column: "Text",
                value: "From AED 5");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 633,
                column: "Text",
                value: "From AED 5");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 634,
                column: "Text",
                value: "From AED 5");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 637,
                column: "Text",
                value: "Get 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 638,
                column: "Text",
                value: "Get 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 639,
                column: "Text",
                value: "Get 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 640,
                column: "Text",
                value: "Get 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 641,
                column: "Text",
                value: "Get 2.5%");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 644,
                column: "Text",
                value: "0 Deduction");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 645,
                column: "Text",
                value: "0 Deduction");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 647,
                column: "Text",
                value: "0 Deduction");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 648,
                column: "Text",
                value: "0 Deduction");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 651,
                column: "Text",
                value: "AED 10 Only");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 652,
                column: "Text",
                value: "AED 10 Only");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 653,
                column: "Text",
                value: "AED 10 Only");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 654,
                column: "Text",
                value: "AED 10 Only");

            migrationBuilder.UpdateData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 655,
                column: "Text",
                value: "AED 10 Only");
        }
    }
}
