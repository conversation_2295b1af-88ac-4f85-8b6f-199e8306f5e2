﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateExperiments : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ExperimentMode",
                table: "Experiments",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "ExperimentGroupMultimedias",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "ExperimentGroupMultimedias",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "ExperimentGroupMultimedias",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "ExperimentGroupMultimedias",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "NationalityCode",
                table: "ExperimentGroupMultimedias",
                type: "varchar(3)",
                maxLength: 3,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "ExperimentGroupMultimedias",
                type: "datetime2",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "Experiments",
                keyColumn: "Id",
                keyValue: 1,
                column: "ExperimentMode",
                value: 1);

            migrationBuilder.UpdateData(
                table: "Experiments",
                keyColumn: "Id",
                keyValue: 2,
                column: "ExperimentMode",
                value: 1);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExperimentMode",
                table: "Experiments");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "ExperimentGroupMultimedias");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "ExperimentGroupMultimedias");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "ExperimentGroupMultimedias");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "ExperimentGroupMultimedias");

            migrationBuilder.DropColumn(
                name: "NationalityCode",
                table: "ExperimentGroupMultimedias");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "ExperimentGroupMultimedias");
        }
    }
}
