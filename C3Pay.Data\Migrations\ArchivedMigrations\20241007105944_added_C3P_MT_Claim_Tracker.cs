﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class added_C3P_MT_Claim_Tracker : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipFreeMoneyTransferClaims",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MembershipUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MembershipStartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MembershipEndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MoneyTransferReferenceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RefundReferenceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RefundAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    ClaimedTriggeredFrom = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipFreeMoneyTransferClaims", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipFreeMoneyTransferClaims_MembershipEndDate",
                table: "C3PayPlusMembershipFreeMoneyTransferClaims",
                column: "MembershipEndDate");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipFreeMoneyTransferClaims_MembershipStartDate",
                table: "C3PayPlusMembershipFreeMoneyTransferClaims",
                column: "MembershipStartDate");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipFreeMoneyTransferClaims_UserId",
                table: "C3PayPlusMembershipFreeMoneyTransferClaims",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipFreeMoneyTransferClaims");
        }
    }
}
