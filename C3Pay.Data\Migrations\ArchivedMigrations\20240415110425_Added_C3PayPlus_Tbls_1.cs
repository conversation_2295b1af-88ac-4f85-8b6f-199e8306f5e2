﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_C3PayPlus_Tbls_1 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMemberships",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IsAvailable = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    C3PayPlusTypeId = table.Column<int>(type: "int", nullable: false),
                    PriceVatInclusive = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMemberships", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipBenefits",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BenefitId = table.Column<int>(type: "int", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Subtitle = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Price = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    C3PayPlusMembershipId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipBenefits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipBenefits_C3PayPlusMemberships_C3PayPlusMembershipId",
                        column: x => x.C3PayPlusMembershipId,
                        principalTable: "C3PayPlusMemberships",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipUsers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    C3PayPlusMembershipId = table.Column<int>(type: "int", nullable: false),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipUsers_C3PayPlusMemberships_C3PayPlusMembershipId",
                        column: x => x.C3PayPlusMembershipId,
                        principalTable: "C3PayPlusMemberships",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipVideos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Url = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    LanguageCode = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    C3PayPlusMembershipId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipVideos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipVideos_C3PayPlusMemberships_C3PayPlusMembershipId",
                        column: x => x.C3PayPlusMembershipId,
                        principalTable: "C3PayPlusMemberships",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipBenefits_C3PayPlusMembershipId",
                table: "C3PayPlusMembershipBenefits",
                column: "C3PayPlusMembershipId");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipUsers_C3PayPlusMembershipId",
                table: "C3PayPlusMembershipUsers",
                column: "C3PayPlusMembershipId");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipUsers_UserId",
                table: "C3PayPlusMembershipUsers",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipVideos_C3PayPlusMembershipId",
                table: "C3PayPlusMembershipVideos",
                column: "C3PayPlusMembershipId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipBenefits");

            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipUsers");

            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipVideos");

            migrationBuilder.DropTable(
                name: "C3PayPlusMemberships");
        }
    }
}
