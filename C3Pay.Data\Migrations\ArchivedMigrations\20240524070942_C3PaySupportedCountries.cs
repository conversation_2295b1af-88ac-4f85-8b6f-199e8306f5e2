﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class C3PaySupportedCountries : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipSupportedCountries",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Code = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    Code3 = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    IsdCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LongName = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    IsPopular = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipSupportedCountries", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 1, "AD", "AND", 20, false, "376", "Principality of Andorra", "Andorra" },
                    { 156, "MX", "MEX", 20, false, "52", "United Mexican States", "Mexico" },
                    { 157, "MY", "MYS", 20, false, "60", "Malaysia", "Malaysia" },
                    { 158, "MZ", "MOZ", 20, false, "258", "Republic of Mozambique", "Mozambique" },
                    { 159, "NA", "NAM", 20, false, "264", "Republic of Namibia", "Namibia" },
                    { 160, "NC", "NCL", 20, false, "687", "New Caledonia", "New Caledonia" },
                    { 161, "NE", "NER", 20, false, "227", "Niger (the)", "Niger" },
                    { 162, "NF", "NFK", 20, false, "672", "Norfolk Island", "Norfolk Island" },
                    { 163, "NG", "NGA", 20, false, "234", "Federal Republic of Nigeria", "Nigeria" },
                    { 164, "NI", "NIC", 20, false, "505", "Republic of Nicaragua", "Nicaragua" },
                    { 165, "NL", "NLD", 20, false, "31", "Netherlands (the)", "Netherlands" },
                    { 166, "NO", "NOR", 20, false, "47", "Kingdom of Norway", "Norway" },
                    { 167, "NP", "NPL", 4, true, "977", "Federal Democratic Republic of Nepal", "Nepal" },
                    { 155, "MW", "MWI", 20, false, "265", "Republic of Malawi", "Malawi" },
                    { 168, "NR", "NRU", 20, false, "674", "Republic of Nauru", "Nauru" },
                    { 170, "OM", "OMN", 20, false, "968", "Sultanate of Oman", "Oman" },
                    { 171, "PA", "PAN", 20, false, "507", "Republic of Panama", "Panama" },
                    { 172, "PE", "PER", 20, false, "51", "Republic of Peru", "Peru" },
                    { 173, "PF", "PYF", 20, false, "689", "French Polynesia", "French Polynesia" },
                    { 174, "PG", "PNG", 20, false, "675", "Independent State of Papua New Guinea", "Papua New Guinea" },
                    { 175, "PH", "PHL", 5, true, "63", "Philippines (the)", "Philippines" },
                    { 176, "PK", "PAK", 2, true, "92", "Islamic Republic of Pakistan", "Pakistan" },
                    { 177, "PL", "POL", 20, false, "48", "Republic of Poland", "Poland" },
                    { 178, "PM", "SPM", 20, false, "508", "Saint Pierre and Miquelon", "Saint Pierre and Miquelon" },
                    { 179, "PN", "PCN", 20, false, "NONE", "Pitcairn", "Pitcairn" },
                    { 180, "PR", "PRI", 20, false, "1+939", "Commonwealth of Puerto Rico", "Puerto Rico" },
                    { 181, "PT", "PRT", 20, false, "351", "Portuguese Republic", "Portugal" },
                    { 169, "NZ", "NZL", 20, false, "64", "New Zealand", "New Zealand" },
                    { 154, "MV", "MDV", 20, false, "960", "Republic of Maldives", "Maldives" },
                    { 153, "MU", "MUS", 20, false, "230", "Republic of Mauritius", "Mauritius" },
                    { 152, "MT", "MLT", 20, false, "356", "Republic of Malta", "Malta" },
                    { 125, "LA", "LAO", 20, false, "856", "Lao Peoples Democratic Republic", "Laos" },
                    { 126, "LB", "LBN", 20, false, "961", "Republic of Lebanon", "Lebanon" },
                    { 127, "LC", "LCA", 20, false, "1+758", "Saint Lucia", "Saint Lucia" },
                    { 128, "LI", "LIE", 20, false, "423", "Principality of Liechtenstein", "Liechtenstein" },
                    { 129, "LK", "LKA", 6, true, "94", "Democratic Socialist Republic of Sri Lanka", "Sri Lanka" },
                    { 130, "LR", "LBR", 20, false, "231", "Republic of Liberia", "Liberia" },
                    { 131, "LS", "LSO", 20, false, "266", "Kingdom of Lesotho", "Lesotho" },
                    { 132, "LT", "LTU", 20, false, "370", "Republic of Lithuania", "Lithuania" },
                    { 133, "LU", "LUX", 20, false, "352", "Grand Duchy of Luxembourg", "Luxembourg" },
                    { 134, "LV", "LVA", 20, false, "371", "Republic of Latvia", "Latvia" },
                    { 135, "LY", "LBY", 20, false, "218", "Libya", "Libya" }
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 136, "MA", "MAR", 20, false, "212", "Kingdom of Morocco", "Morocco" },
                    { 137, "MC", "MCO", 20, false, "377", "Principality of Monaco", "Monaco" },
                    { 138, "MD", "MDA", 20, false, "373", "Republic of Moldova", "Moldava" },
                    { 139, "ME", "MNE", 20, false, "382", "Montenegro", "Montenegro" },
                    { 140, "MF", "MAF", 20, false, "590", "Saint Martin", "Saint Martin" },
                    { 141, "MG", "MDG", 20, false, "261", "Republic of Madagascar", "Madagascar" },
                    { 142, "MH", "MHL", 20, false, "692", "Marshall Islands (the)", "Marshall Islands" },
                    { 143, "MK", "MKD", 20, false, "389", "The Former Yuslav Republic of Macedonia", "Macedonia" },
                    { 144, "ML", "MLI", 20, false, "223", "Republic of Mali", "Mali" },
                    { 145, "MM", "MMR", 20, false, "95", "Republic of the Union of Myanmar", "Myanmar (Burma)" },
                    { 146, "MN", "MNG", 20, false, "976", "Monlia", "Monlia" },
                    { 147, "MO", "MAC", 20, false, "853", "The Macao Special Administrative Region", "Macao" },
                    { 148, "MP", "MNP", 20, false, "1+670", "Northern Mariana Islands (the)", "Northern Mariana Islands" },
                    { 149, "MQ", "MTQ", 20, false, "596", "Martinique", "Martinique" },
                    { 150, "MR", "MRT", 20, false, "222", "Islamic Republic of Mauritania", "Mauritania" },
                    { 151, "MS", "MSR", 20, false, "1+664", "Montserrat", "Montserrat" },
                    { 182, "PW", "PLW", 20, false, "680", "Republic of Palau", "Palau" },
                    { 183, "PY", "PRY", 20, false, "595", "Republic of Paraguay", "Paraguay" },
                    { 184, "QA", "QAT", 20, false, "974", "State of Qatar", "Qatar" },
                    { 185, "RE", "REU", 20, false, "262", "R&eacute;union", "Reunion" },
                    { 217, "TK", "TKL", 20, false, "690", "Tokelau", "Tokelau" },
                    { 218, "TL", "TLS", 20, false, "670", "Democratic Republic of Timor-Leste", "Timor Leste (East Timor)" },
                    { 219, "TM", "TKM", 20, false, "993", "Turkmenistan", "Turkmenistan" },
                    { 220, "TN", "TUN", 20, false, "216", "Republic of Tunisia", "Tunisia" },
                    { 221, "TO", "TON", 20, false, "676", "Kingdom of Tonga", "Tonga" },
                    { 222, "TR", "TUR", 20, false, "90", "Republic of Turkey", "Turkey" },
                    { 223, "TT", "TTO", 20, false, "1+868", "Republic of Trinidad and Toba", "Trinidad and Toba" },
                    { 224, "TV", "TUV", 20, false, "688", "Tuvalu", "Tuvalu" },
                    { 225, "TZ", "TZA", 20, false, "255", "United Republic of Tanzania", "Tanzania" },
                    { 226, "UA", "UKR", 20, false, "380", "Ukraine", "Ukraine" },
                    { 227, "UG", "UGA", 20, false, "256", "Republic of Uganda", "Uganda" },
                    { 228, "UM", "UMI", 20, false, "NONE", "United States Minor Outlying Islands (the)", "United States Minor Outlying Islands" },
                    { 229, "US", "USA", 20, false, "1", "United States of America (the)", "United States" },
                    { 230, "UY", "URY", 20, false, "598", "Eastern Republic of Uruguay", "Uruguay" },
                    { 231, "UZ", "UZB", 20, false, "998", "Republic of Uzbekistan", "Uzbekistan" },
                    { 232, "VA", "VAT", 20, false, "39", "Holy See (the)", "Vatican City" },
                    { 233, "VC", "VCT", 20, false, "1+784", "Saint Vincent and the Grenadines", "Saint Vincent and the Grenadines" },
                    { 234, "VE", "VEN", 20, false, "58", "Bolivarian Republic of Venezuela", "Venezuela" },
                    { 235, "VG", "VGB", 20, false, "1+284", "British Virgin Islands", "Virgin Islands, British" },
                    { 236, "VI", "VIR", 20, false, "1+340", "Virgin Islands of the United States", "Virgin Islands, US" },
                    { 237, "VN", "VNM", 20, false, "84", "Socialist Republic of Vietnam", "Vietnam" },
                    { 238, "VU", "VUT", 20, false, "678", "Republic of Vanuatu", "Vanuatu" }
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 239, "WF", "WLF", 20, false, "681", "Wallis and Futuna", "Wallis and Futuna" },
                    { 240, "WS", "WSM", 20, false, "685", "Independent State of Samoa", "Samoa" },
                    { 241, "YE", "YEM", 20, false, "967", "Republic of Yemen", "Yemen" },
                    { 242, "YT", "MYT", 20, false, "262", "Mayotte", "Mayotte" },
                    { 243, "ZA", "ZAF", 20, false, "27", "Republic of South Africa", "South Africa" },
                    { 216, "TJ", "TJK", 20, false, "992", "Republic of Tajikistan", "Tajikistan" },
                    { 124, "KZ", "KAZ", 20, false, "7", "Republic of Kazakhstan", "Kazakhstan" },
                    { 215, "TH", "THA", 20, false, "66", "Kingdom of Thailand", "Thailand" },
                    { 213, "TF", "ATF", 20, false, "262", "French Southern Territories (the)", "French Southern Territories" },
                    { 186, "RO", "ROU", 20, false, "40", "Romania", "Romania" },
                    { 187, "RS", "SRB", 20, false, "381", "Republic of Serbia", "Serbia" },
                    { 188, "RU", "RUS", 20, false, "7", "Russian Federation (the)", "Russia" },
                    { 189, "RW", "RWA", 20, false, "250", "Republic of Rwanda", "Rwanda" },
                    { 190, "SA", "SAU", 20, false, "966", "Kingdom of Saudi Arabia", "Saudi Arabia" },
                    { 191, "SB", "SLB", 20, false, "677", "Solomon Islands", "Solomon Islands" },
                    { 192, "SC", "SYC", 20, false, "248", "Republic of Seychelles", "Seychelles" },
                    { 193, "SD", "SDN", 20, false, "249", "Sudan (the)", "Sudan" },
                    { 194, "SE", "SWE", 20, false, "46", "Kingdom of Sweden", "Sweden" },
                    { 195, "SG", "SGP", 20, false, "65", "Republic of Singapore", "Singapore" },
                    { 196, "SH", "SHN", 20, false, "290", "Saint Helena, Ascension and Tristan da Cunha", "Saint Helena" },
                    { 197, "SI", "SVN", 20, false, "386", "Republic of Slovenia", "Slovenia" },
                    { 198, "SJ", "SJM", 20, false, "47", "Svalbard and Jan Mayen", "Svalbard and Jan Mayen" },
                    { 199, "SK", "SVK", 20, false, "421", "Slovak Republic", "Slovakia" },
                    { 200, "SL", "SLE", 20, false, "232", "Republic of Sierra Leone", "Sierra Leone" },
                    { 201, "SM", "SMR", 20, false, "378", "Republic of San Marino", "San Marino" },
                    { 202, "SN", "SEN", 20, false, "221", "Republic of Senegal", "Senegal" },
                    { 203, "SO", "SOM", 20, false, "252", "Somali Republic", "Somalia" },
                    { 204, "SR", "SUR", 20, false, "597", "Republic of Suriname", "Suriname" },
                    { 205, "SS", "SSD", 20, false, "211", "Republic of South Sudan", "South Sudan" },
                    { 206, "ST", "STP", 20, false, "239", "Democratic Republic of S&atilde;o Tom&eacute; and Pr&iacute;ncipe", "Sao Tome and Principe" },
                    { 207, "SV", "SLV", 20, false, "503", "Republic of El Salvador", "El Salvador" },
                    { 208, "SX", "SXM", 20, false, "1+721", "Sint Maarten", "Sint Maarten" },
                    { 209, "SY", "SYR", 20, false, "963", "Syrian Arab Republic", "Syria" },
                    { 210, "SZ", "SWZ", 20, false, "268", "Kingdom of Swaziland", "Swaziland" },
                    { 211, "TC", "TCA", 20, false, "1+649", "Turks and Caicos Islands (the)", "Turks and Caicos Islands" },
                    { 212, "TD", "TCD", 20, false, "235", "Republic of Chad", "Chad" },
                    { 214, "TG", "TGO", 20, false, "228", "Togolese Republic", "Togo" },
                    { 244, "ZM", "ZMB", 20, false, "260", "Republic of Zambia", "Zambia" },
                    { 123, "KY", "CYM", 20, false, "1+345", "Cayman Islands (the)", "Cayman Islands" },
                    { 121, "KR", "KOR", 20, false, "82", "Republic of Korea", "South Korea" },
                    { 33, "BT", "BTN", 20, false, "975", "Kingdom of Bhutan", "Bhutan" },
                    { 34, "BV", "BVT", 20, false, "NONE", "Bouvet Island", "Bouvet Island" }
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 35, "BW", "BWA", 20, false, "267", "Republic of Botswana", "Botswana" },
                    { 36, "BY", "BLR", 20, false, "375", "Republic of Belarus", "Belarus" },
                    { 37, "BZ", "BLZ", 20, false, "501", "Belize", "Belize" },
                    { 38, "CA", "CAN", 20, false, "1", "Canada", "Canada" },
                    { 39, "CC", "CCK", 20, false, "61", "Cocos (Keeling) Islands (the)", "Cocos (Keeling) Islands" },
                    { 40, "CD", "COD", 20, false, "243", "Democratic Republic of the Con", "Democratic Republic of the Con" },
                    { 41, "CF", "CAF", 20, false, "236", "Central African Republic (the)", "Central African Republic" },
                    { 42, "CG", "COG", 20, false, "242", "Con (the)", "Con" },
                    { 43, "CH", "CHE", 20, false, "41", "Swiss Confederation", "Switzerland" },
                    { 44, "CI", "CIV", 20, false, "225", "Republic of C&ocirc;te DIvoire (Ivory Coast)", "Cote divoire (Ivory Coast)" },
                    { 32, "BS", "BHS", 20, false, "1+242", "Bahamas (the)", "Bahamas" },
                    { 45, "CL", "CHL", 20, false, "56", "Republic of Chile", "Chile" },
                    { 47, "CN", "CHN", 20, false, "86", "Peoples Republic of China", "China" },
                    { 48, "CO", "COL", 20, false, "57", "Republic of Colombia", "Colombia" },
                    { 49, "CR", "CRI", 20, false, "506", "Republic of Costa Rica", "Costa Rica" },
                    { 50, "CU", "CUB", 20, false, "53", "Republic of Cuba", "Cuba" },
                    { 51, "CV", "CPV", 20, false, "238", "Republic of Cape Verde", "Cape Verde" },
                    { 52, "CW", "CUW", 20, false, "599", "Cura&ccedil;ao", "Curacao" },
                    { 53, "CX", "CXR", 20, false, "61", "Christmas Island", "Christmas Island" },
                    { 54, "CY", "CYP", 20, false, "357", "Republic of Cyprus", "Cyprus" },
                    { 55, "CZ", "CZE", 20, false, "420", "Czech Republic", "Czech Republic" },
                    { 56, "DE", "DEU", 20, false, "49", "Federal Republic of Germany", "Germany" },
                    { 57, "DJ", "DJI", 20, false, "253", "Republic of Djibouti", "Djibouti" },
                    { 58, "DK", "DNK", 20, false, "45", "Kingdom of Denmark", "Denmark" },
                    { 46, "CM", "CMR", 20, false, "237", "Republic of Cameroon", "Cameroon" },
                    { 31, "BR", "BRA", 20, false, "55", "Federative Republic of Brazil", "Brazil" },
                    { 30, "BQ", "BES", 20, false, "599", "Bonaire, Sint Eustatius and Saba", "Bonaire, Sint Eustatius and Saba" },
                    { 29, "BO", "BOL", 20, false, "591", "Plurinational State of Bolivia", "Bolivia" },
                    { 2, "AE", "ARE", 20, false, "971", "United Arab Emirates (the)", "United Arab Emirates" },
                    { 3, "AF", "AFG", 20, false, "93", "Islamic Republic of Afghanistan", "Afghanistan" },
                    { 4, "AG", "ATG", 20, false, "1+268", "Antigua and Barbuda", "Antigua and Barbuda" },
                    { 5, "AI", "AIA", 20, false, "1+264", "Anguilla", "Anguilla" },
                    { 6, "AL", "ALB", 20, false, "355", "Republic of Albania", "Albania" },
                    { 7, "AM", "ARM", 20, false, "374", "Republic of Armenia", "Armenia" },
                    { 8, "AO", "A", 20, false, "244", "Republic of Anla", "Anla" },
                    { 9, "AQ", "ATA", 20, false, "672", "Antarctica", "Antarctica" },
                    { 10, "AR", "ARG", 20, false, "54", "Argentine Republic", "Argentina" },
                    { 11, "AS", "ASM", 20, false, "1+684", "American Samoa", "American Samoa" },
                    { 12, "AT", "AUT", 20, false, "43", "Republic of Austria", "Austria" },
                    { 13, "AU", "AUS", 20, false, "61", "Commonwealth of Australia", "Australia" },
                    { 14, "AW", "ABW", 20, false, "297", "Aruba", "Aruba" },
                    { 15, "AX", "ALA", 20, false, "358", "&Aring;land Islands", "Aland Islands" }
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 16, "AZ", "AZE", 20, false, "994", "Republic of Azerbaijan", "Azerbaijan" },
                    { 17, "BA", "BIH", 20, false, "387", "Bosnia and Herzevina", "Bosnia and Herzevina" },
                    { 18, "BB", "BRB", 20, false, "1+246", "Barbados", "Barbados" },
                    { 19, "BD", "BGD", 3, true, "880", "Peoples Republic of Bangladesh", "Bangladesh" },
                    { 20, "BE", "BEL", 20, false, "32", "Kingdom of Belgium", "Belgium" },
                    { 21, "BF", "BFA", 20, false, "226", "Burkina Faso", "Burkina Faso" },
                    { 22, "BG", "BGR", 20, false, "359", "Republic of Bulgaria", "Bulgaria" },
                    { 23, "BH", "BHR", 20, false, "973", "Kingdom of Bahrain", "Bahrain" },
                    { 24, "BI", "BDI", 20, false, "257", "Republic of Burundi", "Burundi" },
                    { 25, "BJ", "BEN", 20, false, "229", "Republic of Benin", "Benin" },
                    { 26, "BL", "BLM", 20, false, "590", "Saint Barth&eacute;lemy", "Saint Barthelemy" },
                    { 27, "BM", "BMU", 20, false, "1+441", "Bermuda Islands", "Bermuda" },
                    { 28, "BN", "BRN", 20, false, "673", "Brunei Darussalam", "Brunei" },
                    { 59, "DM", "DMA", 20, false, "1+767", "Commonwealth of Dominica", "Dominica" },
                    { 60, "DO", "DOM", 20, false, "1+809, 8", "Dominican Republic (the)", "Dominican Republic" },
                    { 61, "DZ", "DZA", 20, false, "213", "Peoples Democratic Republic of Algeria", "Algeria" },
                    { 62, "EC", "ECU", 20, false, "593", "Republic of Ecuador", "Ecuador" },
                    { 94, "HK", "HKG", 20, false, "852", "Hong Kong", "Hong Kong" },
                    { 95, "HM", "HMD", 20, false, "NONE", "Heard Island and McDonald Islands", "Heard Island and McDonald Islands" },
                    { 96, "HN", "HND", 20, false, "504", "Republic of Honduras", "Honduras" },
                    { 97, "HR", "HRV", 20, false, "385", "Republic of Croatia", "Croatia" },
                    { 98, "HT", "HTI", 20, false, "509", "Republic of Haiti", "Haiti" },
                    { 99, "HU", "HUN", 20, false, "36", "Hungary", "Hungary" },
                    { 100, "ID", "IDN", 20, false, "62", "Republic of Indonesia", "Indonesia" },
                    { 101, "IE", "IRL", 20, false, "353", "Ireland", "Ireland" },
                    { 102, "IL", "ISR", 20, false, "972", "State of Israel", "Israel" },
                    { 103, "IM", "IMN", 20, false, "44", "Isle of Man", "Isle of Man" },
                    { 104, "IN", "IND", 1, true, "91", "Republic of India", "India" },
                    { 105, "IO", "IOT", 20, false, "246", "British Indian Ocean Territory (the)", "British Indian Ocean Territory" },
                    { 106, "IQ", "IRQ", 20, false, "964", "Republic of Iraq", "Iraq" },
                    { 107, "IR", "IRN", 20, false, "98", "Iran (Islamic Republic of)", "Iran" },
                    { 108, "IS", "ISL", 20, false, "354", "Republic of Iceland", "Iceland" },
                    { 109, "IT", "ITA", 20, false, "39", "Italian Republic", "Italy" },
                    { 110, "JE", "JEY", 20, false, "44", "The Bailiwick of Jersey", "Jersey" },
                    { 111, "JM", "JAM", 20, false, "1+876", "Jamaica", "Jamaica" },
                    { 112, "JO", "JOR", 20, false, "962", "Hashemite Kingdom of Jordan", "Jordan" },
                    { 113, "JP", "JPN", 20, false, "81", "Japan", "Japan" },
                    { 114, "KE", "KEN", 20, false, "254", "Republic of Kenya", "Kenya" },
                    { 115, "KG", "KGZ", 20, false, "996", "Kyrgyz Republic", "Kyrgyzstan" },
                    { 116, "KH", "KHM", 20, false, "855", "Kingdom of Cambodia", "Cambodia" },
                    { 117, "KI", "KIR", 20, false, "686", "Republic of Kiribati", "Kiribati" },
                    { 118, "KM", "COM", 20, false, "269", "Comoros (the)", "Comoros" }
                });

            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipSupportedCountries",
                columns: new[] { "Id", "Code", "Code3", "DisplayOrder", "IsPopular", "IsdCode", "LongName", "Name" },
                values: new object[,]
                {
                    { 119, "KN", "KNA", 20, false, "1+869", "Federation of Saint Christopher and Nevis", "Saint Kitts and Nevis" },
                    { 120, "KP", "PRK", 20, false, "850", "Democratic Peoples Republic of Korea", "North Korea" },
                    { 93, "GY", "GUY", 20, false, "592", "Co-operative Republic of Guyana", "Guyana" },
                    { 122, "KW", "KWT", 20, false, "965", "State of Kuwait", "Kuwait" },
                    { 92, "GW", "GNB", 20, false, "245", "Republic of Guinea-Bissau", "Guinea-Bissau" },
                    { 90, "GT", "GTM", 20, false, "502", "Republic of Guatemala", "Guatemala" },
                    { 63, "EE", "EST", 20, false, "372", "Republic of Estonia", "Estonia" },
                    { 64, "EG", "EGY", 20, false, "20", "Arab Republic of Egypt", "Egypt" },
                    { 65, "EH", "ESH", 20, false, "212", "Western Sahara", "Western Sahara" },
                    { 66, "ER", "ERI", 20, false, "291", "State of Eritrea", "Eritrea" },
                    { 67, "ES", "ESP", 20, false, "34", "Kingdom of Spain", "Spain" },
                    { 68, "ET", "ETH", 20, false, "251", "Federal Democratic Republic of Ethiopia", "Ethiopia" },
                    { 69, "FI", "FIN", 20, false, "358", "Republic of Finland", "Finland" },
                    { 70, "FJ", "FJI", 20, false, "679", "Republic of Fiji", "Fiji" },
                    { 71, "FK", "FLK", 20, false, "500", "Falkland Islands (the) [Malvinas]", "Falkland Islands (Malvinas)" },
                    { 72, "FM", "FSM", 20, false, "691", "Federated States of Micronesia", "Micronesia" },
                    { 73, "FO", "FRO", 20, false, "298", "Faroe Islands (the)", "Faroe Islands" },
                    { 74, "FR", "FRA", 20, false, "33", "French Republic", "France" },
                    { 75, "GA", "GAB", 20, false, "241", "Gabonese Republic", "Gabon" },
                    { 76, "GB", "GBR", 20, false, "44", "United Kingdom of Great Britain and Northern Ireland (the)", "United Kingdom" },
                    { 77, "GD", "GRD", 20, false, "1+473", "Grenada", "Grenada" },
                    { 78, "GE", "GEO", 20, false, "995", "Georgia", "Georgia" },
                    { 79, "GF", "GUF", 20, false, "594", "French Guiana", "French Guiana" },
                    { 80, "GG", "GGY", 20, false, "44", "Guernsey", "Guernsey" },
                    { 81, "GH", "GHA", 20, false, "233", "Republic of Ghana", "Ghana" },
                    { 82, "GI", "GIB", 20, false, "350", "Gibraltar", "Gibraltar" },
                    { 83, "GL", "GRL", 20, false, "299", "Greenland", "Greenland" },
                    { 84, "GM", "GMB", 20, false, "220", "Gambia (the)", "Gambia" },
                    { 85, "GN", "GIN", 20, false, "224", "Republic of Guinea", "Guinea" },
                    { 86, "GP", "GLP", 20, false, "590", "Guadeloupe", "Guadaloupe" },
                    { 87, "GQ", "GNQ", 20, false, "240", "Republic of Equatorial Guinea", "Equatorial Guinea" },
                    { 88, "GR", "GRC", 20, false, "30", "Hellenic Republic", "Greece" },
                    { 89, "GS", "SGS", 20, false, "500", "South Georgia and the South Sandwich Islands", "South Georgia and the South Sandwich Islands" },
                    { 91, "GU", "GUM", 20, false, "1+671", "Guam", "Guam" },
                    { 245, "ZW", "ZWE", 20, false, "263", "Republic of Zimbabwe", "Zimbabwe" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipSupportedCountries");
        }
    }
}
