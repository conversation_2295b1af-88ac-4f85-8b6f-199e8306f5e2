﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideoTableAddColumnForValidation : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideResources_LoginVideos_LoginVideoId",
                table: "LoginVideResources");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoginVideResources",
                table: "LoginVideResources");

            migrationBuilder.RenameTable(
                name: "LoginVideResources",
                newName: "LoginVideoResources");

            migrationBuilder.RenameIndex(
                name: "IX_LoginVideResources_LoginVideoId",
                table: "LoginVideoResources",
                newName: "IX_LoginVideoResources_LoginVideoId");

            migrationBuilder.AddColumn<string>(
                name: "ValidationId",
                table: "LoginVideos",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoginVideoResources",
                table: "LoginVideoResources",
                column: "Id");

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "Title", "ValidationId" },
                values: new object[] { "Rates Experiment", "MT_RATES_EXPERIMENT_VIDEO" });

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "Title", "ValidationId" },
                values: new object[] { "Weekly Draw", "C3PAYPLUS_INTRO_VIDEO" });

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideoResources_LoginVideos_LoginVideoId",
                table: "LoginVideoResources",
                column: "LoginVideoId",
                principalTable: "LoginVideos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideoResources_LoginVideos_LoginVideoId",
                table: "LoginVideoResources");

            migrationBuilder.DropPrimaryKey(
                name: "PK_LoginVideoResources",
                table: "LoginVideoResources");

            migrationBuilder.DropColumn(
                name: "ValidationId",
                table: "LoginVideos");

            migrationBuilder.RenameTable(
                name: "LoginVideoResources",
                newName: "LoginVideResources");

            migrationBuilder.RenameIndex(
                name: "IX_LoginVideoResources_LoginVideoId",
                table: "LoginVideResources",
                newName: "IX_LoginVideResources_LoginVideoId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_LoginVideResources",
                table: "LoginVideResources",
                column: "Id");

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                column: "Title",
                value: null);

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 2,
                column: "Title",
                value: null);

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideResources_LoginVideos_LoginVideoId",
                table: "LoginVideResources",
                column: "LoginVideoId",
                principalTable: "LoginVideos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
