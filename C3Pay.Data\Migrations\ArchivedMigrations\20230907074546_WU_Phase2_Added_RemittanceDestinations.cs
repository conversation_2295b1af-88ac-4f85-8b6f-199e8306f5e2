﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU_Phase2_Added_RemittanceDestinations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferProviders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LogoUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DetailsUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferProviders", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RemittanceDestinations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code3 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsPopular = table.Column<bool>(type: "bit", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RemittanceDestinations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferMethods",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferMethodType = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    Rate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    RateExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Code2 = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    RemittanceDestinationId = table.Column<int>(type: "int", nullable: false),
                    MoneyTransferProviderId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferMethods", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferMethods_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferMethods_MoneyTransferProviders_MoneyTransferProviderId",
                        column: x => x.MoneyTransferProviderId,
                        principalTable: "MoneyTransferProviders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferMethods_RemittanceDestinations_RemittanceDestinationId",
                        column: x => x.RemittanceDestinationId,
                        principalTable: "RemittanceDestinations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferMethods_CountryCode",
                table: "MoneyTransferMethods",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferMethods_MoneyTransferProviderId",
                table: "MoneyTransferMethods",
                column: "MoneyTransferProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferMethods_RemittanceDestinationId",
                table: "MoneyTransferMethods",
                column: "RemittanceDestinationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferMethods");

            migrationBuilder.DropTable(
                name: "MoneyTransferProviders");

            migrationBuilder.DropTable(
                name: "RemittanceDestinations");
        }
    }
}
