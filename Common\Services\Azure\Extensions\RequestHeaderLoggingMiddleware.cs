﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;

namespace Edenred.Common.Services.Azure.Extensions
{
    public class RequestHeaderLoggingMiddleware : IMiddleware
    {
        public List<string> RequestHeaders { get; set; } = new List<string>()
        {
            "Authorization"
        };

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            context.Request.EnableBuffering();

            var activity = Activity.Current;

            if (activity != null)
            {
                foreach (var headerName in RequestHeaders)
                {
                    var headers = context.Request.Headers[headerName];
                    if (headers.Any())
                    {
                        // For security headers like Authorization, consider masking or not logging the full value
                        var headerValue = headerName.ToLower() == "authorization" 
                            ? "***MASKED***" 
                            : string.Join(Environment.NewLine, headers);
                        
                        activity.SetTag($"http.request.header.{headerName.ToLower()}", headerValue);
                    }
                }           
            }

            await next(context);
        }
    }
}
