﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Repositories;
using C3Pay.Services.Payments.Queries;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace C3Pay.UnitTest.Tests.Payments
{
    public class GetPaymentAuthRequestQueryHandlerTests
    {
        private readonly Mock<IFeatureManager> _featureManagerMock;
        private readonly Mock<ILogger<GetPaymentAuthRequestQueryHandler>> _loggerMock;
        private readonly Mock<IUserRepository> _userRepositoryMock;

        private readonly GetPaymentAuthRequestQueryHandler _handler;

        public GetPaymentAuthRequestQueryHandlerTests()
        {
            _featureManagerMock = new Mock<IFeatureManager>();
            _loggerMock = new Mock<ILogger<GetPaymentAuthRequestQueryHandler>>();
            _userRepositoryMock = new Mock<IUserRepository>();

            _handler = new GetPaymentAuthRequestQueryHandler(_featureManagerMock.Object, _loggerMock.Object, _userRepositoryMock.Object);
        }

        [Fact]
        public async void Handler_ShouldReturnFailure_WhenFeatureIsNotEnabled()
        {
            // Arrange
            var command = new GetPaymentAuthRequestQuery
            {
                UserPhoneNumber = "00971503145237",
                LanguageCode = "en"
            };


            _featureManagerMock.Setup(x => x.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication))
                .ReturnsAsync(false);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);


            // Assert
            Assert.True(result.IsFailure);
            Assert.Equal(Errors.InAppAuth.PaymentAuthFeatureNotEnabled.Code, result.Error.Code);
        }

        [Fact]
        public async Task Handler_ShouldReturnFailure_WhenQueryIsInvalid()
        {
            // Arrange
            _featureManagerMock.Setup(fm => fm.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication))
                               .ReturnsAsync(true);

            var command = new GetPaymentAuthRequestQuery
            {
                UserPhoneNumber = "",
                LanguageCode = "en"
            };
            var invalidResult = Result.Failure(Errors.InAppAuth.UserNotFound);
         

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsFailure);
            Assert.Equal(Errors.InAppAuth.NoPhoneNumberSent.Code, result.Error.Code);
        }

        [Fact]
        public async Task Handle_ShouldReturnFailure_WhenUserIsNotFound()
        {
            // Arrange
            _featureManagerMock.Setup(fm => fm.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication))
                               .ReturnsAsync(true);

            var invalidPhoneNumber = "1234567890";

            _userRepositoryMock.Setup(ur => ur.GetUserAsync(invalidPhoneNumber, It.IsAny<CancellationToken>()))
                               .ReturnsAsync((User)null);

            var command = new GetPaymentAuthRequestQuery { UserPhoneNumber = invalidPhoneNumber };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsFailure);
            Assert.Equal(Errors.InAppAuth.UserNotFound.Code, result.Error.Code);
        }


        [Fact]
        public async Task Handler_ShouldReturnSuccess_WhenAllConditionsAreMet()
        {
            // Arrange
            var testPhoneNumber = "00971503145237";
            _featureManagerMock.Setup(fm => fm.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication))
                               .ReturnsAsync(true);

            _userRepositoryMock.Setup(ur => ur.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                               .ReturnsAsync(new User()); 

            var command = new GetPaymentAuthRequestQuery { UserPhoneNumber = testPhoneNumber };

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Value);

        }

    }
}
