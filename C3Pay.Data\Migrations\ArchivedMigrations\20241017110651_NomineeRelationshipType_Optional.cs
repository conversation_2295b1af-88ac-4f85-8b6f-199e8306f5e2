﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class NomineeRelationshipType_Optional : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypeId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }
    }
}
