﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddLanguageTables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Languages",
                columns: table => new
                {
                    Code = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    NativeName = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Languages", x => x.Code);
                    table.ForeignKey(
                        name: "FK_Languages_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TextContents",
                columns: table => new
                {
                    Code = table.Column<string>(type: "nvarchar(25)", maxLength: 25, nullable: false),
                    DefaultLanguageCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: true),
                    DefaultText = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Order = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    LanguageCode = table.Column<string>(type: "nvarchar(2)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TextContents", x => x.Code);
                    table.ForeignKey(
                        name: "FK_TextContents_Languages_LanguageCode",
                        column: x => x.LanguageCode,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Translations",
                columns: table => new
                {
                    TextContentCode = table.Column<string>(type: "nvarchar(25)", nullable: false),
                    LanguageCode = table.Column<string>(type: "nvarchar(2)", nullable: false),
                    Text = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Translations", x => new { x.LanguageCode, x.TextContentCode });
                    table.ForeignKey(
                        name: "FK_Translations_Languages_LanguageCode",
                        column: x => x.LanguageCode,
                        principalTable: "Languages",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Translations_TextContents_TextContentCode",
                        column: x => x.TextContentCode,
                        principalTable: "TextContents",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Languages",
                columns: new[] { "Code", "CountryCode", "Description", "Name", "NativeName" },
                values: new object[,]
                {
                    { "bn", "BD", null, "Bengali", "বাংলা" },
                    { "en", "GB", null, "English", "English" },
                    { "hi", "IN", null, "Hindi", "हिन्दी" },
                    { "ml", "IN", null, "Malayalam", "മലയാളം" },
                    { "ta", "IN", null, "Tamil", "தமிழ்" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Languages_CountryCode",
                table: "Languages",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_TextContents_LanguageCode",
                table: "TextContents",
                column: "LanguageCode");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_TextContentCode",
                table: "Translations",
                column: "TextContentCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Translations");

            migrationBuilder.DropTable(
                name: "TextContents");

            migrationBuilder.DropTable(
                name: "Languages");
        }
    }
}
