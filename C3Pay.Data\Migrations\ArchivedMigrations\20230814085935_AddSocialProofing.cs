﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddSocialProofing : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NationalityName",
                table: "Countries",
                type: "varchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "SocialProofing",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FeatureId = table.Column<int>(type: "int", nullable: false),
                    CorporateId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateName = table.Column<string>(type: "varchar(200)", maxLength: 200, nullable: true),
                    NationalityCode = table.Column<string>(type: "varchar(3)", maxLength: 3, nullable: true),
                    Corridor = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true),
                    NoOfUsers = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SocialProofing", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SocialProofing_Features_FeatureId",
                        column: x => x.FeatureId,
                        principalTable: "Features",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SocialProofing_FeatureId",
                table: "SocialProofing",
                column: "FeatureId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SocialProofing");

            migrationBuilder.DropColumn(
                name: "NationalityName",
                table: "Countries");
        }
    }
}
