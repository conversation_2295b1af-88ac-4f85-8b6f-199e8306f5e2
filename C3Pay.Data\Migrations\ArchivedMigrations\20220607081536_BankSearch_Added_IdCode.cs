﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class BankSearch_Added_IdCode : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBranches");

            migrationBuilder.RenameColumn(
                name: "ExternalId",
                table: "MoneyTransferBanks",
                newName: "PrimaryIdentifierCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PrimaryIdentifierCode",
                table: "MoneyTransferBanks",
                newName: "ExternalId");

            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBranches",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
