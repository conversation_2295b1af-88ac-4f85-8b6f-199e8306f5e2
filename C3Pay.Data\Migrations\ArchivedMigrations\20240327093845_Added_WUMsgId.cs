﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_WUMsgId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "WUMsgId",
                table: "MoneyTransferTransactions",
                type: "nvarchar(150)",
                maxLength: 150,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "WUMsgId",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(150)",
                maxLength: 150,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WUMsgId",
                table: "MoneyTransferTransactions");

            migrationBuilder.DropColumn(
                name: "WUMsgId",
                table: "MoneyTransferBeneficiaries");
        }
    }
}
