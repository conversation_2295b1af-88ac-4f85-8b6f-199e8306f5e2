﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedNewTextContents : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "IsActive", "LanguageCode", "Order", "Text", "Type" },
                values: new object[] { "dbnd_vrfd_user", true, "en", 1, "New device added: Someone logged into your C3Pay account from a new device. If it's not you, please reset your password", "dev_bind_msg" });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "IsActive", "LanguageCode", "Order", "Text", "Type" },
                values: new object[] { "dbnd_nvrfd_user", true, "en", 2, "New device added: Someone logged into your account from a new device. If it's not you, please reset your password", "dev_bind_msg" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "dbnd_nvrfd_user");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "dbnd_vrfd_user");
        }
    }
}
