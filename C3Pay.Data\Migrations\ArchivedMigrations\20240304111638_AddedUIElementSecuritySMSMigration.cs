﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedUIElementSecuritySMSMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "UIElements",
                columns: new[] { "Id", "ActionJson", "CreatedDate", "DeletedBy", "DeletedDate", "ElementType", "IsActive", "IsDeleted", "IsVisible", "PartnerType", "PropertiesJson", "UpdatedDate" },
                values: new object[] { new Guid("008399c7-4368-4520-b0bd-4e8e8985ebbc"), "{\"Request\":{\"ActionType\":2,\"ToBeReplacedElementId\":null}}", new DateTime(2024, 3, 4, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 2, true, false, true, 1, "{\"FeeAmount\":3.15,\"ActionExpiryDays\":60,\"MaxViewCount\":2}", null });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "UIElements",
                keyColumn: "Id",
                keyValue: new Guid("008399c7-4368-4520-b0bd-4e8e8985ebbc"));
        }
    }
}
