﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_AdditionalFieldMappings_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "FieldText",
                table: "AdditionalFieldMappings",
                newName: "FieldName");

            migrationBuilder.AddColumn<string>(
                name: "CountryCode",
                table: "AdditionalFieldMappings",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferMethodType",
                table: "AdditionalFieldMappings",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryCode",
                table: "AdditionalFieldMappings");

            migrationBuilder.DropColumn(
                name: "MoneyTransferMethodType",
                table: "AdditionalFieldMappings");

            migrationBuilder.RenameColumn(
                name: "FieldName",
                table: "AdditionalFieldMappings",
                newName: "FieldText");
        }
    }
}
