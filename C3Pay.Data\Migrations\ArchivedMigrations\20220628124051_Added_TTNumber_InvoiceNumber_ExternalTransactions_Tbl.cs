﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_TTNumber_InvoiceNumber_ExternalTransactions_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "InvoiceNumber",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TTNumber",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "InvoiceNumber",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "TTNumber",
                table: "MoneyTransferExternalTransactions");
        }
    }
}
