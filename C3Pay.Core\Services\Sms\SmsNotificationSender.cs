﻿using Application.Messages;
using MassTransit;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.Sms
{
    public class SmsNotificationSender : ISmsNotificationSender
    {
        private readonly IBus _bus;
        private readonly Uri _queueUri;

        public SmsNotificationSender(IBus bus, IConfiguration configuration)
        {
            _bus = bus;

            var queueAddress = configuration["Queues:SendSmsNotification"];
            if (string.IsNullOrWhiteSpace(queueAddress))
                throw new InvalidOperationException("Missing configuration for 'Queues:SendSmsNotification'.");

            _queueUri = new Uri(queueAddress);
        }

        public async Task SendAsync(SendSmsNotificationCommand command)
        {
            var endpoint = await _bus.GetSendEndpoint(_queueUri);
            await endpoint.Send(command);
        }
    }
}