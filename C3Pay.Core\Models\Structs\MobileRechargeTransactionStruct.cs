﻿using System;
using System.Collections.Generic;
using System.Text;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Core.Models.Structs
{
    public class MobileRechargeTransactionStruct
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreatedDate { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string LastName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string NickName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PhoneNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string AccountNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Sender { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Beneficiary { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Receiver { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? SendAmount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string C3REF { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MobileRechargeType? RechargeType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReferenceNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TransactionId { get; set; }
    }
}
