﻿using C3pay.StressTest.Dto;
using NBomber.Contracts;
using NBomber.CSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace C3pay.StressTest
{
    class Program
    {
        static readonly HttpClient httpClient = new HttpClient();
        static readonly HttpClient httpClientPortal = new HttpClient();

        static void Main(string[] args)
        {
            var jsonSettings = File.ReadAllText("./Settings/Settings.json");

            var settings = JsonSerializer.Deserialize<Settings>(jsonSettings);

            int loginDuration = Convert.ToInt32(args[1].Split(':')[1]) * 60;
            int HomePageDuration = Convert.ToInt32(args[3].Split(':')[1]) * 60;
            int MoneyTransferBeneficiaryDuration = Convert.ToInt32(args[5].Split(':')[1]) * 60;
            int SignupDuration = Convert.ToInt32(args[7].Split(':')[1]) * 60;
            int MobileRechargeDuration = Convert.ToInt32(args[9].Split(':')[1]) * 60;
            int MobileRechargeBenificiaryDuration = Convert.ToInt32(args[11].Split(':')[1]) * 60;
            int RMTTransferDuration = Convert.ToInt32(args[13].Split(':')[1]) * 60;
            int forgotPhoneNumberDuration = Convert.ToInt32(args[15].Split(':')[1]) * 60;
            int forgotPasswordDuration = Convert.ToInt32(args[17].Split(':')[1]) * 60;
            int atmPinDuration = Convert.ToInt32(args[19].Split(':')[1]) * 60;

            //Check image settings
            //string imgFront = GetBase64StringForImage($"./images/{settings.fileFront}");
            //string imgback = GetBase64StringForImage($"./images/{settings.fileback}");
            //string imgselfie = GetBase64StringForImage($"./images/{settings.fileSelfie}");

            var ticks = DateTime.UtcNow.Ticks;
            var frontFileName = $"fronttest{ticks}";
            var backtFileName = $"backtest{ticks}";
            var selfieFileName = $"selfietest{ticks}";

            httpClient.BaseAddress = new Uri(settings.apiURL);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);

            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);

            //***********************************************************************************************************
            // Users
            //***********************************************************************************************************
            //Get User By PhoneNumber => kyc call
            var stepGetUserByPhoneNumber = Step.Create("GetUserByPhoneNumber", async context =>
            {
                try
                {
                        var response = await httpClient.GetAsync($"/api/UserMock/by-phone-number/{settings.phoneNumber}");

                        Console.WriteLine($"GetUserByPhoneNumber: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                        return Response.Fail();
                    }
                
            });


            //Get Subscriptions => esmo call
            var stepGetSubscriptions = Step.Create("GetSubscriptions", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/SubscriptionsMock/{settings.userId}");

                    Console.WriteLine($"GetSubscriptions: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                    
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Get Transactions => esmo call
            var stepGetTransactions = Step.Create("GetTransactions", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/UserMock/{settings.userId}/transactions/1");

                    Console.WriteLine($"GetTransactions: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get rmt-profile-status => kyc call
            var stepGetRmtProfile = Step.Create("GetRmtRPofile", async context =>
            {

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                    var response = await httpClient.GetAsync($"/api/UserMock/{settings.userId}/rmt-profile-status");

                    Console.WriteLine($"GetRmtRPofile: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                
            });

            //Get User Popup
            var stepGetUserPopup = Step.Create("GetUserPopup", async context =>
            {

                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                var response = await httpClient.GetAsync($"/api/UserMock/{settings.userId}/popup");

                Console.WriteLine($"GetUserPopup: {response.StatusCode}");

                return response.IsSuccessStatusCode
                    ? Response.Ok()
                    : Response.Fail();

            });

            //Get balance and status => PPS
            var stepGetUserBalance = Step.Create("GetUserBalance", async context =>
            {

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                    var response = await httpClient.GetAsync($"/api/UserMock/{settings.userId}/balance-and-status");

                    Console.WriteLine($"GetUserBalance: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                
            });


            //Update device token
            var stepUserUpdateDevice = Step.Create("UpdateDevice", async context =>
            {
                try
                {
                    var dto = new UpdateUserRequestDto()
                    {
                        UserId = settings.userId,
                        DeviceToken = settings.deviceToken
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PutAsync($"/api/UserMock/device-token", stringContent);

                        Console.WriteLine($"UpdateDevice: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //***********************************************************************************************************
            // Mobile Recharge
            //***********************************************************************************************************

            //Get MobileRecharge transactions
            var stepMobileRechargeTransaction = Step.Create("GetMobileRechargeTransactions", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/transactions/{settings.userId}?pageSize=10&pageNumber=1");

                    Console.WriteLine($"GetMobileRechargeTransactions: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get MobileRecharge benificaires
            var stepMobileRechargeBenificiairy = Step.Create("GetMobileRechargebeneficiaries", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/beneficiary/{settings.userId}");

                    Console.WriteLine($"GetMobileRechargebeneficiaries: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get MobileRecharge products => ding call
            var stepMobileRechargeProducts = Step.Create("GetMobileRechargeProducts", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/beneficiary/{settings.mrBenificiaryId}/products");

                    Console.WriteLine($"GetMobileRechargeProducts: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get MobileRecharge countries
            var stepMobileRechargeCountries = Step.Create("GetMobileRechargeCountries", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/countries");

                    Console.WriteLine($"GetMobileRechargeCountries: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get Money Transfer bank details
            var stepGetMoneyTransferBankDetails = Step.Create("GetMoneyTransferBankDetails", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/bank/{settings.userId}/{settings.ifscCode}");

                    Console.WriteLine($"GetMoneyTransferBankDetails: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });


            //Get MobileRecharge Receipt
            var stepMobileRechargeReceipt = Step.Create("GetMobileRechargeReceipt", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/transaction-receipt/{settings.mrTransactionId}");

                    Console.WriteLine($"GetMobileRechargeReceipt: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });


            //Add Benificiary & Send 
            var stepMRAddBenificiary = Step.Create("MRAddBenificiary", async context =>
            {
                try
                {
                    var randomPhone = new Random().Next(1111111,9999999);
                    
                    //Add Benificiary
                    var dtoBenficiary = new PostMobileRechargeBeneficiaryRequestDto()
                    {
                        UserId = settings.userId,
                        Beneficiary = new MobileRechargeBeneficiaryDto 
                        {
                             FullName = $"User-{randomPhone}",
                             PhoneNumber = $"0097150{randomPhone}",
                             RechargeType = "local",
                             CountryCode= "ae"
                        }
                    };

                    var jsonBenificiary = JsonSerializer.Serialize(dtoBenficiary);
                    MobileRechargeBeneficiarieyDto responseBenificaryDto;
                    using (var stringContent = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json"))
                    {
                        HttpResponseMessage responseAdd = await httpClient.PostAsync($"/api/MobileRechargeMock/beneficiary", stringContent);

                        Console.WriteLine($"Add Benificiary: {responseAdd.StatusCode}");

                        if (!responseAdd.IsSuccessStatusCode) return Response.Fail();

                        var responseBody = await responseAdd.Content.ReadAsStringAsync();
                        responseBenificaryDto = JsonSerializer.Deserialize<MobileRechargeBeneficiarieyDto>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
                        
                    }

                    await Task.Delay(TimeSpan.FromSeconds(2));

                    //Add Transfer
                    HttpResponseMessage responseTransfer;
                    var dtoTransfer = new PostMobileRechargeRequestDto()
                    {
                        UserId = settings.userId,
                        BeneficiaryId = responseBenificaryDto.BeneficiaryId,
                        ProductCode = "ETAEAE97945",
                        SendValue = new AmountDto
                        {
                            Amount = 5,
                            Currency = "AED"
                        },
                        FeeValue = new AmountDto
                        {
                            Amount = 0.5,
                            Currency = "AED"
                        },
                        ReceiveValue = new AmountDto
                        {
                            Amount = 100.0,
                            Currency = "AED"
                        }
                    };

                    var jsonTransfer = JsonSerializer.Serialize(dtoTransfer);
                    using (var stringContent = new StringContent(jsonTransfer, Encoding.UTF8, "application/json"))
                    {
                        responseTransfer = await httpClient.PostAsync($"/api/MobileRechargeMock/send-transfer", stringContent);

                        Console.WriteLine($"MR Transfer: {responseTransfer.StatusCode}");
                        var responseBody = await responseTransfer.Content.ReadAsStringAsync();
                    }

                    await Task.Delay(TimeSpan.FromSeconds(2));

                    //Delete benificiary
                    HttpResponseMessage responseDelete = await httpClient.DeleteAsync($"/api/MobileRechargeMock/beneficiary/{responseBenificaryDto.BeneficiaryId}");
                    Console.WriteLine($"Delete benificiary: {responseDelete.StatusCode}");
                    if (!responseDelete.IsSuccessStatusCode) return Response.Fail();
                    
                    return Response.Ok();

                    //return responseTransfer.IsSuccessStatusCode
                    //   ? Response.Ok()
                    //   : Response.Fail();

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //***********************************************************************************************************
            // Sign Up
            //***********************************************************************************************************
            var stepSignup = Step.Create("SignUp", async context =>
            {
                HttpClient httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri(settings.apiURL);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);

                try
                {
                    //Post Card Eligibilty
                    var dtoCardNumber = new EligibilityRequestDto()
                    {
                        CardNumber = settings.cardNumberSignup
                    };

                    var jsonCardNumber = JsonSerializer.Serialize(dtoCardNumber);
                    using (var stringContent = new StringContent(jsonCardNumber, Encoding.UTF8, "application/json"))
                    {
                        HttpResponseMessage responseCardNo = await httpClient.PostAsync($"/api/SignUpMock/eligibility/card", stringContent);

                        Console.WriteLine($"Step Signup Card number Eligibility: {responseCardNo.StatusCode}");

                        if (!responseCardNo.IsSuccessStatusCode) return Response.Fail();
                    }

                    await Task.Delay(TimeSpan.FromSeconds(2));
                    //Post Phone Number Eligibility
                    var dtoPhoneNumber = new EligibilityRequestDto()
                    {
                        PhoneNumber = settings.phoneNumber
                    };

                    var jsonPhoneNumber = JsonSerializer.Serialize(dtoPhoneNumber);
                    using (var stringContent = new StringContent(jsonPhoneNumber, Encoding.UTF8, "application/json"))
                    {
                        var responsePhoneNo = await httpClient.PostAsync($"/api/SignUpMock/eligibility/phone-number", stringContent);

                        Console.WriteLine($"Post Signup PhoneNumber Eligibility: {responsePhoneNo.StatusCode}");

                        if (!responsePhoneNo.IsSuccessStatusCode) return Response.Fail();
                    }
                    await Task.Delay(TimeSpan.FromSeconds(2));

                    var otpRequest = new SendOTPRequestDto()
                    {
                        PhoneNumber = settings.phoneNumber,
                        Reason = settings.reasonSignUp,
                        RetryCount = 1
                    };
                    //application/json-patch+json
                    var jsonOTP = JsonSerializer.Serialize(otpRequest);
                    HttpResponseMessage responseOTP = new HttpResponseMessage();
                    using (var stringContent = new StringContent(jsonOTP, Encoding.UTF8, "application/json-patch+json"))
                    {
                        responseOTP = await httpClient.PostAsync($"/api/OneTimePasswordMock", stringContent);

                        Console.WriteLine($"Post Signup OTP API: {responseOTP.StatusCode}");

                        if (!responseOTP.IsSuccessStatusCode) return Response.Fail();
                    }
                    var responseOtpBody = await responseOTP.Content.ReadAsStringAsync();
                    var responseOtp = JsonSerializer.Deserialize<string>(responseOtpBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    await Task.Delay(TimeSpan.FromSeconds(2));

                    //OTP Verification
                    var verifyOTP = new VerifyOTPRequestDto()
                    {
                        PhoneNumber = settings.phoneNumber,
                        Reason = settings.reasonSignUp,
                        OTP = responseOtp
                    };
                    //application/json-patch+json
                    var jsonPassword = JsonSerializer.Serialize(verifyOTP);
                    var stringContents = new StringContent(jsonPassword, Encoding.UTF8, "application/json-patch+json");

                    var responseVerifyOTP = await httpClient.PostAsync($"/api/OneTimePasswordMock/verification", stringContents);

                    Console.WriteLine($"Post Signup Verify OTP API: {responseVerifyOTP.StatusCode}");

                    if (!responseVerifyOTP.IsSuccessStatusCode) return Response.Fail();

                    var responseVerifyOtpBody = await responseVerifyOTP.Content.ReadAsStringAsync();
                    var responseVerifyOtp = JsonSerializer.Deserialize<string>(responseVerifyOtpBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    //look up Security Questions 
                    var responseSecurityQuestions = await httpClient.GetAsync($"/api/lookup/security-questions");

                    Console.WriteLine($"GetSecurityQuestions for signup: {responseSecurityQuestions.StatusCode}");

                    //Post new user sign up

                    httpClient.DefaultRequestHeaders.Add("verificationTokenType", settings.oTP);
                    httpClient.DefaultRequestHeaders.Add("verificationToken", responseOtp);
                    httpClient.DefaultRequestHeaders.Add("verificationTokenIdentifier", settings.phoneNumberSignup);

                    //Forgot Password 
                    var SecurityAnswersdata = new List<SecurityAnswerDto>();
                    SecurityAnswersdata.Add(new SecurityAnswerDto() { SecurityQuestionId = 1, SecurityAnswer = settings.securityAnswer1 });
                    SecurityAnswersdata.Add(new SecurityAnswerDto() { SecurityQuestionId = 2, SecurityAnswer = settings.securityAnswer2 });
                    var registerUserRequestDto = new RegisterUserRequestDto()
                    {
                        CardNumber = settings.cardNumberSignup,
                        PhoneNumber = settings.phoneNumberSignup,
                        DeviceToken = settings.deviceToken,
                        Password = settings.password,
                        SecurityAnswers = SecurityAnswersdata

                    };

                    var jsonAddUser = JsonSerializer.Serialize(registerUserRequestDto);
                    var stringContentAdd = new StringContent(jsonAddUser, Encoding.UTF8, "application/json-patch+json");

                    var responseAdd = await httpClient.PostAsync($"/api/SignUpMock", stringContentAdd);

                    Console.WriteLine($"Post Signup Verify OTP API: {responseAdd.StatusCode}");

                    if (!responseAdd.IsSuccessStatusCode) return Response.Fail();

                    var responseSignup = await responseAdd.Content.ReadAsStringAsync();
                    var responseSignupData = JsonSerializer.Deserialize<UsersDto>(responseSignup, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    var responseDelete = await httpClient.GetAsync($"/api/UserMock/remove/{responseSignupData.Id}");

                    Console.WriteLine($"Delete Account: {responseDelete.StatusCode}");

                    return Response.Ok();

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //***********************************************************************************************************
            // Money transfer
            //***********************************************************************************************************

            //Get User Transfer Detail
            var stepGetFxRate = Step.Create("GetFxRate", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"api/MoneyTransferMock/fx-rate/{settings.userId}/INR/1?transferMethod=BANKTRANSFER");

                    Console.WriteLine($"GetFxRate: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Get Transfer Reasons
            var stepGetMoneyTransferReasons = Step.Create("GetMoneyTransferReasons", async context =>
            {
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/reasons");

                    Console.WriteLine($"GetMoneyTransferReasons: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                
            });


            //Get Transfer countries
            var stepGetMoneyTransferCountries = Step.Create("GetMoneyTransferCountries", async context =>
            {

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/countries");

                    Console.WriteLine($"GetMoneyTransferCountries: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                
            });

            //Get Product-Estimate-Rate
            var stepGetMobileRechargeProductEstimate = Step.Create("GetMobileRechargeProductEstimate", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/product-estimate-rate/{settings.productCode}");

                    Console.WriteLine($"GetMobileRechargeProductEstimate: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Get international-calling-cards
            var stepGetMobileRechargeInternationalCard = Step.Create("GetMobileRechargeInternationalCard", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MobileRechargeMock/products/international-calling-cards?operatorName={settings.operatorName}");

                    Console.WriteLine($"GetMobileRechargeInternationalCard: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });


            //Get User Transfer Detail
            var stepGetTransferDetail = Step.Create("GetTransferDetail", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/free-transfer-details/{settings.userId}");

                    Console.WriteLine($"GetTransferDetail: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Get User check Transfer limit illigibility
            var stepGetTransferLimit = Step.Create("stepGetTransferLimit", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/transfer-limit-iseligible/{settings.userId}/BANKTRANSFER/IN/35");

                    Console.WriteLine($"stepGetTransferLimit: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Add Benificiary & Send and Delete
            var stepMTAddBenificiary = Step.Create("MTAddBenificiary", async context =>
            {
                try
                {

                    //Add Benificiary
                    var dtoBenficiary = new PostMoneyTransferBeneficiaryRequestDto()
                    {
                        UserId = settings.userId,
                        transferMethod = settings.transferMethod,
                        Beneficiary = new MoneyTransferBeneficiariesDto
                        { 
                            firstName = "Abdhul",
                            middleName = "K",
                            lastName = "Malik",
                            country = "PK",
                            countryName = "Pakistan"

                        },
                        BankDetails = new MoneyTransferBankDetailsDto
                        {
                            bankName = "Emirates Bank",
                            bankBranchName = "Abudhabi",
                            address1 = "Abudhabi",
                            address2 = "Abudhabi",
                            bankCode = "E723234",
                            bankBranchCode = "EA34334",
                            accountNumber = "Emi2312232323232323"
                        },
                        reasonId = 8


                    };
                    //application/json-patch+json
                    var jsonBenificiary = JsonSerializer.Serialize(dtoBenficiary);
                    MoneyTransferBeneficiarieyDto responseBenificaryDto;
                    //using (var stringContent = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json-patch+json"))
                    var stringContent = new StringContent(jsonBenificiary, Encoding.UTF8, "application/json-patch+json");
                    
                        HttpResponseMessage responseAdd = await httpClient.PostAsync($"/api/MoneyTransferMock/beneficiary", stringContent);

                        Console.WriteLine($"Add Money Transfer Benificiary: {responseAdd.StatusCode}");

                        if (!responseAdd.IsSuccessStatusCode) return Response.Fail();

                        var responseBody = await responseAdd.Content.ReadAsStringAsync();
                        responseBenificaryDto = JsonSerializer.Deserialize<MoneyTransferBeneficiarieyDto>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    

                    await Task.Delay(TimeSpan.FromSeconds(2));



                    //Delete benificiary
                    HttpResponseMessage responseDelete = await httpClient.DeleteAsync($"/api/MoneyTransferMock/beneficiary/{responseBenificaryDto.id}");
                    Console.WriteLine($"Delete benificiary: {responseDelete.StatusCode}");
                    if (!responseDelete.IsSuccessStatusCode) return Response.Fail();

                    return Response.Ok();

                    //return responseTransfer.IsSuccessStatusCode
                    //   ? Response.Ok()
                    //   : Response.Fail();

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });


            //Get Money Tranfer transactions
            var stepMoneyTransferTransactions = Step.Create("GetMTUserTransactions", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/transactions/{settings.userId}");

                    Console.WriteLine($"GetMTUserTransactions: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get Money Tranfer receipt
            var stepMoneyTransferReceipt = Step.Create("GeMoneyTransferReceipt ", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/transaction-receipt/{settings.mtttransactionId}");

                    Console.WriteLine($"GeMoneyTransferReceipt: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            // Get Money Transfer Beneficiary Bank details by Id
            //var stepGetMoneyTransferBeneficiaryBankDetails = Step.Create("GetMoneyTransferBeneficiaryBankDetails", async context =>
            //{
            //    try
            //    {

            //        var response = await httpClientPortal.GetAsync($"/api/MoneyTransfer/beneficiary-bankdetails/{settings.beneficiaryId}");

            //        Console.WriteLine($"GetMoneyTransferBeneficiaryBankDetails: {response.StatusCode}");

            //        return response.IsSuccessStatusCode
            //            ? Response.Ok()
            //            : Response.Fail();


            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});


            //***********************************************************************************************************
            // Forgot Mobile Number
            //***********************************************************************************************************

            //Post Card Verification
            var stepPostVerificationCard = Step.Create("PostVerificationCard", async context =>
            {
                try
                {
                    var dto = new ForgotPhoneNumberDto()
                    {
                        CardNumber = settings.eligiblityCardNumber,
                        UserId = settings.userId,
                        ApplicationId = 1
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/Login/forgot-phone-number/verification/card", stringContent);

                        Console.WriteLine($"PostVerificationCard: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Post Password Verification
            var stepPostPasswordVerification = Step.Create("PostPasswordVerification", async context =>
            {
                try
                {
                    var dto = new PasswordVerificationDto()
                    {
                        UserId = settings.userId,
                        ApplicationId = 1,
                        Password = settings.password
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/Login/forgot-phone-number/verification/password", stringContent);

                        Console.WriteLine($"PostPasswordVerification: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });        

            //Get the status of phone number 
            var stepPostEligabilityPhoneNumber = Step.Create("PostEligabilityPhoneNumber", async context =>
            {
                try
                {
                    var dto = new EligibilityPhoneNumberDto()
                    {
                        CardNumber = settings.eligiblityCardNumber,
                        PhoneNumber = settings.phoneNumber
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/Login/forgot-phone-number/eligibility/phone-number", stringContent);

                        Console.WriteLine($"PostEligabilityPhoneNumber: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Get Security Questions by userId
            var stepGetUserSecurityQuestions = Step.Create("GetUserSecurityQuestions", async context =>
            {
                try
                {
                    httpClient.DefaultRequestHeaders.Add("verificationTokenType", settings.passwordText);
                    httpClient.DefaultRequestHeaders.Add("verificationToken", settings.password);

                    var response = await httpClient.GetAsync($"/api/Login/forgot-phone-number/{settings.userId}/security-questions");

                    Console.WriteLine($"GetUserSecurityQuestions: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Update Phone Number
            var stepPutUpdatePhoneNumber = Step.Create("PutUpdatePhoneNumber", async context =>
            {
                try
                {
                    var dto = new UpdateUserRequestDto()
                    {
                        Password = settings.password,
                        PhoneNumber = settings.phoneNumber,
                        DeviceToken = settings.deviceToken,
                        UserId = settings.userId,
                        ApplicationId = 1
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        httpClient.DefaultRequestHeaders.Add("verificationTokenType", settings.passwordText);
                        httpClient.DefaultRequestHeaders.Add("verificationToken", settings.password);
                        var response = await httpClient.PutAsync($"/api/Login/forgot-phone-number/phone-number", stringContent);

                        Console.WriteLine($"PostEligabilityPhoneNumber: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //***********************************************************************************************************
            // Forgot Password
            //***********************************************************************************************************
            //Post Phone Number Verification
            var stepPostVerificationPhoneNo = Step.Create("PostVerificationPhoneNo", async context =>
            {
                try
                {
                    var dto = new ForgotPasswordDto()
                    {
                        PhoneNumber = settings.phoneNumber
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/Login/forgot-password/verification/phone-number", stringContent);

                        Console.WriteLine($"PostVerificationPhoneNo: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            //Forget Password OTP send, Verify, call other api by sending OTP by header
            var stepForgotPassword = Step.Create("ForgotPassword", async context =>
            {
                HttpClient httpClient = new HttpClient();
                httpClient.BaseAddress = new Uri(settings.apiURL);
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessToken);
                try
                {
                    var otpRequest = new SendOTPRequestDto()
                    {
                        PhoneNumber = settings.phoneNumber,
                        Reason = settings.reasonPassword,
                        RetryCount = 1
                    };
                    //application/json-patch+json
                    var jsonPassword = JsonSerializer.Serialize(otpRequest);
                    var stringContent = new StringContent(jsonPassword, Encoding.UTF8, "application/json-patch+json");

                    HttpResponseMessage responseOTP = await httpClient.PostAsync($"/api/OneTimePasswordMock", stringContent);

                    Console.WriteLine($"Post Send OTP API: {responseOTP.StatusCode}");

                    if (!responseOTP.IsSuccessStatusCode) return Response.Fail();

                    var responseOtpBody = await responseOTP.Content.ReadAsStringAsync();
                    var responseOtp = JsonSerializer.Deserialize<string>(responseOtpBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    await Task.Delay(TimeSpan.FromSeconds(2));

                    //OTP Verification
                    var verifyOTP = new VerifyOTPRequestDto()
                    {
                        PhoneNumber = settings.phoneNumber,
                        Reason = settings.reasonPassword,
                        OTP = responseOtp
                    };
                    //application/json-patch+json
                    jsonPassword = JsonSerializer.Serialize(verifyOTP);  
                    stringContent = new StringContent(jsonPassword, Encoding.UTF8, "application/json-patch+json");

                    var responseVerifyOTP = await httpClient.PostAsync($"/api/OneTimePasswordMock/verification", stringContent);

                    Console.WriteLine($"Post Verify OTP API: {responseVerifyOTP.StatusCode}");

                    if (!responseVerifyOTP.IsSuccessStatusCode) return Response.Fail();

                    var responseVerifyOtpBody = await responseVerifyOTP.Content.ReadAsStringAsync();
                    var responseVerifyOtp = JsonSerializer.Deserialize<string>(responseVerifyOtpBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    httpClient.DefaultRequestHeaders.Add("verificationTokenType", settings.oTP);
                    httpClient.DefaultRequestHeaders.Add("verificationToken", responseOtp);
                    httpClient.DefaultRequestHeaders.Add("verificationTokenIdentifier", settings.phoneNumber);

                    var responseSecurityQuestion = await httpClient.GetAsync($"/api/Login/forgot-password/{settings.userId}/security-questions");
                    Console.WriteLine($"GetUserSecurityQuestions: {responseSecurityQuestion.StatusCode}");
                    if (!responseSecurityQuestion.IsSuccessStatusCode) return Response.Fail();

                    var responseSecurityBody = await responseSecurityQuestion.Content.ReadAsStringAsync();
                    var responseSecurityQuestions = JsonSerializer.Deserialize <IEnumerable<SecurityQuestion>>(responseSecurityBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    //Forgot Password 
                    var updatePasswordRequest = new UpdateUserRequestDto()
                    {
                        Password = settings.password,
                        PhoneNumber = settings.phoneNumber,
                        DeviceToken = settings.deviceToken,
                        UserId = settings.userId,
                        ApplicationId = 1
                    };

                    //application/json-patch+json
                    jsonPassword = JsonSerializer.Serialize(updatePasswordRequest);
                    stringContent = new StringContent(jsonPassword, Encoding.UTF8, "application/json-patch+json");

                    var responseUpdatePassword = await httpClient.PutAsync($"/api/Login/forgot-password/password", stringContent);

                    Console.WriteLine($"Put Forgetpassword API: {responseUpdatePassword.StatusCode}");

                    if (!responseUpdatePassword.IsSuccessStatusCode) return Response.Fail();

                    return Response.Ok();


                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //***********************************************************************************************************
            // ATM Pin
            //***********************************************************************************************************
            var stepViewATMPin = Step.Create("GetViewATMPin", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/UserMock/{settings.atmPinUserId}/card/pin/{settings.cardSerialNumber}/{settings.cvc2}");

                    Console.WriteLine($"GetViewATMPin: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });
            var stepVerifyCVC2ATMPin = Step.Create("GetVerifyCVC2ATMPin", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/UserMock/{settings.atmPinUserId}/card/verify/{settings.atmPinCardNumber}/{settings.cvc2}");

                    Console.WriteLine($"GetVerifyCVC2ATMPin: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Post Check image Quality (Selfie) => Signy call
            //var stepCheckimageQualitySelfie = Step.Create("GetCheckimageQuality", async context =>
            //{
            //    try
            //    {
            //        var ticks = DateTime.UtcNow.Ticks;
            //        var frontFileName = $"fronttest{ticks}";
            //        var backFileName = $"backtest{ticks}";
            //        var selfieFileName = $"selfietest{ticks}";

            //        var response = await CallCheckImageQuality(httpClient, settings.userId, selfieFileName, EmiratesIdDocumentType.selfie.ToString(), imgselfie);
            //        if (!response.IsSuccessStatusCode) return Response.Fail();
            //        await Task.Delay(TimeSpan.FromSeconds(5));

            //        response = await CallCheckImageQuality(httpClient, settings.userId, backFileName, EmiratesIdDocumentType.emirates_id_back_scan.ToString(), imgback);
            //        if (!response.IsSuccessStatusCode) return Response.Fail();
            //        await Task.Delay(TimeSpan.FromSeconds(5));

            //        //response = await CallCheckImageQuality(httpClient, userId, frontFileName, EmiratesIdDocumentType.emirates_id_front_scan.ToString(), imgFront);
            //        //if (!response.IsSuccessStatusCode) return Response.Fail();
            //        //await Task.Delay(TimeSpan.FromSeconds(5));

            //        //response = await httpClient.GetAsync($"api/emiratesidv2/{userId}/face-match/{frontFileName}/{backFileName}");
            //        //if (!response.IsSuccessStatusCode) return Response.Fail();
            //        //await Task.Delay(TimeSpan.FromSeconds(5));

            //        //response = await httpClient.GetAsync($"api/emiratesidv2/{userId}/data-extraction/{frontFileName}/{backFileName}");
            //        //if (!response.IsSuccessStatusCode) return Response.Fail();

            //        Console.WriteLine($"GetCheckimageQuality: {response.StatusCode}");

            //        return Response.Ok();

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});        

            //Post Mobilerecharge ==> Beneficiary-Details-Iseligible
            var stepPostMobilerechargeBeneficiaryDetailsIseligible = Step.Create("PostMobilerechargeBeneficiaryDetailsIseligible", async context =>
            {
                try
                {
                    var dto = new BeneficiaryDetailsEligibilityRequestDto()
                    {
                        UserId = settings.userId,
                        CountryCode = settings.countryCode,
                        PhoneNumber = settings.phoneNumber,
                        NickName = settings.nickName
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/MobileRechargeMock/beneficiary-details-iseligible", stringContent);

                        Console.WriteLine($"PostMobilerechargeBeneficiaryDetailsIseligible: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });

            //Post View/Search Recharge Beneficiary in c3pay.portal
            //var stepPostMobileRechargebeneficiaries = Step.Create("PostMobileRechargebeneficiaries", async context =>
            //{
            //    try
            //    {
            //        var dto = new BeneficiarySearchDto()
            //        {
            //            Page = 1,
            //            Size = 10
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {
            //            var response = await httpClientPortal.PostAsync($"/api/MobileRecharge/beneficiaries/{settings.userId}", stringContent);

            //            Console.WriteLine($"PostMobileRechargebeneficiaries: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            //View recharge Beneficiary transaction in c3pay.portal
            //var stepPostMobileRechargeBeneficiariesTransaction = Step.Create("PostMobileRechargeBeneficiariesTransaction", async context =>
            //{
            //    try
            //    {
            //        var dto = new TransactionSearchDto()
            //        {
            //            Page = 1,
            //            Size = 10,
            //            FromDate = settings.fromDate,
            //            ToDate = settings.toDate
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {
            //            var response = await httpClientPortal.PostAsync($"/api/Transaction/recharge-beneficiaries-transaction/{settings.userId}", stringContent);

            //            Console.WriteLine($"PostMobileRechargeBeneficiariesTransaction: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            

            //Post Send Money in Money Transfer in c3pay.portal
            //var stepPostMoneyTransferBeneficiariesTransaction = Step.Create("PostMoneyTransferBeneficiariesTransaction", async context =>
            //{
            //    try
            //    {
            //        var dto = new MoneyTransferTransactionDto()
            //        {
            //            Page = 1,
            //            Size = 20,
                        
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {
            //            var response = await httpClientPortal.PostAsync($"/api/Transaction/moneytransfer-beneficiaries-transaction/{settings.userId}", stringContent);

            //            Console.WriteLine($"PostMoneyTransferBeneficiariesTransaction: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            //-------------------------------------------------
            //--------------Get User Profile -------------------------
            //-------------------------------------------------

            // Get UserProfile by Id
            //var stepGetUserProfile = Step.Create("GetUserProfile", async context =>
            //{
            //    try
            //    {
            //        using (var httpClientPortal = new HttpClient())
            //        {
            //            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            //            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);
            //            var response = await httpClientPortal.GetAsync($"/api/User/profile/{settings.userId}");

            //            Console.WriteLine($"GetUserProfile: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Get RMT Profile History by Id
            //var stepGetUserRMTProfileHistory = Step.Create("GetUserRMTProfileHistory", async context =>
            //{
            //    try
            //    {
            //        using (var httpClientPortal = new HttpClient())
            //        {
            //            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            //            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);
            //            var response = await httpClientPortal.GetAsync($"/api/User/rmt-profile-history/{settings.userId}");

            //            Console.WriteLine($"GetUserRMTProfileHistory: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Post Money Transfer Beneficiaries by Id
            //var stepPostMoneyTransferBeneficiaries = Step.Create("PostMoneyTransferBeneficiaries", async context =>
            //{
            //    try
            //    {
            //        var dto = new MTBeneficiaryDto()
            //        {
            //            PageNo = settings.moneyTransferBeneficiaryPageNo,
            //            PageSize = settings.moneyTransferBeneficiarySize
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {

            //            var response = await httpClientPortal.PostAsync($"/api/MoneyTransfer/beneficiaries/{settings.userId}", stringContent);

            //            Console.WriteLine($"PostMoneyTransferBeneficiaries: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            // All Users - Search User
            //var stepPostUserAllUsers = Step.Create("PostUserAllUsers", async context =>
            //{
            //    try
            //    {
            //        var dto = new UserDto()
            //        {
            //            PageNo = settings.allUsersPageNo,
            //            PageSize = settings.allUsersPageSize,
            //            Name = settings.name
                        
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {

            //            var response = await httpClientPortal.PostAsync($"/api/User/all-users", stringContent);

            //            Console.WriteLine($"PostUserAllUsers: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            // Get User Card Status by Id
            //var stepGetUserCardStatus = Step.Create("GetUserCardStatus", async context =>
            //{
            //    try
            //    {
            //        using (var httpClientPortal = new HttpClient())
            //        {
            //            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            //            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);
            //            var response = await httpClientPortal.GetAsync($"/api/User/{settings.userId}/card/status");

            //            Console.WriteLine($"GetUserCardStatus: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Get User Comments by Id
            //var stepGetUserComments = Step.Create("GetUserComments", async context =>
            //{
            //    try
            //    {
            //        using (var httpClientPortal = new HttpClient())
            //        {
            //            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            //            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);
            //            var response = await httpClientPortal.GetAsync($"/api/User/usercomments/{settings.userId}");

            //            Console.WriteLine($"GetUserComments: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Get User Registration by Id
            //var stepGetUserRegistration = Step.Create("GetUserRegistration", async context =>
            //{
            //    try
            //    {
            //        using (var httpClientPortal = new HttpClient())
            //        {
            //            httpClientPortal.BaseAddress = new Uri(settings.apiURLPortal);
            //            httpClientPortal.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", settings.accessTokenPortal);
            //            var response = await httpClientPortal.GetAsync($"/api/User/registration/{settings.userId}");

            //            Console.WriteLine($"GetUserRegistration: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Registrations - Search User
            //var stepPostUserRegistrations = Step.Create("PostUserRegistrations", async context =>
            //{
            //    try
            //    {
            //        var dto = new RegistrationDto()
            //        {
            //            RegistrationPageNo = settings.registrationPageNo,
            //            RegistrationPageSize = settings.registrationPageSize,
            //            WithEID = settings.withEID
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {

            //            var response = await httpClientPortal.PostAsync($"/api/User/registrations", stringContent);

            //            Console.WriteLine($"PostUserRegistrations: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            //Post Send Money in Money Transfer
            var stepPostMoneyTransferSendMoney = Step.Create("PostMoneyTransferSendMoney", async context =>
            {
                try
                {
                    var dto = new MoneyTransferRequestDto()
                    {
                        BeneficiaryId = settings.mtBneficiaryId,
                        SendAmount = new AmountDto()
                        {
                            Amount = settings.amount,
                            Currency = settings.currency
                        },
                        ReceiveAmount = new AmountDto()
                        {
                            Amount = settings.amount,
                            Currency = settings.currency
                        },
                        ConversionRate = settings.conversionRate,
                        FeeAmount = settings.feeAmount,
                        ReferralCode = settings.referralCode
                    };

                    var json = JsonSerializer.Serialize(dto);
                    using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
                    {
                        var response = await httpClient.PostAsync($"/api/MoneyTransferMock/send-money", stringContent);

                        Console.WriteLine($"PostMoneyTransferSendMoney: {response.StatusCode}");

                        return response.IsSuccessStatusCode
                            ? Response.Ok()
                            : Response.Fail();
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }
            });


            //Get Money Transfer beneficiary by UserId
            var stepGetMoneyTransferBeneficiary = Step.Create("GetMoneyTransferBeneficiary ", async context =>
            {
                try
                {
                    var response = await httpClient.GetAsync($"/api/MoneyTransferMock/beneficiary/{settings.userId}");

                    Console.WriteLine($"GetMoneyTransferBeneficiary: {response.StatusCode}");

                    return response.IsSuccessStatusCode
                        ? Response.Ok()
                        : Response.Fail();
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    return Response.Fail();
                }

            });

            // Post User Block 
            //var stepPostUserBlock = Step.Create("PostUserBlock", async context =>
            //{
            //    try
            //    {

            //        var response = await httpClientPortal.PostAsync($"/api/User/block/{settings.userId}", null);

            //        Console.WriteLine($"PostUserBlock: {response.StatusCode}");

            //        return response.IsSuccessStatusCode
            //            ? Response.Ok()
            //            : Response.Fail();

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            // Post User UnBlock 
            //var stepPostUserUnBlock = Step.Create("PostUserUnBlock", async context =>
            //{
            //    try
            //    {

            //        var response = await httpClientPortal.PostAsync($"/api/User/unblock/{settings.userId}", null);

            //        Console.WriteLine($"PostUserUnBlock: {response.StatusCode}");

            //        return response.IsSuccessStatusCode
            //            ? Response.Ok()
            //            : Response.Fail();

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            // Post Subscription 
            //var stepPostSubscription = Step.Create("PostSubscription", async context =>
            //{
            //    try
            //    {
            //        var dto = new SubscribeDto()
            //        {
            //            Subscribe = settings.subscribe,
            //            UnSubscribe = settings.unSubscribe
            //        };

            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {

            //            var response = await httpClientPortal.PostAsync($"/api/Subscription/{settings.userId}", stringContent);

            //            Console.WriteLine($"PostSubscription: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});

            // Get Subscription by Id
            //var stepGetSubscriptionPortal = Step.Create("GetSubscription", async context =>
            //{
            //    try
            //    {

            //        var response = await httpClientPortal.GetAsync($"/api/Subscription/{settings.userId}");

            //        Console.WriteLine($"GetSubscription: {response.StatusCode}");

            //        return response.IsSuccessStatusCode
            //            ? Response.Ok()
            //            : Response.Fail();

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});

            // Updates - Search User
            //var stepPostUserUpdates = Step.Create("PostUserUpdates", async context =>
            //{
            //    try
            //    {
            //        var dto = new UpdateDto()
            //        {
            //            UpdatePageNo = settings.updatePageNo,
            //            UpdatePageSize = settings.updatePageSize

            //        };
            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {
            //            var response = await httpClientPortal.PostAsync($"/api/User/updates", stringContent);

            //            Console.WriteLine($"PostUserUpdates: {response.StatusCode}");
            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }
            //});
            // Rejections - Search User
            //var stepPostUserRejections = Step.Create("PostUserRejections", async context =>
            //{
            //    try
            //    {
            //        var dto = new RejectionDto()
            //        {
            //            RejectionPageNo = settings.rejectionPageNo,
            //            RejectionPageSize = settings.rejectionPageSize
            //        };
            //        var json = JsonSerializer.Serialize(dto);
            //        using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            //        {
            //            var response = await httpClientPortal.PostAsync($"/api/User/rejections", stringContent);

            //            Console.WriteLine($"PostUserRejections: {response.StatusCode}");

            //            return response.IsSuccessStatusCode
            //                ? Response.Ok()
            //                : Response.Fail();
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex);
            //        return Response.Fail();
            //    }

            //});


            //------------------------------------------------------------
            //Build scenarios
            //------------------------------------------------------------
            //Build Login page scenario
            var scenarioLoggin = ScenarioBuilder.CreateScenario("Loggin", stepGetUserByPhoneNumber
                                                                        , stepUserUpdateDevice
                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          
                                          .WithLoadSimulations(
                                                Simulation.InjectPerSec(rate: Convert.ToInt32(args[0].Split(':')[1]), during: TimeSpan.FromSeconds(loginDuration))
                                                );

            //Build Home page scenario
            var scenarioHomePage = ScenarioBuilder.CreateScenario("HomePage", stepGetTransactions
                                                                            , stepGetSubscriptions                                                      
                                                                           // , stepGetUserPopup
                                                                            , stepGetTransferDetail
                                                                            //   , stepGetFxRate
                                                                            , stepGetMoneyTransferCountries
                                                                            , stepMoneyTransferTransactions
                                                                            , stepMobileRechargeCountries
                                                                            , stepGetUserBalance
                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                    Simulation.InjectPerSec(rate: Convert.ToInt32(args[2].Split(':')[1]), during: TimeSpan.FromSeconds(HomePageDuration))
                                                );


            //Build Money Transfer scenario
            var scenarioMTBeneficiary = ScenarioBuilder.CreateScenario("MoneyTransferBeneficiary" , stepMTAddBenificiary

                                                                      )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                     Simulation.InjectPerSec(rate: 1, during: TimeSpan.FromSeconds(MoneyTransferBeneficiaryDuration))
                                                );


            //Build Mobile Recharge scenario
            var scenarioMobileRecharge = ScenarioBuilder.CreateScenario("MobileRecharge", stepGetMobileRechargeProductEstimate
                                                                                        , stepGetTransferDetail
                                                                                        , stepGetUserBalance
                                                                                        , stepGetFxRate                                                                                       
                                                                                        , stepMobileRechargeTransaction
                                                                                        , stepMobileRechargeCountries
                                                                                        , stepGetMobileRechargeInternationalCard
                                                                                        , stepMobileRechargeProducts
                                                                                        , stepMobileRechargeBenificiairy
                                                                                        , stepMobileRechargeReceipt
                                                                                        , stepPostMobilerechargeBeneficiaryDetailsIseligible

                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                     Simulation.InjectPerSec(rate: Convert.ToInt32(args[8].Split(':')[1]), during: TimeSpan.FromSeconds(MoneyTransferBeneficiaryDuration))
                                                );



            var scenarioMobileRechargeBenificiary = ScenarioBuilder.CreateScenario("MobileRechargeBenificiary"
                                                                                       , stepMRAddBenificiary

                                                                                           )
                                                                 .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                                                 .WithLoadSimulations(
                                                                             Simulation.InjectPerSec(rate: 1, during: TimeSpan.FromSeconds(MobileRechargeBenificiaryDuration))
                                                                       );

            //Build Signup scenario
            var scenarioSignup = ScenarioBuilder.CreateScenario("Signup"
                                                                        , stepSignup
                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                     Simulation.InjectPerSec(rate: Convert.ToInt32(args[6].Split(':')[1]), during: TimeSpan.FromSeconds(SignupDuration))
                                                );

            //Build RMT View free transfer Availability and see transfer status
            var scenarioRMTTransfer = ScenarioBuilder.CreateScenario("RMTTransfer", stepGetUserByPhoneNumber
                                                                                              //, stepGetUserPopup
                                                                                              , stepGetTransferDetail
                                                                                              , stepGetSubscriptions
                                                                                              , stepGetUserBalance
                                                                                              , stepGetMoneyTransferCountries
                                                                                              , stepGetMoneyTransferReasons
                                                                                              , stepGetFxRate
                                                                                              , stepMoneyTransferTransactions
                                                                                              , stepGetTransferLimit
                                                                                              , stepMoneyTransferReceipt
                                                                                              , stepGetMoneyTransferBankDetails
                                                                                              , stepGetMoneyTransferBeneficiary
                                                                                              , stepPostMoneyTransferSendMoney
                                                                                              //, stepPostMoneyTransferBeneficiariesTransaction
                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                     Simulation.InjectPerSec(rate: Convert.ToInt32(args[12].Split(':')[1]), during: TimeSpan.FromSeconds(RMTTransferDuration))
                                                );

            //Build Forgot Mobile Number Scenario
            var scenarioForgotMobileNumber = ScenarioBuilder.CreateScenario("ForgotMobileNumber", stepPostVerificationCard
                                                                                                , stepPostPasswordVerification
                                                                                                , stepPostEligabilityPhoneNumber
                                                                                                , stepGetUserSecurityQuestions
                                                                                                , stepPutUpdatePhoneNumber

                                                                    )
                                          .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                          .WithLoadSimulations(
                                                     Simulation.InjectPerSec(rate: Convert.ToInt32(args[14].Split(':')[1]), during: TimeSpan.FromSeconds(forgotPhoneNumberDuration))
                                                );
            //Build Forgot Password Scenario 
            var scenarioForgotPassword = ScenarioBuilder.CreateScenario("ForgotPassword", stepPostVerificationPhoneNo
                                                                                        , stepForgotPassword

                                                                   )
                                         .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                         .WithLoadSimulations(
                                                    Simulation.InjectPerSec(rate: Convert.ToInt32(args[16].Split(':')[1]), during: TimeSpan.FromSeconds(forgotPasswordDuration))
                                               );
            //Build Forgot Password Scenario 
            var scenarioATMPin = ScenarioBuilder.CreateScenario("ATMPin", stepViewATMPin
                                                                        , stepVerifyCVC2ATMPin

                                                                   )
                                         .WithWarmUpDuration(TimeSpan.FromSeconds(10))
                                         .WithLoadSimulations(
                                                    Simulation.InjectPerSec(rate: Convert.ToInt32(args[18].Split(':')[1]), during: TimeSpan.FromSeconds(atmPinDuration))
                                               );

            ////Build GetUserProfile scenario
            //var scenarioGetUserProfile = ScenarioBuilder.CreateScenario("GetUserProfile"
            //                                                        , stepGetUserProfile
            //                                                        , stepGetUserRMTProfileHistory
            //                                                        , stepPostMoneyTransferBeneficiaries
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );

            ////Build Search User scenario
            //var scenarioSearchUser = ScenarioBuilder.CreateScenario("SearchUser"                                                           
            //                                                         , stepPostUserAllUsers
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );



            ////Build Registration Verification Page scenario
            //var scenarioRegistrationVerificationPage = ScenarioBuilder.CreateScenario("RegistrationVerificationPage"
            //                                                          , stepGetUserCardStatus
            //                                                          , stepGetUserComments
            //                                                          , stepGetUserRegistration
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );
            ////Build Search RegistrationUser scenario
            //var scenarioSearchRegistrationUser = ScenarioBuilder.CreateScenario("SearchRegistrationUser"
            //                                                         , stepPostUserRegistrations
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );

            ////Build Update Search User scenario
            //var scenarioSearchUpdateUser = ScenarioBuilder.CreateScenario("SearchUpdateUser"
            //                                                         , stepPostUserUpdates
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );

            ////Build Rejection Search User scenario
            //var scenarioSearchRejectectionUser = ScenarioBuilder.CreateScenario("SearchRejectectionUser"
            //                                                         , stepPostUserRejections
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );

            ////Build User Block/UnBlock scenario
            //var scenarioUserBlockUnBlock = ScenarioBuilder.CreateScenario("UserBlockUnBlock"
            //                                                            , stepPostUserBlock
            //                                                            , stepPostUserUnBlock
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );
            ////Build User Subscribe/UnSubscribe scenario
            //var scenarioUserSubscribeUnSubscribe = ScenarioBuilder.CreateScenario("UserSubscribeUnSubscribe"
            //                                                            , stepPostSubscription
            //                                                            , stepGetSubscriptionPortal
            //                                                        )
            //                              .WithWarmUpDuration(TimeSpan.FromSeconds(10))
            //                              .WithLoadSimulations(
            //                                         Simulation.KeepConstant(copies: settings.requestRate, during: TimeSpan.FromSeconds(secondDuration))
            //                                    );


            //------------------------------------------------------------
            //Run scenarios
            //------------------------------------------------------------
            NBomberRunner.RegisterScenarios(
                                              scenarioLoggin
                                             , scenarioHomePage
                                             , scenarioMTBeneficiary
                                             , scenarioSignup
                                             , scenarioMobileRecharge
                                             , scenarioMobileRechargeBenificiary
                                             , scenarioRMTTransfer
                                             , scenarioForgotMobileNumber
                                             , scenarioForgotPassword
                                             , scenarioATMPin
                                            //, scenarioGetUserProfile
                                            //, scenarioSearchUser
                                            //, scenarioRegistrationVerificationPage
                                            //, scenarioSearchRegistrationUser
                                            //, scenarioUserBlockUnBlock
                                            //, scenarioUserSubscribeUnSubscribe
                                            //, scenarioSearchUpdateUser
                                            //, scenarioSearchRejectectionUser
                                            )
                         .Run();

            httpClient.Dispose();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="httpClient"></param>
        /// <param name="userId"></param>
        /// <param name="documentName"></param>
        /// <param name="documentType"></param>
        /// <param name="imageBase64"></param>
        /// <returns></returns>
        private static async Task<HttpResponseMessage> CallCheckImageQuality(HttpClient httpClient, string userId, string documentName, string documentType, string imageBase64)
        {
            var dto = new UploadAndCheckQualityRequestDto()
            {
                DocumentName = documentName,
                DocumentType = documentType,
                ImageBase64 = imageBase64
            };

            var json = JsonSerializer.Serialize(dto);
            using (var stringContent = new StringContent(json, Encoding.UTF8, "application/json"))
            {
                var response = await httpClient.PostAsync($"api/emiratesidv2/{userId}/image-quality", stringContent);

                return response;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="imgPath"></param>
        /// <returns></returns>
        //protected static string GetBase64StringForImage(string imgPath)
        //{
        //    byte[] imageBytes = System.IO.File.ReadAllBytes(imgPath);
        //    string base64String = Convert.ToBase64String(imageBytes);
        //    return base64String;
        //}
    }
}
