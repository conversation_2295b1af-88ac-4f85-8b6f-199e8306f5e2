﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideosUpdateMetadata : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                column: "Metadata",
                value: null);

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 2,
                column: "Metadata",
                value: "{\"primaryCtas\":[{\"style\":\"primary_blue_bg\",\"order\":1,\"stringResourceKey\":\"Ok,GotIt\",\"deeplink\":\"/c3payplus/insuranceVideo\"},{\"style\":\"primary_white_bg\",\"order\":2,\"stringResourceKey\":\"Ok, GotIt\",\"deeplink\":\"/c3payplus/landingPage\"}],\"ctaChangeLanguageLabel\":\"Change Language\",\"ctaChangeLanguageColor\":\"white\",\"ctaSecondaryLabel\":\"Skip\",\"ctaSecondaryLabelColor\":\"white\",\"ctaSecondaryDeepLink\":\"/dashboard\",\"timeOutSeconds\":3,\"mustWatchSeconds\":10,\"showSkipButton\":false,\"showSeekBarView\":true,\"showVideoLoopButton\":true,\"OnCompletionClose\":true}");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1,
                column: "Metadata",
                value: "{\"cta_primary_style\":\"blue_button\",\"cta_primary_deeplink\":\"/c3payplus/insuranceVideo\",\"cta_primary_label\":\"Ok, GotIt\",\"cta_change_language_label\":\"Change Language\",\"cta_secondary_label\":\"Skip\",\"cta_secondary_deeplink\":\"/dashboard\",\"time_out_seconds\":10,\"show_skip_button\":false,\"show_seek_bar_view\":true,\"show_video_replay_button\":true}");

            migrationBuilder.UpdateData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 2,
                column: "Metadata",
                value: null);
        }
    }
}
