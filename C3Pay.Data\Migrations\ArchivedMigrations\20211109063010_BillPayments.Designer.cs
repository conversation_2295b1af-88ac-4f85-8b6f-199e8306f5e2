﻿//// <auto-generated />
//using System;
//using C3Pay.Data;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.EntityFrameworkCore.Infrastructure;
//using Microsoft.EntityFrameworkCore.Metadata;
//using Microsoft.EntityFrameworkCore.Migrations;
//using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

//namespace C3Pay.Data.Migrations
//{
//    [DbContext(typeof(C3PayContext))]
//    [Migration("20211109063010_BillPayments")]
//    partial class BillPayments
//    {
//        protected override void BuildTargetModel(ModelBuilder modelBuilder)
//        {
//#pragma warning disable 612, 618
//            modelBuilder
//                .HasAnnotation("Relational:MaxIdentifierLength", 128)
//                .HasAnnotation("ProductVersion", "5.0.6")
//                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//            modelBuilder.Entity("C3Pay.Core.Models.AuditTrail", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("Action")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("ActionOn")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("IPAddress")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<Guid>("PortalUserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("PortalUserId");

//                    b.HasIndex("UserId");

//                    b.ToTable("AuditTrails");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.BlackListedEntity", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("CountryCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("EntityType")
//                        .HasColumnType("int");

//                    b.Property<string>("Identifier")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<bool>("IsActive")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("LastFailedAttemptTimeStamp")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.ToTable("BlackListedEntities");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProduct", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<decimal>("Amount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<bool>("BusinessDays")
//                        .HasColumnType("bit");

//                    b.Property<string>("Code")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Currency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<int>("DaysToPost")
//                        .HasColumnType("int");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasMaxLength(500)
//                        .HasColumnType("nvarchar(500)");

//                    b.Property<bool>("ExcessPaymentAllowed")
//                        .HasColumnType("bit");

//                    b.Property<bool>("InquiryAvailable")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<decimal>("MaxAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal>("MinAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<bool>("PartialPaymentAllowed")
//                        .HasColumnType("bit");

//                    b.Property<bool>("PastDuePaymentAllowed")
//                        .HasColumnType("bit");

//                    b.Property<int>("ProviderId")
//                        .HasColumnType("int");

//                    b.Property<string>("Type")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("ProviderId");

//                    b.ToTable("BillPaymentProducts");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProductIO", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DataType")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<int>("IOId")
//                        .HasColumnType("int");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<int>("MaxLength")
//                        .HasColumnType("int");

//                    b.Property<int>("MinLength")
//                        .HasColumnType("int");

//                    b.Property<string>("Name")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<int>("Operation")
//                        .HasColumnType("int");

//                    b.Property<int>("ProductId")
//                        .HasColumnType("int");

//                    b.Property<int>("Type")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("ProductId");

//                    b.ToTable("BillPaymentProductIOs");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProvider", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CatalogVersion")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("Code")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<string>("CountryCode")
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Name")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("Type")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.ToTable("BillPaymentProviders");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.Lookup.MoneyTransferPartnerReason", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CountryCode")
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<int>("MoneyTransferPartnerId")
//                        .HasColumnType("int");

//                    b.Property<int>("MoneyTransferReasonId")
//                        .HasColumnType("int");

//                    b.Property<string>("Reason")
//                        .IsRequired()
//                        .HasMaxLength(250)
//                        .HasColumnType("nvarchar(250)");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.HasIndex("MoneyTransferPartnerId");

//                    b.HasIndex("MoneyTransferReasonId");

//                    b.ToTable("MoneyTransferPartnerReasons");

//                    b.HasData(
//                        new
//                        {
//                            Id = 1,
//                            CountryCode = "BD",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "FAMILY MAINTENANCE"
//                        },
//                        new
//                        {
//                            Id = 2,
//                            CountryCode = "BD",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "EDUCATIONAL EXPENSES"
//                        },
//                        new
//                        {
//                            Id = 3,
//                            CountryCode = "BD",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "MEDICAL EXPENSE"
//                        },
//                        new
//                        {
//                            Id = 4,
//                            CountryCode = "BD",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "INSURANCE PAYMENT"
//                        },
//                        new
//                        {
//                            Id = 5,
//                            CountryCode = "PK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "FAMILY MAINTENANCE"
//                        },
//                        new
//                        {
//                            Id = 6,
//                            CountryCode = "PK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "EDUCATIONAL EXPENSES"
//                        },
//                        new
//                        {
//                            Id = 7,
//                            CountryCode = "PK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "MEDICAL EXPENSE"
//                        },
//                        new
//                        {
//                            Id = 8,
//                            CountryCode = "PK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "INSURANCE PAYMENT"
//                        },
//                        new
//                        {
//                            Id = 9,
//                            CountryCode = "IN",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS"
//                        },
//                        new
//                        {
//                            Id = 10,
//                            CountryCode = "IN",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "PAYMENT TO SCHOOLS AND COLLEGES"
//                        },
//                        new
//                        {
//                            Id = 11,
//                            CountryCode = "IN",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "PAYMENT FOR MEDICAL TREATMENT"
//                        },
//                        new
//                        {
//                            Id = 12,
//                            CountryCode = "IN",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS"
//                        },
//                        new
//                        {
//                            Id = 13,
//                            CountryCode = "PH",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "FAMILY MAINTENANCE"
//                        },
//                        new
//                        {
//                            Id = 14,
//                            CountryCode = "PH",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "EDUCATIONAL EXPENSES"
//                        },
//                        new
//                        {
//                            Id = 15,
//                            CountryCode = "PH",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "MEDICAL EXPENSE"
//                        },
//                        new
//                        {
//                            Id = 16,
//                            CountryCode = "PH",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "INSURANCE PAYMENT"
//                        },
//                        new
//                        {
//                            Id = 17,
//                            CountryCode = "NP",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "FAMILY MAINTENANCE"
//                        },
//                        new
//                        {
//                            Id = 18,
//                            CountryCode = "NP",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "EDUCATIONAL EXPENSES"
//                        },
//                        new
//                        {
//                            Id = 19,
//                            CountryCode = "NP",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "MEDICAL EXPENSE"
//                        },
//                        new
//                        {
//                            Id = 20,
//                            CountryCode = "NP",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "INSURANCE PAYMENT"
//                        },
//                        new
//                        {
//                            Id = 21,
//                            CountryCode = "LK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 8,
//                            Reason = "FAMILY MAINTENANCE"
//                        },
//                        new
//                        {
//                            Id = 22,
//                            CountryCode = "LK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 22,
//                            Reason = "EDUCATIONAL EXPENSES"
//                        },
//                        new
//                        {
//                            Id = 23,
//                            CountryCode = "LK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 24,
//                            Reason = "MEDICAL EXPENSE"
//                        },
//                        new
//                        {
//                            Id = 24,
//                            CountryCode = "LK",
//                            MoneyTransferPartnerId = 2,
//                            MoneyTransferReasonId = 21,
//                            Reason = "INSURANCE PAYMENT"
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferCorridor", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<decimal>("BankTransferLatestRate")
//                        .HasPrecision(18, 4)
//                        .HasColumnType("decimal(18,4)");

//                    b.Property<decimal?>("BankTransferMaxAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("BankTransferMaxMonthlyAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<int?>("BankTransferMaxMonthlyCount")
//                        .HasColumnType("int");

//                    b.Property<decimal?>("BankTransferMinAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<DateTime?>("BankTransferRateLastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<decimal>("CashPickUpLatestRate")
//                        .HasPrecision(18, 4)
//                        .HasColumnType("decimal(18,4)");

//                    b.Property<decimal?>("CashPickUpMaxAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("CashPickUpMaxMonthlyAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<int?>("CashPickUpMaxMonthlyCount")
//                        .HasColumnType("int");

//                    b.Property<decimal?>("CashPickUpMinAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<DateTime?>("CashPickUpRateLastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("CountryCode")
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<bool>("EligibleForBankTransfer")
//                        .HasColumnType("bit");

//                    b.Property<bool>("EligibleForCashPickUp")
//                        .HasColumnType("bit");

//                    b.Property<decimal>("Fee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<bool>("IsActive")
//                        .HasColumnType("bit");

//                    b.Property<bool>("MoneyTransferEnabled")
//                        .HasColumnType("bit");

//                    b.Property<int>("MoneyTransferPartnerId")
//                        .HasColumnType("int");

//                    b.Property<string>("Name")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<decimal>("VAT")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.HasIndex("MoneyTransferPartnerId");

//                    b.ToTable("MoneyTransferCorridors");

//                    b.HasData(
//                        new
//                        {
//                            Id = 1,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 15000m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CountryCode = "BD",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = false,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        },
//                        new
//                        {
//                            Id = 2,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 15000m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CashPickUpMaxAmount = 15000m,
//                            CashPickUpMaxMonthlyAmount = 30000m,
//                            CashPickUpMaxMonthlyCount = 7,
//                            CashPickUpMinAmount = 25m,
//                            CountryCode = "PK",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = true,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        },
//                        new
//                        {
//                            Id = 3,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 10000m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CountryCode = "IN",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = false,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        },
//                        new
//                        {
//                            Id = 4,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 29900m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CashPickUpMaxAmount = 3600m,
//                            CashPickUpMaxMonthlyAmount = 30000m,
//                            CashPickUpMaxMonthlyCount = 7,
//                            CashPickUpMinAmount = 25m,
//                            CountryCode = "PH",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = true,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        },
//                        new
//                        {
//                            Id = 5,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 15000m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CashPickUpMaxAmount = 3000m,
//                            CashPickUpMaxMonthlyAmount = 30000m,
//                            CashPickUpMaxMonthlyCount = 7,
//                            CashPickUpMinAmount = 25m,
//                            CountryCode = "NP",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = true,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        },
//                        new
//                        {
//                            Id = 6,
//                            BankTransferLatestRate = 0m,
//                            BankTransferMaxAmount = 19823m,
//                            BankTransferMaxMonthlyAmount = 30000m,
//                            BankTransferMaxMonthlyCount = 7,
//                            BankTransferMinAmount = 25m,
//                            CashPickUpLatestRate = 0m,
//                            CashPickUpMaxAmount = 15000m,
//                            CashPickUpMaxMonthlyAmount = 30000m,
//                            CashPickUpMaxMonthlyCount = 7,
//                            CashPickUpMinAmount = 25m,
//                            CountryCode = "LK",
//                            EligibleForBankTransfer = true,
//                            EligibleForCashPickUp = true,
//                            Fee = 0m,
//                            IsActive = true,
//                            MoneyTransferEnabled = true,
//                            MoneyTransferPartnerId = 2,
//                            VAT = 0m
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Name")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<int>("Type")
//                        .HasColumnType("int");

//                    b.HasKey("Id");

//                    b.ToTable("MoneyTransferPartners");

//                    b.HasData(
//                        new
//                        {
//                            Id = 1,
//                            Name = "RAK Bank",
//                            Type = 1
//                        },
//                        new
//                        {
//                            Id = 2,
//                            Name = "Index",
//                            Type = 2
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferProfileModel", b =>
//                {
//                    b.Property<string>("CorporateId")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("CorporateName")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("EmiratesId")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("FileName")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("FullName")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("PhoneNumber")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<string>("Status")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<int>("TotalCount")
//                        .HasColumnType("int");

//                    b
//                        .HasAnnotation("Relational:SqlQuery", "GetMoneyTransferProfile");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.CardHolder", b =>
//                {
//                    b.Property<string>("Id")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<bool?>("BelongsToExchangeHouse")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("Birthdate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("C3RegistrationId")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("CardNumber")
//                        .HasMaxLength(16)
//                        .HasColumnType("nvarchar(16)");

//                    b.Property<string>("CardSerialNumber")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("City")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("CorporateId")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("CorporateName")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("EmiratesId")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<DateTime?>("EmiratesIdExpiryDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("EmployeeId")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("EmployeeStatus")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("FirstName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("Gender")
//                        .HasMaxLength(6)
//                        .HasColumnType("int");

//                    b.Property<string>("LastName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("Nationality")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<string>("PassportNumber")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("ZipCode")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.HasKey("Id");

//                    b.HasIndex("CardSerialNumber");

//                    b.HasIndex("CorporateId");

//                    b.HasIndex("EmiratesId");

//                    b.HasIndex("FirstName");

//                    b.HasIndex("LastName");

//                    b.ToTable("CardHolders");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Country", b =>
//                {
//                    b.Property<string>("Code")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<decimal>("BankTransferLatestRate")
//                        .HasPrecision(18, 4)
//                        .HasColumnType("decimal(18,4)");

//                    b.Property<decimal>("CashPickUpLatestRate")
//                        .HasPrecision(18, 4)
//                        .HasColumnType("decimal(18,4)");

//                    b.Property<string>("CashPickUpProvider")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("CashPickUpProviderLocationsURL")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("Code3")
//                        .IsRequired()
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<string>("Currency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<int?>("DisplayOrder")
//                        .HasColumnType("int");

//                    b.Property<bool>("EligibleForBankTransfer")
//                        .HasColumnType("bit");

//                    b.Property<bool>("EligibleForCashPickUp")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsPapularCountry")
//                        .HasColumnType("bit");

//                    b.Property<string>("LongName")
//                        .IsRequired()
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("MobileRechargeEnabled")
//                        .HasColumnType("bit");

//                    b.Property<bool>("MobileRechargeEnabledForPartner")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("MobileRechargeLastSynchronizedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("MoneyTransferEnabled")
//                        .HasColumnType("bit");

//                    b.Property<string>("Name")
//                        .IsRequired()
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("RatesLastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("STDCode")
//                        .HasMaxLength(8)
//                        .HasColumnType("nvarchar(8)");

//                    b.HasKey("Code");

//                    b.ToTable("Countries");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Document", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Name")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<int>("Type")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("UserId");

//                    b.ToTable("Documents");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.EmiratesIdUpdate", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("BackScanFileName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<DateTime>("Birthdate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("CardNumber")
//                        .HasMaxLength(9)
//                        .HasColumnType("nvarchar(9)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("ExpiryDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("FaceMatchIsIdeal")
//                        .HasColumnType("bit");

//                    b.Property<decimal>("FaceMatchPercentage")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("FirstName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("FrontScanFileName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("Gender")
//                        .HasMaxLength(6)
//                        .HasColumnType("int");

//                    b.Property<string>("IdNumber")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("LastName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("NameMatchIsIdeal")
//                        .HasColumnType("bit");

//                    b.Property<decimal>("NameMatchPercentage")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("Nationality")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<int?>("OriginalVerifierId")
//                        .HasColumnType("int");

//                    b.Property<string>("PostRemarks")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<bool>("Posted")
//                        .HasColumnType("bit");

//                    b.Property<string>("SelfieFileName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("ServiceReference")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("UpdateType")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<int>("Vendor")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("VerificationDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("VerificationStatus")
//                        .HasColumnType("int");

//                    b.Property<Guid?>("VerifierId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("UserId");

//                    b.HasIndex("VerifierId");

//                    b.ToTable("EmiratesIdUpdates");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("AccountNumber")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<string>("CountryCode")
//                        .IsRequired()
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("NickName")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<int>("RechargeType")
//                        .HasColumnType("int");

//                    b.Property<string>("Remarks")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<int>("Status")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("AccountNumber");

//                    b.HasIndex("CountryCode");

//                    b.HasIndex("CreatedDate");

//                    b.HasIndex("NickName");

//                    b.HasIndex("UserId");

//                    b.ToTable("MobileRechargeBeneficiaries");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiaryProvider", b =>
//                {
//                    b.Property<Guid>("BeneficiaryId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<string>("ProviderCode")
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool?>("IsActive")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("BeneficiaryId", "ProviderCode");

//                    b.HasIndex("ProviderCode");

//                    b.ToTable("MobileRechargeBeneficiaryProviders");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeExternalTransaction", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("AccountNumber")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<string>("CallingCardPin")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<decimal?>("CommisionRate")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("CustomerFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("DistributorFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<DateTime?>("EndDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ExternalStatus")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("ExternalTransactionId")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<DateTime?>("LastStatusDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("MobileRechargeTransactionId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<string>("ReceiptParams")
//                        .HasMaxLength(300)
//                        .HasColumnType("nvarchar(300)");

//                    b.Property<string>("ReceiptText")
//                        .HasMaxLength(300)
//                        .HasColumnType("nvarchar(300)");

//                    b.Property<decimal>("ReceiveAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("ReceiveAmountWithTax")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("ReceiveCurrency")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal>("SendAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("SendCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<DateTime?>("StartDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("StatusDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("StatusDescription")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("TaxCalculation")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("TaxName")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<decimal?>("TaxRate")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.HasKey("Id");

//                    b.HasIndex("ExternalTransactionId");

//                    b.HasIndex("MobileRechargeTransactionId")
//                        .IsUnique();

//                    b.ToTable("MobileRechargeExternalTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
//                {
//                    b.Property<string>("Code")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("AdditionalInformation")
//                        .HasMaxLength(500)
//                        .HasColumnType("nvarchar(500)");

//                    b.Property<string>("Benefits")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<decimal?>("CommissionRate")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("CountryCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<decimal?>("CustomFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("DefaultDisplayText")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool?>("IsActive")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<DateTime?>("LastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("LocalizationKey")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<bool?>("LookupBillsRequired")
//                        .HasColumnType("bit");

//                    b.Property<decimal?>("MaxCustomerFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("MaxDistributorFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MaxReceiveCurrencyIso")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MaxReceiveValue")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("MaxReceiveValueExcludingTax")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MaxSendCurrencyIso")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MaxSendValue")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MaxTaxCalculation")
//                        .HasMaxLength(25)
//                        .HasColumnType("nvarchar(25)");

//                    b.Property<string>("MaxTaxName")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MaxTaxRate")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("MinCustomerFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("MinDistributorFee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MinReceiveCurrencyIso")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MinReceiveValue")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("MinReceiveValueExcludingTax")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MinSendCurrencyIso")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MinSendValue")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("MinTaxCalculation")
//                        .HasMaxLength(25)
//                        .HasColumnType("nvarchar(25)");

//                    b.Property<string>("MinTaxName")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("MinTaxRate")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("ProcessingMode")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("ProviderCode")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("RedemptionMechanism")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<string>("RegionCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<string>("UATNumber")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<string>("ValidityPeriodIso")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.HasKey("Code");

//                    b.HasIndex("CountryCode");

//                    b.HasIndex("ProviderCode");

//                    b.ToTable("MobileRechargeProducts");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
//                {
//                    b.Property<string>("Code")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("CountryCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<string>("CustomerCareNumber")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<bool?>("IsActive")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<bool?>("IsProcessingTransfers")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<DateTime?>("LastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("LogoUrl")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("Name")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("PaymentTypes")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("ProcessStatusMessage")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("RegionCodes")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<string>("ShortName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("ValidationRegex")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.HasKey("Code");

//                    b.HasIndex("CountryCode");

//                    b.ToTable("MobileRechargeProviders");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeSupportedCountry", b =>
//                {
//                    b.Property<string>("CountryCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<string>("Code")
//                        .HasMaxLength(5)
//                        .HasColumnType("nvarchar(5)");

//                    b.Property<bool>("IsActive")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("LastUpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Name")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.HasKey("CountryCode");

//                    b.ToTable("MobileRechargeSupportedCountries");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("AccountNumber")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<Guid?>("BeneficiaryId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<decimal?>("Fee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("ProductCode")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<decimal?>("ReceiveAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("ReceiveCurrency")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<int?>("RechargeType")
//                        .HasColumnType("int");

//                    b.Property<string>("ReferenceNumber")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("Remarks")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<decimal>("SendAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("SendCurrency")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<int>("Status")
//                        .HasColumnType("int");

//                    b.Property<decimal>("TotalAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<int?>("TriesCount")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("BeneficiaryId");

//                    b.HasIndex("CreatedDate");

//                    b.HasIndex("ProductCode");

//                    b.HasIndex("ReferenceNumber")
//                        .IsUnique()
//                        .HasFilter("[ReferenceNumber] IS NOT NULL");

//                    b.HasIndex("UserId");

//                    b.ToTable("MobileRechargeTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("AccountNumber")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("Address1")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("Address2")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<DateTime?>("ApprovalDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("BankBranchName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("BankName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("CountryCode")
//                        .IsRequired()
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DocumentNumber")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<string>("DocumentType")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<string>("FirstName")
//                        .IsRequired()
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("IdentifierCode1")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("IdentifierCode2")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("LastName")
//                        .IsRequired()
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("LastTransferReferenceNumber")
//                        .HasMaxLength(250)
//                        .HasColumnType("nvarchar(250)");

//                    b.Property<string>("MiddleName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int?>("MoneyTransferReasonId")
//                        .IsRequired()
//                        .HasColumnType("int");

//                    b.Property<string>("NationalityCode")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("PhoneNumber")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<DateTime?>("PostDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("Posted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Remarks")
//                        .HasMaxLength(500)
//                        .HasColumnType("nvarchar(500)");

//                    b.Property<bool>("RequiresApproval")
//                        .HasColumnType("bit");

//                    b.Property<int>("Status")
//                        .HasColumnType("int");

//                    b.Property<string>("StatusDescription")
//                        .HasMaxLength(500)
//                        .HasColumnType("nvarchar(500)");

//                    b.Property<int>("TransferType")
//                        .HasMaxLength(30)
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.HasIndex("CreatedDate");

//                    b.HasIndex("FirstName");

//                    b.HasIndex("LastName");

//                    b.HasIndex("MoneyTransferReasonId");

//                    b.HasIndex("UserId");

//                    b.ToTable("MoneyTransferBeneficiaries");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid?>("CreationMessageId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid?>("DeletionMessageId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<string>("ExternalId")
//                        .IsRequired()
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<Guid>("MoneyTransferBeneficiaryId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("MoneyTransferBeneficiaryId")
//                        .IsUnique();

//                    b.ToTable("MoneyTransferExternalBeneficiaries");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalTransaction", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<decimal>("ChargeVat")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("ChargesAmount")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.Property<string>("ChargesCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("CreditCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<string>("DebitCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime>("EndDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ExternalStatus")
//                        .IsRequired()
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("ExternalTransactionId")
//                        .HasMaxLength(30)
//                        .HasColumnType("nvarchar(30)");

//                    b.Property<decimal?>("FxRate")
//                        .HasPrecision(18, 4)
//                        .HasColumnType("decimal(18,4)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("LastStatusDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid?>("MessageId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<Guid>("MoneyTransferTransactionId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime>("StartDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("StatusDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("StatusDescription")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<decimal?>("TotalCharges")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.Property<decimal?>("TotalCreditAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("TotalDebitAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("Type")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<decimal?>("WaivedCharge")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.HasKey("Id");

//                    b.HasIndex("ExternalTransactionId");

//                    b.HasIndex("MoneyTransferTransactionId")
//                        .IsUnique();

//                    b.ToTable("MoneyTransferExternalTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferLimit", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("CountryCode")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool?>("IsActive")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("bit")
//                        .HasDefaultValue(true);

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode")
//                        .IsUnique()
//                        .HasFilter("[CountryCode] IS NOT NULL");

//                    b.ToTable("MoneyTransferLimits");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferProfile", b =>
//                {
//                    b.Property<string>("EmiratesId")
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<string>("BatchId")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DataFilename")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("FileDate")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("FullName")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Status")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("EmiratesId");

//                    b.ToTable("MoneyTransferProfiles");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferReason", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Reason")
//                        .IsRequired()
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.HasKey("Id");

//                    b.ToTable("MoneyTransferReasons");

//                    b.HasData(
//                        new
//                        {
//                            Id = 8,
//                            Reason = "Family maintenance and savings"
//                        },
//                        new
//                        {
//                            Id = 21,
//                            Reason = "Personal or other travel"
//                        },
//                        new
//                        {
//                            Id = 22,
//                            Reason = "Education services"
//                        },
//                        new
//                        {
//                            Id = 24,
//                            Reason = "Medical or health"
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<decimal>("ChargeVat")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<decimal?>("ChargesAmount")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.Property<string>("ChargesCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<decimal?>("ConversionRate")
//                        .HasPrecision(18, 6)
//                        .HasColumnType("decimal(18,6)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<Guid>("MoneyTransferBeneficiaryId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<int?>("MoneyTransferReasonId")
//                        .IsRequired()
//                        .HasColumnType("int");

//                    b.Property<decimal>("ReceiveAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("ReceiveCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<string>("ReferenceNumber")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("ReferralCode")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("Remarks")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<decimal>("SendAmount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("SendCurrency")
//                        .HasMaxLength(3)
//                        .HasColumnType("nvarchar(3)");

//                    b.Property<int>("Status")
//                        .HasColumnType("int");

//                    b.Property<decimal?>("TotalCharges")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.Property<int>("TransferType")
//                        .HasColumnType("int");

//                    b.Property<int?>("TriesCount")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid?>("UserId")
//                        .IsRequired()
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<int>("WaiveType")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasDefaultValue(1);

//                    b.Property<decimal?>("WaivedCharge")
//                        .ValueGeneratedOnAdd()
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)")
//                        .HasDefaultValue(0m);

//                    b.HasKey("Id");

//                    b.HasIndex("CreatedDate");

//                    b.HasIndex("MoneyTransferBeneficiaryId");

//                    b.HasIndex("MoneyTransferReasonId");

//                    b.HasIndex("ReferenceNumber")
//                        .IsUnique()
//                        .HasFilter("[ReferenceNumber] IS NOT NULL");

//                    b.HasIndex("ReferralCode");

//                    b.HasIndex("UserId");

//                    b.ToTable("MoneyTransferTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Popup", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("CountryCode")
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("ImageFileName")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<int>("Order")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("Visible")
//                        .HasColumnType("bit");

//                    b.HasKey("Id");

//                    b.HasIndex("CountryCode");

//                    b.ToTable("Popups");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.PortalUser", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Email")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<bool>("RequiresPasswordReset")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Username")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.HasKey("Id");

//                    b.ToTable("PortalUsers");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.ReferrerCode", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("Code")
//                        .IsRequired()
//                        .HasMaxLength(6)
//                        .HasColumnType("nvarchar(6)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Name")
//                        .IsRequired()
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.ToTable("ReferrerCodes");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SecretAnswer", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("Answer")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("SecurityQuestionId")
//                        .HasColumnType("int");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("SecurityQuestionId");

//                    b.HasIndex("UserId");

//                    b.ToTable("SecretAnswers");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SecurityQuestion", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Question")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.HasKey("Id");

//                    b.ToTable("SecurityQuestions");

//                    b.HasData(
//                        new
//                        {
//                            Id = 1,
//                            Question = "What is your mother's first name?"
//                        },
//                        new
//                        {
//                            Id = 2,
//                            Question = "What is your father's first name?"
//                        },
//                        new
//                        {
//                            Id = 3,
//                            Question = "What was the name of your school?"
//                        },
//                        new
//                        {
//                            Id = 4,
//                            Question = "What is the first name of your first child?"
//                        },
//                        new
//                        {
//                            Id = 5,
//                            Question = "In what city or town was your first job?"
//                        },
//                        new
//                        {
//                            Id = 6,
//                            Question = "Who was your childhood's sports hero?"
//                        },
//                        new
//                        {
//                            Id = 7,
//                            Question = "What is the first name of your best friend in school?"
//                        },
//                        new
//                        {
//                            Id = 8,
//                            Question = "What was the name of the company where you had your first job?"
//                        },
//                        new
//                        {
//                            Id = 9,
//                            Question = "What is your wife or husband's first name?"
//                        },
//                        new
//                        {
//                            Id = 10,
//                            Question = "What was your first car?"
//                        },
//                        new
//                        {
//                            Id = 11,
//                            Question = "What is your eldest brother or sister's name?"
//                        },
//                        new
//                        {
//                            Id = 12,
//                            Question = "What is your youngest brother or sister's name?"
//                        },
//                        new
//                        {
//                            Id = 13,
//                            Question = "What was the name of your favorite school teacher?"
//                        },
//                        new
//                        {
//                            Id = 14,
//                            Question = "What was your father's last job?"
//                        },
//                        new
//                        {
//                            Id = 15,
//                            Question = "What city did you fly for the first time by plane?"
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Subscription", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<string>("Code")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("DisplayOrder")
//                        .HasColumnType("int");

//                    b.Property<decimal>("Fee")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("FullDescription")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<bool>("IsAvailable")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<int>("PaymentFrequency")
//                        .HasColumnType("int");

//                    b.Property<string>("ShortDescription")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("Title")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.ToTable("Subscriptions");

//                    b.HasData(
//                        new
//                        {
//                            Id = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"),
//                            Code = "BE",
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            DisplayOrder = 1,
//                            Fee = 1m,
//                            FullDescription = "View your current balance, save on ATM balance enquiry fees and view your current balance at any time as often as you want on this app",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            PaymentFrequency = 1,
//                            ShortDescription = "View your current balance at any time.",
//                            Title = "Balance Enquiry"
//                        },
//                        new
//                        {
//                            Id = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"),
//                            Code = "T",
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            DisplayOrder = 2,
//                            Fee = 3m,
//                            FullDescription = "Secure your card from being used without your knowledge. Keep your account safe by getting an SMS whenever:",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            PaymentFrequency = 1,
//                            ShortDescription = "Receive an SMS after every transaction.",
//                            Title = "Security Alerts"
//                        },
//                        new
//                        {
//                            Id = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"),
//                            Code = "S",
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            DisplayOrder = 3,
//                            Fee = 0.5m,
//                            FullDescription = "Receive an SMS as soon as your salary is deposited into your account:",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            PaymentFrequency = 2,
//                            ShortDescription = "Receive an SMS with your salary.",
//                            Title = "SMS Salary Alert"
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SubscriptionFeature", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Description")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<bool>("IsAvailable")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<Guid>("SubscriptionId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.HasKey("Id");

//                    b.HasIndex("SubscriptionId");

//                    b.ToTable("SubscriptionFeatures");

//                    b.HasData(
//                        new
//                        {
//                            Id = new Guid("94b6c134-5e52-4019-acb1-9c0884c28e31"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "View your balance",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
//                        },
//                        new
//                        {
//                            Id = new Guid("596a0b4c-9c68-48ab-b574-4a8fc3431edd"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "View your transactions",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
//                        },
//                        new
//                        {
//                            Id = new Guid("170fd6ae-7c74-4604-bc5e-f0f79ea6398a"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Anytime & anywhere",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
//                        },
//                        new
//                        {
//                            Id = new Guid("3e569303-038e-413e-b3e2-3dfe3f3b2b76"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Avoid fees on ATMs",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
//                        },
//                        new
//                        {
//                            Id = new Guid("38f76250-0e8b-4985-adb1-7852368d6884"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "You receive your salary",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
//                        },
//                        new
//                        {
//                            Id = new Guid("09fb418d-6219-4c5e-87df-990c8b050919"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used at the ATM",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
//                        },
//                        new
//                        {
//                            Id = new Guid("0156f81c-a76f-43e8-9a95-b8244c6cd725"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used in shops",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
//                        },
//                        new
//                        {
//                            Id = new Guid("36f0989d-ddf5-474c-b18c-e9343d35edb5"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used online",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
//                        },
//                        new
//                        {
//                            Id = new Guid("5a35e788-6c84-4d40-9d12-03490902a21e"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "You receive your salary",
//                            IsAvailable = true,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
//                        },
//                        new
//                        {
//                            Id = new Guid("76990d20-b275-4ba6-8b99-d9cc27205421"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used at the ATM",
//                            IsAvailable = false,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
//                        },
//                        new
//                        {
//                            Id = new Guid("7e14e718-7981-4bae-a08f-f146a5408642"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used in shops",
//                            IsAvailable = false,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
//                        },
//                        new
//                        {
//                            Id = new Guid("f760a7bf-3f2c-45be-9f23-0bb3e0d96e50"),
//                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
//                            Description = "Your card is used online",
//                            IsAvailable = false,
//                            IsDeleted = false,
//                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
//                        });
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
//                {
//                    b.Property<string>("ReferenceNumber")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<decimal?>("Amount")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<string>("AuthenticationCode")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("BillPayType")
//                        .HasMaxLength(2)
//                        .HasColumnType("nvarchar(2)");

//                    b.Property<string>("CardAccountId")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("CardAccountTerminalAddress")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<string>("CardAccountTerminalId")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("CardNumber")
//                        .HasMaxLength(16)
//                        .HasColumnType("nvarchar(16)");

//                    b.Property<string>("CardSerialNumber")
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Date")
//                        .IsRequired()
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<decimal?>("EndBalance")
//                        .HasPrecision(18, 2)
//                        .HasColumnType("decimal(18,2)");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("MacValue")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<string>("Origin")
//                        .HasMaxLength(20)
//                        .HasColumnType("nvarchar(20)");

//                    b.Property<string>("PayeeId")
//                        .HasMaxLength(5)
//                        .HasColumnType("nvarchar(5)");

//                    b.Property<string>("RelativeSequenceNumber")
//                        .HasMaxLength(40)
//                        .HasColumnType("nvarchar(40)");

//                    b.Property<int>("ServiceProvider")
//                        .HasColumnType("int");

//                    b.Property<string>("StatusCode")
//                        .IsRequired()
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<string>("StatusDescription")
//                        .IsRequired()
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<string>("Time")
//                        .IsRequired()
//                        .HasMaxLength(10)
//                        .HasColumnType("nvarchar(10)");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("ReferenceNumber");

//                    b.HasIndex("UserId");

//                    b.ToTable("Transactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.UploadedDocument", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<string>("Name")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("Type")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("Url")
//                        .HasMaxLength(200)
//                        .HasColumnType("nvarchar(200)");

//                    b.Property<Guid?>("UserId")
//                        .IsRequired()
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("UserId");

//                    b.ToTable("UploadedDocuments");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime?>("ATMPinBlockEndDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int>("ApplicationId")
//                        .HasColumnType("int");

//                    b.Property<DateTime?>("BlockDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int?>("BlockType")
//                        .HasColumnType("int");

//                    b.Property<string>("CardHolderId")
//                        .IsRequired()
//                        .HasMaxLength(15)
//                        .HasColumnType("nvarchar(15)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeviceToken")
//                        .HasMaxLength(300)
//                        .HasColumnType("nvarchar(300)");

//                    b.Property<string>("Email")
//                        .HasMaxLength(100)
//                        .HasColumnType("nvarchar(100)");

//                    b.Property<int>("ExternalId")
//                        .HasColumnType("int");

//                    b.Property<bool>("IsBlocked")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<bool>("IsVerified")
//                        .HasColumnType("bit");

//                    b.Property<int>("MoneyTransferProfileStatus")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasDefaultValue(1);

//                    b.Property<string>("PhoneNumber")
//                        .HasMaxLength(14)
//                        .HasColumnType("nvarchar(14)");

//                    b.Property<string>("ReferralCode")
//                        .HasMaxLength(5)
//                        .HasColumnType("nvarchar(5)");

//                    b.Property<bool>("RequiresPasswordReset")
//                        .HasColumnType("bit");

//                    b.Property<DateTime?>("UnblockDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<int?>("UserBlockReasonId")
//                        .HasColumnType("int");

//                    b.HasKey("Id");

//                    b.HasIndex("CardHolderId");

//                    b.HasIndex("CreatedDate");

//                    b.HasIndex("ExternalId");

//                    b.HasIndex("PhoneNumber");

//                    b.HasIndex("ReferralCode");

//                    b.HasIndex("UserBlockReasonId");

//                    b.ToTable("Users");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.UserBlockReason", b =>
//                {
//                    b.Property<int>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("int")
//                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                    b.Property<string>("Name")
//                        .HasMaxLength(250)
//                        .HasColumnType("nvarchar(250)");

//                    b.HasKey("Id");

//                    b.ToTable("UserBlockReasons");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.VerificationComment", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<string>("Comment")
//                        .HasColumnType("nvarchar(max)");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<int?>("OriginalAdminId")
//                        .HasColumnType("int");

//                    b.Property<Guid?>("PortalUserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("PortalUserId");

//                    b.HasIndex("UserId");

//                    b.ToTable("VerificationComments");
//                });

//            modelBuilder.Entity("C3Pay.Core.UserSubscription", b =>
//                {
//                    b.Property<Guid>("Id")
//                        .ValueGeneratedOnAdd()
//                        .HasColumnType("uniqueidentifier")
//                        .HasDefaultValueSql("NEWSEQUENTIALID()");

//                    b.Property<DateTime>("CreatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<string>("DeletedBy")
//                        .HasMaxLength(50)
//                        .HasColumnType("nvarchar(50)");

//                    b.Property<DateTime?>("DeletedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<DateTime?>("EndDate")
//                        .HasColumnType("datetime2");

//                    b.Property<bool>("IsDeleted")
//                        .HasColumnType("bit");

//                    b.Property<bool>("PaymentWaved")
//                        .HasColumnType("bit");

//                    b.Property<DateTime>("StartDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("SubscriptionId")
//                        .HasColumnType("uniqueidentifier");

//                    b.Property<DateTime?>("UpdatedDate")
//                        .HasColumnType("datetime2");

//                    b.Property<Guid>("UserId")
//                        .HasColumnType("uniqueidentifier");

//                    b.HasKey("Id");

//                    b.HasIndex("SubscriptionId");

//                    b.HasIndex("UserId");

//                    b.ToTable("UserSubscriptions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.AuditTrail", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
//                        .WithMany("AuditTrails")
//                        .HasForeignKey("PortalUserId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("AuditTrails")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("PortalUser");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.BlackListedEntity", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany("BlackListedEntities")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict);

//                    b.Navigation("Country");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProduct", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProvider", "Provider")
//                        .WithMany("Products")
//                        .HasForeignKey("ProviderId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Provider");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProductIO", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProduct", "Product")
//                        .WithMany("IOs")
//                        .HasForeignKey("ProductId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Product");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProvider", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany("BillPaymentProviders")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict);

//                    b.Navigation("Country");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.Lookup.MoneyTransferPartnerReason", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany()
//                        .HasForeignKey("CountryCode");

//                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", "MoneyTransferPartner")
//                        .WithMany()
//                        .HasForeignKey("MoneyTransferPartnerId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
//                        .WithMany()
//                        .HasForeignKey("MoneyTransferReasonId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Country");

//                    b.Navigation("MoneyTransferPartner");

//                    b.Navigation("MoneyTransferReason");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferCorridor", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany()
//                        .HasForeignKey("CountryCode");

//                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", "MoneyTransferPartner")
//                        .WithMany("MoneyTransferCorridors")
//                        .HasForeignKey("MoneyTransferPartnerId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Country");

//                    b.Navigation("MoneyTransferPartner");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Document", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("Documents")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.EmiratesIdUpdate", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("EmiratesIdUpdates")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
//                        .WithMany("VerifiedEmiratesIdUpdates")
//                        .HasForeignKey("VerifierId");

//                    b.Navigation("PortalUser");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany("MobileRechargeBeneficiaries")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("MobileRechargeBeneficiaries")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("Country");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiaryProvider", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MobileRechargeBeneficiary", "Beneficiary")
//                        .WithMany("BeneficiaryProviders")
//                        .HasForeignKey("BeneficiaryId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.MobileRechargeProvider", "Provider")
//                        .WithMany("BeneficiaryProviders")
//                        .HasForeignKey("ProviderCode")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Beneficiary");

//                    b.Navigation("Provider");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeExternalTransaction", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MobileRechargeTransaction", "MobileRechargeTransaction")
//                        .WithOne("ExternalTransaction")
//                        .HasForeignKey("C3Pay.Core.Models.MobileRechargeExternalTransaction", "MobileRechargeTransactionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("MobileRechargeTransaction");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MobileRechargeSupportedCountry", "SupportedCountry")
//                        .WithMany("Products")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.MobileRechargeProvider", "Provider")
//                        .WithMany("Products")
//                        .HasForeignKey("ProviderCode")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Provider");

//                    b.Navigation("SupportedCountry");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MobileRechargeSupportedCountry", "SupportedCountry")
//                        .WithMany("Providers")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("SupportedCountry");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MobileRechargeBeneficiary", "Beneficiary")
//                        .WithMany("MobileRechargeTransactions")
//                        .HasForeignKey("BeneficiaryId")
//                        .OnDelete(DeleteBehavior.Restrict);

//                    b.HasOne("C3Pay.Core.Models.MobileRechargeProduct", "Product")
//                        .WithMany("MobileRechargeTransactions")
//                        .HasForeignKey("ProductCode")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
//                        .WithOne("MobileRechargeTransaction")
//                        .HasForeignKey("C3Pay.Core.Models.MobileRechargeTransaction", "ReferenceNumber");

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("MobileRechargeTransactions")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("Beneficiary");

//                    b.Navigation("Product");

//                    b.Navigation("Transaction");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany("MoneyTransferBeneficiaries")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
//                        .WithMany("MoneyTransferBeneficiaries")
//                        .HasForeignKey("MoneyTransferReasonId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("MoneyTransferBeneficiaries")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("Country");

//                    b.Navigation("MoneyTransferReason");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MoneyTransferBeneficiary", "MoneyTransferBeneficiary")
//                        .WithOne("ExternalBeneficiary")
//                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", "MoneyTransferBeneficiaryId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("MoneyTransferBeneficiary");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalTransaction", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MoneyTransferTransaction", "MoneyTransferTransaction")
//                        .WithOne("ExternalTransaction")
//                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferExternalTransaction", "MoneyTransferTransactionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("MoneyTransferTransaction");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferLimit", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithOne("MoneyTransferLimit")
//                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferLimit", "CountryCode");

//                    b.OwnsOne("C3Pay.Core.Models.BankTransferLimit", "BankTransferLimit", b1 =>
//                        {
//                            b1.Property<int>("MoneyTransferLimitId")
//                                .ValueGeneratedOnAdd()
//                                .HasColumnType("int")
//                                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                            b1.Property<decimal?>("MaxAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<decimal?>("MaxMonthlyAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<int?>("MaxMonthlyCount")
//                                .HasColumnType("int");

//                            b1.Property<decimal?>("MinAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.HasKey("MoneyTransferLimitId");

//                            b1.ToTable("MoneyTransferLimits");

//                            b1.WithOwner()
//                                .HasForeignKey("MoneyTransferLimitId");
//                        });

//                    b.OwnsOne("C3Pay.Core.Models.CashPickUpLimit", "CashPickUpLimit", b1 =>
//                        {
//                            b1.Property<int>("MoneyTransferLimitId")
//                                .ValueGeneratedOnAdd()
//                                .HasColumnType("int")
//                                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

//                            b1.Property<decimal?>("MaxAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<decimal?>("MaxMonthlyAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<int?>("MaxMonthlyCount")
//                                .HasColumnType("int");

//                            b1.Property<decimal?>("MinAmount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.HasKey("MoneyTransferLimitId");

//                            b1.ToTable("MoneyTransferLimits");

//                            b1.WithOwner()
//                                .HasForeignKey("MoneyTransferLimitId");
//                        });

//                    b.Navigation("BankTransferLimit");

//                    b.Navigation("CashPickUpLimit");

//                    b.Navigation("Country");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.MoneyTransferBeneficiary", "MoneyTransferBeneficiary")
//                        .WithMany("MoneyTransferTransactions")
//                        .HasForeignKey("MoneyTransferBeneficiaryId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
//                        .WithMany("MoneyTransferTransactions")
//                        .HasForeignKey("MoneyTransferReasonId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
//                        .WithOne("MoneyTransferTransaction")
//                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferTransaction", "ReferenceNumber");

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("MoneyTransferTransactions")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("MoneyTransferBeneficiary");

//                    b.Navigation("MoneyTransferReason");

//                    b.Navigation("Transaction");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Popup", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Country", "Country")
//                        .WithMany("Popups")
//                        .HasForeignKey("CountryCode")
//                        .OnDelete(DeleteBehavior.Restrict);

//                    b.Navigation("Country");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SecretAnswer", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.SecurityQuestion", "SecurityQuestion")
//                        .WithMany("SecretAnswers")
//                        .HasForeignKey("SecurityQuestionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("SecretAnswers")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("SecurityQuestion");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SubscriptionFeature", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Subscription", "Subscription")
//                        .WithMany("Features")
//                        .HasForeignKey("SubscriptionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.Navigation("Subscription");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("Transactions")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.OwnsOne("C3Pay.Core.Models.TranscationReversal", "Reversal", b1 =>
//                        {
//                            b1.Property<string>("TransactionReferenceNumber")
//                                .HasColumnType("nvarchar(20)");

//                            b1.Property<decimal?>("Amount")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<string>("AuthenticationCode")
//                                .HasMaxLength(10)
//                                .HasColumnType("nvarchar(10)");

//                            b1.Property<DateTime?>("Date")
//                                .HasColumnType("datetime2");

//                            b1.Property<decimal?>("EndBalance")
//                                .HasPrecision(18, 2)
//                                .HasColumnType("decimal(18,2)");

//                            b1.Property<string>("ReferenceNumber")
//                                .HasMaxLength(30)
//                                .HasColumnType("nvarchar(30)");

//                            b1.Property<string>("StatusCode")
//                                .HasMaxLength(10)
//                                .HasColumnType("nvarchar(10)");

//                            b1.Property<string>("StatusDescription")
//                                .HasColumnType("nvarchar(max)");

//                            b1.HasKey("TransactionReferenceNumber");

//                            b1.ToTable("Transactions");

//                            b1.WithOwner()
//                                .HasForeignKey("TransactionReferenceNumber");
//                        });

//                    b.Navigation("Reversal");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.UploadedDocument", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("UploadedDocuments")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.CardHolder", "CardHolder")
//                        .WithMany("Users")
//                        .HasForeignKey("CardHolderId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.UserBlockReason", "UserBlockReason")
//                        .WithMany("Users")
//                        .HasForeignKey("UserBlockReasonId");

//                    b.OwnsOne("C3Pay.Core.Models.C3Pay.User.UserAutoUnblock", "AutoUnblock", b1 =>
//                        {
//                            b1.Property<Guid>("UserId")
//                                .HasColumnType("uniqueidentifier");

//                            b1.Property<DateTime?>("LastWrongAttemptDate")
//                                .HasColumnType("datetime2");

//                            b1.Property<int>("WrongAttemptsCount")
//                                .HasColumnType("int");

//                            b1.HasKey("UserId");

//                            b1.ToTable("Users");

//                            b1.WithOwner()
//                                .HasForeignKey("UserId");
//                        });

//                    b.OwnsOne("C3Pay.Core.Models.C3Pay.User.UserPreferences", "Preferences", b1 =>
//                        {
//                            b1.Property<Guid>("UserId")
//                                .HasColumnType("uniqueidentifier");

//                            b1.Property<bool>("ATMPinPopupEnabled")
//                                .HasColumnType("bit");

//                            b1.HasKey("UserId");

//                            b1.ToTable("Users");

//                            b1.WithOwner()
//                                .HasForeignKey("UserId");
//                        });

//                    b.Navigation("AutoUnblock");

//                    b.Navigation("CardHolder");

//                    b.Navigation("Preferences");

//                    b.Navigation("UserBlockReason");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.VerificationComment", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
//                        .WithMany("VerificationComments")
//                        .HasForeignKey("PortalUserId");

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("VerificationComments")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("PortalUser");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.UserSubscription", b =>
//                {
//                    b.HasOne("C3Pay.Core.Models.Subscription", "Subscription")
//                        .WithMany("UserSubscriptions")
//                        .HasForeignKey("SubscriptionId")
//                        .OnDelete(DeleteBehavior.Cascade)
//                        .IsRequired();

//                    b.HasOne("C3Pay.Core.Models.User", "User")
//                        .WithMany("Subscriptions")
//                        .HasForeignKey("UserId")
//                        .OnDelete(DeleteBehavior.Restrict)
//                        .IsRequired();

//                    b.Navigation("Subscription");

//                    b.Navigation("User");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProduct", b =>
//                {
//                    b.Navigation("IOs");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPayments.BillPaymentProvider", b =>
//                {
//                    b.Navigation("Products");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", b =>
//                {
//                    b.Navigation("MoneyTransferCorridors");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.CardHolder", b =>
//                {
//                    b.Navigation("Users");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Country", b =>
//                {
//                    b.Navigation("BillPaymentProviders");

//                    b.Navigation("BlackListedEntities");

//                    b.Navigation("MobileRechargeBeneficiaries");

//                    b.Navigation("MoneyTransferBeneficiaries");

//                    b.Navigation("MoneyTransferLimit");

//                    b.Navigation("Popups");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
//                {
//                    b.Navigation("BeneficiaryProviders");

//                    b.Navigation("MobileRechargeTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
//                {
//                    b.Navigation("MobileRechargeTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
//                {
//                    b.Navigation("BeneficiaryProviders");

//                    b.Navigation("Products");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeSupportedCountry", b =>
//                {
//                    b.Navigation("Products");

//                    b.Navigation("Providers");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
//                {
//                    b.Navigation("ExternalTransaction");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
//                {
//                    b.Navigation("ExternalBeneficiary");

//                    b.Navigation("MoneyTransferTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferReason", b =>
//                {
//                    b.Navigation("MoneyTransferBeneficiaries");

//                    b.Navigation("MoneyTransferTransactions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
//                {
//                    b.Navigation("ExternalTransaction");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.PortalUser", b =>
//                {
//                    b.Navigation("AuditTrails");

//                    b.Navigation("VerificationComments");

//                    b.Navigation("VerifiedEmiratesIdUpdates");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.SecurityQuestion", b =>
//                {
//                    b.Navigation("SecretAnswers");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Subscription", b =>
//                {
//                    b.Navigation("Features");

//                    b.Navigation("UserSubscriptions");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
//                {
//                    b.Navigation("MobileRechargeTransaction");

//                    b.Navigation("MoneyTransferTransaction");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
//                {
//                    b.Navigation("AuditTrails");

//                    b.Navigation("Documents");

//                    b.Navigation("EmiratesIdUpdates");

//                    b.Navigation("MobileRechargeBeneficiaries");

//                    b.Navigation("MobileRechargeTransactions");

//                    b.Navigation("MoneyTransferBeneficiaries");

//                    b.Navigation("MoneyTransferTransactions");

//                    b.Navigation("SecretAnswers");

//                    b.Navigation("Subscriptions");

//                    b.Navigation("Transactions");

//                    b.Navigation("UploadedDocuments");

//                    b.Navigation("VerificationComments");
//                });

//            modelBuilder.Entity("C3Pay.Core.Models.UserBlockReason", b =>
//                {
//                    b.Navigation("Users");
//                });
//#pragma warning restore 612, 618
//        }
//    }
//}
