﻿using System;

namespace C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Billings
{
    public class C3PayPlusMembershipRenewalBilling
    {
        /// <summary>
        /// Unique identifier for the billing record.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Unique identifier for the C3PayPlus membership user associated with this billing.
        /// </summary>
        public Guid C3PayPlusMembershipUserId { get; set; }

        /// <summary>
        /// The target date for which the renewal billing is being processed.
        /// </summary>
        public DateTime RenewingFor { get; set; }

        /// <summary>
        /// The reference number associated with this billing transaction.
        /// </summary>
        public string ReferenceNumber { get; set; }

        /// <summary>
        /// The date on which the billing was initiated.
        /// </summary>
        public DateTime BillingDate { get; set; }

        /// <summary>
        /// The total amount charged for the renewal.
        /// </summary>
        public decimal BillingAmount { get; set; }

        /// <summary>
        /// The current status of the billing process.
        /// </summary>
        public C3PayPlusRenewalBillingStatus Status { get; set; }

        /// <summary>
        /// The C3PayPlus membership user associated with this billing record.
        /// </summary>
        public C3PayPlusMembershipUser C3PayPlusMembershipUser { get; set; }
    }

}
