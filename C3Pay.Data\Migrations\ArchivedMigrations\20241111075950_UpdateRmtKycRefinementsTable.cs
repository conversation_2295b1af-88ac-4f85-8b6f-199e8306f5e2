﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRmtKycRefinementsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Remark",
                table: "RmtKycRefinements",
                newName: "Remarks");

            migrationBuilder.AlterColumn<string>(
                name: "RequiredAction",
                table: "RmtKycRefinements",
                type: "varchar(20)",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Remarks",
                table: "RmtKycRefinements",
                newName: "Remark");

            migrationBuilder.AlterColumn<string>(
                name: "RequiredAction",
                table: "RmtKycRefinements",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(20)",
                oldMaxLength: 20,
                oldNullable: true);
        }
    }
}
