﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Billings;
using C3Pay.Core.Repositories.C3Pay.Membership;
using Edenred.Common.Data;
using Microsoft.EntityFrameworkCore;

namespace C3Pay.Data.Repositories.C3Pay.Membership
{
    public class C3PayPlusMembershipRenewalBillingRepository : Repository<C3PayPlusMembershipRenewalBilling>, IC3PayPlusMembershipRenewalBillingRepository
    {
        public C3PayPlusMembershipRenewalBillingRepository(DbContext context) : base(context) { }

        private C3PayContext C3PayContext
        {
            get { return Context as C3PayContext; }
        }
    }
}
