﻿using C3Pay.Core;
using C3Pay.Core.Repositories;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using Moq;
using System.Threading.Tasks;
using System.Threading;
using Xunit;
using C3Pay.Services.Payments.Commands;
using C3Pay.Core.Common;
using C3Pay.Core.Models.DTOs.Payments.Requests;
using System;
using C3Pay.Core.Models;

namespace C3Pay.UnitTest.Tests.Payments
{
    public class UpdatePaymentAuthorisationDecisionCommandHandlerTests
    {
        private readonly Mock<IFeatureManager> _featureManagerMock;
        private readonly Mock<ILogger<UpdatePaymentAuthorisationDecisionCommandHandler>> _loggerMock;
        private readonly Mock<IUserRepository> _userRepositoryMock;
        private readonly UpdatePaymentAuthorisationDecisionCommandHandler _handler;
        public UpdatePaymentAuthorisationDecisionCommandHandlerTests()
        {
            _featureManagerMock = new Mock<IFeatureManager>();
            _loggerMock = new Mock<ILogger<UpdatePaymentAuthorisationDecisionCommandHandler>>();
            _userRepositoryMock = new Mock<IUserRepository>();
            _handler = new UpdatePaymentAuthorisationDecisionCommandHandler(_featureManagerMock.Object, _loggerMock.Object, _userRepositoryMock.Object);
        }

        [Fact]
        public async Task Handler_ShouldReturnSuccess_WhenAllConditionsAreMet()
        {
            // Arrange
            _featureManagerMock.Setup(fm => fm.IsEnabledAsync(FeatureFlags.InAppPaymentAuthentication))
                               .ReturnsAsync(true);

            var command = new UpdatePaymentAuthorisationDecisionCommand
            {
                UserPhoneNumber = "00971503145237",
                LanguageCode = "en",
                DecisionRequest = new PaymentAuthorisationDecisionRequestDto
                {
                    Decision = PaymentAuthDecision.APPROVE,
                    Id = Guid.NewGuid(),
                }
            };
            _userRepositoryMock.Setup(ur => ur.GetUserAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                           .ReturnsAsync(new User());



            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
           
        }
    }
}
