using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using OpenTelemetry;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using OpenTelemetry.Metrics;
using OpenTelemetry.Logs;
using System;
using System.Collections.Generic;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using Azure.Monitor.OpenTelemetry.Exporter;
using Microsoft.Extensions.Logging;

namespace C3Pay.Portal.API.Extensions
{
    public static class LoggingExtension
    {
        public static void InjectLogging(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure OpenTelemetry with Azure Monitor
            var connectionString = configuration["ApplicationInsights:ConnectionString"];
            var serviceName = configuration["OpenTelemetry:ServiceName"] ?? "C3Pay.Portal.API";
            var serviceVersion = configuration["OpenTelemetry:ServiceVersion"] ?? "1.0.0";

            // Only configure Azure Monitor if connection string is provided
            if (!string.IsNullOrEmpty(connectionString))
            {
                // Configure OpenTelemetry with Azure Monitor
                services.AddOpenTelemetry()
                    .ConfigureResource(resource => resource
                        .AddService(serviceName, serviceVersion))
                    .UseAzureMonitor(options =>
                    {
                        options.ConnectionString = connectionString;
                    })
                    .WithTracing(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation(options =>
                            {
                                options.RecordException = true;
                            })
                            .AddHttpClientInstrumentation()
                            .AddSqlClientInstrumentation(options =>
                            {
                                options.SetDbStatementForText = true;
                                options.SetDbStatementForStoredProcedure = true;
                                options.RecordException = true;
                            });
                    })
                    .WithMetrics(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation();
                    });
            }
            else
            {
                // Configure basic OpenTelemetry without Azure Monitor for local development
                services.AddOpenTelemetry()
                    .ConfigureResource(resource => resource
                        .AddService(serviceName, serviceVersion))
                    .WithTracing(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation(options =>
                            {
                                options.RecordException = true;
                            })
                            .AddHttpClientInstrumentation()
                            .AddSqlClientInstrumentation(options =>
                            {
                                options.SetDbStatementForText = true;
                                options.SetDbStatementForStoredProcedure = true;
                                options.RecordException = true;
                            })
                            .AddConsoleExporter(); // Add console exporter for local development
                    })
                    .WithMetrics(builder =>
                    {
                        builder
                            .AddAspNetCoreInstrumentation()
                            .AddHttpClientInstrumentation()
                            .AddConsoleExporter(); // Add console exporter for local development
                    });
            }

            // Configure Serilog with OpenTelemetry
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .Enrich.FromLogContext()
                .CreateLogger();

            services.AddLogging(options =>
            {
                options.AddSerilog();
                
                // Only add Azure Monitor log exporter if connection string is available
                if (!string.IsNullOrEmpty(connectionString))
                {
                    options.AddOpenTelemetry(builder =>
                    {
                        builder.SetResourceBuilder(ResourceBuilder.CreateDefault()
                            .AddService(serviceName, serviceVersion));
                        builder.AddAzureMonitorLogExporter(options =>
                        {
                            options.ConnectionString = connectionString;
                        });
                    });
                }
            });
        }
    }
} 