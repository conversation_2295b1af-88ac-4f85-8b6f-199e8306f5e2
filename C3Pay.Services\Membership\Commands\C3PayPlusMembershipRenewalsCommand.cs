﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Billings;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logging;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Services.C3Pay.Membership;
using Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.PPS;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Transaction;
using Edenred.Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Services.Integration;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Commands
{
    public class C3PayPlusMembershipRenewalsCommand : IRequest<Result>
    {
    }
    public class C3PayPlusMembershipRenewalsCommandHandler : IRequestHandler<C3PayPlusMembershipRenewalsCommand, Result>
    {
        private readonly IC3PayPlusMembershipLookupService _lookupService;
        private readonly ITransactionsB2CService _transactionsB2CService;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly IESMOWebService _esmoWebService;
        private readonly IFeatureManager _featureManager;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPPSService _ppsService;
        private readonly ILogger _logger;

        private readonly decimal _membershipPrice = 10.5m;
        private readonly List<string> _edenredCorporateIds = ["99999", "99998"];
        private readonly bool _skipEdenredCardCheck = false;
        private enum RenewalsCancelationReasons
        {
            UserNotFoundOrBlockedOrDeleted = 1,
            UserUnsubscribed,
            CantGetAgeInformation,
            UserIsTooOldOrTooYoung,
            NonActiveCard,
            NoSalaryInTheLast3Months,
            Other
        }


        public C3PayPlusMembershipRenewalsCommandHandler(ILogger<C3PayPlusMembershipRenewalsCommandHandler> logger,
                                                         IC3PayPlusMembershipLookupService lookupService,
                                                         ITransactionsB2CService transactionsB2CService,
                                                         IPPSWebAuthService ppsWebAuthService,
                                                         IFeatureManager featureManager,
                                                         IESMOWebService esmoWebService,
                                                         IUnitOfWork unitOfWork,
                                                         IPPSService ppsService)
        {
            _transactionsB2CService = transactionsB2CService;
            _ppsWebAuthService = ppsWebAuthService;
            _esmoWebService = esmoWebService;
            _featureManager = featureManager;
            _lookupService = lookupService;
            _unitOfWork = unitOfWork;
            _ppsService = ppsService;
            _logger = logger;
        }
        public async Task<Result> Handle(C3PayPlusMembershipRenewalsCommand command, CancellationToken ct)
        {
            // If C3Pay+ renewals is disabled, exit.
            var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.Memberships_C3PayPlus_RNWLS);
            if (isEnabled == false)
            {
                _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.FeatureDisabled));
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlusRenewalsErrors.FeatureDisabled);
            }


            // Set timestamp to start job.
            var currentDate = DateTime.Now;

            // First create an entry for the WebJob logs.
            var logEntry = new Log_C3PayPlusMembershipRenewals()
            {
                RenewingFor = currentDate.Date,
                JobInvokedAt = currentDate,
                RunStatus = C3PayPlusRenewalRunStatus.Started
            };


            await this._unitOfWork.Logs_C3PayPlusMembershipRenewals.AddAsync(logEntry);
            await this._unitOfWork.CommitAsync();

            // Keep a copy of the memberships that have been renewed to perform final checks against this list.
            var completedRenewals = new List<C3PayPlusMembershipUser>();

            try
            {
                // Log start details.
                logEntry.JobStartTime = DateTime.Now;
                logEntry.RunStatus = C3PayPlusRenewalRunStatus.FetchingSubscriptions;
                await this._unitOfWork.CommitAsync();


                // First get all subscriptions that we should renew for today.
                var renewals = await this._unitOfWork.C3PayPlusMembershipUsers.FindAsync(x => x.IsActive == true
                                                                                              && x.UserSubscribedOn.Day == currentDate.Day,
                                                                                              withoutTraking: false,
                                                                                              includeProperties: i => i.User.CardHolder);

                // Update log entry.
                logEntry.TotalRenewalsExpectedToProcess = renewals.Count;
                logEntry.RunStatus = C3PayPlusRenewalRunStatus.Renewing;
                await this._unitOfWork.CommitAsync();


                // Begin the renewal process.
                foreach (var subscriber in renewals)
                {
                    // Case 1: Find user.
                    var user = await this._unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == subscriber.UserId
                                                                                     && x.IsDeleted == false
                                                                                     && x.IsBlocked == false
                                                                                     && x.ApplicationId == MobileApplicationId.C3Pay,
                                                                                     i => i.CardHolder);

                    // If the user is not found (e.g., deleted or blocked), end the membership.
                    if (user is null)
                    {
                        await this.EndMembership(RenewalsCancelationReasons.UserNotFoundOrBlockedOrDeleted, subscriber, logEntry);
                        continue;
                    }



                    // Case 2: Close the membership for users who have decided to cancel.
                    if (subscriber.UserHasCanceled.HasValue && subscriber.UserHasCanceled.Value == true)
                    {
                        await this.EndMembership(RenewalsCancelationReasons.UserUnsubscribed, subscriber, logEntry);
                        continue;
                    }



                    // Case 3: Close the membership if we cant get the user's DOB.
                    if (subscriber.User.CardHolder.Birthdate.HasValue == false)
                    {
                        await this.EndMembership(RenewalsCancelationReasons.CantGetAgeInformation, subscriber, logEntry);
                        continue;
                    }



                    // Case 4: Close membership if the user is not between 18 and 59;
                    var dob = subscriber.User.CardHolder.Birthdate.Value;
                    int age = currentDate.Year - dob.Year;
                    if (currentDate.Month < dob.Month || (currentDate.Month == dob.Month && currentDate.Day < dob.Day))
                    {
                        age--;
                    }

                    if (age < 18 || age > 59)
                    {
                        await this.EndMembership(RenewalsCancelationReasons.UserIsTooOldOrTooYoung, subscriber, logEntry);
                        continue;
                    }



                    // Case 5: Close the membership if user has an non active (dormant) card.
                    var tryCheckIsCardDormant = await this._esmoWebService.IsCardDormant(subscriber.User.CardHolder.C3RegistrationId);
                    if (tryCheckIsCardDormant.IsSuccessful == false)
                    {
                        logEntry.UnsubscribedDueToDormantCardCheckFailure++;
                        await this._unitOfWork.CommitAsync();
                        continue;
                    }

                    // If the card is dormant, close membership.
                    if (tryCheckIsCardDormant.Data.IsCardDormant == true)
                    {
                        await this.EndMembership(RenewalsCancelationReasons.NonActiveCard, subscriber, logEntry);
                        continue;
                    }


                    // Case 6: Close membership if we user did not get salary in the last 3 months.
                    var tryGetBalance = await this.GetBalance(user.CardHolder);
                    if (tryGetBalance.IsSuccessful == false)
                    {
                        logEntry.SkippedRenewalsDueToUnableToRetrieveBalance++;
                        await this._unitOfWork.CommitAsync();
                        continue;
                    }


                    if (tryGetBalance.Data < _membershipPrice)
                    {
                        var tryCheckingIfUserGotSalaryInLast3Months = await this._esmoWebService.GotSalaryInLast3Months(user.CardHolderId);
                        if (tryCheckingIfUserGotSalaryInLast3Months.IsSuccessful == false || tryCheckingIfUserGotSalaryInLast3Months.Data.IsSuccessful == false)
                        {
                            logEntry.SkippedRenewalsDueToUnableToConfirmSalaryReceived++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }

                        if (tryCheckingIfUserGotSalaryInLast3Months.Data.GotSalary == false)
                        {
                            await this.EndMembership(RenewalsCancelationReasons.NoSalaryInTheLast3Months, subscriber, logEntry);
                            continue;
                        }
                    }


                    // At this point, we are able to renew the membership.
                    // To do so, we need to first get C3Pay+ transactions for this user. Skip if Edenred card.
                    if (_skipEdenredCardCheck == true && _edenredCorporateIds.Contains(user.CardHolder.CorporateId) == false)
                    {
                        var getC3PayPlusTransactionsRequest = new GetC3PayPlusTransactionsRequest()
                        {
                            CardholderId = subscriber.User.CardHolderId
                        };

                        var tryGetC3PayPlusTransactions = await this._transactionsB2CService.GetC3PayPlusTransactions(getC3PayPlusTransactionsRequest);

                        // We should expect at least one billing to be returned.
                        // If we can't get the transactions for this user, it could mean that the user was not billed for the first time,
                        // or that the Orian service is not working. For this we can't end the membership so we will just set a remark for it.
                        if (tryGetC3PayPlusTransactions.IsSuccessful == false)
                        {
                            logEntry.SkippedRenewalsDueToNoBillingHistoryFound++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }

                        var billingTransactions = tryGetC3PayPlusTransactions?.Data?.BillingTransactions;
                        if (billingTransactions is null || billingTransactions.Count == 0)
                        {
                            logEntry.SkippedRenewalsDueToNoBillingHistoryFound++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }


                        // Get the most recent billing for this user.
                        var lastBilling = billingTransactions.OrderByDescending(x => x.Date).First();
                        var billingDateDifference = (DateTime.Now - lastBilling.Date.Value).Days;
                        if (billingDateDifference < 20)
                        {
                            logEntry.SkippedRenewalsDueToBillingInSameMonth++;
                            await this._unitOfWork.CommitAsync();
                            continue;
                        }
                    }

                    // Perform billing.
                    var referenceNumber = await GenerateBillingReferenceNumber();
                    var renewalBilling = new C3PayPlusMembershipRenewalBilling()
                    {
                        ReferenceNumber = referenceNumber,
                        Status = C3PayPlusRenewalBillingStatus.BillingStarted,
                        C3PayPlusMembershipUserId = subscriber.Id,
                        RenewingFor = currentDate.Date
                    };

                    await this._unitOfWork.C3PayPlusMembershipRenewalBillings.AddAsync(renewalBilling);
                    await this._unitOfWork.CommitAsync();


                    var tryBillUserForC3PayPlus = await this.DecreaseBalance(subscriber);
                    if (tryBillUserForC3PayPlus.IsSuccessful == false)
                    {
                        logEntry.SkippedRenewalsDueToUnableToDebitUser++;
                        await this._unitOfWork.CommitAsync();
                        continue;
                    }

                    // Save billing details.
                    renewalBilling.Status = C3PayPlusRenewalBillingStatus.BillingSubmitedButNotConfirmed;

                    // Renew membership.
                    subscriber.RenewMembership();


                    completedRenewals.Add(subscriber);
                    logEntry.RenewalsCompletedSuccessfully++;
                    await this._unitOfWork.CommitAsync();
                }

                // Here, we have completed the rewal process. We now need to perform final checks.
                logEntry.RunStatus = C3PayPlusRenewalRunStatus.FinalChecks;
                await this._unitOfWork.CommitAsync();

                foreach (var subscriber in completedRenewals)
                {
                    // Only perform final checks on non-Edenred cards.
                    if (_skipEdenredCardCheck == true && _edenredCorporateIds.Contains(subscriber.User.CardHolder.CorporateId) == false)
                    {
                        var renewalBilling = await this._unitOfWork.C3PayPlusMembershipRenewalBillings.FirstOrDefaultAsync(x => x.C3PayPlusMembershipUserId == subscriber.Id
                                                                                                                                && x.RenewingFor == currentDate);

                        if (renewalBilling is null)
                        {
                            _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantFindBillingRecordInC3PayDB));
                            continue;
                        }

                        var getC3PayPlusTransactionsRequest = new GetC3PayPlusTransactionsRequest()
                        {
                            CardholderId = subscriber.User.CardHolderId
                        };

                        // Get C3Pay+ billing transactions again.
                        var tryGetC3PayPlusTransactions = await this._transactionsB2CService.GetC3PayPlusTransactions(getC3PayPlusTransactionsRequest);
                        if (tryGetC3PayPlusTransactions.IsSuccessful == false)
                        {
                            continue;
                        }

                        var billingTransactions = tryGetC3PayPlusTransactions?.Data?.BillingTransactions;
                        if (billingTransactions is null || billingTransactions.Count == 0)
                        {
                            continue;
                        }

                        // Find the billing and match it with what we got from Orian.
                        var billingConfirmation = billingTransactions.FirstOrDefault(x => x.ReferenceNumber == renewalBilling.ReferenceNumber);
                        if (billingConfirmation is null)
                        {
                            continue;
                        }

                        renewalBilling.BillingAmount = billingConfirmation.Amount;
                        renewalBilling.BillingDate = billingConfirmation.Date.Value;
                        renewalBilling.Status = C3PayPlusRenewalBillingStatus.BillingConfirmed;
                        await this._unitOfWork.CommitAsync();
                    }
                }


                // Renewal is completed.
                logEntry.JobEndTime = DateTime.Now;
                logEntry.JobDurationInSeconds = (logEntry.JobStartTime - logEntry.JobEndTime).Value.Seconds;
                logEntry.RunStatus = C3PayPlusRenewalRunStatus.Finished;

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());

                logEntry.Remarks = $"FATAL ERROR: {ex}";
                logEntry.RunStatus = C3PayPlusRenewalRunStatus.Interrupted;
                return Result.Success();
            }

        }
        private async Task EndMembership(RenewalsCancelationReasons reason, C3PayPlusMembershipUser subscriber, Log_C3PayPlusMembershipRenewals logEntry)
        {
            // We first need to rollback to the user's old subscriptions.
            var tryRollbackToOldSubscriptions = await this.RollbackToOldSubscriptions(subscriber);
            if (tryRollbackToOldSubscriptions.IsSuccessful == false)
            {
                // Update log entry.
                if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.BalanceEnquirySubscriptionNotFound.Code)
                {
                    logEntry.SkippedRenewalsDueToBalanceEnquirySubscriptionNotFound++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionCode.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSecuritySmsSubscriptionCode++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionFee.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSecuritySmsSubscriptionFee++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSecuritySms.Code)
                {
                    logEntry.SkippedRenewalsDueToIssueSubscribingBackToSecuritySms++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionCode.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSalaryAlertSubscriptionCode++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionFees.Code)
                {
                    logEntry.SkippedRenewalsDueToMissingSalaryAlertSubscriptionFee++;
                }
                else if (tryRollbackToOldSubscriptions.ErrorMessage == Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSalaryAlerts.Code)
                {
                    logEntry.SkippedRenewalsDueToIssueSubscribingBackToSalaryAlert++;
                }

                logEntry.TotalSkippedRenewals++;

                // Save and continue.
                await this._unitOfWork.CommitAsync();
                return;
            }


            // Here, rollback was succesfull, so we can close the membership.
            switch (reason)
            {
                case RenewalsCancelationReasons.UserNotFoundOrBlockedOrDeleted:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_ClosedMembershipDueToUserNotFoundOrBlockedOrDeleted);
                    logEntry.UnsubscribedDueToDeletedUsers++;
                    break;
                case RenewalsCancelationReasons.UserUnsubscribed:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_CloseMembershipDueToUsersDecision);
                    logEntry.UnsubscribedDueToUserDecision++;
                    break;
                case RenewalsCancelationReasons.CantGetAgeInformation:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_CloseMembershipDueToAgeNotFound);
                    logEntry.UnsubscribedDueToMissingAgeInformation++;
                    break;
                case RenewalsCancelationReasons.UserIsTooOldOrTooYoung:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_CloseMembershipDueToAgeLimitExceeded);
                    logEntry.UnsubscribedDueToExceedingAgeLimit++;
                    break;
                case RenewalsCancelationReasons.NoSalaryInTheLast3Months:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_CloseMembershipDueToNoSalaryCreditedInLast3Months);
                    logEntry.UnsubscribedDueToNoSalaryCreditedInLast3Months++;
                    break;
                case RenewalsCancelationReasons.Other:
                    subscriber.EndMembership(ConstantParam.C3PayPlus_RNWLS_CloseMembershipDueToInactiveCard);
                    logEntry.UnsubscribedDueToOtherReasons++;
                    break;
                default:
                    break;
            }

            // Update log entry.
            logEntry.SuccessfulUnsubscribes++;

            // Save and continue.
            await this._unitOfWork.CommitAsync();
        }
        private async Task<ServiceResponse> RollbackToOldSubscriptions(C3PayPlusMembershipUser subscriber)
        {
            try
            {
                // Get all cached subscrptions.
                var allSubscriptions = await _lookupService.GetCachedSubscriptions();

                if (subscriber.UserHadBalanceEnquirySubscription == true)
                {
                    var balanceEnquirySubscriptionCode = allSubscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.BE.ToString());

                    // Find current BE subscription that is created because of C3Pay+.
                    var c3pBalanceEnquirySubscriptions = await this._unitOfWork.UserSubscriptions.FindAsync(x => x.Subscription.Code == SMSSubscriptionType.BE.ToString()
                                                                                                             && x.UserId == subscriber.UserId
                                                                                                             && x.C3PayPlusMembershipUserId == subscriber.Id,
                                                                                                             withoutTraking: false,
                                                                                                             includeProperties: null);

                    if (c3pBalanceEnquirySubscriptions is null)
                    {
                        // Log this error. This case should not be possible and it means that the subscription was not created successfully.
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.BalanceEnquirySubscriptionNotFound));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.BalanceEnquirySubscriptionNotFound);
                    }


                    foreach (var c3pBalanceEnquirySubscription in c3pBalanceEnquirySubscriptions)
                    {
                        // End the C3Pay+ BE subscription.
                        c3pBalanceEnquirySubscription.EndDate = DateTime.UtcNow;
                    }

                    await this._unitOfWork.CommitAsync();

                    // Create a new BE subscription.
                    var newBalanceEnquirySubscrption = new UserSubscription()
                    {
                        UserId = subscriber.UserId,
                        SubscriptionId = balanceEnquirySubscriptionCode.Id,
                        StartDate = DateTime.Now,
                    };

                    // Add the new BE subscription.
                    await this._unitOfWork.UserSubscriptions.AddAsync(newBalanceEnquirySubscrption);
                    await this._unitOfWork.CommitAsync();
                }

                if (subscriber.UserHadSecuritySmsSubscription == true)
                {
                    var securitySmsSubscription = allSubscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.T.ToString());
                    if (securitySmsSubscription is null)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionCode));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionCode);
                    }

                    var subscriptionFee = await this._unitOfWork.Subscriptions.GetSubscriptionFee(securitySmsSubscription.Id, subscriber.User.CardHolder.CorporateId);
                    if (subscriptionFee is null)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionFee));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantGetSecuritySmsSubscriptionFee);
                    }


                    var tryToggleC3PayPlusSmsSubscription = await this._esmoWebService.ToggleC3PayPlusSmsSubscription(new ToggleC3PayPlusSmsSubscriptionRequestDto()
                    {
                        CardSerialNumber = subscriber.User.CardHolder.CardSerialNumber,
                        CorporateId = int.Parse(subscriber.User.CardHolder.CorporateId),
                        NotificationType = securitySmsSubscription.Code,
                        PhoneNumber = subscriber.User.PhoneNumber,
                        SMSFee = subscriptionFee.Fee
                    });


                    if (tryToggleC3PayPlusSmsSubscription.IsSuccessful == false)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSecuritySms));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSecuritySms);
                    }

                }

                else if (subscriber.UserHadSalaryAlertSmsSubscription == true)
                {
                    var salaryAlertSubscription = allSubscriptions.FirstOrDefault(x => x.Code == SMSSubscriptionType.S.ToString());
                    if (salaryAlertSubscription is null)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionCode));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionCode);
                    }

                    var subscriptionFee = await this._unitOfWork.Subscriptions.GetSubscriptionFee(salaryAlertSubscription.Id, subscriber.User.CardHolder.CorporateId);
                    if (subscriptionFee is null)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionFees));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantGetSalaryAlertSubscriptionFees);
                    }


                    var tryToggleC3PayPlusSmsSubscription = await this._esmoWebService.ToggleC3PayPlusSmsSubscription(new ToggleC3PayPlusSmsSubscriptionRequestDto()
                    {
                        CardSerialNumber = subscriber.User.CardHolder.CardSerialNumber,
                        CorporateId = int.Parse(subscriber.User.CardHolder.CorporateId),
                        NotificationType = salaryAlertSubscription.Code,
                        PhoneNumber = subscriber.User.PhoneNumber,
                        SMSFee = subscriptionFee.Fee
                    });

                    if (tryToggleC3PayPlusSmsSubscription.IsSuccessful == false)
                    {
                        _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSalaryAlerts));
                        return new ServiceResponse(false, Errors.C3PayPlusRenewalsErrors.CantSubscribeBackToSalaryAlerts);
                    }
                }

                return new ServiceResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.ToString());
                return new ServiceResponse(false, ex.ToString());
            }
        }

        private async Task<string> GenerateBillingReferenceNumber()
        {
            var referencePrefix = TransactionPrefix.C3P.ToString();

            var dateStamp = DateTime.Now;

            var startYear = dateStamp.Year.ToString().Substring(2);
            var startMonth = dateStamp.Month.ToString();
            var startDay = dateStamp.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipRenewalBillings.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }
        private async Task<ServiceResponse> DecreaseBalance(C3PayPlusMembershipUser membershipUser)
        {
            var cardNumber = membershipUser.User.CardHolder.CardNumber;
            var cardSerialNumber = membershipUser.User.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var decreaseBalanceRequest = new PPSDecreaseBalanceRequest()
            {
                accountField = new PPSAccountDetails()
                {
                    accountNoField = membershipUser.User.CardHolder.PpsAccountNumber,
                    cardSerialField = cardSerialNumber,
                    cardPanField = cardPanNumber
                },
                amountField = (long)(_membershipPrice * 100),
                reasonField = "C3Pay+ Monthly Billing",
                schemeRefField = membershipUser.LastBillingTransactionNumber,
                allowNegativeBalance = true
            };

            ServiceResponse tryDecreaseBalance;

            try
            {
                tryDecreaseBalance = await _ppsService.DecreaseBalance(decreaseBalanceRequest, true);
            }
            catch (Exception ex)
            {
                _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantDebitUser));
                _logger.LogError(ex.ToString());
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlusRenewalsErrors.CantDebitUser);
            }

            if (tryDecreaseBalance.IsSuccessful == false)
            {
                _logger.LogError(GetErrorMessage(Errors.C3PayPlusRenewalsErrors.CantDebitUser));
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlusRenewalsErrors.CantDebitUser);
            }

            return new ServiceResponse();
        }
        public async Task<ServiceResponse<decimal>> GetBalance(CardHolder cardholder)
        {
            var cardNumber = cardholder.CardNumber;
            var cardSerialNumber = cardholder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var request = new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(TransactionPrefix.BAL.ToString(), 12)
            };

            var getCardBalanceResult = await _ppsWebAuthService.GetCardBalance(request);

            if (!getCardBalanceResult.IsSuccessful)
            {
                return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            if (getCardBalanceResult.Data.StatusCode != "00")
            {
                if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.CARDNOTACTIVATED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ActivateYourCard.ToString());
                }
                else if (getCardBalanceResult.Data.Message == EnumUtility.GetDescriptionFromEnumValue(PPSResponseStatus.MAXPINEXCEEDED))
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.UnblockYourCard.ToString());
                }
                else
                {
                    return new ServiceResponse<decimal>(false, TransferStatusValidationMessage.ErrorGettingBalance.ToString());
                }
            }

            var balance = TypeUtility.GetDecimalFromString(getCardBalanceResult.Data.EndBalanace.Amt) / 100;
            return new ServiceResponse<decimal>(balance);
        }

        private string GetErrorMessage(Error error)
        {
            return error.Code + ". More Info: " + error.Message;
        }
    }
}