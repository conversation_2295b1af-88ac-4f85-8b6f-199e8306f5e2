﻿//using System;
//using Microsoft.EntityFrameworkCore.Migrations;

//namespace C3Pay.Data.Migrations
//{
//    public partial class BillPayments : Migration
//    {
//        protected override void Up(MigrationBuilder migrationBuilder)
//        {
//            migrationBuilder.CreateTable(
//                name: "BillPaymentProviders",
//                columns: table => new
//                {
//                    Id = table.Column<int>(type: "int", nullable: false)
//                        .Annotation("SqlServer:Identity", "1, 1"),
//                    Code = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
//                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
//                    Description = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
//                    Type = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
//                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
//                    CatalogVersion = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
//                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
//                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
//                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
//                },
//                constraints: table =>
//                {
//                    table.PrimaryKey("PK_BillPaymentProviders", x => x.Id);
//                    table.ForeignKey(
//                        name: "FK_BillPaymentProviders_Countries_CountryCode",
//                        column: x => x.CountryCode,
//                        principalTable: "Countries",
//                        principalColumn: "Code",
//                        onDelete: ReferentialAction.Restrict);
//                });

//            migrationBuilder.CreateTable(
//                name: "BillPaymentProducts",
//                columns: table => new
//                {
//                    Id = table.Column<int>(type: "int", nullable: false)
//                        .Annotation("SqlServer:Identity", "1, 1"),
//                    ProviderId = table.Column<int>(type: "int", nullable: false),
//                    Code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
//                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
//                    Type = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
//                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
//                    MinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
//                    MaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
//                    Currency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
//                    DaysToPost = table.Column<int>(type: "int", nullable: false),
//                    BusinessDays = table.Column<bool>(type: "bit", nullable: false),
//                    InquiryAvailable = table.Column<bool>(type: "bit", nullable: false),
//                    PastDuePaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
//                    ExcessPaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
//                    PartialPaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
//                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
//                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
//                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
//                },
//                constraints: table =>
//                {
//                    table.PrimaryKey("PK_BillPaymentProducts", x => x.Id);
//                    table.ForeignKey(
//                        name: "FK_BillPaymentProducts_BillPaymentProviders_ProviderId",
//                        column: x => x.ProviderId,
//                        principalTable: "BillPaymentProviders",
//                        principalColumn: "Id",
//                        onDelete: ReferentialAction.Cascade);
//                });

//            migrationBuilder.CreateTable(
//                name: "BillPaymentProductIOs",
//                columns: table => new
//                {
//                    Id = table.Column<int>(type: "int", nullable: false)
//                        .Annotation("SqlServer:Identity", "1, 1"),
//                    ProductId = table.Column<int>(type: "int", nullable: false),
//                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
//                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
//                    Type = table.Column<int>(type: "int", nullable: false),
//                    DataType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
//                    MinLength = table.Column<int>(type: "int", nullable: false),
//                    MaxLength = table.Column<int>(type: "int", nullable: false),
//                    IOId = table.Column<int>(type: "int", nullable: false),
//                    Operation = table.Column<int>(type: "int", nullable: false),
//                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
//                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
//                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
//                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
//                },
//                constraints: table =>
//                {
//                    table.PrimaryKey("PK_BillPaymentProductIOs", x => x.Id);
//                    table.ForeignKey(
//                        name: "FK_BillPaymentProductIOs_BillPaymentProducts_ProductId",
//                        column: x => x.ProductId,
//                        principalTable: "BillPaymentProducts",
//                        principalColumn: "Id",
//                        onDelete: ReferentialAction.Cascade);
//                });

//            migrationBuilder.CreateIndex(
//                name: "IX_BillPaymentProductIOs_ProductId",
//                table: "BillPaymentProductIOs",
//                column: "ProductId");

//            migrationBuilder.CreateIndex(
//                name: "IX_BillPaymentProducts_ProviderId",
//                table: "BillPaymentProducts",
//                column: "ProviderId");

//            migrationBuilder.CreateIndex(
//                name: "IX_BillPaymentProviders_CountryCode",
//                table: "BillPaymentProviders",
//                column: "CountryCode");
//        }

//        protected override void Down(MigrationBuilder migrationBuilder)
//        {
//            migrationBuilder.DropTable(
//                name: "BillPaymentProductIOs");

//            migrationBuilder.DropTable(
//                name: "BillPaymentProducts");

//            migrationBuilder.DropTable(
//                name: "BillPaymentProviders");
//        }
//    }
//}
