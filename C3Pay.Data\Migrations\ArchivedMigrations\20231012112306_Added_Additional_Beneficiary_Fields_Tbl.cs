﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Additional_Beneficiary_Fields_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdditionalFieldMappings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FieldCode = table.Column<int>(type: "int", nullable: false),
                    ProviderFieldCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldText = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdditionalFieldMappings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BeneficiaryAdditionalFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FieldCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferBeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryAdditionalFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryAdditionalFields_MoneyTransferBeneficiaries_MoneyTransferBeneficiaryId",
                        column: x => x.MoneyTransferBeneficiaryId,
                        principalTable: "MoneyTransferBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryAdditionalFields_MoneyTransferBeneficiaryId",
                table: "BeneficiaryAdditionalFields",
                column: "MoneyTransferBeneficiaryId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdditionalFieldMappings");

            migrationBuilder.DropTable(
                name: "BeneficiaryAdditionalFields");
        }
    }
}
