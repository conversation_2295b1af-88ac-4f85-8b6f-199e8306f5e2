﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus;
using C3Pay.Core.Models.DTOs.Membership.C3PayPlus.Responses;
using C3Pay.Core.Repositories;
using C3Pay.Core.Services;
using C3Pay.Services.Helper;
using Edenred.Common.Core;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Commands
{
    public class ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommand : IRequest<Result>
    {
    }

    public class ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommandHandler : IRequestHandler<ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommand, Result>
    {
        private readonly IExperimentService _experimentService;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly IFeatureManager _featureManager;
        private readonly IUserRepository _userRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;

        public ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommandHandler(ILogger<ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommandHandler> logger,
                                                                                IPPSWebAuthService ppsWebAuthService,
                                                                                IExperimentService experimentService,
                                                                                IFeatureManager featureManager,
                                                                                IUserRepository userRepository,
                                                                                IUnitOfWork unitOfWork)
        {
            _experimentService = experimentService;
            _ppsWebAuthService = ppsWebAuthService;
            _featureManager = featureManager;
            _userRepository = userRepository;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Result> Handle(ProcessC3PayPlusMembershipFreeMoneyTransferRefundsCommand request, CancellationToken ct)
        {
            // If money transfer is disabled, exit.
            var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.C3PayPlusLucky_ProcessMoneyTransferRefunds);
            if (isEnabled == false)
            {
                _logger.LogError(Errors.C3PayPlus.MoneyTransferRefundDisabled.Code);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.MoneyTransferRefundDisabled);
            }


            try
            {
                // Get all subscribers who are part of variant B (Money Transfer Refund).
                var variantBSubscribers = await this._unitOfWork.C3PayPlusMembershipUsers.FindAsync(x => x.IsActive == true
                && x.User.CardHolder.ExperimentUsers.Any(x => x.ExperimentId == (int)ExperimentType.C3PayPlus && x.GroupCode == "B"), false, i => i.User.CardHolder);

                if (variantBSubscribers is null || variantBSubscribers.Count == 0)
                {
                    _logger.LogError(Errors.C3PayPlus.MoneyTransferRefundNoSubscribersFound.Code);
                    return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.MoneyTransferRefundNoSubscribersFound);
                }


                // Loop subscribers.
                foreach (var subscriber in variantBSubscribers)
                {
                    // Make sure user has an active C3Pay+ subscription.
                    var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers.FirstOrDefaultAsync(x => x.UserId == subscriber.UserId && x.IsActive == true);
                    if (membershipUser is null)
                    {
                        _logger.LogError(Errors.C3PayPlus.NoMembershipFound.Code);
                        continue;
                    }


                    // Make sure user is part of the free money transfer variant.
                    var tryCheckUserIsInC3PayPlus = await _experimentService.CheckUserIsInC3PayPlus(subscriber.User.CardHolderId);
                    if (tryCheckUserIsInC3PayPlus.IsSuccessful == false || tryCheckUserIsInC3PayPlus.Data is null || tryCheckUserIsInC3PayPlus.Data.GroupCode != "B")
                    {
                        _logger.LogError(Errors.C3PayPlus.UserNotInExperiment.Code);
                        continue;
                    }

                    // Make sure user did not claim this benefit yet for this month.
                    if (membershipUser.HasClaimedTheFreeMoneyTransferForThisMonth == true)
                    {
                        _logger.LogError(Errors.C3PayPlus.UserAlreadyClaimedFreeMoneyTransferBenefitForThisMonth.Code);
                        continue;
                    }
                    else
                    {
                        // Confirm that this user has not claimed this benefit.
                        var thisMonthsClaim = await this._unitOfWork.C3PayPlusMembershipFreeMoneyTransferClaims.FirstOrDefaultAsync(x => x.UserId == subscriber.UserId
                                                                                                                                        && x.MembershipStartDate == membershipUser.StartDate
                                                                                                                                        && x.MembershipEndDate == membershipUser.ValidUntill);
                        if (thisMonthsClaim != null)
                        {

                            membershipUser.ClaimBenefit(C3PayPlusMonitaryBenefits.MoneyTransferRefund);
                            await this._unitOfWork.CommitAsync();

                            // Error in tracking.
                            _logger.LogError(Errors.C3PayPlus.UserAlreadyClaimedFreeMoneyTransferBenefitForThisMonth.Code);
                            continue;
                        }
                    }

                    // Filter transactions to only keep the eligible ones for refund.
                    var allTransactions = await this._unitOfWork.MoneyTransferTransactions.FindAsync(x => x.UserId == subscriber.UserId && x.CreatedDate > subscriber.StartDate
                    && x.CreatedDate < subscriber.ValidUntill
                    && x.Status == Status.SUCCESSFUL
                    && x.TransferType == TransactionType.RAKMoney
                    && x.TotalCharges > 0);


                    if (allTransactions is null || allTransactions.Count == 0)
                    {
                        _logger.LogError(Errors.C3PayPlus.TransactionNotFound.Code);
                        continue;
                    }

                    var transactionEligibleForRefund = allTransactions.OrderBy(x => x.CreatedDate).FirstOrDefault();
                    if (transactionEligibleForRefund is null)
                    {
                        _logger.LogError(Errors.C3PayPlus.TransactionNotFound.Code);
                        continue;
                    }

                    // Make sure the transaction type is supported.
                    if (transactionEligibleForRefund.TransferType == TransactionType.Direct)
                    {
                        _logger.LogError(Errors.C3PayPlus.TransactionTypeNotSupported.Code);
                        continue;
                    }

                    // Make sure the transaction is successful.
                    if (transactionEligibleForRefund.Status != Status.SUCCESSFUL)
                    {
                        _logger.LogError(Errors.C3PayPlus.TransactionNotSuccessful.Code);
                        continue;
                    }

                    // Make sure the transaction has a fee.
                    if (transactionEligibleForRefund.TotalCharges.HasValue == false || transactionEligibleForRefund.TotalCharges.Value == 0)
                    {
                        _logger.LogError(Errors.C3PayPlus.TransactionAlreadyFree.Code);
                        continue;
                    }

                    // Perform refund.
                    var moneyTransferClaim = new C3PayPlusMembershipFreeMoneyTransferClaim()
                    {
                        UserId = subscriber.UserId,
                        MembershipUserId = membershipUser.Id,

                        MembershipStartDate = membershipUser.StartDate,
                        MembershipEndDate = membershipUser.ValidUntill,

                        MoneyTransferReferenceNumber = transactionEligibleForRefund.ReferenceNumber,
                        ClaimedAt = DateTime.Now,
                        RefundAmount = transactionEligibleForRefund.TotalCharges.Value,

                        ClaimedTriggeredFrom = C3PayPlusFreeMoneyTransferClaimTrigger.DailyWorker
                    };



                    var tryRefundMoneyTransfer = await this.RefundMoneyTransfer(subscriber.User.CardHolder, transactionEligibleForRefund.ChargesAmount.Value);
                    if (tryRefundMoneyTransfer.IsFailure == true)
                    {
                        _logger.LogError(tryRefundMoneyTransfer.Error.Code);
                        continue;
                    }


                    // Set reference number.
                    moneyTransferClaim.RefundReferenceNumber = tryRefundMoneyTransfer.Value;
                    await this._unitOfWork.C3PayPlusMembershipFreeMoneyTransferClaims.AddAsync(moneyTransferClaim);

                    membershipUser.ClaimBenefit(C3PayPlusMonitaryBenefits.MoneyTransferRefund);
                    await this._unitOfWork.CommitAsync();
                }

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund.Code + "Ex: " + ex.Message);
                return Result.Failure<C3PayPlusMembershipBaseDto<C3PayPlusMembershipDto>>(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund);
            }
        }


        private async Task<Result<string>> RefundMoneyTransfer(CardHolder cardholder, decimal amount)
        {
            var referencePrefix = TransactionPrefix.C3PMTR.ToString();

            // Format start date component.
            var startYear = DateTime.Now.Year.ToString().Substring(2);
            var startMonth = DateTime.Now.Month.ToString();
            var startDay = DateTime.Now.Day.ToString();

            // We are doing this because we want the date in this format.
            // 1994/08/29 becomes: 940829
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 10;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipFreeMoneyTransferClaims.Any(t => t.RefundReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }


            var cardNumber = cardholder.CardNumber;
            var cardSerialNumber = cardholder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var topUpRequest = new TopUpRequest()
            {
                CardSerialNumber = cardSerialNumber,
                CardPanNumber = cardPanNumber,
                MerchantLoopCode = "5000",
                Amount = decimal.Truncate(amount * 100),

                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusMoneyTransferRefund),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusMoneyTransferRefund),

                TerminalId = TransactionMerchantCodeService.GetMerchantCode(false, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.C3PayPlusMoneyTransferRefund),
                ReferenceNumber = referenceNumber
            };

            ServiceResponse<PPSWebAuthResponseModel> tryTopUpUser;

            try
            {
                tryTopUpUser = await _ppsWebAuthService.DoTopUp(topUpRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund.Code + "Ex: " + ex.Message);
                return Result.Failure<string>(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund);
            }

            if (tryTopUpUser.IsSuccessful == false)
            {
                _logger.LogError(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund.Code + "Error: " + tryTopUpUser.ErrorMessage);
                return Result.Failure<string>(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund);
            }

            if (tryTopUpUser.Data.StatusCode != "00")
            {
                _logger.LogError(Errors.C3PayPlus.ErrorPerformingMoneyTransferRefund.Code + "Error: " + tryTopUpUser.Data?.Message);
                return Result.Failure<string>(Errors.C3PayPlus.CantPerformCredit);
            }

            return Result.Success<string>(referenceNumber);
        }
    }
}
