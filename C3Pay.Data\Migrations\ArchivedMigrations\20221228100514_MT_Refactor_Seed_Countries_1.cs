﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class MTRefactorSeedCountries1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "MoneyTransferCountries",
                columns: new[] { "Id", "Alpha2Code", "Alpha3Code", "Currency", "DisplayOrder", "IsActive", "IsPopular", "IsdCode", "Name" },
                values: new object[,]
                {
                    { 1, "IN", "IND", "INR", 1, true, true, "91", "India" },
                    { 2, "PK", "PAK", "PKR", 2, true, true, "92", "Pakistan" },
                    { 3, "BD", "BGD", "BDT", 3, true, true, "880", "Bangladesh" },
                    { 4, "LK", "LKA", "LKR", 4, true, true, "94", "Sri Lanka" }
                });

            migrationBuilder.InsertData(
                table: "CountryTransferMethods",
                columns: new[] { "Id", "DetailsUrl", "IsActive", "MoneyTransferCountryId", "ProviderLogoUrl", "ProviderName", "Type" },
                values: new object[,]
                {
                    { 1, null, true, 1, null, "Bank Transfer", "BankTransfer" },
                    { 2, null, true, 1, null, "Cash Pickup", "CashPickup" },
                    { 3, null, true, 2, null, "BankTransfer", "BankTransfer" },
                    { 4, null, true, 2, null, "Cash Pickup", "CashPickup" },
                    { 5, null, true, 2, null, "Wallet 1", "Wallet" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "CountryTransferMethods",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "CountryTransferMethods",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "CountryTransferMethods",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "CountryTransferMethods",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "CountryTransferMethods",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "MoneyTransferCountries",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "MoneyTransferCountries",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "MoneyTransferCountries",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "MoneyTransferCountries",
                keyColumn: "Id",
                keyValue: 2);
        }
    }
}
