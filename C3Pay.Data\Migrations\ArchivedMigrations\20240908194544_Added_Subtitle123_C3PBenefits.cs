﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Subtitle123_C3PBenefits : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IconUrl",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubtitleText1",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubtitleText2",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubtitleText3",
                table: "C3PayPlusMembershipBenefits",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IconUrl",
                table: "C3PayPlusMembershipBenefits");

            migrationBuilder.DropColumn(
                name: "SubtitleText1",
                table: "C3PayPlusMembershipBenefits");

            migrationBuilder.DropColumn(
                name: "SubtitleText2",
                table: "C3PayPlusMembershipBenefits");

            migrationBuilder.DropColumn(
                name: "SubtitleText3",
                table: "C3PayPlusMembershipBenefits");
        }
    }
}
