﻿using Microsoft.AspNetCore.Http;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;

namespace Edenred.Common.Services.Azure.Extensions
{
    public class RequestBodyLoggingMiddleware : IMiddleware
    {
        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var method = context.Request.Method;

            context.Request.EnableBuffering();

            if (context.Request.Body.CanRead && (method == HttpMethods.Post || method == HttpMethods.Put))
            {
                using var reader = new StreamReader(
                    context.Request.Body,
                    Encoding.UTF8,
                    detectEncodingFromByteOrderMarks: false,
                    bufferSize: 512, leaveOpen: true);

                var requestBody = await reader.ReadToEndAsync();

                context.Request.Body.Position = 0;

                var activity = Activity.Current;
                if (activity != null && !string.IsNullOrEmpty(requestBody))
                {
                    // Only log non-sensitive request bodies (you may want to filter this)
                    activity.SetTag("http.request.body", requestBody);
                    activity.SetTag("http.request.body.size", requestBody.Length);
                }
            }

            await next(context);
        }
    }
}
