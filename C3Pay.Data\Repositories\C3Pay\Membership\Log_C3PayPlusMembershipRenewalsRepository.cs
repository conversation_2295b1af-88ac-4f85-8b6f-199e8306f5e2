﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logging;
using C3Pay.Core.Repositories.C3Pay.Membership;
using Edenred.Common.Data;
using Microsoft.EntityFrameworkCore;

namespace C3Pay.Data.Repositories.C3Pay.Membership
{
    public class Log_C3PayPlusMembershipRenewalsRepository : Repository<Log_C3PayPlusMembershipRenewals>, ILog_C3PayPlusMembershipRenewalsRepository
    {
        public Log_C3PayPlusMembershipRenewalsRepository(DbContext context) : base(context) { }

        private C3PayContext C3PayContext
        {
            get { return Context as C3PayContext; }
        }
    }
}
