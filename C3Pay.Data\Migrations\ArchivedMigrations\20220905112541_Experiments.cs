﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Experiments : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Experiments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
                    EstimatedStartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EstimatedEndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ActualStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActualEndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    HaveUniqueUser = table.Column<bool>(type: "bit", nullable: false),
                    TotalUserCount = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Experiments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ExperimentGroups",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Label = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true),
                    RulesJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExperimentGroups_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ExperimentLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExperimentLogs_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ExperimentGroupUsers",
                columns: table => new
                {
                    GroupId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserType = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentGroupUsers", x => new { x.UserId, x.GroupId });
                    table.ForeignKey(
                        name: "FK_ExperimentGroupUsers_ExperimentGroups_GroupId",
                        column: x => x.GroupId,
                        principalTable: "ExperimentGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ExperimentGroupUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Experiments",
                columns: new[] { "Id", "ActualEndDate", "ActualStartDate", "CreatedDate", "DeletedBy", "DeletedDate", "Description", "EstimatedEndDate", "EstimatedStartDate", "HaveUniqueUser", "IsActive", "IsDeleted", "Status", "TotalUserCount", "Type", "UpdatedDate" },
                values: new object[] { new Guid("388863e6-c871-4089-99c3-f88907add739"), null, null, new DateTime(2022, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Subscription ABTesting", new DateTime(2022, 9, 30, 0, 0, 0, 0, DateTimeKind.Unspecified), new DateTime(2022, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified), false, true, false, 0, 20000, 2, null });

            migrationBuilder.InsertData(
                table: "ExperimentGroups",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "ExperimentId", "IsDeleted", "Label", "RulesJson", "UpdatedDate" },
                values: new object[] { new Guid("2e713d85-c4d6-4839-97d6-ca4bf4466a06"), new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, new Guid("388863e6-c871-4089-99c3-f88907add739"), false, "A", "", null });

            migrationBuilder.InsertData(
                table: "ExperimentGroups",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "ExperimentId", "IsDeleted", "Label", "RulesJson", "UpdatedDate" },
                values: new object[] { new Guid("df767c68-424d-4efe-994c-78d27fefdf63"), new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, new Guid("388863e6-c871-4089-99c3-f88907add739"), false, "B", "", null });

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentGroups_ExperimentId",
                table: "ExperimentGroups",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentGroupUsers_GroupId",
                table: "ExperimentGroupUsers",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentLogs_ExperimentId",
                table: "ExperimentLogs",
                column: "ExperimentId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ExperimentGroupUsers");

            migrationBuilder.DropTable(
                name: "ExperimentLogs");

            migrationBuilder.DropTable(
                name: "ExperimentGroups");

            migrationBuilder.DropTable(
                name: "Experiments");
        }
    }
}
