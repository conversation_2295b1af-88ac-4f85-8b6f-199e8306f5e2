﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace C3Pay.Data.Migrations
{
    public partial class EHMoneyTransferChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferCorridors");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartnerReasons");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartners");

            migrationBuilder.CreateTable(
               name: "MoneyTransferPartners",
               columns: table => new
               {
                   Id = table.Column<int>(type: "int", nullable: false)
                       .Annotation("SqlServer:Identity", "1, 1"),
                   Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   Type = table.Column<int>(type: "int", nullable: false),
               },
               constraints: table =>
               {
                   table.PrimaryKey("PK_MoneyTransferPartners", x => x.Id);
               });

            migrationBuilder.CreateTable(
                name: "MoneyTransferCorridors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    VAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MoneyTransferEnabled = table.Column<bool>(type: "bit", nullable: false),
                    EligibleForBankTransfer = table.Column<bool>(type: "bit", nullable: false),
                    BankTransferLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    BankTransferRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BankTransferMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    EligibleForCashPickUp = table.Column<bool>(type: "bit", nullable: false),
                    CashPickUpLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    CashPickUpRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CashPickUpMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferCorridors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_MoneyTransferPartners_MoneyTransferPartnerId",
                        column: x => x.MoneyTransferPartnerId,
                        principalTable: "MoneyTransferPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                   name: "MoneyTransferPartnerReasons",
                   columns: table => new
                   {
                       Id = table.Column<int>(type: "int", nullable: false)
                           .Annotation("SqlServer:Identity", "1, 1"),
                       Reason = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
                       MoneyTransferReasonId = table.Column<int>(type: "int", nullable: false),
                       MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                       CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                   },
                   constraints: table =>
                   {
                       table.PrimaryKey("PK_MoneyTransferPartnerReasons", x => x.Id);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_Countries_CountryCode",
                           column: x => x.CountryCode,
                           principalTable: "Countries",
                           principalColumn: "Code",
                           onDelete: ReferentialAction.Restrict);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_MoneyTransferPartners_MoneyTransferPartnerId",
                           column: x => x.MoneyTransferPartnerId,
                           principalTable: "MoneyTransferPartners",
                           principalColumn: "Id",
                           onDelete: ReferentialAction.Cascade);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_MoneyTransferReasons_MoneyTransferReasonId",
                           column: x => x.MoneyTransferReasonId,
                           principalTable: "MoneyTransferReasons",
                           principalColumn: "Id",
                           onDelete: ReferentialAction.Cascade);
                   });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartners",
                columns: new[] { "Id", "Name", "Type" },
                values: new object[] { 1, "RAK Bank", 1 });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartners",
                columns: new[] { "Id", "Name", "Type" },
                values: new object[] { 2, "Index", 2 });

            migrationBuilder.InsertData(
                table: "MoneyTransferCorridors",
                columns: new[] { "Id", "BankTransferLatestRate", "BankTransferMaxAmount", "BankTransferMaxMonthlyAmount", "BankTransferMaxMonthlyCount", "BankTransferMinAmount", "BankTransferRateLastUpdatedDate", "CashPickUpLatestRate", "CashPickUpMaxAmount", "CashPickUpMaxMonthlyAmount", "CashPickUpMaxMonthlyCount", "CashPickUpMinAmount", "CashPickUpRateLastUpdatedDate", "CountryCode", "EligibleForBankTransfer", "EligibleForCashPickUp", "Fee", "IsActive", "MoneyTransferEnabled", "MoneyTransferPartnerId", "Name", "VAT" },
                values: new object[,]
                {
                    { 6, 0m, 19823m, 30000m, 7, 25m, null, 0m, 15000m, 30000m, 7, 25m, null, "LK", true, true, 0m, true, true, 2, null, 0m },
                    { 4, 0m, 29900m, 30000m, 7, 25m, null, 0m, 3600m, 30000m, 7, 25m, null, "PH", true, true, 0m, true, true, 2, null, 0m },
                    { 3, 0m, 10000m, 30000m, 7, 25m, null, 0m, null, null, null, null, null, "IN", true, false, 0m, true, true, 2, null, 0m },
                    { 2, 0m, 15000m, 30000m, 7, 25m, null, 0m, 15000m, 30000m, 7, 25m, null, "PK", true, true, 0m, true, true, 2, null, 0m },
                    { 1, 0m, 15000m, 30000m, 7, 25m, null, 0m, null, null, null, null, null, "BD", true, false, 0m, true, true, 2, null, 0m },
                    { 5, 0m, 15000m, 30000m, 7, 25m, null, 0m, 3000m, 30000m, 7, 25m, null, "NP", true, true, 0m, true, true, 2, null, 0m }
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartnerReasons",
                columns: new[] { "Id", "CountryCode", "MoneyTransferPartnerId", "MoneyTransferReasonId", "Reason" },
                values: new object[,]
                {
                    { 24, "LK", 2, 21, "INSURANCE PAYMENT" },
                    { 23, "LK", 2, 24, "MEDICAL EXPENSE" },
                    { 22, "LK", 2, 22, "EDUCATIONAL EXPENSES" },
                    { 21, "LK", 2, 8, "FAMILY MAINTENANCE" },
                    { 20, "NP", 2, 21, "INSURANCE PAYMENT" },
                    { 19, "NP", 2, 24, "MEDICAL EXPENSE" },
                    { 18, "NP", 2, 22, "EDUCATIONAL EXPENSES" },
                    { 17, "NP", 2, 8, "FAMILY MAINTENANCE" },
                    { 16, "PH", 2, 21, "INSURANCE PAYMENT" },
                    { 15, "PH", 2, 24, "MEDICAL EXPENSE" },
                    { 13, "PH", 2, 8, "FAMILY MAINTENANCE" },
                    { 12, "IN", 2, 21, "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS" },
                    { 11, "IN", 2, 24, "PAYMENT FOR MEDICAL TREATMENT" },
                    { 10, "IN", 2, 22, "PAYMENT TO SCHOOLS AND COLLEGES" },
                    { 9, "IN",  2, 8, "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS" },
                    { 8, "PK",  2, 21, "INSURANCE PAYMENT" },
                    { 7, "PK",  2, 24, "MEDICAL EXPENSE" },
                    { 6, "PK",  2, 22, "EDUCATIONAL EXPENSES" },
                    { 5, "PK",  2, 8, "FAMILY MAINTENANCE" },
                    { 4, "BD",  2, 21, "INSURANCE PAYMENT" },
                    { 3, "BD",  2, 24, "MEDICAL EXPENSE" },
                    { 2, "BD",  2, 22, "EDUCATIONAL EXPENSES" },
                    { 14, "PH", 2, 22, "EDUCATIONAL EXPENSES" },
                    { 1, "BD",  2, 8, "FAMILY MAINTENANCE" }
               });

            migrationBuilder.AddColumn<decimal>(
                name: "ChargeVat",
                table: "MoneyTransferExternalTransactions",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true,
                defaultValue: 0m);          

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "MoneyTransferReasons",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.UpdateData(
                table: "MoneyTransferReasons",
                keyColumn: "Id",
                keyValue: 8,
                column: "IsActive",
                value: true);

            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBranches",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBanks",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBanks_MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                column: "MoneyTransferPartnerId");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferBanks_MoneyTransferPartners_MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                column: "MoneyTransferPartnerId",
                principalTable: "MoneyTransferPartners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBranches");

            migrationBuilder.RenameColumn(
                name: "ExternalId",
                table: "MoneyTransferBanks",
                newName: "PrimaryIdentifierCode");

            migrationBuilder.AddColumn<string>(
                name: "CustomerAddress",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDExpDate",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDIssueDate",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IDPlaceOfIssue",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UniqueIdentificationNo",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
               name: "InvoiceNumber",
               table: "MoneyTransferExternalTransactions",
               type: "nvarchar(max)",
               nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TTNumber",
                table: "MoneyTransferExternalTransactions",
                type: "nvarchar(max)",
                nullable: true);

            var query = @"  
                Update MoneyTransferBanks set MoneyTransferPartnerId = 1;
            ";

            migrationBuilder.Sql(query);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferCorridors");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartnerReasons");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartners");

            migrationBuilder.CreateTable(
               name: "MoneyTransferPartners",
               columns: table => new
               {
                   Id = table.Column<int>(type: "int", nullable: false)
                       .Annotation("SqlServer:Identity", "1, 1"),
                   Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   Type = table.Column<int>(type: "int", nullable: false)                   
               },
               constraints: table =>
               {
                   table.PrimaryKey("PK_MoneyTransferPartners", x => x.Id);
               });

            migrationBuilder.CreateTable(
                name: "MoneyTransferCorridors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    VAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MoneyTransferEnabled = table.Column<bool>(type: "bit", nullable: false),
                    EligibleForBankTransfer = table.Column<bool>(type: "bit", nullable: false),
                    BankTransferLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    BankTransferRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BankTransferMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    EligibleForCashPickUp = table.Column<bool>(type: "bit", nullable: false),
                    CashPickUpLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    CashPickUpRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CashPickUpMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyCount = table.Column<int>(type: "int", nullable: true)                   
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferCorridors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_MoneyTransferPartners_MoneyTransferPartnerId",
                        column: x => x.MoneyTransferPartnerId,
                        principalTable: "MoneyTransferPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.DropColumn(
                name: "ChargeVat",
                table: "MoneyTransferExternalTransactions");                          

            migrationBuilder.DropColumn(
               name: "IsActive",
               table: "MoneyTransferReasons");

            migrationBuilder.DropForeignKey(
               name: "FK_MoneyTransferBanks_MoneyTransferPartners_MoneyTransferPartnerId",
               table: "MoneyTransferBanks");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferBanks_MoneyTransferPartnerId",
                table: "MoneyTransferBanks");

            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBranches");

            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBanks");

            migrationBuilder.DropColumn(
                name: "MoneyTransferPartnerId",
                table: "MoneyTransferBanks");

            migrationBuilder.RenameColumn(
                name: "PrimaryIdentifierCode",
                table: "MoneyTransferBanks",
                newName: "ExternalId");

            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBranches",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.DropColumn(
                name: "CustomerAddress",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDExpDate",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDIssueDate",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "IDPlaceOfIssue",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "UniqueIdentificationNo",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "InvoiceNumber",
                table: "MoneyTransferExternalTransactions");

            migrationBuilder.DropColumn(
                name: "TTNumber",
                table: "MoneyTransferExternalTransactions");
        }
    }
}
