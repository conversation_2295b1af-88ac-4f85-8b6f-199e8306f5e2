﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class ModifiedRenewalTableName : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MobileRechargeRenewal_Users_UserId",
                table: "MobileRechargeRenewal");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MobileRechargeRenewal",
                table: "MobileRechargeRenewal");

            migrationBuilder.RenameTable(
                name: "MobileRechargeRenewal",
                newName: "MobileRechargeRenewals");

            migrationBuilder.RenameIndex(
                name: "IX_MobileRechargeRenewal_UserId",
                table: "MobileRechargeRenewals",
                newName: "IX_MobileRechargeRenewals_UserId");

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "MobileRechargeRenewals",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWSEQUENTIALID()",
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MobileRechargeRenewals",
                table: "MobileRechargeRenewals",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeRenewals_RenewalDate",
                table: "MobileRechargeRenewals",
                column: "RenewalDate");

            migrationBuilder.AddForeignKey(
                name: "FK_MobileRechargeRenewals_Users_UserId",
                table: "MobileRechargeRenewals",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MobileRechargeRenewals_Users_UserId",
                table: "MobileRechargeRenewals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_MobileRechargeRenewals",
                table: "MobileRechargeRenewals");

            migrationBuilder.DropIndex(
                name: "IX_MobileRechargeRenewals_RenewalDate",
                table: "MobileRechargeRenewals");

            migrationBuilder.RenameTable(
                name: "MobileRechargeRenewals",
                newName: "MobileRechargeRenewal");

            migrationBuilder.RenameIndex(
                name: "IX_MobileRechargeRenewals_UserId",
                table: "MobileRechargeRenewal",
                newName: "IX_MobileRechargeRenewal_UserId");

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "MobileRechargeRenewal",
                type: "uniqueidentifier",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldDefaultValueSql: "NEWSEQUENTIALID()");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MobileRechargeRenewal",
                table: "MobileRechargeRenewal",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_MobileRechargeRenewal_Users_UserId",
                table: "MobileRechargeRenewal",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
