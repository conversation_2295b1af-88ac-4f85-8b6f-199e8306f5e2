﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class RemoveLoginVideoSeeding : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "LoginVideoResources",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideoResources",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "LoginVideoResources",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "LoginVideoResources",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideos",
                keyColumn: "Id",
                keyValue: 2);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "LoginVideos",
                columns: new[] { "Id", "Metadata", "Owner", "Title", "ValidationId" },
                values: new object[] { 1, null, "MoneyTransfer", "Rates Experiment", null });

            migrationBuilder.InsertData(
                table: "LoginVideos",
                columns: new[] { "Id", "Metadata", "Owner", "Title", "ValidationId" },
                values: new object[] { 2, "{\"primaryCtas\":[{\"style\":\"PrimaryCtaWithWhiteBg\",\"order\":1,\"label\":\"lgvideo_cta_gotit\",\"deeplink\":\"/home\"},{\"style\":\"PrimaryCtaWithTransparentBg\",\"order\":2,\"label\":\"lgvideo_txt_cpmn_fmt\",\"deeplink\":\"/C3PayPlus/LifeInsuranceOnBoardingVideo\"}],\"ctaChangeLanguageLabel\":\"lgvideo_txt_chng_lang\",\"ctaChangeLanguageColor\":\"White\",\"ctaSecondaryLabel\":\"lgvideo_ctrl_skip\",\"ctaSecondaryLabelColor\":\"White\",\"ctaSecondaryDeepLink\":\"/c3payplus\",\"timeOutSeconds\":3,\"mustWatchSeconds\":5,\"showSkipButton\":true,\"showSeekBarView\":true,\"showVideoLoopButton\":false,\"onCompletionClose\":true}", "C3PayPlus", "Weekly Draw", "C3PayPlusLoginVideoValidator" });

            migrationBuilder.InsertData(
                table: "LoginVideoResources",
                columns: new[] { "Id", "LanguageCode", "LoginVideoId", "Url" },
                values: new object[,]
                {
                    { 1, "en", 1, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_en.mp4" },
                    { 2, "hi", 1, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_hi.mp4" },
                    { 3, "en", 2, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Eng_10AED.mp4" },
                    { 4, "hi", 2, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Hi_10AED.mp4" }
                });

            migrationBuilder.InsertData(
                table: "LoginVideoSlots",
                columns: new[] { "Id", "DisplayOrder", "LoginVideoId" },
                values: new object[,]
                {
                    { 1, 1, 1 },
                    { 2, 2, 2 },
                    { 3, 3, 2 }
                });
        }
    }
}
