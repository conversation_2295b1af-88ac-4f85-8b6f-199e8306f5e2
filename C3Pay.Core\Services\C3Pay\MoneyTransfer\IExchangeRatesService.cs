﻿using C3Pay.Core.Models.C3Pay.MoneyTransfer.ExchangeRates;
using Edenred.Common.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace C3Pay.Core.Services.C3Pay.MoneyTransfer
{
    public interface IExchangeRatesService
    {
        Task<ServiceResponse<FxRateResponseRakModel>> GetExchangeRates(GetExchangeRatesOptions options);
    }
}
