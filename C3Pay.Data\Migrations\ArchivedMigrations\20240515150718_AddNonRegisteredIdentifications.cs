﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddNonRegisteredIdentifications : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "NonRegisteredIdentification",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    CardHolderId = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    CardSerialNumber = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    FirstName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LastName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Birthdate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DocumentNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
                    CardNumber = table.Column<string>(type: "nvarchar(9)", maxLength: 9, nullable: true),
                    Nationality = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Gender = table.Column<int>(type: "int", maxLength: 6, nullable: false),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateType = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false, defaultValue: 1),
                    FaceMatchPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FaceMatchIsIdeal = table.Column<bool>(type: "bit", nullable: false),
                    NameMatchPercentage = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    NameMatchIsIdeal = table.Column<bool>(type: "bit", nullable: false),
                    FrontScanFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    BackScanFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    SelfieFileName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    VerifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    VerificationStatus = table.Column<int>(type: "int", nullable: false),
                    VerificationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OriginalVerifierId = table.Column<int>(type: "int", nullable: true),
                    Vendor = table.Column<int>(type: "int", nullable: false),
                    ServiceReference = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Posted = table.Column<bool>(type: "bit", nullable: false),
                    PostRemarks = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UpdatePostedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UserRegistrationRejectionReasonId = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NonRegisteredIdentification", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NonRegisteredIdentification_UserRegistrationRejectionReasons_UserRegistrationRejectionReasonId",
                        column: x => x.UserRegistrationRejectionReasonId,
                        principalTable: "UserRegistrationRejectionReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_NonRegisteredIdentification_UserRegistrationRejectionReasonId",
                table: "NonRegisteredIdentification",
                column: "UserRegistrationRejectionReasonId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "NonRegisteredIdentification");
        }
    }
}
