﻿using Edenred.Common.Core;
using Microsoft.Azure.Cosmos.Table;
using System.Net;
using System.Threading.Tasks;

namespace Edenred.Common.Services
{
    public class TableStorageService : ITableStorageService
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly CloudTableClient _tableServiceClient;

        /// <summary>
        /// 
        /// </summary>
        /// 
        public TableStorageService(CloudTableClient tableServiceClient)
        {
            this._tableServiceClient = tableServiceClient;
        }

        public async Task<ServiceResponse> SetEntityAsync(string tableName, ValueEntity entity)
        {
            var table = _tableServiceClient.GetTableReference(tableName);

            var tableExists = await table.ExistsAsync();

            if (!tableExists)
            {
                return new ServiceResponse(false, string.Format(SystemMessages.StorageTableNotFound, tableName));
            }

            var insertOrMergeOperation = TableOperation.InsertOrMerge(entity);

            var result = await table.ExecuteAsync(insertOrMergeOperation);

            if (result.HttpStatusCode != (int)HttpStatusCode.NoContent)
            {
                return new ServiceResponse(false, result.Result.ToString());
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse> InsertEntityAsync(string tableName, ValueEntity entity)
        {
            var table = _tableServiceClient.GetTableReference(tableName);

            var tableExists = await table.ExistsAsync();
            if (!tableExists)
            {
                return new ServiceResponse(false, string.Format(SystemMessages.StorageTableNotFound, tableName));
            }

            try
            {
                var insertOperation = TableOperation.Insert(entity);
                var result = await table.ExecuteAsync(insertOperation);

                if (result.HttpStatusCode != (int)HttpStatusCode.NoContent)
                {
                    return new ServiceResponse(false, result.Result?.ToString());
                }

                return new ServiceResponse();
            }
            catch (StorageException ex) when (ex.RequestInformation.HttpStatusCode == 409)
            {
                return new ServiceResponse(false, SystemMessages.TableStorageConflictException);
            }
        }

        public async Task<ServiceResponse<ValueEntity>> GetEntityAsync(string tableName, string partitionKey, string rowKey)
        {
            var table = _tableServiceClient.GetTableReference(tableName);

            var tableExists = await table.ExistsAsync();

            if (!tableExists)
            {
                return new ServiceResponse<ValueEntity>(false, string.Format(SystemMessages.StorageTableNotFound, tableName));
            }

            var retrieveOperation = TableOperation.Retrieve<ValueEntity>(partitionKey, rowKey);

            var result = await table.ExecuteAsync(retrieveOperation);

            var entity = result.Result as ValueEntity;

            return new ServiceResponse<ValueEntity>(entity);
        }

        public async Task<ServiceResponse> DeleteEntityAsync(string tableName, ValueEntity entity)
        {
            var table = _tableServiceClient.GetTableReference(tableName);

            var tableExists = await table.ExistsAsync();

            if (!tableExists)
            {
                return new ServiceResponse(false, string.Format(SystemMessages.StorageTableNotFound, tableName));
            }          

            var deleteOperation = TableOperation.Delete(entity);

            var result = await table.ExecuteAsync(deleteOperation);

            if (result.HttpStatusCode != (int)HttpStatusCode.NoContent)
            {
                return new ServiceResponse(false, result.Result.ToString());
            }

            return new ServiceResponse();
        }
    }
}
