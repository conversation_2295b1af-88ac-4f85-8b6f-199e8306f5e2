# ASP.NET
# Build and test ASP.NET projects.
# Add steps that publish symbols, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/apps/aspnet/build-aspnet-4

trigger:
- Release

pool: 
   name: Windows
   demands:
    - agent.name -equals self 

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

jobs:
  - job: Build
    steps:
    - task: SonarQubePrepare@5
      inputs:
        SonarQube: 'Sonarqube check'
        scannerMode: 'MSBuild'
        projectKey: 'C3Pay.Backend_C3Pay.Backend_AYHOnKvx3OBW9dYTo7Ry'
        projectName: 'C3Pay.Backend'

    - task: NuGetToolInstaller@1

    - task: DotNetCoreCLI@2
      displayName: dotnet restore
      inputs:
        command: restore
        projects: '**/*.csproj'
        feedsToUse: config
        nugetConfigPath: 'NuGet/NuGet.config'


    - task: VSBuild@1
      inputs:
        solution: '$(solution)'
        msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation="$(build.artifactStagingDirectory)"'
        platform: '$(buildPlatform)'
        configuration: '$(buildConfiguration)'

    - task: VSTest@2
      inputs:
        platform: '$(buildPlatform)'
        configuration: '$(buildConfiguration)'
        
    - task: SonarQubeAnalyze@5

    - task: SonarQubePublish@5
      inputs:
        pollingTimeoutSec: '300'
    
  - job: WaitingApproval
    dependsOn: Build
    pool: server
    timeoutInMinutes: 1000    
    steps:
    - task: ManualValidation@0
      inputs:
           notifyUsers: '<EMAIL>'
           instructions: 'check Sonarqube report'
  
  - job: Deploy
    dependsOn: WaitingApproval
    steps:
      - task: NuGetToolInstaller@1

      - task: DotNetCoreCLI@2
        displayName: dotnet restore
        inputs:
          command: restore
          projects: '**/*.csproj'
          feedsToUse: config
          nugetConfigPath: 'NuGet/NuGet.config'


      - task: VSBuild@1
        inputs:
          solution: '$(solution)'
          msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:PackageLocation="$(build.artifactStagingDirectory)"'
          platform: '$(buildPlatform)'
          configuration: '$(buildConfiguration)'

      - task: VSTest@2
        inputs:
          platform: '$(buildPlatform)'
          configuration: '$(buildConfiguration)'
         