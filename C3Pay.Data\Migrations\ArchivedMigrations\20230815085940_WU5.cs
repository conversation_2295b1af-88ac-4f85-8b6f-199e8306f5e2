﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU5 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "RatesLastUpdateAttemptSuccessful",
                table: "Countries",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "WalletLatestRate",
                table: "Countries",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RatesLastUpdateAttemptSuccessful",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "WalletLatestRate",
                table: "Countries");
        }
    }
}
