﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_Fields_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferFieldGroups_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId",
                principalTable: "MoneyTransferCountries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferFieldGroups_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.DropColumn(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "MoneyTransferFieldGroups");
        }
    }
}
