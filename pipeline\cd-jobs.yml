﻿parameters:
  - name: azureSubscription
    type: string
  - name: azureSubscriptionEQ
    type: string
  - name: targetenvironment
    type: string
  - name: templateLocation
    type: string
  - name: entityCode
    type: string
  - name: environment
    type: string
  - name: appName
    type: string
  - name: existingAppServiceName
    type: string
  - name: existingWebjobServiceName
    type: string
  - name: location
    type: string
  - name: secondaryLocation
    type: string
  - name: rgName
    type: string
  - name: farmSkuName
    type: string
  - name: webJobFarmSkuName
    type: string
  - name: farmSkuCapacity
    type: number
  - name: farmSkuCapacityMin
    type: number
    default: 0
  - name: farmSkuCapacityMax
    type: number
    default: 0
  - name: dbName
    type: string
  - name: dbSkuName
    type: string
  - name: dbWeeklyLtr
    type: string
  - name: dbMonthlyLtr
    type: string
  - name: identityDbSkuName
    type: string
  - name: serviceBusSkuName
    type: string
  - name: infraFile
    type: string
  - name: webAppBinariesFile
    type: string
  - name: webJobBinariesFile
    type: string
  - name: portalWebAppBinariesFile
    type: string
  - name: sqlFile
    type: string
  - name: identitySqlFile
    type: string
  - name: sqlsrvAdministratorLogin
    type: string
  - name: sqlsrvAdministratorPassword
    type: string
  - name: DevIP
    type: string
  - name: rakCertificateName
    type: string
  - name: rakCertificatePassword
    type: string
  - name: rakPrivateKeyName
    type: string
  - name: rakPublicKeyName
    type: string
  - name: SettingRakSftpEndPoint
    type: string
  - name: SettingRakSftpPort
    type: string
  - name: SettingRakSftpUsername
    type: string
  - name: SettingRakSftpPassword
    type: string
  - name: SettingRAKSFTPInputRootDirectory
    type: string
  - name: SettingRAKSFTPOutputRootDirectory
    type: string
  - name: SettingRAKSFTPTransactionStatusDirectory
    type: string
  - name: SettingRAKSFTPTransactionBlobContainerName
    type: string
  - name: SettingRAKSFTPProfileStatusDirectory
    type: string
  - name: SettingRAKSFTPBranchesDirectory
    type: string
  - name: SettingRAKSFTPBranchesArchiveDirectory
    type: string
  - name: SettingRakSftpReportDirectory
    type: string
  - name: SettingRakSftpRmtAcknowledgementDirectory
    type: string
  - name: SettingRakSftpRmtTempSubmissionDirectory
    type: string
  - name: SettingRakEnableRakTokenCache
    type: string
  - name: SettingEDCAuthority
    type: string
  - name: SettingEDCApiName
    type: string
  - name: SettingEDCApiSecret
    type: string
  - name: SettingPortalEDCAuthority
    type: string
  - name: SettingPortalEDCApiName
    type: string
  - name: SettingPortalEDCApiSecret
    type: string
  - name: SettingAADAuthority
    type: string
  - name: SettingAADAudience
    type: string
  - name: SettingAADAllowedClientIds
    type: string
  - name: SettingKeyName
    type: string
  - name: SettingSignzyURL
    type: string
  - name: SettingSignzyFileExchangeAddress
    type: string
  - name: SettingSignzyId
    type: string
  - name: SettingSignzyUserId
    type: string
  - name: SettingSignzyImageQualityTimeout
    type: string
  - name: SettingSignzyFaceMatchTimeout
    type: string
  - name: SettingSignzyReadDocumentTimeout
    type: string
  - name: SettingAzureOCREndpoint
    type: string
  - name: SettingAzureOCRKey
    type: string
  - name: SettingAzureOCRWaitTimeInMilliSeconds
    type: string
  - name: SettingAzureFaceEndpoint
    type: string
  - name: SettingAzureFaceKey
    type: string
  - name: SettingAzureIdentificationServiceUseAzureOCR
    type: string
  - name: SettingAzureIdentificationServiceUseAzureFace
    type: string
  - name: SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck
    type: string
  - name: SettingKycExpiryCheckStartDate
    type: string
  - name: SettingKycExpiryValidateEmiratesIdExpiryScheduler
    type: string
  - name: SettingKycExpiryValidateAdditionKYCScheduler
    type: string
  - name: SettingRakBaseURL
    type: string
  - name: SettingRakClientId
    type: string
  - name: SettingRakClientSecret
    type: string
  - name: SettingRakProcessBankTransactionsReportsSchedule
    type: string
  - name: SettingRakUpdatePendingBankTransactionsSchedule
    type: string
  - name: SettingRakReverseFailedBankTransactionsSchedule
    type: string
  - name: SettingRakProcessRakStatementSchedule
    type: string
  - name: SettingRakProcessRmtSubmissionSchedule
    type: string
  - name: SettingRakProcessRmtAcknowledgementSchedule
    type: string
  - name: SettingUploadMissingProfilesImagesSchedule
    type: string 
  - name: SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule
    type: string
  - name: SettingExchangeHouseUpdateStatusSchedule
    type: string
  - name: SettingRakMoneyTransferBeneficiaryCount
    type: string
  - name: SettingRakBanksMaxRecords
    type: string
  - name: SettingRakMaxTransactionTriesCount
    type: string
  - name: SettingRakMessageProcessInDelay
    type: string
  - name: SettingRakTransactionUpdateDelay
    type: string
  - name: SettingRakMoneyTransferBeneficiaryDelayInMins
    type: string
  - name: SettingRakRmtProfilePositiveStatus
    type: string
  - name: SettingRakLoyaltyImplementDate
    type: string
  - name: SettingRakLoyaltyLimitCount
    type: string
  - name: SettingRakLoyaltyLimitAmount
    type: string
  - name: SettingRakRefreshRatesSchedule
    type: string
  - name: SettingExchangeHouseRefreshRatesSchedule
    type: string
  - name: SettingRakRefreshRatesEmiratesId
    type: string
  - name: SettingRakURLPath
    type: string
  - name: SettingPPSWebAuthBaseURL
    type: string
  - name: SettingPPSWebAuthClientId
    type: string
  - name: SettingPPSWebAuthClientSecretkey
    type: string
  - name: SettingPPSEndpointAddress
    type: string
  - name: SettingPPSUsername
    type: string
  - name: SettingPPSPassword
    type: string
  - name: SettingPPSSponsorCode
    type: string
  - name: SettingPPSCustomerCode
    type: string
  - name: SettingPPSSharedSecret
    type: string
  - name: SettingPPSTimeout
    type: string
  - name: SettingEtisalatSMSUsername
    type: string
  - name: SettingEtisalatSMSPassword
    type: string
  - name: SettingEtisalatSMSBaseAddress
    type: string
  - name: SettingEtisalatSMSTimeout
    type: string
  - name: SettingEtisalatSMSRetryCount
    type: string
  - name: SettingInfobipSMSUsername
    type: string
  - name: SettingInfobipSMSPassword
    type: string
  - name: SettingInfobipSMSBaseAddress
    type: string
  - name: SettingInfobipSMSAuthKey
    type: string
  - name: SettingInfobipSMSAuthKeyBaseUrl
    type: string
  - name: SettingInfobipSMSSmsMode
    type: string
  - name: SettingInfobipSMSTimeout
    type: string
  - name: SettingInfobipSMSRetryCount
    type: string
  - name: SettingDingBaseURL
    type: string
  - name: SettingDingClientApiKey
    type: string
  - name: SettingSecondaryDingClientApiKey
    type: string
  - name: SettingMobileRechargeSynchronizeWithDingSchedule
    type: string
  - name: SettingMobileRechargeUpdateStatusSchedule
    type: string
  - name: SettingMobileRechargeNickNameLength
    type: string
  - name: SettingMobileRechargeCallingCardAccountNumberLive
    type: string
  - name: SettingMobileRechargeTransactionEnvironment
    type: string
  - name: SettingMobileRechargeC3FeeMode
    type: string
  - name: SettingMobileRechargeMySalaryFeeMode
    type: string
  - name: SettingMobileRechargeSelectedCorporatesWithFee
    type: string
  - name: SettingMobileRechargeFeeAmount
    type: string
  - name: SettingMobileRechargeNonVerifiedLimit
    type: string
  - name: SettingMobileRechargeVerifiedLimit
    type: string
  - name: SettingMobileRechargeCustomCallingCardName
    type: string
  - name: SettingMobileRechargeCustomCallingCardCode
    type: string
  - name: SettingMobileRechargeCustomCallingCardLogoUrl
    type: string
  - name: SettingMobileRechargeCustomCallingCardValidationRegex
    type: string 
  - name: SettingMobileRechargeDynamicPackageSeperator
    type: string
  - name: SettingMobileRechargeServiceServiceBusTopicName
    type: string
  - name: SettingMobileRechargeServiceServiceBusSubscriptionName
    type: string
  - name: SettingMobileRechargeServiceRenewalSchedule
    type: string
  - name: SettingMobileRechargeServiceLowBalanceRenewalSchedule
    type: string
  - name: SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays
    type: string
  - name: SettingVpnMembershipRenewalSchedule
    type: string
  - name: SettingRenewalCardUpdateServiceBusTopicName
    type: string
  - name: SettingRenewalCardUpdateServiceBusSubscriptionName
    type: string
  - name: SettingMoneyTransferMultimediaURL
    type: string 
  - name: SettingMoneyTransferRefreshBanksAndBranchesSchedule
    type: string
  - name: SettingMoneyTransferReversalStartDate
    type: string
  - name: SettingMoneyTransferReversalMode
    type: string
  - name: SettingMoneyTransferComparisonReceiveAmount
    type: string
  - name: SettingMoneyTransferComparisonEHTransferFee
    type: string
  - name: SettingMoneyTransferComparisonEHRateIncrement
    type: string
  - name: SettingMoneyTransferGWNationalities
    type: string
  - name: SettingMoneyTransferGWLanguages
    type: string
  - name: SettingMoneyTransferGWStartDate
    type: string
  - name: SettingMoneyTransferGeneralDefaultAmount
    type: string
  - name: SettingMoneyTransferGeneralDefaultCurrency
    type: string
  - name: SettingMoneyTransferGeneralDefaultType
    type: string
  - name: SettingMoneyTransferMaxRepeatTransferCount
    type: string
  - name: SettingMoneyTransferMinUserBalanceForRepeatTransfer
    type: string
  - name: SettingMoneyTransferReverseOnHoldSchedule
    type: string
  - name: SettingMoneyTransferReverseOnHoldMinNoOfDays
    type: string
  - name: SettingMoneyTransferRateExpiryInMinutes
    type: string
  - name: SettingMoneyTransferRmtCreationSchedule
    type: string
  - name: SettingMoneyTransferPendingSchedulerMinNoOfDays
    type: string
  - name: SettingMoneyTransferCheckMinSuspiciousDate
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountIND
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountPHL
    type: string
  - name: SettingExperimentNoLoyaltyMaxCountNPL
    type: string
  - name: SettingEdenredIdentityManagerBaseAddress
    type: string
  - name: SettingEdenredIdentityManagerAuthority
    type: string
  - name: SettingEdenredIdentityManagerResourceId
    type: string
  - name: SettingEdenredIdentityManagerClientId
    type: string
  - name: SettingEdenredIdentityManagerClientSecret
    type: string
  - name: SettingFirebaseCloudMessagingBaseAddress
    type: string
  - name: SettingFirebaseCloudMessagingKey
    type: string
  - name: SettingFirebaseCloudMessagingSenderId
    type: string
  - name: SettingFirebaseCloudMessagingRetryCount
    type: string
  - name: SettingFirebaseCloudMessagingTimeout
    type: string
  - name: SettingESMOServiceBaseAddress
    type: string
  - name: SettingESMOServiceClientId
    type: string
  - name: SettingESMOServiceClientSecret
    type: string
  - name: SettingESMOServiceAuthority
    type: string
  - name: SettingESMOServiceScope
    type: string
  - name: SettingESMOServiceTimeout
    type: string
  - name: SettingTransactionsB2CServiceAuthority
    type: string
  - name: SettingTransactionsB2CServiceClientId
    type: string
  - name: SettingTransactionsB2CServiceScope
    type: string
  - name: SettingTransactionsB2CServiceClientSecret
    type: string
  - name: SettingTransactionsB2CServiceBaseAddress
    type: string
  - name: SettingTransactionsB2CServiceGrantType
    type: string
  - name: SettingTransactionsB2CServiceAPIVersion
    type: string
  - name: SettingTransactionsB2CServiceTimeout
    type: string
  - name: SettingKYCBaseAddress
    type: string
  - name: SettingKYCUsername
    type: string
  - name: SettingKYCPassword
    type: string
  - name: SettingKYCUniqueRef
    type: string
  - name: SettingKYCSponsorCode
    type: string
  - name: SettingKYCSharedSecret
    type: string
  - name: SettingMobileAppHashKey
    type: string
  - name: SettingSendGridSenderEmail
    type: string
  - name: SettingSendGridAPIKey
    type: string
  - name: SettingSendGridCardHolderRegistrationRejectedEmailTemplateId
    type: string
  - name: SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId
    type: string
  - name: SettingSendGridBankStatementEmailTemplateId
    type: string
  - name: SettingSendGridStoreOrderPlacedEmailTemplateId
    type: string
  - name: SettingSendGridPortalUserCreatedEmailTemplateId
    type: string
  - name: SettingPortalAdminPasswordResetEmailTemplateId
    type: string
  - name: SettingRedisConnection
    type: string
  - name: SettingServiceBusConnection
    type: string
  - name: SettingRakReadRMTProfileResponsesSchedule
    type: string
  - name: SettingRakSftpRMTProfileResponsesDirectory
    type: string
  - name: SettingRakSftpMissingRakFileAlertPhoneNumbers
    type: string
  - name: SettingFirstBlackV1PlasticCardId
    type: string
  - name: SettingFirstBlackV2PlasticCardId
    type: string
  - name: SettingCleverTapBaseAddress
    type: string
  - name: SettingCleverTapProjectId
    type: string
  - name: SettingCleverTapPassCode
    type: string
  - name: SettingExchangeHouseBaseAddress
    type: string
  - name: SettingExchangeHouseUsername
    type: string
  - name: SettingExchangeHousePassword
    type: string
  - name: SettingExchangeHouseMaxAllowedBeneficiaryCount
    type: string
  - name: SettingExchangeHouseMoneyTransferQueueConnectionString
    type: string
  - name: SettingExchangeHouseMoneyTransferQueueName
    type: string
  - name: SettingReferralProgramMoneyTransferCountThreshold
    type: string
  - name: SettingReferralProgramMoneyTransferAmountThreshold
    type: string
  - name: SettingReferralProgramMoneyTransferRewardAmount
    type: string
  - name: SettingReferralProgramMoneyTransferReferralProgramStartDate
    type: string
  - name: SettingPayrollServiceBaseAddress
    type: string
  - name: SettingPayrollServiceAuthority
    type: string
  - name: SettingPayrollServiceClientId
    type: string
  - name: SettingPayrollServiceClientSecret
    type: string
  - name: SettingPayrollServiceScope
    type: string
  - name: SettingHRServiceBaseAddress
    type: string
  - name: SettingHRServiceAuthority
    type: string
  - name: SettingHRServiceClientId
    type: string
  - name: SettingHRServiceClientSecret
    type: string
  - name: SettingHRServiceScope
    type: string
  - name: SettingHRServiceCacheInMinutes
    type: string

    # Step 4: Add your keys here like this: "Setting" + <setting name (same as property)> with the type of the key.
  - name: SettingDirectTransferMaxBeneficiariesCount
    type: string
  - name: SettingDirectTransferMinAmountToSend
    type: string
  - name: SettingDirectTransferMaxAmountToSend
    type: string
  - name: SettingDirectTransferMaxAmountToSendPerMonth
    type: string
  - name: SettingDirectTransferFee
    type: string
  - name: SettingDirectTransferVAT
    type: string
  - name: SettingClaimPendingDirectTransfersQueueConnectionString
    type: string
  - name: SettingClaimPendingDirectTransfersQueueName
    type: string 
  - name: SettingReversePendingDirectMoneyTransfersSchedule
    type: string
  - name: SettingReversePendingDirectMoneyTransfersDurationInMin
    type: string
  - name: SettingReverseFailedDirectMoneyTransfersSchedule
    type: string
  - name: SettingPaykiiServiceBaseAddress
    type: string 
  - name: SettingPaykiiServiceAPIKey
    type: string 
  - name: SettingPaykiiServiceToken
    type: string 
  - name: SettingPaykiiServiceCashierId
    type: string
  - name: SettingPaykiiServiceCustomerId
    type: string
  - name: SettingPaykiiServiceBillerCatalogUrl
    type: string
  - name: SettingPaykiiServiceSKUCatalogUrl
    type: string
  - name: SettingPaykiiServiceIOCatalogUrl
    type: string
  - name: SettingPaykiiServiceAmountDueUrl
    type: string
  - name: SettingPaykiiServiceProcessPaymentUrl
    type: string
  - name: SettingPaykiiServiceVerifyPaymentStatusUrl
    type: string
  - name: SettingPaykiiServiceBalanceUrl
    type: string
  - name: SettingPaykiiServiceBillNotificationUrl
    type: string
  - name: SettingPaykiiServiceMobileCarrierLookupUrl
    type: string
  - name: SettingPaykiiServiceDailyFXRatePerBillerTypeUrl
    type: string
  - name: SettingPaykiiServiceBillerFeesCatalogUrl
    type: string 
  - name: SettingPaykiiServiceRefreshDataIntervalCronExpression
    type: string
  - name: SettingPaykiiServiceLocationId
    type: string
  - name: SettingPaykiiServicePointOfSaleId
    type: string
  - name: SettingBillPaymentPendingBillsRefreshIntervalCronExpression
    type: string
  - name: SettingBillPaymentIconBaseUrl
    type: string
  - name: SettingBillPaymentTransactionEnvironment
    type: string 
  - name: SettingBillPaymentFxRateRefreshIntervalCronExpression
    type: string
  - name: SettingBillPaymentGridViewDisplayProviderIds
    type: string  
  - name: SettingBillPaymentMaximumLocalBillersLimit
    type: string
  - name: SettingBillPaymentMaximumInternationalBillersLimit
    type: string
  - name: SettingBillPaymentMaximumAllowedBillAmountPerTransaction
    type: string
  - name: SettingBillPaymentMaximumAllowedBillAmountPerMonth
    type: string
  - name: SettingBillPaymentNolProviderCode
    type: string
  - name: SettingBillPaymentAllowedNolAmountForNotVerified
    type: string
  - name: SettingBillPaymentNolRechargeMonthlyLimitForVerified
    type: string
  - name: SettingBillPaymentNolRechargeMonthlyLimitForNotVerified
    type: string
  - name: SettingBillPaymentIconContainerName
    type: string 
  - name: SettingBillPaymentSalikProviderCode
    type: string 
  - name: SettingBillPaymentMockUserId
    type: string 
  - name: SettingBillPaymentAddBillerQueueName
    type: string
  - name: SettingBillPaymentProcessPaymentQueueName
    type: string
  - name: SettingBillPaymentMockAddBillerQueueName
    type: string
  - name: SettingBillPaymentMockProcessPaymentQueueName
    type: string
  - name: SettingBillPaymentAmountDueExpireIntervalCronExpression
    type: string
  - name: SettingBillPaymentAmountDueExpiryHours
    type: string 
  - name: SettingGeneralEmiratesIdStorageURL
    type: string  
  - name: SettingGeneralQAUserPhoneNumbers
    type: string  
  - name: SettingGeneralIsMiddleNavExperimentActive
    type: string  
  - name: SettingGeneralIsSecuritySMSAwarenessActive
    type: string  
  - name: SettingGeneralAuthenticationTokenSecretKey
    type: string  
  - name: SettingGeneralUATPentestPhoneNumbers
    type: string  
  - name: SettingGeneralIsDbSaveRetryEnabled
    type: string
  - name: SettingGeneralIsSecuritySMSMigrationActive
    type: string
  - name: SettingGeneralMaxDevicesAllowedForBinding
    type: string
  - name: SettingGeneralDeviceIdBindingPrefix
    type: string
  - name: SettingGeneralQAAutomationPhoneNumbers
    type: string 
  - name: SettingGeneralPrimarySmsProvider
    type: string
  - name: SettingGeneralTestKey
    type: string
  - name: SettingGeneralEnableRedis
    type: string  
  - name: SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled
    type: string
  - name: SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays
    type: string
  - name: SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays
    type: string
  - name: SettingMoneyTransferServiceNonWUCorridors
    type: string
  - name: SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled
    type: string
  - name: SettingMoneyTransferServiceLastRaffleWinnerName
    type: string
  - name: SettingMoneyTransferServiceLastRaffleWinnerTicketNumber
    type: string
  - name: SettingMoneyTransferServiceRaffleDateString
    type: string
  - name: SettingSwaggerUsername
    type: string
  - name: SettingSwaggerPassword
    type: string
  - name: SettingEnableSwagger
    type: string
  - name: SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds
    type: string
  - name: SettingApiManagementName
    type: string 
  - name: DevEmails
    type: string
  - name: SettingRatingMinimumDaysToShowInApp
    type: string
  - name: SettingSparkVMIP
    type: string
  - name: SettingRatingMinimumDaysToShowStore
    type: string
  - name: SettingMoneyTransferMonthlyAmountLimit
    type: string
  - name: SettingMoneyTransferMonthlyCountLimit
    type: string
  - name: SettingStoreEmailRecepients
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusTopicName
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusUserTopicName
    type: string
  - name: SettingUnEmploymentInsuranceServiceBusSubscriptionName
    type: string
  - name: SettingBalanceEnquirySubscriptionServiceBusTopicName
    type: string
  - name: SettingBalanceEnquirySubscriptionServiceBusSubscriptionName
    type: string
  - name: SettingAuditTrailServiceBusQueueName
    type: string
  - name: SettingMoneyTransferFreeTransferExpiryScheduler
    type: string
  - name: SettingMoneyTransferRetryBeneficiarySchedule
    type: string
  - name: SettingMoneyTransferMaxBeneficiaryRetryLimit
    type: string
  - name: SettingMoneyTransferRetryBeneficiaryDurationInMin
    type: string
  - name: SettingDingServiceRetryCount
    type: string
  - name: SettingDingServiceSleepDuration
    type: string
  - name: SettingDingServiceIsRetryEnabled
    type: string
  - name: SettingTestingMRDynamicPackageTestNepalNumbers
    type: string
  - name: SettingTestingMRInlineFeeCalculationTestNumbers
    type: string

  - name: SettingRakBankMoneyTransferBaseUrl
    type: string
  - name: SettingRakBankMoneyTransferUrlPath
    type: string
  - name: SettingRakBankMoneyTransferClientId
    type: string
  - name: SettingRakBankMoneyTransferClientSecretkey
    type: string
  - name: SettingRakBankMoneyTransferSslCertificateName
    type: string
  - name: SettingRakBankMoneyTransferSslCertificatePassword
    type: string
  - name: SettingRakBankMoneyTransferPayloadPrivateKey
    type: string
  - name: SettingRakBankMoneyTransferPayloadPublicKey
    type: string
  - name: SettingRakBankMoneyTransferTokenGrantType
    type: string
  - name: SettingRakBankMoneyTransferTokenScope
    type: string
  - name: SettingRakBankMoneyTransferContentType
    type: string
  - name: SettingRakBankMoneyTransferX509Certificate2Bytes
    type: string

   ###########################################################################################################
    #Mock Keys
  - name: SettingMoneyTransferEnableRakMock
    type: string
  - name: SettingMoneyTransferEnableRakNegativeScenarioMock
    type: string

    ###########################################################################################################

    ###########----Encryption Settings----##########################################################################
  - name: SettingEncryptionSettingsIsActive
    type: string
  - name: SettingEncryptionSettingsPrivateKey
    type: string
  - name: SettingEncryptionSettingsPublicKey
    type: string

  - name: SettingMoneyTransferCorridorsCorporateId
    type: string
  - name: SettingC3PayPlusMembershipLuckyDrawSchedule
    type: string
  - name: SettingC3PayPlusMembershipLuckyDrawWinnersCount
    type: string
  - name: SettingC3PayPlusMembershipCdnUrl
    type: string
  - name: SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule
    type: string
  - name: SettingFirebaseNotificationAuthEndpoint
    type: string
  - name: SettingFirebaseNotificationBaseUrl
    type: string
  - name: SettingFirebaseNotificationSendMethodUrl
    type: string
  - name: SettingGoogleAuthType
    type: string
  - name: SettingGoogleAuthProjectId
    type: string
  - name: SettingGoogleAuthPrivateKeyId
    type: string
  - name: SettingGoogleAuthPrivateKey
    type: string
  - name: SettingGoogleAuthClientEmail
    type: string
  - name: SettingGoogleAuthClientId
    type: string
  - name: SettingGoogleAuthAuthUri
    type: string
  - name: SettingGoogleAuthTokenUri
    type: string
  - name: SettingGoogleAuthAuthProviderX509CertUrl
    type: string
  - name: SettingGoogleAuthClientX509CertUrl
    type: string
  - name: SettingGoogleAuthUniverseDomain
    type: string
  - name: SettingKycBlockExclusionsShouldBeDeletedAfter
    type: string
  - name: SettingKycBlockExclusionsScheduleTime
    type: string
  - name: SettingC3PayPlusMembershipGenerateTicketsMaxCount
    type: string
  - name: SettingC3PayPlusMembershipOverrideLuckyDrawDate
    type: string
  - name: SettingC3PayPlusMembershipOverrideLuckyDrawTime
    type: string
  - name: SettingC3PayPlusMembershipRenewalSchedule
    type: string
  - name: SettingC3PayPlusMembershipConfirmFirstDebitSchedule
    type: string		
  - name: SettingSanctionScreeningApiAddress
    type: string
  - name: SettingRewardServiceBaseAddress
    type: string
  - name: SettingRewardServiceResendScheduleTime
    type: string
  - name: SettingRewardServiceRetryCount
    type: string
  - name: SettingRewardServiceTimeout
    type: string
  - name: SettingRewardServiceTestAccountUsernames
    type: string
  - name: SettingInfobipVoiceCallSettingsBaseUrl
    type: string
  - name: SettingInfobipVoiceCallSettingsAppKey
    type: string
  - name: SettingInfobipVoiceCallSettingsFromNumber
    type: string
  - name: SettingInfobipVoiceCallSettingsCallbackUrl
    type: string
  - name: SettingInfobipVoiceCallSettingsCallbackSecret
    type: string
  - name: isSlotDeploymentEnabled
    type: string
  - name: slotDeploymentInstanceName
    type: string
  - name: SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule
    type: string
  - name: SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName
    type: string
  - name: SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString
    type: string
  - name: SettingAzureAdInstance
    type: string
  - name: SettingAzureAdTenantId
    type: string
  - name: SettingAzureAdClientId
    type: string
  - name: SettingAzureAdClientSecret
    type: string
  - name: SettingAzureAdCallbackPath
    type: string
  - name: SettingAzureAdAudience
    type: string
  - name: SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck
    type: string
  - name: SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays
    type: string
  - name: SettingC3PayPlusMembershipTargetedDiscountCooldownDays
    type: string
  - name: SettingC3PayPlusMembershipAllowedPhoneNumbers
    type: string
  - name: SettingLoginVideoSlotInterval
    type: string
  - name: SettingSalaryPaidEventTopicName
    type: string
  - name: SettingSalaryPaidEventSubscriptionName
    type: string
  - name: SettingSalaryPaidEventConnectionString
    type: string

jobs:
      - job: RegressionTests
        pool:
          vmImage: 'windows-latest'
        condition: and(succeeded(), eq(variables['stage.environment'],'PROD'))
        steps:            
          - checkout: automationRegressionArtifacts
            displayName: 'checkout atuomation regression repository'
            path: "s/regressionArtifacts"              
          - task: JavaToolInstaller@0
            displayName: 'Java Setup'
            inputs:
              versionSpec: 11
              jdkArchitectureOption: x64
              jdkSourceOption: PreInstalled
          - task: NodeTool@0
            displayName: 'Use Node 12.x'
            inputs:
              versionSpec: 12.x
          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact: iSafe_cucumber_Edenred_10102022'
            inputs:
              PathtoPublish: 'isafe_Edenred/iSafe_cucumber_Edenred_10102022.jar'
              ArtifactName: 'iSafe_cucumber_Edenred_10102022'
            condition: succeededOrFailed()
          - task: CopyFiles@2
            displayName: 'Copy Files to: $(build.artifactstagingdirectory)'
            inputs:
              SourceFolder: '$(system.defaultworkingdirectory)'
              Contents: '**/*.jar'
              TargetFolder: '$(build.artifactstagingdirectory)'
            condition: succeededOrFailed()
          - task: Maven@3
            displayName: 'Maven install'
            inputs:
              mavenPomFile: 'isafe_Edenred/pom.xml'
              goals: 'install:install-file -Dfile=iSafe_cucumber_Edenred_10102022.jar -DgroupId=org.iSAFE -DartifactId=Edenred -Dversion=10.10.2022 -Dpackaging=jar'                
              mavenOptions: '-Xmx2048m'                            
          - task: Maven@3
            displayName: 'Maven clean install'
            inputs:
              mavenPomFile: 'isafe_Edenred/pom.xml'
              goals: 'install -PTestsAll'
              options: verify
              testResultsFiles: '**/TEST-*.xml'
              mavenOptions: '-Xmx2048m'
            continueOnError: false
          - task: MaciejMaciejewski.azure-pipelines-cucumber.PublishCucumberReport.PublishCucumberReport@1
            displayName: 'Publish Cucumber Report'
            inputs:
              jsonDir: 'isafe_Edenred/target/jsonReports'
              outputPath: 'isafe_Edenred/TestResults'
              name: 'Automation Test'
              title: API
            condition: succeededOrFailed()
          - task: PublishPipelineArtifact@1
            displayName: 'Publish Pipeline Artifact'
            inputs:
              targetPath: 'isafe_Edenred/TestResults'
              artifact: 'Test Results'
      - deployment: Infra
        dependsOn: RegressionTests
        condition: in(dependencies.RegressionTests.result, 'Succeeded', 'Skipped')         
        environment: ${{ parameters.targetenvironment }}
        workspace:
          clean: all

        strategy:
          runOnce:
            deploy:
              steps:
                # Downlaod Infra artifacts (Defined above in the resources)
                - download: infra

                # Generate SaS token and store it in $(ehqtemplates.sasToken) variable
                - template: pipelines/sastoken-az-tools.yml@devopsTemplates
                  parameters:
                    azConnection: ${{ parameters.azureSubscriptionEQ }}

                # Downlaod Powershell script tools in $(System.DefaultWorkingDirectory)
                - template: pipelines/dl-az-tools.yml@devopsTemplates
                  parameters:
                    azConnection: ${{ parameters.azureSubscriptionEQ }}

                # run infra deployment
                - template: cd-infra-steps.yml
                  parameters:
                    azureSubscription: ${{ parameters.azureSubscription }}
                    templateLocation: ${{ parameters.templateLocation  }}
                    templateSasToken: ?$(ehqtemplates.sasToken)
                    environment: ${{ parameters.environment }}
                    entityCode: ${{ parameters.entityCode  }}
                    appName: ${{ parameters.appName }}
                    existingAppServiceName: ${{ parameters.existingAppServiceName }}
                    existingWebjobServiceName: ${{ parameters.existingWebjobServiceName }}
                    location: ${{ parameters.location }}
                    secondaryLocation: ${{ parameters.secondaryLocation }}
                    rgName: ${{ parameters.rgName }}
                    farmSkuName: ${{ parameters.farmSkuName }}  
                    webJobFarmSkuName: ${{ parameters.webJobFarmSkuName }}  
                    farmSkuCapacity: ${{ parameters.farmSkuCapacity }}
                    farmSkuCapacityMin: ${{ parameters.farmSkuCapacityMin }}
                    farmSkuCapacityMax: ${{ parameters.farmSkuCapacityMax }}
                    dbName: ${{ parameters.dbName }}
                    dbSkuName: ${{ parameters.dbSkuName }}
                    dbWeeklyLtr: ${{ parameters.dbWeeklyLtr }}
                    dbMonthlyLtr: ${{ parameters.dbMonthlyLtr }}
                    identityDbSkuName: ${{ parameters.identityDbSkuName }}
                    serviceBusSkuName: ${{ parameters.serviceBusSkuName }}
                    infraFile: ${{ parameters.infraFile }} 
                    sqlsrvAdministratorLogin:  ${{ parameters.sqlsrvAdministratorLogin }}
                    sqlsrvAdministratorPassword: ${{ parameters.sqlsrvAdministratorPassword }}
                    DevIP: ${{ parameters.DevIP }}
                    DevEmails: ${{ parameters.DevEmails }}
                    SettingApiManagementName: ${{ parameters.SettingApiManagementName }}
                    SparkVMIP: ${{ parameters.SettingSparkVMIP }}
                    isSlotDeploymentEnabled: ${{ parameters.isSlotDeploymentEnabled }}
                    slotDeploymentInstanceName: ${{ parameters.slotDeploymentInstanceName }}

      - deployment: Binaries
        dependsOn: Infra
        condition: in(dependencies.RegressionTests.result, 'Succeeded', 'Skipped')
        environment: ${{ parameters.targetenvironment }}
        variables:
          # Retrieve the output variables of the previous job and bind them to the local variables
          job.webAppName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.webAppName'] ]
          job.portalWebAppName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.portalWebAppName'] ]
          job.webJobName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.webJobName'] ]
          job.sqlName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.sqlName'] ]
          job.identityDbName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.identityDbName'] ]
          job.dbName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.dbName'] ]
          job.aiName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.aiName'] ]
          job.keyVaultName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.keyVaultName'] ]
          job.keyVaultUri: $[ dependencies.Infra.outputs['Infra.setOutputParameters.keyVaultUri'] ]
          job.serviceBusName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.serviceBusName'] ]
          job.storageName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.storageName'] ]
          job.storageWebJobName: $[ dependencies.Infra.outputs['Infra.setOutputParameters.storageWebJobName'] ]

        workspace:
          clean: all
        strategy:
          runOnce:
            deploy:
              steps:
                # Downlaod Binaries artifacts (Defined above in the resources)
                - download: binaries

                # Display the current Job variables
                - script: echo webAppName=$(job.webAppName) portalWebAppName=$(job.portalWebAppName) webJobName=$(job.webJobName) - sqlName=$(job.sqlName) - dbName=$(job.dbName) - identityDbName=$(job.identityDbName) - aiName=$(job.aiName) - keyVaultName=$(job.keyVaultName) - storageName=$(job.storageName) - serviceBusName=$(job.serviceBusName)
                  name: echovar

                  # Downlaod Powershell script tools in $(System.DefaultWorkingDirectory)
                - template: pipelines/dl-az-tools.yml@devopsTemplates
                  parameters:
                    azConnection: ${{ parameters.azureSubscriptionEQ }}

                # run binaries deployment
                # Step 5: Add your keys here like this: "Setting" + <setting name (same as property)>
                - template: cd-binaries-steps.yml
                  parameters:
                    azureSubscription: ${{ parameters.azureSubscription }}
                    rgName: ${{ parameters.rgName }}
                    webAppName: $(job.webAppName)
                    portalWebAppName: $(job.portalWebAppName)
                    webJobName: $(job.webJobName)
                    sqlName: $(job.sqlName)
                    dbName: $(job.dbName)
                    identityDbName: $(job.identityDbName)
                    aiName: $(job.aiName)
                    keyVaultName: $(job.keyVaultName)
                    serviceBusName: $(job.serviceBusName)
                    storageName: $(job.storageName)
                    storageWebJobName: $(job.storageWebJobName)
                    webAppBinariesFile: ${{ parameters.webAppBinariesFile }}
                    portalWebAppBinariesFile: ${{ parameters.portalWebAppBinariesFile }}
                    webJobBinariesFile: ${{ parameters.webJobBinariesFile }}
                    sqlFile: ${{ parameters.sqlFile }}
                    identitySqlFile: ${{ parameters.identitySqlFile }}
                    sqlsrvAdministratorLogin: ${{ parameters.sqlsrvAdministratorLogin }}
                    sqlsrvAdministratorPassword: ${{ parameters.sqlsrvAdministratorPassword }}
                    rakCertificateName: ${{ parameters.rakCertificateName }}
                    rakCertificatePassword: ${{ parameters.rakCertificatePassword }}
                    rakPrivateKeyName: ${{ parameters.rakPrivateKeyName }}
                    rakPublicKeyName: ${{ parameters.rakPublicKeyName }}
                    SettingEDCAuthority: ${{ parameters.SettingEDCAuthority }}
                    SettingEDCApiName: ${{ parameters.SettingEDCApiName }}
                    SettingEDCApiSecret: ${{ parameters.SettingEDCApiSecret }}
                    SettingPortalEDCAuthority: ${{ parameters.SettingPortalEDCAuthority }}
                    SettingPortalEDCApiName: ${{ parameters.SettingPortalEDCApiName }}
                    SettingPortalEDCApiSecret: ${{ parameters.SettingPortalEDCApiSecret }}
                    SettingKeyName: ${{ parameters.SettingKeyName }}
                    SettingAADAuthority: ${{ parameters.SettingAADAuthority }}
                    SettingAADAudience: ${{ parameters.SettingAADAudience }}
                    SettingAADAllowedClientIds: ${{ parameters.SettingAADAllowedClientIds }}
                    SettingSignzyURL: ${{ parameters.SettingSignzyURL }}
                    SettingSignzyFileExchangeAddress: ${{ parameters.SettingSignzyFileExchangeAddress }}
                    SettingSignzyId: ${{ parameters.SettingSignzyId }}
                    SettingSignzyUserId: ${{ parameters.SettingSignzyUserId }}
                    SettingSignzyImageQualityTimeout: ${{ parameters.SettingSignzyImageQualityTimeout }}
                    SettingSignzyFaceMatchTimeout: ${{ parameters.SettingSignzyFaceMatchTimeout }}
                    SettingSignzyReadDocumentTimeout: ${{ parameters.SettingSignzyReadDocumentTimeout }}
                    SettingAzureOCREndpoint: ${{ parameters.SettingAzureOCREndpoint }}
                    SettingAzureOCRKey: ${{ parameters.SettingAzureOCRKey }}
                    SettingAzureOCRWaitTimeInMilliSeconds: ${{ parameters.SettingAzureOCRWaitTimeInMilliSeconds }}
                    SettingAzureFaceEndpoint: ${{ parameters.SettingAzureFaceEndpoint }}
                    SettingAzureFaceKey: ${{ parameters.SettingAzureFaceKey }}
                    SettingAzureIdentificationServiceUseAzureOCR: ${{ parameters.SettingAzureIdentificationServiceUseAzureOCR }}
                    SettingAzureIdentificationServiceUseAzureFace: ${{ parameters.SettingAzureIdentificationServiceUseAzureFace }}
                    SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck: ${{ parameters.SettingAzureIdentificationServiceMaxDifferenceInDigitsForRecheck }}
                    SettingKycExpiryCheckStartDate: ${{ parameters.SettingKycExpiryCheckStartDate }}
                    SettingKycExpiryValidateEmiratesIdExpiryScheduler: ${{ parameters.SettingKycExpiryValidateEmiratesIdExpiryScheduler }}
                    SettingKycExpiryValidateAdditionKYCScheduler: ${{ parameters.SettingKycExpiryValidateAdditionKYCScheduler }}
                    SettingTransactionsB2CServiceAuthority: ${{ parameters.SettingTransactionsB2CServiceAuthority }}    
                    SettingTransactionsB2CServiceClientId: ${{ parameters.SettingTransactionsB2CServiceClientId }}    
                    SettingTransactionsB2CServiceScope: ${{ parameters.SettingTransactionsB2CServiceScope }}    
                    SettingTransactionsB2CServiceClientSecret: ${{ parameters.SettingTransactionsB2CServiceClientSecret }}    
                    SettingTransactionsB2CServiceBaseAddress: ${{ parameters.SettingTransactionsB2CServiceBaseAddress }}    
                    SettingTransactionsB2CServiceGrantType: ${{ parameters.SettingTransactionsB2CServiceGrantType }}    
                    SettingTransactionsB2CServiceAPIVersion: ${{ parameters.SettingTransactionsB2CServiceAPIVersion }}    
                    SettingTransactionsB2CServiceTimeout: ${{ parameters.SettingTransactionsB2CServiceTimeout }}    
                    SettingRakBaseURL: ${{ parameters.SettingRakBaseURL }}
                    SettingRakClientId: ${{ parameters.SettingRakClientId }}
                    SettingRakClientSecret: ${{ parameters.SettingRakClientSecret }}
                    SettingRakProcessBankTransactionsReportsSchedule: ${{ parameters.SettingRakProcessBankTransactionsReportsSchedule }}
                    SettingRakUpdatePendingBankTransactionsSchedule: ${{ parameters.SettingRakUpdatePendingBankTransactionsSchedule }}
                    SettingRakReverseFailedBankTransactionsSchedule: ${{ parameters.SettingRakReverseFailedBankTransactionsSchedule }}
                    SettingRakProcessRakStatementSchedule: ${{ parameters.SettingRakProcessRakStatementSchedule }}
                    SettingRakProcessRmtSubmissionSchedule: ${{ parameters.SettingRakProcessRmtSubmissionSchedule }}
                    SettingRakProcessRmtAcknowledgementSchedule: ${{ parameters.SettingRakProcessRmtAcknowledgementSchedule }}
                    SettingUploadMissingProfilesImagesSchedule: ${{ parameters.SettingUploadMissingProfilesImagesSchedule }}
                    SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule: ${{ parameters.SettingMoneyTransferServiceAutoDeleteProfileAndSendUAfterRSchedule }} 
                    SettingExchangeHouseUpdateStatusSchedule: ${{ parameters.SettingExchangeHouseUpdateStatusSchedule }}
                    SettingRakBanksMaxRecords: ${{ parameters.SettingRakBanksMaxRecords }}
                    SettingRakMaxTransactionTriesCount: ${{ parameters.SettingRakMaxTransactionTriesCount }}
                    SettingRakMessageProcessInDelay: ${{ parameters.SettingRakMessageProcessInDelay }}
                    SettingRakTransactionUpdateDelay: ${{ parameters.SettingRakTransactionUpdateDelay }}
                    SettingRakMoneyTransferBeneficiaryDelayInMins: ${{ parameters.SettingRakMoneyTransferBeneficiaryDelayInMins }}
                    SettingRakRmtProfilePositiveStatus: ${{ parameters.SettingRakRMtProfilePositiveStatus }}
                    SettingRakLoyaltyImplementDate: ${{ parameters.SettingRakLoyaltyImplementDate }}
                    SettingRakLoyaltyLimitCount: ${{ parameters.SettingRakLoyaltyLimitCount }}
                    SettingRakLoyaltyLimitAmount: ${{ parameters.SettingRakLoyaltyLimitAmount }}
                    SettingRakURLPath: ${{ parameters.SettingRakURLPath }}
                    SettingRakMoneyTransferBeneficiaryCount: ${{ parameters.SettingRakMoneyTransferBeneficiaryCount }}
                    SettingRakEnableRakTokenCache: ${{ parameters.SettingRakEnableRakTokenCache }}
                    SettingRakSftpEndPoint: ${{ parameters.SettingRakSftpEndPoint }}
                    SettingRakSftpPort: ${{ parameters.SettingRakSftpPort }}
                    SettingRakSftpUsername: ${{ parameters.SettingRakSftpUsername }}
                    SettingRakSftpPassword: ${{ parameters.SettingRakSftpPassword }}
                    SettingRAKSFTPInputRootDirectory: ${{ parameters.SettingRAKSFTPInputRootDirectory }}
                    SettingRAKSFTPOutputRootDirectory: ${{ parameters.SettingRAKSFTPOutputRootDirectory }}
                    SettingRAKSFTPTransactionStatusDirectory: ${{ parameters.SettingRAKSFTPTransactionStatusDirectory }}
                    SettingRAKSFTPTransactionBlobContainerName: ${{ parameters.SettingRAKSFTPTransactionBlobContainerName }}
                    SettingRAKSFTPProfileStatusDirectory: ${{ parameters.SettingRAKSFTPProfileStatusDirectory }}
                    SettingRAKSFTPBranchesDirectory: ${{ parameters.SettingRAKSFTPBranchesDirectory }}
                    SettingRAKSFTPBranchesArchiveDirectory: ${{ parameters.SettingRAKSFTPBranchesArchiveDirectory }}
                    SettingRakSftpReportDirectory: ${{ parameters.SettingRakSftpReportDirectory }}
                    SettingRakSftpRmtAcknowledgementDirectory: ${{ parameters.SettingRakSftpRmtAcknowledgementDirectory }}
                    SettingRakSftpRmtTempSubmissionDirectory: ${{ parameters.SettingRakSftpRmtTempSubmissionDirectory }}
                    SettingRakRefreshRatesSchedule: ${{ parameters.SettingRakRefreshRatesSchedule }}
                    SettingExchangeHouseRefreshRatesSchedule: ${{ parameters.SettingExchangeHouseRefreshRatesSchedule }}
                    SettingRakRefreshRatesEmiratesId: ${{ parameters.SettingRakRefreshRatesEmiratesId }}
                    SettingPPSWebAuthBaseURL: ${{ parameters.SettingPPSWebAuthBaseURL }}
                    SettingPPSWebAuthClientId: ${{ parameters.SettingPPSWebAuthClientId }}
                    SettingPPSWebAuthClientSecretkey: ${{ parameters.SettingPPSWebAuthClientSecretkey }}
                    SettingPPSEndpointAddress: ${{ parameters.SettingPPSEndpointAddress }}
                    SettingPPSUsername: ${{ parameters.SettingPPSUsername }}
                    SettingPPSPassword: ${{ parameters.SettingPPSPassword }}
                    SettingPPSSponsorCode: ${{ parameters.SettingPPSSponsorCode }}
                    SettingPPSCustomerCode: ${{ parameters.SettingPPSCustomerCode }}
                    SettingPPSSharedSecret: ${{ parameters.SettingPPSSharedSecret }}
                    SettingPPSTimeout: ${{ parameters.SettingPPSTimeout }}
                    SettingEtisalatSMSUsername: ${{ parameters.SettingEtisalatSMSUsername }}
                    SettingEtisalatSMSPassword: ${{ parameters.SettingEtisalatSMSPassword }}
                    SettingEtisalatSMSBaseAddress: ${{ parameters.SettingEtisalatSMSBaseAddress }}
                    SettingEtisalatSMSTimeout: ${{ parameters.SettingEtisalatSMSTimeout }}
                    SettingEtisalatSMSRetryCount: ${{ parameters.SettingEtisalatSMSRetryCount }}
                    SettingInfobipSMSUsername: ${{ parameters.SettingInfobipSMSUsername }}
                    SettingInfobipSMSPassword: ${{ parameters.SettingInfobipSMSPassword }}
                    SettingInfobipSMSBaseAddress: ${{ parameters.SettingInfobipSMSBaseAddress }}
                    SettingInfobipSMSAuthKey: ${{ parameters.SettingInfobipSMSAuthKey }}
                    SettingInfobipSMSAuthKeyBaseUrl: ${{ parameters.SettingInfobipSMSAuthKeyBaseUrl }}
                    SettingInfobipSMSSmsMode: ${{ parameters.SettingInfobipSMSSmsMode }}
                    SettingInfobipSMSTimeout: ${{ parameters.SettingInfobipSMSTimeout }}
                    SettingInfobipSMSRetryCount: ${{ parameters.SettingInfobipSMSRetryCount }}
                    SettingDingBaseURL: ${{ parameters.SettingDingBaseURL }}
                    SettingDingClientApiKey: ${{ parameters.SettingDingClientApiKey }}
                    SettingSecondaryDingClientApiKey: ${{ parameters.SettingSecondaryDingClientApiKey }}
                    SettingMobileRechargeSynchronizeWithDingSchedule: ${{ parameters.SettingMobileRechargeSynchronizeWithDingSchedule }}
                    SettingMobileRechargeUpdateStatusSchedule: ${{ parameters.SettingMobileRechargeUpdateStatusSchedule }}
                    SettingMobileRechargeNickNameLength: ${{ parameters.SettingMobileRechargeNickNameLength }}
                    SettingMobileRechargeCallingCardAccountNumberLive: ${{ parameters.SettingMobileRechargeCallingCardAccountNumberLive }}
                    SettingMobileRechargeTransactionEnvironment: ${{ parameters.SettingMobileRechargeTransactionEnvironment }}
                    SettingMobileRechargeC3FeeMode: ${{ parameters.SettingMobileRechargeC3FeeMode }}
                    SettingMobileRechargeMySalaryFeeMode: ${{ parameters.SettingMobileRechargeMySalaryFeeMode }}
                    SettingMobileRechargeSelectedCorporatesWithFee: ${{ parameters.SettingMobileRechargeSelectedCorporatesWithFee }}
                    SettingMobileRechargeFeeAmount: ${{ parameters.SettingMobileRechargeFeeAmount }}
                    SettingMobileRechargeNonVerifiedLimit: ${{ parameters.SettingMobileRechargeNonVerifiedLimit }}
                    SettingMobileRechargeVerifiedLimit: ${{ parameters.SettingMobileRechargeVerifiedLimit }}
                    SettingMobileRechargeCustomCallingCardName: ${{ parameters.SettingMobileRechargeCustomCallingCardName }}
                    SettingMobileRechargeCustomCallingCardCode: ${{ parameters.SettingMobileRechargeCustomCallingCardCode }}
                    SettingMobileRechargeCustomCallingCardLogoUrl: ${{ parameters.SettingMobileRechargeCustomCallingCardLogoUrl }}
                    SettingMobileRechargeCustomCallingCardValidationRegex: ${{ parameters.SettingMobileRechargeCustomCallingCardValidationRegex }}
                    SettingMobileRechargeDynamicPackageSeperator: $(MobileRechargeDynamicPackageSeperator)
                    SettingMobileRechargeServiceServiceBusTopicName: ${{ parameters.SettingMobileRechargeServiceServiceBusTopicName }}
                    SettingMobileRechargeServiceServiceBusSubscriptionName: ${{ parameters.SettingMobileRechargeServiceServiceBusSubscriptionName }}
                    SettingMobileRechargeServiceRenewalSchedule: ${{ parameters.SettingMobileRechargeServiceRenewalSchedule }}
                    SettingMobileRechargeServiceLowBalanceRenewalSchedule: ${{ parameters.SettingMobileRechargeServiceLowBalanceRenewalSchedule }}
                    SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays: ${{ parameters.SettingMobileRechargeServiceLowBalanceRetryMaxThresholdInDays }}
                    SettingVpnMembershipRenewalSchedule: ${{ parameters.SettingVpnMembershipRenewalSchedule }}
                    SettingRenewalCardUpdateServiceBusTopicName: ${{ parameters.SettingRenewalCardUpdateServiceBusTopicName }}
                    SettingRenewalCardUpdateServiceBusSubscriptionName: ${{ parameters.SettingRenewalCardUpdateServiceBusSubscriptionName }}
                    SettingMoneyTransferMultimediaURL: ${{ parameters.SettingMoneyTransferMultimediaURL }} 
                    SettingMoneyTransferRefreshBanksAndBranchesSchedule: ${{ parameters.SettingMoneyTransferRefreshBanksAndBranchesSchedule }} 
                    SettingMoneyTransferMonthlyAmountLimit: ${{ parameters.SettingMoneyTransferMonthlyAmountLimit }} 
                    SettingMoneyTransferMonthlyCountLimit: ${{ parameters.SettingMoneyTransferMonthlyCountLimit }} 
                    SettingMoneyTransferReversalStartDate: ${{ parameters.SettingMoneyTransferReversalStartDate }} 
                    SettingMoneyTransferReversalMode: ${{ parameters.SettingMoneyTransferReversalMode }}
                    SettingMoneyTransferComparisonReceiveAmount: ${{ parameters.SettingMoneyTransferComparisonReceiveAmount }}
                    SettingMoneyTransferComparisonEHTransferFee: ${{ parameters.SettingMoneyTransferComparisonEHTransferFee }}
                    SettingMoneyTransferComparisonEHRateIncrement: ${{ parameters.SettingMoneyTransferComparisonEHRateIncrement }}
                    SettingMoneyTransferGWNationalities: ${{ parameters.SettingMoneyTransferGWNationalities }}
                    SettingMoneyTransferGWLanguages: ${{ parameters.SettingMoneyTransferGWLanguages }}
                    SettingMoneyTransferGWStartDate: ${{ parameters.SettingMoneyTransferGWStartDate }}
                    SettingMoneyTransferGeneralDefaultAmount: ${{ parameters.SettingMoneyTransferGeneralDefaultAmount }}
                    SettingMoneyTransferGeneralDefaultCurrency: ${{ parameters.SettingMoneyTransferGeneralDefaultCurrency }}
                    SettingMoneyTransferGeneralDefaultType: ${{ parameters.SettingMoneyTransferGeneralDefaultType }}
                    SettingMoneyTransferMaxRepeatTransferCount: ${{ parameters.SettingMoneyTransferMaxRepeatTransferCount }}
                    SettingMoneyTransferMinUserBalanceForRepeatTransfer: ${{ parameters.SettingMoneyTransferMinUserBalanceForRepeatTransfer }}
                    SettingMoneyTransferReverseOnHoldSchedule: ${{ parameters.SettingMoneyTransferReverseOnHoldSchedule }}
                    SettingMoneyTransferReverseOnHoldMinNoOfDays: ${{ parameters.SettingMoneyTransferReverseOnHoldMinNoOfDays }}
                    SettingMoneyTransferCorridorsCorporateId: ${{ parameters.SettingMoneyTransferCorridorsCorporateId }}
                    SettingMoneyTransferRateExpiryInMinutes: ${{ parameters.SettingMoneyTransferRateExpiryInMinutes }}
                    SettingMoneyTransferRmtCreationSchedule: ${{ parameters.SettingMoneyTransferRmtCreationSchedule }}
                    SettingMoneyTransferPendingSchedulerMinNoOfDays: ${{ parameters.SettingMoneyTransferPendingSchedulerMinNoOfDays }}
                    SettingMoneyTransferCheckMinSuspiciousDate: ${{ parameters.SettingMoneyTransferCheckMinSuspiciousDate }}
                    SettingExperimentNoLoyaltyMaxCountIND: ${{ parameters.SettingExperimentNoLoyaltyMaxCountIND }}
                    SettingExperimentNoLoyaltyMaxCountPHL: ${{ parameters.SettingExperimentNoLoyaltyMaxCountPHL }}
                    SettingExperimentNoLoyaltyMaxCountNPL: ${{ parameters.SettingExperimentNoLoyaltyMaxCountNPL }}
                    SettingMoneyTransferFreeTransferExpiryScheduler: ${{ parameters.SettingMoneyTransferFreeTransferExpiryScheduler }}
                    SettingMoneyTransferRetryBeneficiarySchedule: ${{ parameters.SettingMoneyTransferRetryBeneficiarySchedule }}
                    SettingMoneyTransferMaxBeneficiaryRetryLimit: ${{ parameters.SettingMoneyTransferMaxBeneficiaryRetryLimit }}
                    SettingMoneyTransferRetryBeneficiaryDurationInMin: ${{ parameters.SettingMoneyTransferRetryBeneficiaryDurationInMin }}
                    SettingMoneyTransferEnableRakMock: ${{ parameters.SettingMoneyTransferEnableRakMock }}
                    SettingMoneyTransferEnableRakNegativeScenarioMock: ${{ parameters.SettingMoneyTransferEnableRakNegativeScenarioMock }}
                    SettingEdenredIdentityManagerBaseAddress: ${{ parameters.SettingEdenredIdentityManagerBaseAddress }}
                    SettingEdenredIdentityManagerAuthority: ${{ parameters.SettingEdenredIdentityManagerAuthority }}
                    SettingEdenredIdentityManagerResourceId: ${{ parameters.SettingEdenredIdentityManagerResourceId }}
                    SettingEdenredIdentityManagerClientId: ${{ parameters.SettingEdenredIdentityManagerClientId }}
                    SettingEdenredIdentityManagerClientSecret: ${{ parameters.SettingEdenredIdentityManagerClientSecret }}
                    SettingFirebaseCloudMessagingKey: ${{ parameters.SettingFirebaseCloudMessagingKey }}
                    SettingFirebaseCloudMessagingSenderId: ${{ parameters.SettingFirebaseCloudMessagingSenderId }}
                    SettingFirebaseCloudMessagingBaseAddress: ${{ parameters.SettingFirebaseCloudMessagingBaseAddress }}
                    SettingFirebaseCloudMessagingRetryCount: ${{ parameters.SettingFirebaseCloudMessagingRetryCount }}
                    SettingFirebaseCloudMessagingTimeout: ${{ parameters.SettingFirebaseCloudMessagingTimeout }}
                    SettingESMOServiceBaseAddress: ${{ parameters.SettingESMOServiceBaseAddress }}
                    SettingESMOServiceClientId: ${{ parameters.SettingESMOServiceClientId }}
                    SettingESMOServiceClientSecret: ${{ parameters.SettingESMOServiceClientSecret }}
                    SettingESMOServiceAuthority: ${{ parameters.SettingESMOServiceAuthority }}
                    SettingESMOServiceScope: ${{ parameters.SettingESMOServiceScope }}
                    SettingESMOServiceTimeout: ${{ parameters.SettingESMOServiceTimeout }}
                    SettingKYCBaseAddress: ${{ parameters.SettingKYCBaseAddress }}
                    SettingKYCUsername: ${{ parameters.SettingKYCUsername }}
                    SettingKYCPassword: ${{ parameters.SettingKYCPassword }}
                    SettingKYCUniqueRef: ${{ parameters.SettingKYCUniqueRef }}
                    SettingKYCSponsorCode: ${{ parameters.SettingKYCSponsorCode }}
                    SettingKYCSharedSecret: ${{ parameters.SettingKYCSharedSecret }}
                    SettingMobileAppHashKey: ${{ parameters.SettingMobileAppHashKey }}
                    SettingSendGridSenderEmail: ${{ parameters.SettingSendGridSenderEmail }}
                    SettingSendGridAPIKey: ${{ parameters.SettingSendGridAPIKey }}
                    SettingSendGridCardHolderRegistrationRejectedEmailTemplateId: ${{ parameters.SettingSendGridCardHolderRegistrationRejectedEmailTemplateId }}
                    SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId: ${{ parameters.SettingSendGridCardHolderRMTProfileCreatedEmailTemplateId }}
                    SettingSendGridBankStatementEmailTemplateId: ${{ parameters.SettingSendGridBankStatementEmailTemplateId }}
                    SettingSendGridStoreOrderPlacedEmailTemplateId: ${{ parameters.SettingSendGridStoreOrderPlacedEmailTemplateId }}
                    SettingSendGridPortalUserCreatedEmailTemplateId: ${{ parameters.SettingSendGridPortalUserCreatedEmailTemplateId }}
                    SettingPortalAdminPasswordResetEmailTemplateId: $(PortalAdminPasswordResetEmailTemplateId)
                    SettingRedisConnection: ${{ parameters.SettingRedisConnection }}
                    SettingServiceBusConnection: ${{ parameters.SettingServiceBusConnection }}
                    SettingRakReadRMTProfileResponsesSchedule: ${{ parameters.SettingRakReadRMTProfileResponsesSchedule }}
                    SettingRakSftpRMTProfileResponsesDirectory: ${{ parameters.SettingRakSftpRMTProfileResponsesDirectory }}
                    SettingRakSftpMissingRakFileAlertPhoneNumbers: ${{ parameters.SettingRakSftpMissingRakFileAlertPhoneNumbers }}
                    SettingFirstBlackV1PlasticCardId: ${{ parameters.SettingFirstBlackV1PlasticCardId }}
                    SettingFirstBlackV2PlasticCardId: ${{ parameters.SettingFirstBlackV2PlasticCardId }}
                    SettingCleverTapBaseAddress: ${{ parameters.SettingCleverTapBaseAddress }}
                    SettingCleverTapProjectId: ${{ parameters.SettingCleverTapProjectId }}
                    SettingCleverTapPassCode: ${{ parameters.SettingCleverTapPassCode }}
                    SettingExchangeHouseBaseAddress: ${{ parameters.SettingExchangeHouseBaseAddress }}
                    SettingExchangeHouseUsername: ${{ parameters.SettingExchangeHouseUsername }}
                    SettingExchangeHousePassword: ${{ parameters.SettingExchangeHousePassword }}
                    SettingExchangeHouseMaxAllowedBeneficiaryCount: ${{ parameters.SettingExchangeHouseMaxAllowedBeneficiaryCount }}
                    SettingExchangeHouseMoneyTransferQueueConnectionString: ${{ parameters.SettingExchangeHouseMoneyTransferQueueConnectionString }}
                    SettingExchangeHouseMoneyTransferQueueName: ${{ parameters.SettingExchangeHouseMoneyTransferQueueName }}
                    SettingReferralProgramMoneyTransferCountThreshold: ${{ parameters.SettingReferralProgramMoneyTransferCountThreshold }}
                    SettingReferralProgramMoneyTransferAmountThreshold: ${{ parameters.SettingReferralProgramMoneyTransferAmountThreshold }}
                    SettingReferralProgramMoneyTransferRewardAmount: ${{ parameters.SettingReferralProgramMoneyTransferRewardAmount }}
                    SettingReferralProgramMoneyTransferReferralProgramStartDate: ${{ parameters.SettingReferralProgramMoneyTransferReferralProgramStartDate }}                
                    SettingPayrollServiceBaseAddress: ${{ parameters.SettingPayrollServiceBaseAddress }}
                    SettingPayrollServiceAuthority: ${{ parameters.SettingPayrollServiceAuthority }}
                    SettingPayrollServiceClientId: ${{ parameters.SettingPayrollServiceClientId }}
                    SettingPayrollServiceClientSecret: ${{ parameters.SettingPayrollServiceClientSecret }}
                    SettingPayrollServiceScope: ${{ parameters.SettingPayrollServiceScope }}
                    SettingHRServiceBaseAddress: ${{ parameters.SettingHRServiceBaseAddress }}
                    SettingHRServiceAuthority: ${{ parameters.SettingHRServiceAuthority }}
                    SettingHRServiceClientId: ${{ parameters.SettingHRServiceClientId }}
                    SettingHRServiceClientSecret: ${{ parameters.SettingHRServiceClientSecret }}
                    SettingHRServiceScope: ${{ parameters.SettingHRServiceScope }}
                    SettingHRServiceCacheInMinutes: ${{ parameters.SettingHRServiceCacheInMinutes }}
                    SettingDirectTransferMaxBeneficiariesCount: ${{ parameters.SettingDirectTransferMaxBeneficiariesCount }}
                    SettingDirectTransferMinAmountToSend: ${{ parameters.SettingDirectTransferMinAmountToSend }}
                    SettingDirectTransferMaxAmountToSend: ${{ parameters.SettingDirectTransferMaxAmountToSend }}
                    SettingDirectTransferMaxAmountToSendPerMonth: ${{ parameters.SettingDirectTransferMaxAmountToSendPerMonth }}
                    SettingDirectTransferFee: ${{ parameters.SettingDirectTransferFee }}
                    SettingDirectTransferVAT: ${{ parameters.SettingDirectTransferVAT }}
                    SettingClaimPendingDirectTransfersQueueConnectionString: ${{ parameters.SettingClaimPendingDirectTransfersQueueConnectionString }}
                    SettingClaimPendingDirectTransfersQueueName: ${{ parameters.SettingClaimPendingDirectTransfersQueueName }}    
                    SettingReversePendingDirectMoneyTransfersSchedule: ${{ parameters.SettingReversePendingDirectMoneyTransfersSchedule }}
                    SettingReversePendingDirectMoneyTransfersDurationInMin: ${{ parameters.SettingReversePendingDirectMoneyTransfersDurationInMin }}
                    SettingReverseFailedDirectMoneyTransfersSchedule: ${{ parameters.SettingReverseFailedDirectMoneyTransfersSchedule }}
                    SettingPaykiiServiceBaseAddress: $(PaykiiServiceBaseAddress)
                    SettingPaykiiServiceAPIKey: $(PaykiiServiceAPIKey)
                    SettingPaykiiServiceToken: $(PaykiiServiceToken)
                    SettingPaykiiServiceCashierId: $(PaykiiServiceCashierId)
                    SettingPaykiiServiceCustomerId: $(PaykiiServiceCustomerId)
                    SettingPaykiiServiceBillerCatalogUrl: $(PaykiiServiceBillerCatalogUrl)
                    SettingPaykiiServiceSKUCatalogUrl: $(PaykiiServiceSKUCatalogUrl)
                    SettingPaykiiServiceIOCatalogUrl: $(PaykiiServiceIOCatalogUrl)
                    SettingPaykiiServiceAmountDueUrl: $(PaykiiServiceAmountDueUrl)
                    SettingPaykiiServiceProcessPaymentUrl: $(PaykiiServiceProcessPaymentUrl)
                    SettingPaykiiServiceVerifyPaymentStatusUrl: $(PaykiiServiceVerifyPaymentStatusUrl)
                    SettingPaykiiServiceBalanceUrl: $(PaykiiServiceBalanceUrl)
                    SettingPaykiiServiceBillNotificationUrl: $(PaykiiServiceBillNotificationUrl)
                    SettingPaykiiServiceMobileCarrierLookupUrl: $(PaykiiServiceMobileCarrierLookupUrl)
                    SettingPaykiiServiceDailyFXRatePerBillerTypeUrl: $(PaykiiServiceDailyFXRatePerBillerTypeUrl)
                    SettingPaykiiServiceBillerFeesCatalogUrl: $(PaykiiServiceBillerFeesCatalogUrl)
                    SettingPaykiiServiceRefreshDataIntervalCronExpression: $(PaykiiServiceRefreshDataIntervalCronExpression)
                    SettingPaykiiServiceLocationId: $(PaykiiServiceLocationId)
                    SettingPaykiiServicePointOfSaleId: $(PaykiiServicePointOfSaleId)
                    SettingBillPaymentIconBaseUrl: $(BillPaymentIconBaseUrl)
                    SettingBillPaymentPendingBillsRefreshIntervalCronExpression: $(BillPaymentPendingBillsRefreshIntervalCronExpression)
                    SettingBillPaymentTransactionEnvironment: $(BillPaymentTransactionEnvironment)
                    SettingBillPaymentFxRateRefreshIntervalCronExpression: $(BillPaymentFxRateRefreshIntervalCronExpression)
                    SettingBillPaymentGridViewDisplayProviderIds: $(BillPaymentGridViewDisplayProviderIds) 
                    SettingBillPaymentMaximumLocalBillersLimit: $(BillPaymentMaximumLocalBillersLimit) 
                    SettingBillPaymentMaximumInternationalBillersLimit: $(BillPaymentMaximumInternationalBillersLimit) 
                    SettingBillPaymentMaximumAllowedBillAmountPerTransaction: $(BillPaymentMaximumAllowedBillAmountPerTransaction) 
                    SettingBillPaymentMaximumAllowedBillAmountPerMonth: $(BillPaymentMaximumAllowedBillAmountPerMonth) 
                    SettingBillPaymentNolProviderCode: $(BillPaymentNolProviderCode) 
                    SettingBillPaymentAllowedNolAmountForNotVerified: $(BillPaymentAllowedNolAmountForNotVerified) 
                    SettingBillPaymentNolRechargeMonthlyLimitForVerified: $(BillPaymentNolRechargeMonthlyLimitForVerified) 
                    SettingBillPaymentNolRechargeMonthlyLimitForNotVerified: $(BillPaymentNolRechargeMonthlyLimitForNotVerified)
                    SettingBillPaymentIconContainerName: $(BillPaymentBillPaymentIconContainerName)
                    SettingBillPaymentSalikProviderCode: $(BillPaymentSalikProviderCode) 
                    SettingBillPaymentMockUserId: $(BillPaymentMockUserId)  
                    SettingBillPaymentAddBillerQueueName: $(BillPaymentAddBillerQueueName)
                    SettingBillPaymentProcessPaymentQueueName: $(BillPaymentProcessPaymentQueueName)
                    SettingBillPaymentMockAddBillerQueueName: $(BillPaymentMockAddBillerQueueName)
                    SettingBillPaymentMockProcessPaymentQueueName: $(BillPaymentMockProcessPaymentQueueName)
                    SettingBillPaymentAmountDueExpireIntervalCronExpression: $(BillPaymentAmountDueExpireIntervalCronExpression)
                    SettingBillPaymentAmountDueExpiryHours: $(BillPaymentAmountDueExpiryHours)
                    SettingGeneralEmiratesIdStorageURL: $(GeneralEmiratesIdStorageURL)
                    SettingGeneralQAUserPhoneNumbers: $(GeneralQAUserPhoneNumbers)
                    SettingGeneralIsMiddleNavExperimentActive: $(GeneralIsMiddleNavExperimentActive)
                    SettingGeneralIsSecuritySMSAwarenessActive: $(GeneralIsSecuritySMSAwarenessActive)
                    SettingGeneralUATPentestPhoneNumbers: $(GeneralUATPentestPhoneNumbers)
                    SettingGeneralAuthenticationTokenSecretKey: $(GeneralAuthenticationTokenSecretKey)
                    SettingGeneralIsDbSaveRetryEnabled: $(GeneralIsDbSaveRetryEnabled)
                    SettingMoneyTransferServiceWUTransactionMinLimitValidationEnabled: $(MoneyTransferServiceWUTransactionMinLimitValidationEnabled)
                    SettingMoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceNonWUEmiratesIdExpiryGracePeriodDays)
                    SettingMoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays: $(MoneyTransferServiceWUEmiratesIdExpiryGracePeriodDays)
                    SettingMoneyTransferServiceNonWUCorridors: $(MoneyTransferServiceNonWUCorridors)
                    SettingMoneyTransferServiceRMTStatusFromCreatedToPendingEnabled: $(MoneyTransferServiceRMTStatusFromCreatedToPendingEnabled)
                    SettingMoneyTransferServiceLastRaffleWinnerName: $(MoneyTransferServiceLastRaffleWinnerName)
                    SettingMoneyTransferServiceLastRaffleWinnerTicketNumber: $(MoneyTransferServiceLastRaffleWinnerTicketNumber)
                    SettingMoneyTransferServiceRaffleDateString: $(MoneyTransferServiceRaffleDateString)
                    SettingGeneralIsSecuritySMSMigrationActive: $(GeneralIsSecuritySMSMigrationActive)
                    SettingGeneralMaxDevicesAllowedForBinding: $(GeneralMaxDevicesAllowedForBinding)
                    SettingGeneralDeviceIdBindingPrefix: $(GeneralDeviceIdBindingPrefix)
                    SettingGeneralQAAutomationPhoneNumbers: $(GeneralQAAutomationPhoneNumbers)
                    SettingGeneralPrimarySmsProvider: $(GeneralPrimarySmsProvider)
                    SettingGeneralTestKey: $(GeneralTestKey)
                    SettingGeneralEnableRedis: $(GeneralEnableRedis)
                    SettingMultimediaAutoPlayMoneyTransferVideoCorporateIds: $(MultimediaAutoPlayMoneyTransferVideoCorporateIds)
                    SettingSwaggerUsername: ${{ parameters.SettingSwaggerUsername }}
                    SettingSwaggerPassword: ${{ parameters.SettingSwaggerPassword }}
                    SettingEnableSwagger: ${{ parameters.SettingEnableSwagger }}
                    SettingRatingMinimumDaysToShowInApp: ${{ parameters.SettingRatingMinimumDaysToShowInApp }}
                    SettingRatingMinimumDaysToShowStore: ${{ parameters.SettingRatingMinimumDaysToShowStore }}
                    SettingStoreEmailRecepients: ${{ parameters.SettingStoreEmailRecepients }}
                    SettingUnEmploymentInsuranceServiceBusTopicName: ${{ parameters.SettingUnEmploymentInsuranceServiceBusTopicName }}
                    SettingUnEmploymentInsuranceServiceBusUserTopicName: ${{ parameters.SettingUnEmploymentInsuranceServiceBusUserTopicName }}
                    SettingUnEmploymentInsuranceServiceBusSubscriptionName: ${{ parameters.SettingUnEmploymentInsuranceServiceBusSubscriptionName }}
                    SettingBalanceEnquirySubscriptionServiceBusTopicName: ${{ parameters.SettingBalanceEnquirySubscriptionServiceBusTopicName }}
                    SettingBalanceEnquirySubscriptionServiceBusSubscriptionName: ${{ parameters.SettingBalanceEnquirySubscriptionServiceBusSubscriptionName }}
                    SettingAuditTrailServiceBusQueueName: ${{ parameters.SettingAuditTrailServiceBusQueueName }}
                    SettingDingServiceRetryCount: ${{ parameters.SettingDingServiceRetryCount }}
                    SettingDingServiceSleepDuration: ${{ parameters.SettingDingServiceSleepDuration }}
                    SettingDingServiceIsRetryEnabled: ${{ parameters.SettingDingServiceIsRetryEnabled }}
                    SettingTestingMRDynamicPackageTestNepalNumbers: ${{ parameters.SettingTestingMRDynamicPackageTestNepalNumbers }}
                    SettingTestingMRInlineFeeCalculationTestNumbers: ${{ parameters.SettingTestingMRInlineFeeCalculationTestNumbers }}
                    SettingRakBankMoneyTransferBaseUrl: ${{ parameters.SettingRakBankMoneyTransferBaseUrl }}
                    SettingRakBankMoneyTransferUrlPath: ${{ parameters.SettingRakBankMoneyTransferUrlPath }}
                    SettingRakBankMoneyTransferClientId: ${{ parameters.SettingRakBankMoneyTransferClientId }}
                    SettingRakBankMoneyTransferClientSecretkey: ${{ parameters.SettingRakBankMoneyTransferClientSecretkey }}
                    SettingRakBankMoneyTransferSslCertificateName: ${{ parameters.SettingRakBankMoneyTransferSslCertificateName }}
                    SettingRakBankMoneyTransferSslCertificatePassword: ${{ parameters.SettingRakBankMoneyTransferSslCertificatePassword }}
                    SettingRakBankMoneyTransferPayloadPrivateKey: ${{ parameters.SettingRakBankMoneyTransferPayloadPrivateKey }}
                    SettingRakBankMoneyTransferPayloadPublicKey: ${{ parameters.SettingRakBankMoneyTransferPayloadPublicKey }}
                    SettingRakBankMoneyTransferTokenGrantType: ${{ parameters.SettingRakBankMoneyTransferTokenGrantType }}
                    SettingRakBankMoneyTransferTokenScope: ${{ parameters.SettingRakBankMoneyTransferTokenScope }}
                    SettingRakBankMoneyTransferContentType: ${{ parameters.SettingRakBankMoneyTransferContentType }}
                    SettingRakBankMoneyTransferX509Certificate2Bytes: ${{ parameters.SettingRakBankMoneyTransferX509Certificate2Bytes }}
                    SettingEncryptionSettingsIsActive: ${{ parameters.SettingEncryptionSettingsIsActive }}
                    SettingEncryptionSettingsPrivateKey: ${{ parameters.SettingEncryptionSettingsPrivateKey }}
                    SettingEncryptionSettingsPublicKey: ${{ parameters.SettingEncryptionSettingsPublicKey }}
                    SettingC3PayPlusMembershipLuckyDrawSchedule: ${{ parameters.SettingC3PayPlusMembershipLuckyDrawSchedule }}
                    SettingC3PayPlusMembershipLuckyDrawWinnersCount: ${{ parameters.SettingC3PayPlusMembershipLuckyDrawWinnersCount }}
                    SettingC3PayPlusMembershipCdnUrl: ${{ parameters.SettingC3PayPlusMembershipCdnUrl }}
                    SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule: ${{ parameters.SettingC3PayPlusMembershipAtmWithdrawalFeeReversalSchedule }}
                    SettingFirebaseNotificationAuthEndpoint: ${{ parameters.SettingFirebaseNotificationAuthEndpoint }}
                    SettingFirebaseNotificationBaseUrl: ${{ parameters.SettingFirebaseNotificationBaseUrl }}
                    SettingFirebaseNotificationSendMethodUrl: ${{ parameters.SettingFirebaseNotificationSendMethodUrl }}
                    SettingGoogleAuthType: ${{ parameters.SettingGoogleAuthType }}
                    SettingGoogleAuthProjectId: ${{ parameters.SettingGoogleAuthProjectId }}
                    SettingGoogleAuthPrivateKeyId: ${{ parameters.SettingGoogleAuthPrivateKeyId }}
                    SettingGoogleAuthPrivateKey: ${{ parameters.SettingGoogleAuthPrivateKey }}
                    SettingGoogleAuthClientEmail: ${{ parameters.SettingGoogleAuthClientEmail }}
                    SettingGoogleAuthClientId: ${{ parameters.SettingGoogleAuthClientId }}
                    SettingGoogleAuthAuthUri: ${{ parameters.SettingGoogleAuthAuthUri }}
                    SettingGoogleAuthTokenUri: ${{ parameters.SettingGoogleAuthTokenUri }}
                    SettingGoogleAuthAuthProviderX509CertUrl: ${{ parameters.SettingGoogleAuthAuthProviderX509CertUrl }}
                    SettingGoogleAuthClientX509CertUrl: ${{ parameters.SettingGoogleAuthClientX509CertUrl }}
                    SettingGoogleAuthUniverseDomain: ${{ parameters.SettingGoogleAuthUniverseDomain }}
                    SettingKycBlockExclusionsShouldBeDeletedAfter: ${{ parameters.SettingKycBlockExclusionsShouldBeDeletedAfter }}
                    SettingKycBlockExclusionsScheduleTime: ${{ parameters.SettingKycBlockExclusionsScheduleTime }}
                    SettingC3PayPlusMembershipGenerateTicketsMaxCount: ${{ parameters.SettingC3PayPlusMembershipGenerateTicketsMaxCount }}
                    SettingC3PayPlusMembershipOverrideLuckyDrawDate: ${{ parameters.SettingC3PayPlusMembershipOverrideLuckyDrawDate }}
                    SettingC3PayPlusMembershipOverrideLuckyDrawTime: ${{ parameters.SettingC3PayPlusMembershipOverrideLuckyDrawTime }}
                    SettingC3PayPlusMembershipRenewalSchedule: ${{ parameters.SettingC3PayPlusMembershipRenewalSchedule }}
                    SettingC3PayPlusMembershipConfirmFirstDebitSchedule: ${{ parameters.SettingC3PayPlusMembershipConfirmFirstDebitSchedule }}
                    SettingSanctionScreeningApiAddress: ${{ parameters.SettingSanctionScreeningApiAddress }}
                    SettingRewardServiceBaseAddress: ${{ parameters.SettingRewardServiceBaseAddress }}
                    SettingRewardServiceResendScheduleTime: ${{ parameters.SettingRewardServiceResendScheduleTime }}
                    SettingRewardServiceRetryCount: ${{ parameters.SettingRewardServiceRetryCount }}
                    SettingRewardServiceTimeout: ${{ parameters.SettingRewardServiceTimeout }}
                    SettingRewardServiceTestAccountUsernames: ${{ parameters.SettingRewardServiceTestAccountUsernames }}
                    SettingInfobipVoiceCallSettingsBaseUrl: ${{ parameters.SettingInfobipVoiceCallSettingsBaseUrl }}
                    SettingInfobipVoiceCallSettingsAppKey: ${{ parameters.SettingInfobipVoiceCallSettingsAppKey }}
                    SettingInfobipVoiceCallSettingsFromNumber: ${{ parameters.SettingInfobipVoiceCallSettingsFromNumber }}
                    SettingInfobipVoiceCallSettingsCallbackUrl: ${{ parameters.SettingInfobipVoiceCallSettingsCallbackUrl }}
                    SettingInfobipVoiceCallSettingsCallbackSecret: ${{ parameters.SettingInfobipVoiceCallSettingsCallbackSecret }}
                    isSlotDeploymentEnabled: ${{ parameters.isSlotDeploymentEnabled }}
                    slotDeploymentInstanceName: $ {{parameters.slotDeploymentInstanceName }}
                    SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule: ${{ parameters.SettingC3PayPlusMembershipFreeMoneyTransferRefundsSchedule }}
                    SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString: $ {{ parameters.SettingC3PayPlusMembershipATMWithdrawalRefundsQueueConnectionString }}
                    SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName: $ {{ parameters.SettingC3PayPlusMembershipATMWithdrawalRefundsQueueName }}                    
                    SettingAzureAdInstance: ${{ parameters.SettingAzureAdInstance }}
                    SettingAzureAdTenantId: ${{ parameters.SettingAzureAdTenantId }}
                    SettingAzureAdClientId: ${{ parameters.SettingAzureAdClientId }}
                    SettingAzureAdClientSecret: ${{   parameters.SettingAzureAdClientSecret }}
                    SettingAzureAdCallbackPath: ${{ parameters.SettingAzureAdCallbackPath }}
                    SettingAzureAdAudience: ${{ parameters.SettingAzureAdAudience }}
                    SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck: ${{ parameters.SettingMoneyTransferServiceC3ToC3MinVersionForOtpCheck }}
                    SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays: ${{ parameters.SettingC3PayPlusMembershipLoginVideoLastSeenIntervalInDays }}
                    SettingC3PayPlusMembershipTargetedDiscountCooldownDays: ${{ parameters.SettingC3PayPlusMembershipTargetedDiscountCooldownDays }}
                    SettingC3PayPlusMembershipAllowedPhoneNumbers: ${{ parameters.SettingC3PayPlusMembershipAllowedPhoneNumbers }}
                    SettingLoginVideoSlotInterval: ${{ parameters.SettingLoginVideoSlotInterval }}
                    SettingSalaryPaidEventTopicName: ${{ parameters.SettingSalaryPaidEventTopicName }}
                    SettingSalaryPaidEventSubscriptionName: ${{ parameters.SettingSalaryPaidEventSubscriptionName }}
                    SettingSalaryPaidEventConnectionString: ${{ parameters.SettingSalaryPaidEventConnectionString }}
