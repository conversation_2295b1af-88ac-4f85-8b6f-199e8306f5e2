﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class modifylanguagetables : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Translations",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "DefaultLanguageCode",
                table: "TextContents");

            migrationBuilder.RenameColumn(
                name: "DefaultText",
                table: "TextContents",
                newName: "Text");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "Translations",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "TextContents",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Translations",
                table: "Translations",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations",
                column: "LanguageCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Translations",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "TextContents");

            migrationBuilder.RenameColumn(
                name: "Text",
                table: "TextContents",
                newName: "DefaultText");

            migrationBuilder.AddColumn<string>(
                name: "DefaultLanguageCode",
                table: "TextContents",
                type: "nvarchar(2)",
                maxLength: 2,
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Translations",
                table: "Translations",
                columns: new[] { "LanguageCode", "TextContentCode" });
        }
    }
}
