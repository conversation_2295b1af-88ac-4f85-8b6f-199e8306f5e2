﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Globalization;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace C3Pay.API
{
    /// <summary>
    /// Digital Signature Helper
    /// </summary>
    public static class DigitalSignatureHelper
    {
        /// <summary>
        /// Calculate Digital Signature
        /// </summary>
        /// <param name="data"></param>
        /// <param name="privateKey"></param>
        /// <returns></returns>
        public static string CalculateDigitalSignature(string data, string privateKey)
        {
            var jsonData = data;
            //GenerateKeys();
            if (IsValidJson(data))
            {
                JObject root = JObject.Parse(ConvertToCamelCase(data));
                jsonData = root.ToString(Formatting.None);
            }
      
            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider())
            {
                rsa.FromXmlString(privateKey);
                byte[] dataBytes = Encoding.UTF8.GetBytes(jsonData);
                byte[] signatureBytes = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                string encryptedBase64 = Convert.ToBase64String(signatureBytes);
                return encryptedBase64;
            }
        }

        /// <summary>
        /// Verify Digital Signature
        /// </summary>
        /// <param name="data"></param>
        /// <param name="signature"></param>
        /// <param name="publicKey"></param>
        /// <returns></returns>
        public static bool VerifyDigitalSignature(string data, string signature, string publicKey)
        {
            JObject root = JObject.Parse(ConvertToCamelCase(data));
            var jsonData = root.ToString(Formatting.None);
            using (RSACryptoServiceProvider rsa = new RSACryptoServiceProvider())
            {
                rsa.FromXmlString(publicKey);
                byte[] dataBytes = Encoding.UTF8.GetBytes(jsonData);
                return rsa.VerifyData(dataBytes, Convert.FromBase64String(signature), HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            }
        }

        /// <summary>
        /// Generate Keys Method - Use it when you need
        /// to generate new set of Keys
        /// </summary>
        public static void GenerateKeys()
        {
            // Create a new RSA key pair
            using (var rsa = new RSACryptoServiceProvider(2048))
            {
                try
                {
                    // Export the private key
                    var privateKey = rsa.ToXmlString(true);

                    // Export the public key
                    var publicKey = rsa.ToXmlString(false);
                }
                finally
                {
                    // Clear the RSA key container
                    rsa.PersistKeyInCsp = false;
                }
            }
        }

        #region Camel Case Convertor
        /// <summary>
        /// Private - Convert To CamelCase
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static string ConvertToCamelCase(string json)
        {
            JObject root = JObject.Parse(json);
            ConvertKeysToCamelCase(root);
            return root.ToString(Formatting.None);
        }

        /// <summary>
        ///  Private - Convert Keys To CamelCase
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        private static JToken ConvertKeysToCamelCase(JToken node)
        {
            if (node == null) return null;

            if (node.Type == JTokenType.Object)
            {
                JObject obj = (JObject)node;
                var properties = obj.Properties().ToList();
                foreach (var property in properties)
                {
                    string camelCaseKey = ToCamelCase(property.Name);
                    property.Replace(new JProperty(camelCaseKey, ConvertKeysToCamelCase(property.Value)));
                }
                return obj;
            }
            else if (node.Type == JTokenType.Array)
            {
                JArray array = (JArray)node;
                for (int i = 0; i < array.Count; i++)
                {
                    array[i] = ConvertKeysToCamelCase(array[i]);
                }
                return array;
            }
            else
            {
                return node;
            }
        }

        /// <summary>
        /// Private - To Camel Case
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private static string ToCamelCase(string input)
        {
            if (string.IsNullOrEmpty(input) || !char.IsUpper(input[0]))
            {
                return input;
            }
            return char.ToLower(input[0], CultureInfo.InvariantCulture).ToString() + input.Substring(1);
        }
        public static bool IsValidJson(this string str)
        {
            try
            {
                JsonDocument.Parse(str);
                return true;
            }
            catch (System.Text.Json.JsonException)
            {
                return false;
            }
        }
        #endregion
    }
}
