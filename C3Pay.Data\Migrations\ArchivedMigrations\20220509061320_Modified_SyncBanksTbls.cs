﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modified_SyncBanksTbls : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBranches",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExternalId",
                table: "MoneyTransferBanks",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBanks_MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                column: "MoneyTransferPartnerId");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferBanks_MoneyTransferPartners_MoneyTransferPartnerId",
                table: "MoneyTransferBanks",
                column: "MoneyTransferPartnerId",
                principalTable: "MoneyTransferPartners",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferBanks_MoneyTransferPartners_MoneyTransferPartnerId",
                table: "MoneyTransferBanks");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferBanks_MoneyTransferPartnerId",
                table: "MoneyTransferBanks");

            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBranches");

            migrationBuilder.DropColumn(
                name: "ExternalId",
                table: "MoneyTransferBanks");

            migrationBuilder.DropColumn(
                name: "MoneyTransferPartnerId",
                table: "MoneyTransferBanks");
        }
    }
}
