﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class ChangeLanguageCodeLength : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TextContents_Languages_LanguageCode",
                table: "TextContents");

            migrationBuilder.DropForeignKey(
                name: "FK_Translations_Languages_LanguageCode",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations");

            migrationBuilder.DropIndex(
                name: "IX_TextContents_LanguageCode",
                table: "TextContents");

            migrationBuilder.DeleteData(
                table: "Languages",
                keyColumn: "Code",
                keyValue: "bn");

            migrationBuilder.DeleteData(
                table: "Languages",
                keyColumn: "Code",
                keyValue: "en");

            migrationBuilder.DeleteData(
                table: "Languages",
                keyColumn: "Code",
                keyValue: "hi");

            migrationBuilder.DeleteData(
                table: "Languages",
                keyColumn: "Code",
                keyValue: "ml");

            migrationBuilder.DeleteData(
                table: "Languages",
                keyColumn: "Code",
                keyValue: "ta");

            migrationBuilder.DropColumn(
                name: "LanguageCode",
                table: "Translations");

            migrationBuilder.DropColumn(
                name: "LanguageCode",
                table: "TextContents");

            migrationBuilder.AlterColumn<string>(
                name: "TextContentCode",
                table: "Translations",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Languages",
                type: "nvarchar(5)",
                maxLength: 5,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(2)",
                oldMaxLength: 2);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "TextContentCode",
                table: "Translations",
                type: "nvarchar(25)",
                maxLength: 25,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(25)",
                oldMaxLength: 25,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LanguageCode",
                table: "Translations",
                type: "nvarchar(2)",
                maxLength: 2,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "LanguageCode",
                table: "TextContents",
                type: "nvarchar(2)",
                maxLength: 2,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Code",
                table: "Languages",
                type: "nvarchar(2)",
                maxLength: 2,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(5)",
                oldMaxLength: 5);

            migrationBuilder.InsertData(
                table: "Languages",
                columns: new[] { "Code", "CountryCode", "Description", "Name", "NativeName" },
                values: new object[,]
                {
                    { "bn", "BD", null, "Bengali", "বাংলা" },
                    { "en", "GB", null, "English", "English" },
                    { "hi", "IN", null, "Hindi", "हिन्दी" },
                    { "ml", "IN", null, "Malayalam", "മലയാളം" },
                    { "ta", "IN", null, "Tamil", "தமிழ்" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Translations_LanguageCode",
                table: "Translations",
                column: "LanguageCode");

            migrationBuilder.CreateIndex(
                name: "IX_TextContents_LanguageCode",
                table: "TextContents",
                column: "LanguageCode");

            migrationBuilder.AddForeignKey(
                name: "FK_TextContents_Languages_LanguageCode",
                table: "TextContents",
                column: "LanguageCode",
                principalTable: "Languages",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Translations_Languages_LanguageCode",
                table: "Translations",
                column: "LanguageCode",
                principalTable: "Languages",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
