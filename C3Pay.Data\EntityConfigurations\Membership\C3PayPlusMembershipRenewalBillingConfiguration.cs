﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Billings;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.EntityConfigurations.Membership
{
    public class C3PayPlusMembershipRenewalBillingConfiguration : IEntityTypeConfiguration<C3PayPlusMembershipRenewalBilling>
    {
        public void Configure(EntityTypeBuilder<C3PayPlusMembershipRenewalBilling> builder)
        {
            builder.ToTable("C3PayPlusMembershipRenewalBillings");
            builder.<PERSON><PERSON>ey(e => e.Id);
        }
    }
}
