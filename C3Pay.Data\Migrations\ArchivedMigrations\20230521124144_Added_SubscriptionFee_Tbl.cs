﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_SubscriptionFee_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SubscriptionFees",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    SubscriptionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PartnerId = table.Column<int>(type: "int", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    PaymentFrequency = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionFees", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionFees_Partners_PartnerId",
                        column: x => x.PartnerId,
                        principalTable: "Partners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SubscriptionFees_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "SubscriptionFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "PartnerId", "PaymentFrequency", "SubscriptionId", "UpdatedDate" },
                values: new object[] { new Guid("648ebeef-497e-4ee5-8438-d3fe5b64054e"), new DateTime(2023, 5, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 1m, false, 1, 1, new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"), null });

            migrationBuilder.InsertData(
                table: "SubscriptionFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "PartnerId", "PaymentFrequency", "SubscriptionId", "UpdatedDate" },
                values: new object[] { new Guid("f05df535-6972-4c09-b439-e12547338c67"), new DateTime(2023, 5, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 3m, false, 1, 1, new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"), null });

            migrationBuilder.InsertData(
                table: "SubscriptionFees",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "Fee", "IsDeleted", "PartnerId", "PaymentFrequency", "SubscriptionId", "UpdatedDate" },
                values: new object[] { new Guid("5a41404e-6dec-45e2-94c4-0057a085899c"), new DateTime(2023, 5, 18, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0.5m, false, 1, 2, new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"), null });

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionFees_PartnerId",
                table: "SubscriptionFees",
                column: "PartnerId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionFees_SubscriptionId",
                table: "SubscriptionFees",
                column: "SubscriptionId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SubscriptionFees");
        }
    }
}
