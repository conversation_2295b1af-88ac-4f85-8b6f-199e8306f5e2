﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_MobileRechargeDiscounts : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "DiscountAmount",
                table: "MobileRechargeTransactions",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DiscountCurrency",
                table: "MobileRechargeTransactions",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UserDiscountId",
                table: "MobileRechargeTransactions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MobileRechargeDiscounts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    LogoUrl = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DiscountValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    CapValue = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MaxUsageCount = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeDiscounts", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MobileRechargeUserDiscounts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DiscountId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsPredefined = table.Column<bool>(type: "bit", nullable: false),
                    IsUsed = table.Column<bool>(type: "bit", nullable: false),
                    MobileRechargeTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MobileRechargeUserDiscounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MobileRechargeUserDiscounts_MobileRechargeDiscounts_DiscountId",
                        column: x => x.DiscountId,
                        principalTable: "MobileRechargeDiscounts",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MobileRechargeUserDiscounts_MobileRechargeTransactions_MobileRechargeTransactionId",
                        column: x => x.MobileRechargeTransactionId,
                        principalTable: "MobileRechargeTransactions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MobileRechargeUserDiscounts_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "MobileRechargeDiscounts",
                columns: new[] { "Id", "CapValue", "CreatedDate", "DeletedBy", "DeletedDate", "Description", "DiscountValue", "IsDeleted", "LogoUrl", "MaxUsageCount", "Title", "Type", "UpdatedDate" },
                values: new object[] { new Guid("d142a228-5d6e-4852-b85a-0ad42789aaa1"), 5m, new DateTime(2023, 7, 23, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, "Valid till 30th July", 50m, false, "https://eae-c3pay-cdn-p.azureedge.net/mobile-recharge-icons/promoicon.png", 1, "50% Discount", 2, null });

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeUserDiscounts_DiscountId",
                table: "MobileRechargeUserDiscounts",
                column: "DiscountId");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeUserDiscounts_MobileRechargeTransactionId",
                table: "MobileRechargeUserDiscounts",
                column: "MobileRechargeTransactionId",
                unique: true,
                filter: "[MobileRechargeTransactionId] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeUserDiscounts_UserId",
                table: "MobileRechargeUserDiscounts",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MobileRechargeUserDiscounts");

            migrationBuilder.DropTable(
                name: "MobileRechargeDiscounts");

            migrationBuilder.DropColumn(
                name: "DiscountAmount",
                table: "MobileRechargeTransactions");

            migrationBuilder.DropColumn(
                name: "DiscountCurrency",
                table: "MobileRechargeTransactions");

            migrationBuilder.DropColumn(
                name: "UserDiscountId",
                table: "MobileRechargeTransactions");
        }
    }
}
