﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Orient_Corporates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.InsertData(
                table: "PartnerCorporates",
                columns: new[] { "Id", "CorporateId", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "PartnerId", "UpdatedDate" },
                values: new object[] { 5, "44402", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PartnerCorporates",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.InsertData(
                table: "PartnerCorporates",
                columns: new[] { "Id", "CorporateId", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "PartnerId", "UpdatedDate" },
                values: new object[] { 1, "177", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null });

            migrationBuilder.InsertData(
                table: "PartnerCorporates",
                columns: new[] { "Id", "CorporateId", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "PartnerId", "UpdatedDate" },
                values: new object[] { 2, "296", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null });

            migrationBuilder.InsertData(
                table: "PartnerCorporates",
                columns: new[] { "Id", "CorporateId", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "PartnerId", "UpdatedDate" },
                values: new object[] { 4, "4901", new DateTime(2023, 7, 10, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, false, 5, null });
        }
    }
}
