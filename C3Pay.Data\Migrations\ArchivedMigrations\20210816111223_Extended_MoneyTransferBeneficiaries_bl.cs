﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Extended_MoneyTransferBeneficiaries_bl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "NationalityCode",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StatusDescription",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NationalityCode",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropColumn(
                name: "StatusDescription",
                table: "MoneyTransferBeneficiaries");
        }
    }
}
