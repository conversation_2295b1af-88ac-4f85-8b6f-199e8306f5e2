﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateMissingKyc : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                table: "MissingKycCardholders",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1900, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "DeletedBy",
                table: "MissingKycCardholders",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DeletedDate",
                table: "MissingKycCardholders",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                table: "MissingKycCardholders",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedDate",
                table: "MissingKycCardholders",
                type: "datetime2",
                nullable: true);

            migrationBuilder.DropForeignKey(
                name: "FK_MissingKycCardholders_CardHolders_CitizenId",
                table: "MissingKycCardholders");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "DeletedBy",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "DeletedDate",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "UpdatedDate",
                table: "MissingKycCardholders");

            migrationBuilder.AddForeignKey(
                name: "FK_MissingKycCardholders_CardHolders_CitizenId",
                table: "MissingKycCardholders",
                column: "CitizenId",
                principalTable: "CardHolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
