﻿using System.Collections.Generic;
using System.Threading.Tasks;
using static Edenred.Common.Core.Enums;

namespace Edenred.Common.Core.Services.Integration
{
    public interface IIdentificationMockService
    {
        Task<ServiceResponse<string>> UploadDocument(string content, string documentName, IdentificationDocumentMimeType documentMimeType);
        Task<ServiceResponse<bool>> CheckDocumentQuality(string fileName, string documentUrl, Enums.DocumentType documentType);
        Task<ServiceResponse<object>> ReadDocument(string frontScanUrl, string backScanUrl, Enums.DocumentType documentType, string nameToMatch);
        Task<ServiceResponse<FaceMatchResultDto>> CheckFaceMatch(string selfieFileName, string frontScanFileName);
        Task<ServiceResponse> SaveDocuments(List<IdentificationDocumentDto> documents);
    }
}
