﻿using C3Pay.Core;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using C3Pay.Services.Payments.Queries;
using System.Threading;
using System.Threading.Tasks;
using C3Pay.Core.Models.DTOs.Payments.Responses;
using C3Pay.Services.Payments.Commands;
using C3Pay.Core.Models.DTOs.Payments.Requests;

namespace C3Pay.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [InputValidation]
    [Authorize]
    public class PaymentController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMediator _mediator;

        public PaymentController(IHttpContextAccessor httpContextAccessor, IMediator mediator)
        {
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;
        }

        /// <summary>
        /// Command: <see cref="GetPaymentAuthRequestQuery" />
        /// Handler: <see cref="GetPaymentAuthRequestQueryHandler" />
        /// </summary>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpGet("authorisation-challenges")]
        public async Task<ActionResult<PaymentAuthorisationResponseDto>> GetPaymentAuthorisationRequests(CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var getPaymentAuthRequestResult = await _mediator.Send(new GetPaymentAuthRequestQuery()
            {
                CancellationToken = ct,
                UserPhoneNumber = userPhoneNumber,
                LanguageCode = languageCode
            });


            if (getPaymentAuthRequestResult.IsFailure)
            {
                return BadRequest(getPaymentAuthRequestResult.Error.Code);
            }

            return Ok(getPaymentAuthRequestResult.Value);
        }


        /// <summary>
        /// Command: <see cref="UpdatePaymentAuthorisationDecisionCommand" />
        /// Handler: <see cref="UpdatePaymentAuthorisationDecisionCommandHandler" />
        /// </summary>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        [HttpPost("authorisation-challenges")]
        public async Task<ActionResult> PaymentAuthorisationDecision(PaymentAuthorisationDecisionRequestDto request, CancellationToken ct = default)
        {
            var languageCode = Request.Headers["x-lang-code"];
            var userPhoneNumber = _httpContextAccessor.HttpContext.User.FindFirst(ConstantParam.Username)?.Value;

            var updateAuthRequestDecisionResult = await _mediator.Send(new UpdatePaymentAuthorisationDecisionCommand()
            {
                CancellationToken = ct,
                UserPhoneNumber = userPhoneNumber,
                LanguageCode = languageCode,
                DecisionRequest = request
            });


            if (updateAuthRequestDecisionResult.IsFailure)
            {
                return BadRequest(updateAuthRequestDecisionResult.Error.Code);
            }

            return Ok();
        }
    }
}
