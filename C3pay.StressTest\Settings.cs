﻿using System;
using System.Collections.Generic;
using System.Text;

namespace C3pay.StressTest
{
    public class Settings
    {
        public string apiURL { get; set; }
        public string apiURLPortal { get; set; }      
        public string accessToken { get; set; }
        public string accessTokenPortal { get; set; }

        public int minutesDuration { get; set; }
        public int requestRate { get; set; }

        //User settings
        public string phoneNumber { get; set; }

        public string userId { get; set; }

        public string password { get; set; }

        public string deviceToken { get; set; }
        //Mobile recharge transfer settings
        public string mrTransactionId { get; set; }
        public string mrBenificiaryId { get; set; }

        //Money transfer settings
        public string mtttransactionId { get; set; }

        //eligibility settings
        public string eligibilityPhoneNumber { get; set; }
        public string eligiblityCardNumber { get; set; }

        //Check image settings
        public string fileFront { get; set; }
        public string fileback { get; set; }
        public string fileSelfie { get; set; }

        //MoneyTransfer
        public int? moneyTransferBeneficiaryPageNo { get; set; }
        public int? moneyTransferBeneficiarySize { get; set; }
        public string beneficiaryId { get; set; }


        public int? allUsersPageNo { get; set; }
        public int? allUsersPageSize { get; set; }
        public string name { get; set; }

        public int? registrationPageNo { get; set; }
        public int? registrationPageSize { get; set; }
        public bool? withEID { get; set; }

        public int? updatePageNo { get; set; }
        public int? updatePageSize { get; set; }

        public int? rejectionPageNo { get; set; }
        public int? rejectionPageSize { get; set; }

        public string subscribe { get; set; }
        public string unSubscribe { get; set; }

        //For MobileRechargeBeneficiary Eligability
        public string countryCode { get; set; }
        public string nickName { get; set; }

        //For Mobile Recharge Transaction 
        public DateTime? fromDate { get; set; }
        public DateTime? toDate { get; set; }
        public string mtBneficiaryId { get; set; }
        public double amount { get; set; }
        public string currency { get; set; }
        public double conversionRate { get; set; }
        public double feeAmount { get; set; }
        public string referralCode { get; set; }
        public string beneficiaryName { get; set; }
        public string transferMethod { get; set; }
        public string reasonPassword { get; set; }
        public string reasonPhoneNumber { get; set; }
        public string oTP { get; set; }
        public string passwordText { get; set; }
        public string productCode { get; set; }
        public string operatorName { get; set; }
        public string cardNumberSignup { get; set; }
        public string phoneNumberSignup { get; set; }
        public string reasonSignUp { get; set; }
        public string securityAnswer1 { get; set; }
        public string securityAnswer2 { get; set; }
        public string ifscCode { get; set; }
        public string cardSerialNumber { get; set; }
        public string atmPinCardNumber { get; set; }
        public string cvc2 { get; set; }
        public string atmPinUserId { get; set; }


    }

}
