using Microsoft.AspNetCore.Builder;

namespace Edenred.Common.Services.Azure.Extensions
{
    public static class OpenTelemetryExtensions
    {
        public static IApplicationBuilder UseRequestBodyLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestBodyLoggingMiddleware>();
        }

        public static IApplicationBuilder UseResponseBodyLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<ResponseBodyLoggingMiddleware>();
        }

        public static IApplicationBuilder UseRequestHeadersLogging(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RequestHeaderLoggingMiddleware>();
        }
    }
} 