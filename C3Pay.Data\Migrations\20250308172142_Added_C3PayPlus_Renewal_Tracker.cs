﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class Added_C3PayPlus_Renewal_Tracker : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipRenewalBillings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    C3PayPlusMembershipUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RenewingFor = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ReferenceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BillingDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    BillingAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipRenewalBillings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipRenewalBillings_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                        column: x => x.C3PayPlusMembershipUserId,
                        principalTable: "C3PayPlusMembershipUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Logs_C3PayPlusMembershipRenewals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RenewingFor = table.Column<DateTime>(type: "datetime2", nullable: false),
                    JobInvokedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    JobStartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    JobEndTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    JobDurationInSeconds = table.Column<int>(type: "int", nullable: true),
                    TotalRenewalsExpectedToProcess = table.Column<int>(type: "int", nullable: false),
                    RenewalsCompletedSuccessfully = table.Column<int>(type: "int", nullable: false),
                    SuccessfulUnsubscribes = table.Column<int>(type: "int", nullable: false),
                    TotalSkippedRenewals = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToDeletedUsers = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToUserDecision = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToMissingAgeInformation = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToExceedingAgeLimit = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToDormantCardCheckFailure = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToDormantCard = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToNoSalaryCreditedInLast3Months = table.Column<int>(type: "int", nullable: false),
                    UnsubscribedDueToOtherReasons = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToBalanceEnquirySubscriptionNotFound = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToMissingSecuritySmsSubscriptionCode = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToMissingSecuritySmsSubscriptionFee = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToIssueSubscribingBackToSecuritySms = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToMissingSalaryAlertSubscriptionCode = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToMissingSalaryAlertSubscriptionFee = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToIssueSubscribingBackToSalaryAlert = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToNoBillingHistoryFound = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToBillingInSameMonth = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToUnableToRetrieveBalance = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToUnableToConfirmSalaryReceived = table.Column<int>(type: "int", nullable: false),
                    SkippedRenewalsDueToUnableToDebitUser = table.Column<int>(type: "int", nullable: false),
                    RunStatus = table.Column<int>(type: "int", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Logs_C3PayPlusMembershipRenewals", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipRenewalBillings_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipRenewalBillings",
                column: "C3PayPlusMembershipUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipRenewalBillings");

            migrationBuilder.DropTable(
                name: "Logs_C3PayPlusMembershipRenewals");
        }
    }
}
