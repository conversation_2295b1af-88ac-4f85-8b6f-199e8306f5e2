﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_SpendPolicy_Details_User_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "UserSpendPolicy_SpendPolicyActive",
                table: "Users",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UserSpendPolicy_SpendPolicyId",
                table: "Users",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserSpendPolicy_SpendPolicyActive",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "UserSpendPolicy_SpendPolicyId",
                table: "Users");
        }
    }
}
