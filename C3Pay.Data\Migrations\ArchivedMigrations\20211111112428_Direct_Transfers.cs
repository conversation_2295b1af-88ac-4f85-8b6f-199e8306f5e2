﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Direct_Transfers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferBeneficiaries_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferTransactions_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferTransactions");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferReasonId",
                table: "MoneyTransferTransactions",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<string>(
                name: "LastName",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AddColumn<Guid>(
                name: "LinkedUserId",
                table: "MoneyTransferBeneficiaries",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferBeneficiaries_LinkedUserId",
                table: "MoneyTransferBeneficiaries",
                column: "LinkedUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferBeneficiaries_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries",
                column: "MoneyTransferReasonId",
                principalTable: "MoneyTransferReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferBeneficiaries_Users_LinkedUserId",
                table: "MoneyTransferBeneficiaries",
                column: "LinkedUserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferTransactions_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferTransactions",
                column: "MoneyTransferReasonId",
                principalTable: "MoneyTransferReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddColumn<decimal>(
                name: "ChargeVat",
                table: "MoneyTransferTransactions",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "NationalityCode",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StatusDescription",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferBeneficiaries_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferBeneficiaries_Users_LinkedUserId",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferTransactions_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferTransactions");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferBeneficiaries_LinkedUserId",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropColumn(
                name: "LinkedUserId",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferReasonId",
                table: "MoneyTransferTransactions",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LastName",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferBeneficiaries_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferBeneficiaries",
                column: "MoneyTransferReasonId",
                principalTable: "MoneyTransferReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferTransactions_MoneyTransferReasons_MoneyTransferReasonId",
                table: "MoneyTransferTransactions",
                column: "MoneyTransferReasonId",
                principalTable: "MoneyTransferReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.DropColumn(
               name: "ChargeVat",
               table: "MoneyTransferTransactions");

            migrationBuilder.DropColumn(
                name: "NationalityCode",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropColumn(
                name: "StatusDescription",
                table: "MoneyTransferBeneficiaries");           
        }
    }
}
