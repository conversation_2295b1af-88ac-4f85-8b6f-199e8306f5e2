﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_FeatureFlagsWU : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "WUAddBeneficiaryEnabled",
                table: "MoneyTransferProviders",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "WUGetRatesEnabled",
                table: "MoneyTransferProviders",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "WUSendMoneyEnabled",
                table: "MoneyTransferProviders",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WUAddBeneficiaryEnabled",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "WUGetRatesEnabled",
                table: "MoneyTransferProviders");

            migrationBuilder.DropColumn(
                name: "WUSendMoneyEnabled",
                table: "MoneyTransferProviders");
        }
    }
}
