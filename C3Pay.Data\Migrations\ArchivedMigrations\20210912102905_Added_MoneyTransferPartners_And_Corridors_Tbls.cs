﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_MoneyTransferPartners_And_Corridors_Tbls : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferPartnerReasons",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Reason = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
                    MoneyTransferReasonId = table.Column<int>(type: "int", nullable: false),
                    MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                    MoneyTransferPartnerId1 = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferPartnerReasons", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferPartnerReasons_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferPartnerReasons_MoneyTransferPartners_MoneyTransferPartnerId1",
                        column: x => x.MoneyTransferPartnerId1,
                        principalTable: "MoneyTransferPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferPartnerReasons_MoneyTransferReasons_MoneyTransferReasonId",
                        column: x => x.MoneyTransferReasonId,
                        principalTable: "MoneyTransferReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferPartnerReasons_CountryCode",
                table: "MoneyTransferPartnerReasons",
                column: "CountryCode");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferPartnerReasons_MoneyTransferPartnerId1",
                table: "MoneyTransferPartnerReasons",
                column: "MoneyTransferPartnerId1");

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferPartnerReasons_MoneyTransferReasonId",
                table: "MoneyTransferPartnerReasons",
                column: "MoneyTransferReasonId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferPartnerReasons");
        }
    }
}
