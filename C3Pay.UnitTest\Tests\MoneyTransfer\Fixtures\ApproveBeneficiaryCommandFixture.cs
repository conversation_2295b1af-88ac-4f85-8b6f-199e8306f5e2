﻿using C3Pay.Core;
using C3Pay.Core.Abstractions.Cache;
using C3Pay.Core.Models;
using C3Pay.Core.Services;
using Edenred.Common.Core;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using static C3Pay.Core.BaseEnums;
using Bogus.DataSets;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using Edenred.Common.Core.Models.Messages.Integration.MoneyTransfer.Base.Beneficiaries;
using C3Pay.Services.MoneyTransfer.Commands;

namespace C3Pay.UnitTest.Tests.MoneyTransfer.Fixtures
{
    public class ApproveBeneficiaryCommandFixture
    {
        public Mock<IUnitOfWork> UnitOfWorkMock { get; }
        public Mock<ICacheService> CacheServiceMock { get; }
        public Mock<IRAKService> RAKServiceMock { get; }
        public Mock<IOptions<MoneyTransferServiceSettings>> SettingsMock { get; }
        public Mock<ITextMessageSenderService> TextMessageSenderServiceMock { get; }
        public Mock<IMessagingQueueService> MessagingQueueServiceMock { get; }
        public Mock<IOptions<RAKSettings>> RAKSettingsMock { get; }
        public Mock<IAuditTrailService> AuditTrailServiceMock { get; }
        public Mock<ILogger<ApproveBeneficiaryCommandHandler>> LoggerMock { get; }
        public MoneyTransferServiceSettings Settings { get; }
        public RAKSettings RakSettings { get; }
        public MoneyTransferBeneficiary DbData { get; }
        public ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>> RakSuccessResponse { get; }
        public  MoneyTransferTransaction OnHoldTransaction { get; }

        public ApproveBeneficiaryCommandFixture()
        {
            CacheServiceMock = new Mock<ICacheService>();
            UnitOfWorkMock = new Mock<IUnitOfWork>();
            RAKServiceMock = new Mock<IRAKService>();
            SettingsMock = new Mock<IOptions<MoneyTransferServiceSettings>>();
            TextMessageSenderServiceMock = new Mock<ITextMessageSenderService>();
            MessagingQueueServiceMock = new Mock<IMessagingQueueService>();
            RAKSettingsMock = new Mock<IOptions<RAKSettings>>();
            AuditTrailServiceMock = new Mock<IAuditTrailService>();
            LoggerMock = new Mock<ILogger<ApproveBeneficiaryCommandHandler>>();
            Settings = new MoneyTransferServiceSettings
            {
                MaxBeneficiaryRetryLimit = 4
            };
            RakSettings = new RAKSettings
            {
                MoneyTransferQueueConnectionString = "connectionstring_mock",
                MoneyTransferQueueName = "QueueName_mock"
            };
            RAKSettingsMock.Setup(x => x.Value).Returns(RakSettings);   
            SettingsMock.Setup(s => s.Value).Returns(Settings);
            DbData = new MoneyTransferBeneficiary
            {
                Id = Guid.NewGuid(),
                Status = Status.PENDING,
                IsDeleted = false,
                RetryCount = 2,
                User = new User
                {
                    MoneyTransferProfileStatus = MoneyTransferProfileStatus.Created,
                    CardHolder = new CardHolder { EmiratesId = "123456" },
                    PhoneNumber = "0097122233344"
                },
                ExternalBeneficiary = null
            };
            RakSuccessResponse = new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>
            {
                IsSuccessful = true,
                Data = new RakResponse<BeneficiariesDetailsRakModel>
                {
                    MessageId = Guid.NewGuid(),
                    Data = new BeneficiariesDetailsRakModel { RakBeneficiayId = "rak123" }
                }
            };
            OnHoldTransaction = new MoneyTransferTransaction
            {
                Id = Guid.NewGuid(),
                Status = Status.ONHOLD,
                MoneyTransferBeneficiaryId = DbData.Id,
                ReferenceNumber = "ref123",
                MoneyTransferStatusSteps = new List<MoneyTransferStatusStep>()
            };
        }

        public void SetupDatabaseToReturnData()
        {
            UnitOfWorkMock.Setup(uow => uow.MoneyTransferBeneficiaries.FirstOrDefaultAsync(
                   It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                   It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()
               )).ReturnsAsync(DbData);
        }

        public void SetupDatabaseToReturnNull()
        {
            UnitOfWorkMock.Setup(uow => uow.MoneyTransferBeneficiaries.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<MoneyTransferBeneficiary, bool>>>(),
                It.IsAny<Expression<Func<MoneyTransferBeneficiary, object>>[]>()
            )).ReturnsAsync((MoneyTransferBeneficiary)null);
        }

        public void SetupRakServiceToReturnSuccessfulResponse()
        {
            RAKServiceMock.Setup(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()))
               .ReturnsAsync(RakSuccessResponse);
        }
        public void SetupRakServiceToReturnFailedlResponse()
        {
            RAKServiceMock.Setup(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()))
                .ReturnsAsync(new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>
                {
                    IsSuccessful = false,
                    ErrorMessage = "External service error"
                });
        }

        public void SetupRakServiceToReturnFailedlResponse_WithRetryMessage()
        {
            RAKServiceMock.Setup(service => service.AddRAKMoneyTransferBeneficiary(It.IsAny<string>(), It.IsAny<NewBeneficiaryRequestRakModel>()))
                .ReturnsAsync(new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>
                {
                    IsSuccessful = false,
                    ErrorMessage = "Failed to initialize RAK Bank service"
                });
        }
        public void SetupDbToReturnMoneyTransferTransactions()
        {
            UnitOfWorkMock.Setup(uow => uow.MoneyTransferTransactions
            .FindAsync(It.IsAny<Expression<Func<MoneyTransferTransaction, bool>>>(),
          It.IsAny<Expression<Func<MoneyTransferTransaction, object>>>()
                       )).ReturnsAsync(new List<MoneyTransferTransaction> { OnHoldTransaction});
        }

        public void ResetMocks()
        {
            CacheServiceMock.Reset();
            UnitOfWorkMock.Reset();
            RAKServiceMock.Reset();
            MessagingQueueServiceMock.Reset();
            TextMessageSenderServiceMock.Reset();
        }
    }
}
