﻿using C3Pay.Services.Payments.Queries;
using System;

namespace C3Pay.Core.Models.DTOs.Payments.Responses
{
    public class PaymentAuthorisationResponseDto
    {
        public Guid Id { get; set; }
        public DateTime RequestDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public PaymentAuthAmountDto Amount { get; set; }
        public PaymentAuthorisationMerchantDto Merchant { get; set; }
        public PaymentAuthRequestStatus Status { get; set; }
    }
}