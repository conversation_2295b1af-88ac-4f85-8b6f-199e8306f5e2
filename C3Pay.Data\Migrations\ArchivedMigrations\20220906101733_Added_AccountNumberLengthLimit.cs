﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_AccountNumberLengthLimit : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "AccountNumberLengthLimitType",
                table: "MoneyTransferBanks",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AccountNumberLengthLimitValues",
                table: "MoneyTransferBanks",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AccountNumberLengthLimitType",
                table: "MoneyTransferBanks");

            migrationBuilder.DropColumn(
                name: "AccountNumberLengthLimitValues",
                table: "MoneyTransferBanks");
        }
    }
}
