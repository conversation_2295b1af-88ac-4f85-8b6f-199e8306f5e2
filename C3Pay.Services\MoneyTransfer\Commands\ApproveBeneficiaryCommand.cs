﻿using C3Pay.Core.Models;
using C3Pay.Core;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using Edenred.Common.Core;
using static C3Pay.Core.BaseEnums;
using C3Pay.Core.Common;
using Microsoft.Extensions.Options;
using C3Pay.Core.Services;
using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using C3Pay.Core.Abstractions.Cache;
using Microsoft.Extensions.Logging;

namespace C3Pay.Services.MoneyTransfer.Commands
{
   public record ApproveBeneficiaryCommand(Guid beneficiaryId, Guid? portalUserId = null, string portalEmailId = null) : IRequest<Result<MoneyTransferBeneficiary>>;

   public class ApproveBeneficiaryCommandHandler : IRequestHandler<ApproveBeneficiaryCommand, Result<MoneyTransferBeneficiary>>
   {
      private readonly IUnitOfWork _unitOfWork;
      private readonly IRAKService _rakService;
      private readonly MoneyTransferServiceSettings _moneyTransferServiceSettings;
      private readonly ITextMessageSenderService _textMessageSenderService;
      private readonly IMessagingQueueService _messagingQueueService;
      private readonly RAKSettings _rakSettings;
      private readonly IAuditTrailService _auditTrailService;
      private readonly ICacheService _cacheService;
      private readonly ILogger<ApproveBeneficiaryCommandHandler> _logger;
      private readonly string _beneficiaryListCacheKeyPrefix = CacheKeys.Prefix.beneficiaryListCacheKeyPrefix;

      public ApproveBeneficiaryCommandHandler(IUnitOfWork unitOfWork,
                                              IRAKService rakService,
                                              IOptions<MoneyTransferServiceSettings> moneyTransferServiceSettings,
                                              ITextMessageSenderService textMessageSenderService,
                                              IMessagingQueueService messagingQueueService,
                                              IOptions<RAKSettings> rakSettings,
                                              IAuditTrailService auditTrailService,
                                              ICacheService cacheService,
                                              ILogger<ApproveBeneficiaryCommandHandler> logger)
      {
         _unitOfWork = unitOfWork;
         _rakService = rakService;
         _moneyTransferServiceSettings = moneyTransferServiceSettings.Value;
         _textMessageSenderService = textMessageSenderService;
         _messagingQueueService = messagingQueueService;
         _rakSettings = rakSettings.Value;
         _auditTrailService = auditTrailService;
         _cacheService = cacheService;
         _logger = logger;
      }
      public async Task<Result<MoneyTransferBeneficiary>> Handle(ApproveBeneficiaryCommand request, CancellationToken cancellationToken)
      {
         var beneficiary = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(x => x.Id == request.beneficiaryId &&
                       x.Status == Status.PENDING &&
                       x.IsDeleted == false,
                       x => x.User, x => x.User.CardHolder, x => x.ExternalBeneficiary);

         if (beneficiary == null)
         {
            _logger.LogWarning($"Due to invalid beneficiary id, beneficiary approval failed");
            return Result.Failure<MoneyTransferBeneficiary>(new Error("NotFound", TransferStatusValidationMessage.BeneficiaryNotExists.ToString()));

         }
         beneficiary.ApprovalDate = DateTime.Now;
         await _unitOfWork.CommitAsync();

         if (beneficiary.User.MoneyTransferProfileStatus == MoneyTransferProfileStatus.Created)

            await AddExternalBeneficiary(beneficiary);

         var createdBeneficiary = await _unitOfWork.MoneyTransferBeneficiaries.FirstOrDefaultAsync(param => param.Id == request.beneficiaryId, x => x.MoneyTransferReason, x => x.Country);

         if (request.portalUserId != null)

            await _auditTrailService.AddAuditTrail(request.portalUserId, request.portalEmailId, beneficiary.User.Id, ConstantParam.AuditTrailApproveBeneficiary, beneficiary.User.PhoneNumber, null);

         return Result.Success(createdBeneficiary);
      }
      private async Task AddExternalBeneficiary(MoneyTransferBeneficiary beneficiary)
      {
         beneficiary.RetryCount++;

         var createBeneficiaryRequest = BuildBeneficiaryRequest(beneficiary);
         var createBeneficiaryResult = await TryCreateBeneficiary(beneficiary, createBeneficiaryRequest);

         if (createBeneficiaryResult.IsSuccessful)
         {
            beneficiary.Status = Status.APPROVED;
            beneficiary.UpdatedDate = DateTime.Now;
            if (beneficiary.ExternalBeneficiary != null)
            {
               beneficiary.ExternalBeneficiary.CreationMessageId = createBeneficiaryResult.Data.MessageId;
               beneficiary.ExternalBeneficiary.UpdatedDate = DateTime.Now;
               beneficiary.ExternalBeneficiary.ExternalId = createBeneficiaryResult.Data.Data.RakBeneficiayId;
            }
            else
            {
               beneficiary.ExternalBeneficiary = new MoneyTransferExternalBeneficiary
               {
                  CreationMessageId = createBeneficiaryResult.Data.MessageId,
                  ExternalId = createBeneficiaryResult.Data.Data.RakBeneficiayId
               };
            }
         }
         else
         {
            beneficiary.Remarks = createBeneficiaryResult.ErrorMessage;
            beneficiary.Status = beneficiary.ShouldRetry(_moneyTransferServiceSettings.MaxBeneficiaryRetryLimit, beneficiary.Remarks)
                ? Status.PENDING : Status.FAILED;

            _logger.LogWarning($"Failed to send beneficiary details to RAK: {createBeneficiaryResult.ErrorMessage}");
         }
         beneficiary.Posted = true;
         beneficiary.PostDate = DateTime.Now;
         await _unitOfWork.CommitAsync();

         //Clearing the beneficiary list cache for this user
         var cacheKey = $"{_beneficiaryListCacheKeyPrefix}{beneficiary.UserId}_";
         await _cacheService.RemoveAsync(cacheKey);

         //notify user and send on hold transactions
         if (beneficiary.Status == Status.APPROVED)
         {
            await NotifyUser(beneficiary);
            await SendOnHoldTransaction(beneficiary);
         }
      }
      private NewBeneficiaryRequestRakModel BuildBeneficiaryRequest(MoneyTransferBeneficiary beneficiary)
      {
         var emiratesId = beneficiary.User.CardHolder.EmiratesId;
         beneficiary.DocumentNumber = emiratesId;

         var firstName = string.IsNullOrEmpty(beneficiary.MiddleName) ?
             beneficiary.FirstName :
             $"{beneficiary.FirstName} {beneficiary.MiddleName}";

         return new NewBeneficiaryRequestRakModel
         {
            FirstName = firstName,
            LastName = beneficiary.LastName,
            TransferType = beneficiary.TransferType.ToString(),
            BankName = beneficiary.BankName,
            BankBranchName = beneficiary.BankBranchName,
            IdentifierCode1 = beneficiary.IdentifierCode1,
            Identifier1Name = null,
            AccountNumber = beneficiary.AccountNumber,
            BankCountry = beneficiary.CountryCode,
            RakBeneficiayId = string.Empty,
            IdentifierCode2 = beneficiary.IdentifierCode2,
            Identifier2Name = null,
            Address = new AddressRakModel
            {
               Address1 = beneficiary.Address1?.Truncate(100),
               Address2 = beneficiary.Address2?.Truncate(100),
               Country = beneficiary.CountryCode
            },
            Documents = new List<DocumentRakModel>
                {
                    new DocumentRakModel { Number = emiratesId, Type = "Emirates_ID" }
                }
         };
      }
      private async Task<ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>> TryCreateBeneficiary(MoneyTransferBeneficiary beneficiary, NewBeneficiaryRequestRakModel request)
      {
         try
         {
            return await _rakService.AddRAKMoneyTransferBeneficiary(beneficiary.DocumentNumber, request);
         }
         catch (Exception ex)
         {
            beneficiary.Remarks = string.Join(" : ", "Exception", ex.Message);
            beneficiary.UpdatedDate = DateTime.Now;
            beneficiary.Status = beneficiary.ShouldRetry(_moneyTransferServiceSettings.MaxBeneficiaryRetryLimit, beneficiary.Remarks)
                ? Status.PENDING : Status.FAILED;

            await _unitOfWork.CommitAsync();

            var cacheKey = $"{_beneficiaryListCacheKeyPrefix}{beneficiary.UserId}_";
            await _cacheService.RemoveAsync(cacheKey);

            return new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>(false, beneficiary.Remarks);
         }
      }
      private async Task NotifyUser(MoneyTransferBeneficiary beneficiary)
      {
         var user = beneficiary.User;
         var fullName = beneficiary.GetFullName();
         var result = await _textMessageSenderService.SendMTBeneficiaryApprovedMessage(user.PhoneNumber.ToShortPhoneNumber(), fullName);

         if (!result.IsSuccessful)
         {
            _logger.LogWarning($"Failed to send SMS: {result.ErrorMessage}");
         }
      }
      private async Task SendOnHoldTransaction(MoneyTransferBeneficiary beneficiary)
      {
         var moneyTransferTransactions = await _unitOfWork.MoneyTransferTransactions.FindAsync(f => f.MoneyTransferBeneficiaryId == beneficiary.Id &&
                                                                                                        f.Status == Status.ONHOLD,
                                                                                                        include => include.MoneyTransferStatusSteps);
         foreach (var moneyTransferTransaction in moneyTransferTransactions)
         {
            try
            {
               await _messagingQueueService.SendAsync(new MoneyTransferMessageDto
               {
                  Id = moneyTransferTransaction.Id,
                  Action = MessageAction.Create
               },
               this._rakSettings.MoneyTransferQueueConnectionString,
               this._rakSettings.MoneyTransferQueueName, null);

            }
            catch (Exception ex)
            {
               _logger.LogError($"Connectivity Issue :{moneyTransferTransaction.ReferenceNumber} && exception details {ex?.Message}");

               moneyTransferTransaction.Status = Status.PENDINGREVERSE;
               moneyTransferTransaction.Remarks = $"Connectivity Issue {ex?.Message}";

               // Add an entry to money transfer status progress.
               moneyTransferTransaction.MoneyTransferStatusSteps ??= new List<MoneyTransferStatusStep>();
               moneyTransferTransaction.MoneyTransferStatusSteps.Add(new MoneyTransferStatusStep()
               {
                  Status = Status.PENDINGREVERSE,
                  Message = ConstantParam.UM_TransferFailedButWillBeReversed,
                  Log = $"Transfer failed because of connectivity issues when trying to send the transfer details to the messaging queue service. Exception details {ex?.Message}.",
               });

               await _unitOfWork.CommitAsync();
            }
         }
      }
   }
}
