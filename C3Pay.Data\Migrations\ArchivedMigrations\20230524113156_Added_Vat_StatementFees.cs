﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Vat_StatementFees : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "VatPercentage",
                table: "StatementFees",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b13b85df-fc93-4370-b748-7e4f25eef1f0"),
                columns: new[] { "Fee", "VatPercentage" },
                values: new object[] { 50m, 5m });

            migrationBuilder.UpdateData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b24bdea9-82e5-4482-93f3-03a8f98fa953"),
                columns: new[] { "Fee", "VatPercentage" },
                values: new object[] { 25m, 5m });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VatPercentage",
                table: "StatementFees");

            migrationBuilder.UpdateData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b13b85df-fc93-4370-b748-7e4f25eef1f0"),
                column: "Fee",
                value: 26.25m);

            migrationBuilder.UpdateData(
                table: "StatementFees",
                keyColumn: "Id",
                keyValue: new Guid("b24bdea9-82e5-4482-93f3-03a8f98fa953"),
                column: "Fee",
                value: 26.25m);
        }
    }
}
