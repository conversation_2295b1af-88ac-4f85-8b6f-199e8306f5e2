﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class spGetMoneyTransferProfile : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
			var sp = @"Exec (N'Create or alter PROCEDURE [dbo].[GetMoneyTransferProfile] 
							@Mobile NVARCHAR(28) =NULL,
							@CompanyName NVARCHAR(400) =NULL, 
							@BranchId NVARCHAR(20) =NULL,
							@FromDate DATETIME = NULL, 
							@ToDate DATETIME = NULL,
							@Page INT =NULL,
							@Size INT = NULL
						AS
						  BEGIN
						  Set NoCount on;
						  DECLARE @TOTALCOUNT INT =0
						  DECLARE @SQL NVARCHAR(MAX) = 
							   '' Select 
									m.FullName,
									m.EmiratesId,
									m.CreatedDate,
									m.Status,
									c.CorporateId, 
									c.CorporateName,
									u.PhoneNumber,
									@TOTALCOUNT TotalCount
								from
									MoneyTransferProfiles m inner join
									CardHolders c on m.EmiratesId=c.EmiratesId 
									left join Users u on c.id = u.CardHolderId and u.IsDeleted=0 
								Where 1=1 ''

							IF @Mobile IS NOT NULL AND @Mobile !=''''
							BEGIN
								SET @SQL = @SQL + '' AND u.PhoneNumber LIKE  ''''%''+@Mobile+''%''''''
							END
							IF @CompanyName IS NOT NULL AND @CompanyName !=''''
							BEGIN
								SET @SQL = @SQL + '' AND c.CorporateName LIKE  ''''%''+@CompanyName+''%''''''
							END
							IF @BranchId IS NOT NULL AND @BranchId !=''''
							BEGIN
								SET @SQL = @SQL + '' AND c.CorporateId LIKE  ''''%''+@BranchId+''%''''''
							END
							IF @FromDate IS NOT NULL AND @FromDate !=''''
							BEGIN
								SET @SQL = @SQL + '' AND m.CreatedDate >= ''''''+ CONVERT(varchar,@FromDate, 23) + ''''''''
							END
							IF @ToDate IS NOT NULL AND @ToDate !=''''
							BEGIN
								SET @SQL = @SQL + '' AND m.CreatedDate <= ''''''+ CONVERT(varchar,@ToDate, 23) + ''''''''
							END
	

							DECLARE @SQLCOUNT_QUERY NVARCHAR(MAX) ='' SELECT @TOTALCOUNT=COUNT(1) FROM ('' + @SQL + '') t ''

							EXECUTE sp_executesql @SQLCOUNT_QUERY , N''@TOTALCOUNT INT out'', @TOTALCOUNT out

							SET @SQL = @SQL + ''  ORDER BY m.CreatedDate DESC ''
	
							IF @Page is NOT NULL AND @Page !='''' AND @Size is NOT NULL AND @Size !='''' 
							BEGIN
								SET @SQL = @SQL + '' OFFSET ''+ CAST((@Page-1) * @Size as VARCHAR(10)) +'' ROWS FETCH NEXT ''+ CAST(@Size as VARCHAR(10)) +'' ROWS ONLY ''
							END 

							EXECUTE sp_executesql @SQL,N''@TOTALCOUNT INT'',@TOTALCOUNT

						  END')";
			migrationBuilder.Sql(sp);
		
		}

		protected override void Down(MigrationBuilder migrationBuilder)
        {
			migrationBuilder.Sql("DROP PROCEDURE GetMoneyTransferProfile");

		}
	}
}
