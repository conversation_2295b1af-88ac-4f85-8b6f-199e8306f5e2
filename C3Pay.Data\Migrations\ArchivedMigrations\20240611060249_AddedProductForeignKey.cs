﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddedProductForeignKey : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "ProductCode",
                table: "MobileRechargeRenewals",
                type: "nvarchar(50)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MobileRechargeRenewals_ProductCode",
                table: "MobileRechargeRenewals",
                column: "ProductCode");

            migrationBuilder.AddForeignKey(
                name: "FK_MobileRechargeRenewals_MobileRechargeProducts_ProductCode",
                table: "MobileRechargeRenewals",
                column: "ProductCode",
                principalTable: "MobileRechargeProducts",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MobileRechargeRenewals_MobileRechargeProducts_ProductCode",
                table: "MobileRechargeRenewals");

            migrationBuilder.DropIndex(
                name: "IX_MobileRechargeRenewals_ProductCode",
                table: "MobileRechargeRenewals");

            migrationBuilder.AlterColumn<string>(
                name: "ProductCode",
                table: "MobileRechargeRenewals",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(50)",
                oldNullable: true);
        }
    }
}
