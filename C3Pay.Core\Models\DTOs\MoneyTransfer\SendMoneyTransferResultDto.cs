﻿using C3Pay.Core.Models.C3Pay.MoneyTransfer;
using System.Collections.Generic;

namespace C3Pay.Core.Models.DTOs.MoneyTransfer
{
    public class SendMoneyTransferResultDto
    {
        public MoneyTransferTransaction MoneyTransferTransaction { get; set; }
        public MoneyTransferDelay MoneyTransferDelay { get; set; }
        public Dictionary<string, string> TransferSummary { get; set; }
    }
}
