﻿using C3Pay.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.Configurations
{
    public class MoneyTransferSuspiciousInformationConfiguration : IEntityTypeConfiguration<MoneyTransferSuspiciousInformation>
    {
        public void Configure(EntityTypeBuilder<MoneyTransferSuspiciousInformation> builder)
        {
            builder.ToTable("MoneyTransferSuspiciousInformation");

            builder.<PERSON><PERSON><PERSON>(c => c.Id);

            builder.Property(c => c.Id).HasDefaultValueSql("NEWSEQUENTIALID()");

            builder.Property(c => c.NationalIdNo)
                .HasColumnType("varchar(20)")
                .HasMaxLength(20)
                .IsRequired();

            builder.Property(c => c.FullName)
                .HasColumnType("varchar(100)")
                .HasMaxLength(100)
                .IsRequired();

            builder
                .HasOne(c => c.MoneyTransferBeneficiary)
                .WithOne(p => p.MoneyTransferSuspiciousInformation)
                .HasForeignKey<MoneyTransferSuspiciousInformation>(c => c.MoneyTransferBeneficiaryId);
        }
    }
}
