﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class ExternalUnemploymentInsurance : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PartnerCode",
                table: "UnemploymentInsurancePaymentOptions",
                type: "nvarchar(150)",
                maxLength: 150,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "UnemploymentInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 1,
                column: "PartnerCode",
                value: "Direct");

            migrationBuilder.UpdateData(
                table: "UnemploymentInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 2,
                column: "PartnerCode",
                value: "Direct");

            migrationBuilder.InsertData(
                table: "UnemploymentInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Description", "Fee", "FeeCurrency", "Frequency", "Name", "PartnerCode" },
                values: new object[,]
                {
                    { 3, 5m, "AED", 0.25m, "AED", "AED 6/Month", 0.75m, "AED", 0, "Monthly", "ExchangeHouseSample" },
                    { 4, 60m, "AED", 3m, "AED", "AED 70 (1 Year)", 7m, "AED", 1, "Full", "ExchangeHouseSample" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "UnemploymentInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "UnemploymentInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DropColumn(
                name: "PartnerCode",
                table: "UnemploymentInsurancePaymentOptions");
        }
    }
}
