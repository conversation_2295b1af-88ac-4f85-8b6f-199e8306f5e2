﻿using System;

namespace C3Pay.Core.Models
{
    public class MoneyTransferSuspiciousInformationDto
    {
        public Guid Id { get; set; }
        public Guid MoneyTransferBeneficiaryId { get; set; }
        public string NationalIdNo { get; set; }
        public string FullName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int CityId { get; set; }
        public bool IsApproved { get; set; }
        public DateTime ApprovalDateTime { get; set; }
    }

    public class SaveMoneyTransferSuspiciousInformationDto
    {
        public Guid MoneyTransferBeneficiaryId { get; set; }
        public string NationalIdNo { get; set; }
        public string FullName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int CityId { get; set; }
    }

    public class UpdateMoneyTransferSuspiciousInformationDto
    {
        public Guid Id { get; set; }
        public Guid MoneyTransferBeneficiaryId { get; set; }
        public string NationalIdNo { get; set; }
        public string FullName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public int CityId { get; set; }
    }
}
