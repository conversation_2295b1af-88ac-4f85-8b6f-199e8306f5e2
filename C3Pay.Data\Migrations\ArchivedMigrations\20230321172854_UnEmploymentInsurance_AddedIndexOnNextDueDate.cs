﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnEmploymentInsurance_AddedIndexOnNextDueDate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UnEmpInsurancePayments_Users_UserId",
                table: "UnEmpInsurancePayments");

            migrationBuilder.CreateIndex(
                name: "IX_UnEmpInsurancePayments_NextDueDate",
                table: "UnEmpInsurancePayments",
                column: "NextDueDate");

            migrationBuilder.AddForeignKey(
                name: "FK_UnEmpInsurancePayments_Users_UserId",
                table: "UnEmpInsurancePayments",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropFore<PERSON><PERSON>ey(
                name: "FK_UnEmpInsurancePayments_Users_UserId",
                table: "UnEmpInsurancePayments");

            migrationBuilder.DropIndex(
                name: "IX_UnEmpInsurancePayments_NextDueDate",
                table: "UnEmpInsurancePayments");

            migrationBuilder.AddForeignKey(
                name: "FK_UnEmpInsurancePayments_Users_UserId",
                table: "UnEmpInsurancePayments",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
