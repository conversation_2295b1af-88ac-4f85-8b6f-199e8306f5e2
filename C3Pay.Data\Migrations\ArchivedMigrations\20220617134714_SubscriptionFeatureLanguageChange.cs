﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SubscriptionFeatureLanguageChange : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TextContentCode",
                table: "SubscriptionFeatures",
                type: "nvarchar(25)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionFeatures_TextContentCode",
                table: "SubscriptionFeatures",
                column: "TextContentCode");

            migrationBuilder.AddForeignKey(
                name: "FK_SubscriptionFeatures_TextContents_TextContentCode",
                table: "SubscriptionFeatures",
                column: "TextContentCode",
                principalTable: "TextContents",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SubscriptionFeatures_TextContents_TextContentCode",
                table: "SubscriptionFeatures");

            migrationBuilder.DropIndex(
                name: "IX_SubscriptionFeatures_TextContentCode",
                table: "SubscriptionFeatures");

            migrationBuilder.DropColumn(
                name: "TextContentCode",
                table: "SubscriptionFeatures");
        }
    }
}
