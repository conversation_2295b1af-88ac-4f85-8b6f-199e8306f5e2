﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateMoneyTransferProfiles : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApplicationId",
                table: "MissingKycCardholders");

            migrationBuilder.AddColumn<int>(
                name: "RetryCount",
                table: "MoneyTransferProfiles",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RetryCount",
                table: "MoneyTransferProfiles");

            migrationBuilder.AddColumn<int>(
                name: "ApplicationId",
                table: "MissingKycCardholders",
                type: "int",
                nullable: false,
                defaultValue: 1);
        }
    }
}
