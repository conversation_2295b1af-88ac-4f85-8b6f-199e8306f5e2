﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_MissingKyc_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MissingKycCardholders",
                columns: table => new
                {
                    CitizenId = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    IsUnblocked = table.Column<bool>(type: "bit", nullable: false),
                    UnblockDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MissingKycCardholders", x => x.CitizenId);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MissingKycCardholders");
        }
    }
}
