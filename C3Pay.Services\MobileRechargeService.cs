﻿
using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models;
using C3Pay.Core.Models.C3Pay.Experiment.ExperimentConfig;
using C3Pay.Core.Models.DTOs.MobileRecharge;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Messages.MobileRecharge.Analytics;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Structs;
using C3Pay.Core.Services;
using C3Pay.Data;
using C3Pay.Data.Migrations;
using C3Pay.Services.Filters;
using C3Pay.Services.Helper;
using C3Pay.Services.IntegrationEvents.Out.Enums;
using Common.Core.Models;
using Edenred.Common.Core;
using Edenred.Common.Services;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Microsoft.FeatureManagement.FeatureFilters;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services
{
    public class MobileRechargeService : IMobileRechargeService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDingService _dingService;
        private readonly IPPSWebAuthService _ppsService;
        private readonly IIdentityService _identityService;
        private readonly IMessagingQueueService _messagingQueueService;
        private readonly MobileRechargeServiceSettings _mobileRechargeServiceSettings;
        private readonly IAuditTrailService _auditTrailService;
        private readonly TestingSettings _testingSettings;
        private readonly ILogger _logger;
        private readonly string[] _inActiveProducts = new string[] { "ETAEAE52423", "ETAEAE28740", "ETAEAE73690" };
        private readonly string[] _failedStatuses = { Enums.ExternalDingStatus.Cancelled.ToString(), Enums.ExternalDingStatus.Cancelling.ToString(), Enums.ExternalDingStatus.Failed.ToString() };
        TransactionPrefix mrTransacationPrefix = TransactionPrefix.MOB;
        private readonly List<string> callingCardOperators = Enum.GetNames(typeof(BaseEnums.CallingCardOperator)).ToList();
        private readonly IUnitOfWorkReadOnly _unitOfWorkReadOnly;
        private readonly IFeatureManager _featureManager;
        private readonly ILookupService _lookupService;
        private readonly IAnalyticsPublisherService _analyticsPublisherService;
        private readonly ITextMessageSenderService _textMessageSenderService;
        private readonly IPushNotificationSenderService _pushNotificationSenderService;
        private readonly IDistributedCache _cache;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="dingService"></param>
        /// <param name="messagingQueueService"></param>
        /// <param name="dingSettings"></param>
        /// <param name="logger"></param>
        /// <param name="unitOfWorkReadOnly"></param>
        public MobileRechargeService(IUnitOfWork unitOfWork, IDingService dingService, IPPSWebAuthService ppsService, IIdentityService identityService, IMessagingQueueService messagingQueueService, IOptions<MobileRechargeServiceSettings> mobileRechargeServiceSettings, ILogger<MobileRechargeService> logger, IUnitOfWorkReadOnly unitOfWorkReadOnly, IAuditTrailService auditTrailService, IOptions<TestingSettings> testingSettings, IFeatureManager featureManager,
            ILookupService lookupService, IAnalyticsPublisherService analyticsPublisherService, ITextMessageSenderService textMessageSenderService, IPushNotificationSenderService pushNotificationSenderService, IDistributedCache cache)
        {
            this._unitOfWork = unitOfWork;
            this._dingService = dingService;
            this._ppsService = ppsService;
            this._identityService = identityService;
            this._messagingQueueService = messagingQueueService;
            this._mobileRechargeServiceSettings = mobileRechargeServiceSettings.Value;
            this._logger = logger;
            this._unitOfWorkReadOnly = unitOfWorkReadOnly;
            this._auditTrailService = auditTrailService;
            this._testingSettings = testingSettings.Value;
            this._featureManager = featureManager;
            this._lookupService = lookupService;
            this._analyticsPublisherService = analyticsPublisherService;
            this._textMessageSenderService = textMessageSenderService;
            this._pushNotificationSenderService = pushNotificationSenderService;
            this._cache = cache;
        }

        #region Data Synchronize With DingData

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingCountryWithDB()
        {
            //Synchronize Country with DB
            //Ding API call
            var CountryDingReponse = await _dingService.GetSupportedCountries();

            //if failure with Ding Service
            if (CountryDingReponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (CountryDingReponse.Data == null || !CountryDingReponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            List<CountryResponseDingModel> CountryDingList = new List<CountryResponseDingModel>(CountryDingReponse.Data).ToList();

            //listing country code with unique
            List<string> CountryCodeDingList = new List<string>(CountryDingList.Select(z => z.CountryCode).Distinct());

            //update not exist list
            var CountryNotExistsUpdateDbList = await _unitOfWork.Countries.FindAsync(param => !CountryCodeDingList.Contains(param.Code) && param.MobileRechargeEnabledForPartner == true, false);

            foreach (Country countryitem in CountryNotExistsUpdateDbList)
            {
                countryitem.MobileRechargeEnabledForPartner = false;
                countryitem.MobileRechargeLastSynchronizedDate = DateTime.Now;
            }

            //update exists list
            var CountryExistsUpdateDbList = await _unitOfWork.Countries.FindAsync(param => CountryCodeDingList.Contains(param.Code) && param.MobileRechargeEnabledForPartner == false, false);

            foreach (Country countryitem in CountryExistsUpdateDbList)
            {
                countryitem.MobileRechargeEnabledForPartner = true;
                countryitem.MobileRechargeLastSynchronizedDate = DateTime.Now;
            }

            await _unitOfWork.CommitAsync();

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingRegionWithDB()
        {
            //Synchronize Region with DB
            //ding API call
            var getSupportedCountriesResponse = await _dingService.GetRegions();

            //service connection failure with Ding Service
            if (getSupportedCountriesResponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (getSupportedCountriesResponse.Data == null || !getSupportedCountriesResponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            var supportedCountries = getSupportedCountriesResponse.Data.ToList();

            var supportedCountriesCodes = supportedCountries.Select(z => z.RegionCode).Distinct().ToList();

            var localSupportedCountries = await _unitOfWork.MobileRechargeSupportedCountries.GetAllSupportedCountries();

            var localSupportedCountriesCodes = localSupportedCountries.Select(z => z.Code).Distinct().ToList();

            var inActiveCountries = localSupportedCountries.Where(param => supportedCountriesCodes.Contains(param.Code) && !param.IsActive);


            foreach (var inActiveCountry in inActiveCountries)
            {
                inActiveCountry.IsActive = true;
                inActiveCountry.LastUpdatedDate = DateTime.Now;
            }

            var activeCountries = localSupportedCountries.Where(param => !supportedCountriesCodes.Contains(param.Code) && param.IsActive);


            foreach (var activeCountry in activeCountries)
            {
                activeCountry.IsActive = false;
                activeCountry.LastUpdatedDate = DateTime.Now;
            }

            var missingSupportedCountries = supportedCountries.Where(x => !localSupportedCountriesCodes.Contains(x.RegionCode));


            var countriesToAdd = missingSupportedCountries.Select(country => new MobileRechargeSupportedCountry()
            {
                CountryCode = country.CountryCode,
                Code = country.RegionCode,
                IsActive = true,
                Name = country.RegionName,
                LastUpdatedDate = DateTime.Now
            }).Where(c => c.Code.Length == 2).ToList();

            await this._unitOfWork.MobileRechargeSupportedCountries.BulkInsertAsync(countriesToAdd);
            await _unitOfWork.CommitAsync();

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingProviderWithDB()
        {
            //Synchronize Providers with DB

            //Ding API call
            var ProvidersDingResponse = await _dingService.GetProductProviders();

            //service connection failure with Ding Service
            if (ProvidersDingResponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (ProvidersDingResponse.Data == null || !ProvidersDingResponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            //convert Ienumerable to list
            List<ProvidersResponseDingModel> ProviderDingList = new List<ProvidersResponseDingModel>(ProvidersDingResponse.Data);

            //listing Provider code with unique
            List<string> ProviderCodeDingList = new List<string>(ProviderDingList.Select(z => z.ProviderCode).Distinct());

            var UpdateProviderList = await _unitOfWork.MobileRechargeProviders.FindAsync(param => ProviderCodeDingList.Contains(param.Code), false);


            //Activate and update Providers exists in DB and Ding            
            foreach (MobileRechargeProvider provideritem in UpdateProviderList)
            {
                var providerdingitem = ProviderDingList.FirstOrDefault(x => x.ProviderCode == provideritem.Code);

                provideritem.CountryCode = providerdingitem.CountryCode;
                provideritem.CustomerCareNumber = providerdingitem.CustomerCareNumber;
                provideritem.LogoUrl = providerdingitem.LogoUrl;
                provideritem.PaymentTypes = providerdingitem.PaymentTypesListString().ToString();
                provideritem.Name = providerdingitem.ProviderName;
                provideritem.ShortName = providerdingitem.ShortName;
                provideritem.RegionCodes = providerdingitem.RegionCodesListString().ToString();
                provideritem.ValidationRegex = providerdingitem.ValidationRegex;
                provideritem.IsActive = true;
                provideritem.LastUpdatedDate = DateTime.Now;
            }

            var DeactiveProviderList = await _unitOfWork.MobileRechargeProviders.FindAsync(param => !ProviderCodeDingList.Contains(param.Code)
            && (param.IsActive.HasValue && param.IsActive.Value), false);


            //Deactivate Products in DB and not exists in Ding
            foreach (MobileRechargeProvider provideritem in DeactiveProviderList)
            {
                provideritem.IsActive = false;
                provideritem.LastUpdatedDate = DateTime.Now;
            }

            //listing Provider code with unique from DB 
            var ProviderDBList = await _unitOfWork.MobileRechargeProviders.GetAllAsync();
            List<string> ProviderCodeDBList = new List<string>(ProviderDBList.Select(z => z.Code).Distinct());

            //adding missing providers in DB from Ding   
            var MissingProvidersList = ProviderDingList.Where(x => !ProviderCodeDBList.Contains(x.ProviderCode));


            List<MobileRechargeProvider> MissingProviders = MissingProvidersList.Select(missingListItem => new MobileRechargeProvider()
            {
                CountryCode = missingListItem.CountryCode,
                CustomerCareNumber = missingListItem.CustomerCareNumber,
                Code = missingListItem.ProviderCode,
                LogoUrl = missingListItem.LogoUrl,
                PaymentTypes = missingListItem.PaymentTypesListString().ToString(),
                Name = missingListItem.ProviderName,
                ShortName = missingListItem.ShortName,
                RegionCodes = missingListItem.RegionCodesListString().ToString(),
                ValidationRegex = missingListItem.ValidationRegex,
                IsActive = true,
                LastUpdatedDate = DateTime.Now
            }).ToList();

            if (MissingProviders.Any())
            {
                await _unitOfWork.MobileRechargeProviders.AddRangeAsync(MissingProviders);
                await _unitOfWork.CommitAsync();
            }

            // Update Countries 
            var alreadyExistingMRCountries = await _unitOfWork.Countries.FindAsync(a => a.MobileRechargeEnabled);
            if (alreadyExistingMRCountries.Any())
            {
                var activeProvidersList = ProviderDBList.Where(a => a.IsActive == true).ToList();
                // find missing countries
                var providerCountryCodes = activeProvidersList.Select(a => a.CountryCode).Distinct().ToList();

                var missedCountries = alreadyExistingMRCountries.Where(a => !providerCountryCodes.Contains(a.Code)).ToList();

                if (missedCountries.Any())
                {
                    missedCountries.ForEach(_item =>
                    {
                        _item.MobileRechargeEnabled = false;
                    });
                    await _unitOfWork.Countries.BulkUpdateAsync(missedCountries);
                    await _unitOfWork.CommitAsync();
                }
            }

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingProviderStatusWithDB()
        {
            //Synchronize Provider Status with DB
            //Ding API call
            var ProviderStatusReponse = await _dingService.GetProviderStatus();

            //service connection failure with Ding Service
            if (ProviderStatusReponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (ProviderStatusReponse.Data == null || !ProviderStatusReponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            //Converting to list
            List<ProviderStatusResponseDingModel> ProviderStatusDingList = new List<ProviderStatusResponseDingModel>(ProviderStatusReponse.Data).ToList();

            //listing country code with unique
            List<string> ProviderCodeDingList = new List<string>(ProviderStatusDingList.Select(z => z.ProviderCode).Distinct());

            //getting existing provider from DB
            var ProviderDBUpdateList = await _unitOfWork.MobileRechargeProviders.FindAsync(param => ProviderCodeDingList.Contains(param.Code), false);


            //update provider status to exists list
            foreach (var Provideritem in ProviderDBUpdateList)
            {
                var providerdingitem = ProviderStatusDingList.FirstOrDefault(x => x.ProviderCode == Provideritem.Code);

                Provideritem.IsProcessingTransfers = providerdingitem.IsProcessingTransfers;
                Provideritem.ProcessStatusMessage = providerdingitem.Message;
                Provideritem.LastUpdatedDate = DateTime.Now;
            }

            //Save provider status data
            await _unitOfWork.CommitAsync();

            return true;

        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingProductsDescriptionWithDB()
        {
            //Synchronize Product description with DB
            //Ding API call
            var ProductDescriptionReponse = await _dingService.GetProductDescriptions();

            //service connection failure with Ding Service
            if (ProductDescriptionReponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (ProductDescriptionReponse.Data == null || !ProductDescriptionReponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            //Converting to list
            List<ProductDescriptionResponseDingModel> ProductDescriptionDingList = new List<ProductDescriptionResponseDingModel>(ProductDescriptionReponse.Data).Where(x => !string.IsNullOrEmpty(x.AdditionalInformation)).ToList();

            //listing country code with unique
            List<string> ProductCodeDingList = new List<string>(ProductDescriptionDingList.Where(x => !string.IsNullOrEmpty(x.AdditionalInformation)).Select(z => z.LocalizationKey).Distinct());

            //getting existing provider from DB
            var ProductDBUpdateList = await _unitOfWork.MobileRechargeProducts.FindAsync(param => ProductCodeDingList.Contains(param.Code), false);


            //update provider status to exists list
            foreach (var Productitem in ProductDBUpdateList)
            {
                var providerdingitem = ProductDescriptionDingList.FirstOrDefault(x => x.LocalizationKey == Productitem.Code);
                Productitem.AdditionalInformation = (!string.IsNullOrEmpty(providerdingitem.AdditionalInformation) && providerdingitem.AdditionalInformation.Length > 200) ? providerdingitem.AdditionalInformation.Substring(0, 199) : providerdingitem.AdditionalInformation;
                Productitem.LastUpdatedDate = DateTime.Now;
            }

            //Save provider status data
            await _unitOfWork.CommitAsync();

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<bool> SynchronizeDingProductsWithDB()
        {
            int countrycount = 1;
            //Synchronize Products with DB

            //Getting Product list from Ding
            var ProductDingResponse = await _dingService.GetProducts();

            //service connection failure with Ding Service
            if (ProductDingResponse.IsSuccessful == false)
            {
                _logger.LogWarning(SystemMessages.UnableToConnectToDingService);
                return false;
            }

            //No Data found
            if (ProductDingResponse.Data == null || !ProductDingResponse.Data.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            //listing country and region list from DB
            var CountryDBList = await _unitOfWork.MobileRechargeSupportedCountries.FindAsync(param => param.IsActive == true);
            if (CountryDBList == null || !CountryDBList.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }

            //get list only for mobile data and bundles
            var ProductDingResult = (from prd in (ProductDingResponse.Data.Where(x => (x.BenefitsListString().ToString() == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.Data).ToString()
                                                                         || x.BenefitsListString().ToString() == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.Credit).ToString()
                                                                         || x.BenefitsListString().ToString() == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.CreditData))
                                                                         ))
                                     join cty in CountryDBList on prd.RegionCode equals cty.Code
                                     select new
                                     {
                                         Product = prd,
                                         CountryCode = cty.CountryCode,
                                     }).ToList();

            if (ProductDingResult == null || !ProductDingResult.Any())
            {
                _logger.LogWarning(SystemMessages.NoDataFound);
                return false;
            }


            var CountryList = ProductDingResult.Select(z => z.CountryCode).Distinct();

            //Synchronize data country wise 
            foreach (string CountryCode in CountryList)
            {
                //listing Products from Ding Response                
                List<ProductResponseDingModel> ProductDingList = ProductDingResult.Where(x => x.CountryCode == CountryCode).Select(z => z.Product).ToList();
                List<string> ProductCodeDingList = ProductDingList.Select(z => z.ProductCode).Distinct().ToList();

                //listing Products from DB
                var ProductDBList = await _unitOfWork.MobileRechargeProducts.FindAsync(z => z.CountryCode == CountryCode);
                List<string> ProductCodeDBList = new List<string>(ProductDBList.Select(z => z.Code).Distinct());

                //update products in DB, Exists in Ding and DB
                await this.UpdateProductsExistsInDingAndDb(ProductDingList, ProductCodeDingList, CountryCode);

                //Deactivate products in DB, Not Exists in Ding and exists in DB.
                await this.DeactivateProductsNotExistsInDingAndExistsInDb(ProductCodeDingList, CountryCode);

                //Insert products in DB, Exists in Ding and not exists in DB.
                await this.InsertMissingProductsExistsInDingAndNotExistsInDb(ProductDingList, ProductCodeDBList, CountryCode);

            }

            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="DingResponseData"></param>
        /// <param name="DingProductCodeList"></param>
        /// <param name="CountryCode"></param>
        /// <returns></returns>
        private async Task UpdateProductsExistsInDingAndDb(List<ProductResponseDingModel> DingResponseData, List<string> DingProductCodeList, string CountryCode)
        {
            //Activate and update Providers exists in DB and Ding data        
            var ExistsProducts = await _unitOfWork.MobileRechargeProducts.FindAsync(param => DingProductCodeList.Contains(param.Code), false);
            foreach (MobileRechargeProduct productitem in ExistsProducts)
            {
                ProductResponseDingModel productdingitem = DingResponseData.FirstOrDefault(x => x.ProductCode == productitem.Code);

                productitem.ProviderCode = productdingitem.ProviderCode;
                productitem.RegionCode = productdingitem.RegionCode;
                productitem.LocalizationKey = productdingitem.LocalizationKey;
                productitem.MaxCustomerFee = productdingitem.MaximumPayment.CustomerFee;
                productitem.MaxDistributorFee = productdingitem.MaximumPayment.CustomerFee;
                productitem.MaxReceiveCurrencyIso = productdingitem.MaximumPayment.ReceiveCurrencyIso;
                productitem.MaxReceiveValue = productdingitem.MaximumPayment.ReceiveValue;
                productitem.MaxReceiveValueExcludingTax = productdingitem.MaximumPayment.ReceiveValueExcludingTax;
                productitem.MaxSendCurrencyIso = productdingitem.MaximumPayment.SendCurrencyIso;
                productitem.MaxSendValue = productdingitem.MaximumPayment.SendValue;
                productitem.MaxTaxCalculation = productdingitem.MaximumPayment.TaxCalculation;
                productitem.MaxTaxName = productdingitem.MaximumPayment.TaxName;
                productitem.MaxTaxRate = productdingitem.MaximumPayment.TaxRate;
                productitem.MinCustomerFee = productdingitem.MinimumPayment.CustomerFee;
                productitem.MinDistributorFee = productdingitem.MinimumPayment.CustomerFee;
                productitem.MinReceiveCurrencyIso = productdingitem.MinimumPayment.ReceiveCurrencyIso;
                productitem.MinReceiveValue = productdingitem.MinimumPayment.ReceiveValue;
                productitem.MinReceiveValueExcludingTax = productdingitem.MinimumPayment.ReceiveValueExcludingTax;
                productitem.MinSendCurrencyIso = productdingitem.MinimumPayment.SendCurrencyIso;
                productitem.MinSendValue = productdingitem.MinimumPayment.SendValue;
                productitem.MinTaxCalculation = productdingitem.MinimumPayment.TaxCalculation;
                productitem.MinTaxName = productdingitem.MinimumPayment.TaxName;
                productitem.MinTaxRate = productdingitem.MinimumPayment.TaxRate;
                productitem.CommissionRate = productdingitem.CommissionRate;
                productitem.CountryCode = CountryCode;
                productitem.DefaultDisplayText = productdingitem.DefaultDisplayText;
                productitem.LookupBillsRequired = productdingitem.LookupBillsRequired.ToLower() == "true" ? true : false;
                productitem.Benefits = this.GetMobileBenefit(productdingitem.BenefitsListString().ToString(), productdingitem.RedemptionMechanism);
                productitem.ProcessingMode = productdingitem.ProcessingMode;
                productitem.RedemptionMechanism = productdingitem.RedemptionMechanism;
                productitem.UATNumber = productdingitem.UatNumber;
                productitem.ValidityPeriodIso = productdingitem.ValidityPeriodIso;
                productitem.IsActive = !_inActiveProducts.Contains(productdingitem.ProductCode);
                productitem.LastUpdatedDate = DateTime.Now;
            }

            //Save data in DB
            await _unitOfWork.CommitAsync();

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="DingProductCodeList"></param>
        /// <param name="CountryCode"></param>
        /// <returns></returns>
        private async Task DeactivateProductsNotExistsInDingAndExistsInDb(List<string> DingProductCodeList, string CountryCode)
        {
            //listing deactivate data
            var DeactivateProducts = await _unitOfWork.MobileRechargeProducts.FindAsync(param => !DingProductCodeList.Contains(param.Code) && param.CountryCode == CountryCode && (param.IsActive.HasValue && param.IsActive.Value), false);


            //Deactivate Products in DB and not exists in Ding
            foreach (MobileRechargeProduct provideritem in DeactivateProducts)
            {
                provideritem.IsActive = false;
                provideritem.LastUpdatedDate = DateTime.Now;
            }

            //Save data in DB
            await _unitOfWork.CommitAsync();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="DingResponseData"></param>
        /// <param name="DbProductCodeList"></param>
        /// <param name="CountryCode"></param>
        /// <returns></returns>
        private async Task InsertMissingProductsExistsInDingAndNotExistsInDb(List<ProductResponseDingModel> DingResponseData, List<string> DbProductCodeList, string CountryCode)
        {
            //Listing insertion data
            var MissingProductList = DingResponseData.Where(x => !DbProductCodeList.Contains(x.ProductCode));

            //adding missing product in DB from Ding   
            List<MobileRechargeProduct> MissingProducts = MissingProductList.Select(missingListItem => new MobileRechargeProduct()
            {
                ProviderCode = missingListItem.ProviderCode,
                RegionCode = missingListItem.RegionCode,
                Code = missingListItem.ProductCode,
                LocalizationKey = missingListItem.LocalizationKey,
                MaxCustomerFee = missingListItem.MaximumPayment.CustomerFee,
                MaxDistributorFee = missingListItem.MaximumPayment.CustomerFee,
                MaxReceiveCurrencyIso = missingListItem.MaximumPayment.ReceiveCurrencyIso,
                MaxReceiveValue = missingListItem.MaximumPayment.ReceiveValue,
                MaxReceiveValueExcludingTax = missingListItem.MaximumPayment.ReceiveValueExcludingTax,
                MaxSendCurrencyIso = missingListItem.MaximumPayment.SendCurrencyIso,
                MaxSendValue = missingListItem.MaximumPayment.SendValue,
                MaxTaxCalculation = missingListItem.MaximumPayment.TaxCalculation,
                MaxTaxName = missingListItem.MaximumPayment.TaxName,
                MaxTaxRate = missingListItem.MaximumPayment.TaxRate,
                MinCustomerFee = missingListItem.MinimumPayment.CustomerFee,
                MinDistributorFee = missingListItem.MinimumPayment.CustomerFee,
                MinReceiveCurrencyIso = missingListItem.MinimumPayment.ReceiveCurrencyIso,
                MinReceiveValue = missingListItem.MinimumPayment.ReceiveValue,
                MinReceiveValueExcludingTax = missingListItem.MinimumPayment.ReceiveValueExcludingTax,
                MinSendCurrencyIso = missingListItem.MinimumPayment.SendCurrencyIso,
                MinSendValue = missingListItem.MinimumPayment.SendValue,
                MinTaxCalculation = missingListItem.MinimumPayment.TaxCalculation,
                MinTaxName = missingListItem.MinimumPayment.TaxName,
                MinTaxRate = missingListItem.MinimumPayment.TaxRate,
                CommissionRate = missingListItem.CommissionRate,
                CountryCode = CountryCode,
                DefaultDisplayText = missingListItem.DefaultDisplayText,
                LookupBillsRequired = missingListItem.LookupBillsRequired.ToLower() == "true" ? true : false,
                Benefits = this.GetMobileBenefit(missingListItem.BenefitsListString().ToString(), missingListItem.RedemptionMechanism),
                ProcessingMode = missingListItem.ProcessingMode,
                RedemptionMechanism = missingListItem.RedemptionMechanism,
                UATNumber = missingListItem.UatNumber,
                ValidityPeriodIso = missingListItem.ValidityPeriodIso,
                IsActive = !_inActiveProducts.Contains(missingListItem.ProductCode),
                LastUpdatedDate = DateTime.Now
            }).ToList();

            if (MissingProducts.Any())
                await _unitOfWork.MobileRechargeProducts.AddRangeAsync(MissingProducts);

            //Save data in DB
            await _unitOfWork.CommitAsync();

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dingbenefits"></param>
        /// <param name="redemption"></param>
        /// <returns></returns>
        private string GetMobileBenefit(string dingbenefits, string redemption)
        {
            string Benefits = BaseEnums.MobileRechargeProducts.CREDIT.ToString();

            if (dingbenefits == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.Credit)
                  && redemption == Enums.DingProductRedemption.Immediate.ToString())
                Benefits = BaseEnums.MobileRechargeProducts.CREDIT.ToString();
            else if ((dingbenefits == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.Data) ||
                dingbenefits == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.CreditData))
                && redemption == Enums.DingProductRedemption.Immediate.ToString())
                Benefits = BaseEnums.MobileRechargeProducts.DATA.ToString();
            else if (dingbenefits == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.MobileProductCategory.CreditData)
                && redemption == Enums.DingProductRedemption.ReadReceipt.ToString())
                Benefits = BaseEnums.MobileRechargeProducts.CALLINGCARDS.ToString();
            else
                Benefits = dingbenefits;

            return Benefits;
        }

        #endregion

        #region Products and beneficiary related API Methods

        /// <summary>
        /// 
        /// </summary>
        /// <param name="BeneficiaryId"></param>
        /// <param name="ProductType"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetProducts(Guid beneficiaryId, string productType, int? pageSize, int? pageNumber, MobileApplicationId applicationId, string corporateId, bool rechargeForMyself = false)
        {
            int? skipValue;
            int? pageRecords;

            // If we need to recharge the user's own phone number
            // 'beneficiaryId' is the current user's ID.
            if (rechargeForMyself)
            {
                // Find current user.
                var currentUser = await this._unitOfWork.Users.FirstOrDefaultAsync(
                    u => u.Id == beneficiaryId
                    && u.IsDeleted == false
                    && u.IsBlocked == false);

                if (currentUser is null)
                {
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, ConstantParam.UserNotFound);
                }

                // Get the list of providers available for the user's phone number.
                var availableProviders = await this._dingService.GetAccountLookup(currentUser.PhoneNumber);

                // Checking provider status.
                if (availableProviders.IsSuccessful == false || (availableProviders.IsSuccessful && (availableProviders.Data == null || (availableProviders.Data != null && availableProviders.Data.Data.Count == 0))))
                {
                    _logger.LogWarning($"Error: No providers found");
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString());
                }

                // Check if provider country is UAE.          
                if (availableProviders.Data.CountryCode != ConstantParam.DefaultCountryCode)
                {
                    _logger.LogWarning($"Error: country for self beneficiary is not UAE.");
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }

                // Get provider codes.
                var providerCodes = availableProviders.Data.Data.Select(p => p.ProviderCode).ToList();

                if (providerCodes.Count == 0)
                {
                    _logger.LogWarning(ConstantParam.NotExistsProductsForBeneficiary);
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.ProductsNotExistsForBeneficiary.ToString());
                }

                if (pageSize > 0 && pageNumber > 0)
                {
                    skipValue = pageNumber == 1 ? 0 : (pageNumber - 1) * pageSize;
                    pageRecords = pageSize;
                }
                else
                {
                    skipValue = null;
                    pageRecords = null;
                }

                // Get a list of available products for the current provider.
                var products = await _unitOfWork.MobileRechargeProducts.FindAsync(param => providerCodes.Contains(param.ProviderCode)
                                                                              && (productType == null || productType == string.Empty || param.Benefits == productType) && param.RedemptionMechanism == Enums.DingProductRedemption.Immediate.ToString()
                                                                              && param.IsActive.HasValue && param.IsActive.Value
                                                                              && param.Provider.IsProcessingTransfers.HasValue && param.Provider.IsProcessingTransfers.Value
                                                                              && param.Provider.IsActive.HasValue && param.Provider.IsActive.Value,
                                                                              orderby => orderby.MinSendValue, false,
                                                                              skipValue, pageRecords, false,
                                                                              x => x.Provider,
                                                                              x => x.SupportedCountry);

                return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(products.OrderByDescending(o => o.IsHighlighted));

            }
            else
            {

                //Get beneficiary  data
                var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId, y => y.BeneficiaryProviders);

                if (beneficiary == null)
                {
                    _logger.LogWarning($"Due to invalid beneficiay id, get product list failed");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                }

                // Cross Check Beneficiary validation 
                if (beneficiary.IsDeleted && beneficiary.CrossCheckedBeneficiaryId != null)
                {
                    beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiary.CrossCheckedBeneficiaryId, y => y.BeneficiaryProviders);
                    if (beneficiary == null)
                        return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                }

                //update provider details
                var dingLookupResponse = await this._dingService.GetAccountLookup(beneficiary.AccountNumber);

                //checking provider status
                if (!dingLookupResponse.IsSuccessful || (dingLookupResponse.IsSuccessful && (dingLookupResponse.Data == null || (dingLookupResponse.Data != null && dingLookupResponse.Data.Data.Count == 0))))
                {
                    _logger.LogWarning($"Due to invalid provider, beneficiary creation failed for {beneficiary.NickName}");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString());
                }

                //checking provider country is valid or not            
                if (dingLookupResponse.Data.CountryCode != beneficiary.CountryCode.ToUpper())
                {
                    _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {beneficiary.AccountNumber}");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }

                var dingProviders = dingLookupResponse.Data.Data;
                var beneficiaryProviders = beneficiary.BeneficiaryProviders.Where(p => p.IsActive.HasValue && p.IsActive.Value).Select(p => p.ProviderCode);

                if (!beneficiary.IsProviderSelected) // Added this condition to prioritize the user operator selection over ding's operator response
                {
                    var missingProviders = dingProviders.Where(p =>
                        !callingCardOperators.Contains(p.ProviderCode)
                        && !beneficiaryProviders.Contains(p.ProviderCode)).ToList();

                    if (missingProviders.Count > 0)
                    {
                        missingProviders.ForEach(provider =>
                        {
                            beneficiary.BeneficiaryProviders.Add(new MobileRechargeBeneficiaryProvider
                            {
                                BeneficiaryId = beneficiary.Id,
                                ProviderCode = provider.ProviderCode,
                                IsActive = true,
                                UpdatedDate = DateTime.Now,
                            });
                        });

                        await this._unitOfWork.CommitAsync();
                    }
                }

                //get provider list
                var providers = beneficiary.BeneficiaryProviders.Select(p => p.ProviderCode).ToList();

                if (providers.Count == 0)
                {
                    _logger.LogWarning(string.Format(ConstantParam.NotExistsProductsForBeneficiary, beneficiary.NickName, beneficiary.AccountNumber));

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.ProductsNotExistsForBeneficiary.ToString());
                }

                if (pageSize > 0 && pageNumber > 0)
                {
                    skipValue = pageNumber == 1 ? 0 : (pageNumber - 1) * pageSize;
                    pageRecords = pageSize;
                }
                else
                {
                    skipValue = null;
                    pageRecords = null;
                }

                var products = (await _unitOfWork.MobileRechargeProducts.FindAsync(param => providers.Contains(param.ProviderCode)
                                                                                 && (productType == null || productType == string.Empty || param.Benefits == productType) && param.RedemptionMechanism == Enums.DingProductRedemption.Immediate.ToString()
                                                                                 && (param.IsActive.HasValue && param.IsActive.Value)
                                                                                 && (param.Provider.IsProcessingTransfers.HasValue && param.Provider.IsProcessingTransfers.Value)
                                                                                 && (param.Provider.IsActive.HasValue && param.Provider.IsActive.Value),
                                                                                 orderby => orderby.MinSendValue, false,
                                                                                 skipValue, pageRecords, false,
                                                                                 x => x.Provider,
                                                                                 x => x.SupportedCountry)).ToList();

                //----------------------------------------------------------------------------------------------
                //  Adjustable Price Package Check   
                //----------------------------------------------------------------------------------------------
                if (products.Any(a => a.IsAdjustablePrice))
                {
                    // Take the Product with Code of  NP_NC_TopUp
                    var thisProduct = products.FirstOrDefault(a => a.IsAdjustablePrice);
                    products = ConvertToDynamicPackages(products, thisProduct);
                }


                foreach (var product in products)
                {
                    switch (applicationId)
                    {
                        // Based on mobile recharge fee settings, check if we need to add an extra fee.
                        // We only need to add a fee for international mobile recharge.
                        case MobileApplicationId.C3Pay:
                            if (product.CountryCode != ConstantParam.DefaultCountryCode)
                            {
                                switch (this._mobileRechargeServiceSettings.C3FeeMode)
                                {
                                    case MobileRechargeFeeMode.NoFee:
                                        break;
                                    case MobileRechargeFeeMode.OnlyOnSelectedCorporates:
                                        // If the current user's corporate ID is in the list, we need to add a fee.
                                        var selectedCorporatesWithFee = this._mobileRechargeServiceSettings.SelectedCorporatesWithFee.Split(',').ToList();
                                        if (string.IsNullOrEmpty(corporateId) == false && selectedCorporatesWithFee.Contains(corporateId))
                                        {
                                            if (product.CustomFee is null) { product.CustomFee = 0; }
                                            product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
                                        }
                                        break;
                                    case MobileRechargeFeeMode.All:
                                        if (product.CustomFee is null) { product.CustomFee = 0; }
                                        product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;

                                        if (product.MaxSendValue is null) { product.MaxSendValue = 0; }
                                        product.MaxSendValue += this._mobileRechargeServiceSettings.FeeAmount;
                                        break;
                                    default:
                                        break;
                                }
                            }
                            break;
                        case MobileApplicationId.MySalary:
                            if (product.CountryCode != ConstantParam.DefaultCountryCode)
                            {
                                switch (this._mobileRechargeServiceSettings.MySalaryFeeMode)
                                {
                                    case MobileRechargeFeeMode.NoFee:
                                        break;
                                    case MobileRechargeFeeMode.All:
                                        if (product.CustomFee is null) { product.CustomFee = 0; }
                                        product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;

                                        if (product.MaxSendValue is null) { product.MaxSendValue = 0; }
                                        product.MaxSendValue += this._mobileRechargeServiceSettings.FeeAmount;
                                        break;
                                    default:
                                        break;
                                }
                            }
                            break;
                        default: break;
                    }
                }

                return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(products.OrderByDescending(o => o.IsHighlighted));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="BeneficiaryId"></param>
        /// <param name="ProductType"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetProductsV2(Guid beneficiaryId, int? pageNumber, MobileApplicationId applicationId, string corporateId, string cardHolderId, Guid userId, bool rechargeForMyself = false)
        {
            int defaultPageSize = 10;
            int skipValue = 0;

            if (pageNumber != null && pageNumber > 0)
            {
                skipValue = (int)((pageNumber - 1) * defaultPageSize);
            }

            if (rechargeForMyself)
            {
                // Find current user.
                var currentUser = await this._unitOfWork.Users.FirstOrDefaultAsync(
                    u => u.Id == beneficiaryId
                    && u.IsDeleted == false
                    && u.IsBlocked == false);

                if (currentUser is null)
                {
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, ConstantParam.UserNotFound);
                }

                // Get the list of providers available for the user's phone number.
                var availableProviders = await this._dingService.GetAccountLookup(currentUser.PhoneNumber);

                // Checking provider status.
                if (availableProviders.IsSuccessful == false || (availableProviders.IsSuccessful && (availableProviders.Data == null || (availableProviders.Data != null && availableProviders.Data.Data.Count == 0))))
                {
                    _logger.LogWarning($"Error: No providers found");
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString());
                }

                // Check if provider country is UAE.          
                if (availableProviders.Data.CountryCode != ConstantParam.DefaultCountryCode)
                {
                    _logger.LogWarning($"Error: country for self beneficiary is not UAE.");
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }

                // Get provider codes.
                var providerCodes = availableProviders.Data.Data.Select(p => p.ProviderCode).ToList();

                if (providerCodes.Count == 0)
                {
                    _logger.LogWarning(ConstantParam.NotExistsProductsForBeneficiary);
                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, RechargeStatusValidationMessage.ProductsNotExistsForBeneficiary.ToString());
                }

                // Get a list of available products for the current provider.
                var products = await _unitOfWork.MobileRechargeProducts.GetProductsForProviderAsync(providerCodes, skipValue, defaultPageSize);

                return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(products);
            }
            else
            {
                //Get beneficiary  data
                var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId, y => y.BeneficiaryProviders);

                if (beneficiary == null)
                {
                    _logger.LogWarning($"Due to invalid beneficiay id, get product list failed");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                }

                //update provider details
                var dingLookupResponse = await this._dingService.GetAccountLookup(beneficiary.AccountNumber);

                //checking provider status
                if (!dingLookupResponse.IsSuccessful || (dingLookupResponse.IsSuccessful && (dingLookupResponse.Data == null || (dingLookupResponse.Data != null && dingLookupResponse.Data.Data.Count == 0))))
                {
                    _logger.LogWarning($"Due to invalid provider, beneficiary creation failed for {beneficiary.NickName}");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString());
                }

                //checking provider country is valid or not            
                if (dingLookupResponse.Data.CountryCode != beneficiary.CountryCode.ToUpper())
                {
                    _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {beneficiary.AccountNumber}");

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }

                var dingProviders = dingLookupResponse.Data.Data;
                var beneficiaryProviders = beneficiary.BeneficiaryProviders.Where(p => p.IsActive.HasValue && p.IsActive.Value).Select(p => p.ProviderCode);

                if (!beneficiary.IsProviderSelected) // Added this condition to prioritize the user operator selection over ding's operator response
                {
                    var missingProviders = dingProviders.Where(p =>
                        !callingCardOperators.Contains(p.ProviderCode)
                        && !beneficiaryProviders.Contains(p.ProviderCode)).ToList();

                    if (missingProviders.Count > 0)
                    {
                        missingProviders.ForEach(provider =>
                        {
                            beneficiary.BeneficiaryProviders.Add(new MobileRechargeBeneficiaryProvider
                            {
                                BeneficiaryId = beneficiary.Id,
                                ProviderCode = provider.ProviderCode,
                                IsActive = true,
                                UpdatedDate = DateTime.Now,
                            });
                        });

                        await this._unitOfWork.CommitAsync();
                    }
                }

                //get provider list
                var providers = beneficiary.BeneficiaryProviders
                    .Where(p => p.IsActive.HasValue && p.IsActive.Value && !p.IsDeleted)
                    .Select(p => p.ProviderCode).ToList();

                if (providers.Count == 0)
                {
                    _logger.LogWarning(string.Format(ConstantParam.NotExistsProductsForBeneficiary, beneficiary.NickName, beneficiary.AccountNumber));

                    return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(false, BaseEnums.RechargeStatusValidationMessage.ProductsNotExistsForBeneficiary.ToString());
                }

                // Get a list of available products for the current provider.
                var products = await _unitOfWork.MobileRechargeProducts.GetProductsForProviderAsync(providers, skipValue, defaultPageSize);

                // Execute rates experiment if applicable.
                var isRatesExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRatesExperiment);
                if (isRatesExperimentEnabled
                    && TryValidateUserForRatesExperiment(cardHolderId, out string jsonConfig))
                {
                    products = await ExecuteRatesExperiment(products, jsonConfig);
                }

                //----------------------------------------------------------------------------------------------
                //  Adjustable Price Package Check   
                //----------------------------------------------------------------------------------------------
                if (products.Any(a => a.IsAdjustablePrice))
                {
                    // Take the Product with Code of  NP_NC_TopUp
                    var thisProduct = products.FirstOrDefault(a => a.IsAdjustablePrice);
                    products = ConvertToDynamicPackages(products, thisProduct);
                }

                foreach (var product in products)
                {
                    if (product.CountryCode != ConstantParam.DefaultCountryCode)
                    {
                        if (product.CustomFee is null) { product.CustomFee = 0; }
                        product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;

                        if (product.MaxSendValue is null) { product.MaxSendValue = 0; }
                        product.MaxSendValue += this._mobileRechargeServiceSettings.FeeAmount;
                    }
                }

                //Execute Targeted Discount Offers if applicable
                var targetedDiscountOfferEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableTargetedDiscount);
                if (targetedDiscountOfferEnabled)
                {
                    var activeTargetedDiscountOffer = await _unitOfWork.MobileRechargeTargetedDiscounts.GetActiveOfferAsync(cardHolderId);
                    if (activeTargetedDiscountOffer != null)
                    {
                        if (activeTargetedDiscountOffer.OfferStatus == (int)MobileRecharge_TargetedDiscountOfferStatus.Initiated)
                        {
                            // Start the targeted discount offer
                            activeTargetedDiscountOffer.OfferStartDateTime = DateTime.Now;
                            activeTargetedDiscountOffer.OfferStatus = (int)MobileRecharge_TargetedDiscountOfferStatus.Started;
                        }

                        // To set the adjusted price in the product mapping - no tracking
                        products.ForEach(product => product.IsForTargetedDiscount = true);

                        // Save changes
                        await _unitOfWork.CommitAsync();
                    }
                }

                var bestValueExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableBestValue);
                if (bestValueExperimentEnabled)
                {
                    var eligibleForBestValueExperiment = TryValidateBestValueExperiment(cardHolderId);

                    if (eligibleForBestValueExperiment.Key)
                    {
                        foreach (var product in products)
                        {
                            // If the user is part of MRBestValueControl, disable the IsForBestValueExperiment flag
                            if (eligibleForBestValueExperiment.Value == ExperimentType.MRBestValueControl.ToString() && product.IsForBestValueExperiment == true)
                                product.IsForBestValueExperiment = false;

                            // Disable Most Popular/IsHighlighted tag if best value is enabled
                            if (product.IsHighlighted == true)
                                product.IsHighlighted = false;
                        }
                        //Make sure IsForBestValueExperiment comes first if provider IsHighlighted/Most Popular tag was disabled
                        products = products.OrderByDescending(o => o.IsForBestValueExperiment ?? false).ToList();
                    }
                    else
                    {
                        //Set BestValue to false if user is not part of experiment
                        DisableBestValueExperiment(products);
                    }
                }
                else
                {
                    //Set BestValue to false if feature is turned off
                    DisableBestValueExperiment(products);
                }

                return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(products);
            }
        }

        private KeyValuePair<bool, string> TryValidateBestValueExperiment(string cardHolderId)
        {
            // Define experiment types for Best Value and Control
            var validExperimentNames = new List<string>()
            {
                ExperimentType.MRBestValue.ToString(),
                ExperimentType.MRBestValueControl.ToString()
            };

            // Retrieve experiment based on cardholder ID and valid experiment IDs
            var experiment = _unitOfWork.ExperimentUsers.GetExperimentByNameAndCardHolder(cardHolderId, validExperimentNames);

            // Check if experiment exists and matches Best Value or Control type
            if (experiment?.Result?.Name is string experimentName && validExperimentNames.Contains(experimentName))
            {
                return new KeyValuePair<bool, string>(true, experimentName);
            }

            // Return false if no valid experiment is found
            return new KeyValuePair<bool, string>(false, string.Empty);
        }

        private void DisableBestValueExperiment(List<MobileRechargeProduct> products)
        {
            products.ForEach(product => product.IsForBestValueExperiment = false);
        }

        private bool TryValidateUserForRatesExperiment(string cardHolderId, out string jsonConfig)
        {
            // Get JSON config if the user is part of the experiment.
            jsonConfig = _unitOfWork.ExperimentUsers.GetExperimentConfigAsync(cardHolderId)?.Result;

            // If the user is not the part of the experiment.
            if (jsonConfig is null)
                return false;

            return true;
        }

        private Task<List<MobileRechargeProduct>> ExecuteRatesExperiment(List<MobileRechargeProduct> products, string jsonConfig)
        {
            /*
            // Check for Airtel - India products. 
            var hasAirtelProvider = products.Any(x => x.ProviderCode == MobileRecharge_Provider.AIIN.ToString());

            // Early exit
            if (!hasAirtelProvider)
            {
                return Task.FromResult(products);
            }
            */

            // Json config should always be available for users that are part of the experiment.
            if (jsonConfig is null)
            {
                _logger.LogError("RatesExperiment: JSON config was null or empty.");
                return Task.FromResult(products);
            }

            var experimentConfig = JsonConvert.DeserializeObject<RatesExperiment>(jsonConfig);

            foreach (var product in products)
            {
                var config = experimentConfig.ProductConfig.FirstOrDefault(x => x.ProductCode == product.Code);

                if (!(config is null))
                {
                    product.MinSendValue = config.Rate;
                    product.MaxSendValue = config.Rate;
                }
            }

            return Task.FromResult(products);
        }

        private Task<MobileRechargeTransaction> ExecuteRatesExperiment(MobileRechargeTransaction mobileRechargeTransaction, string jsonConfig, out bool isRatesExperimentInitiated)
        {
            /*
            // Check for Airtel - India product. 
            var hasAirtelProvider = mobileRechargeTransaction.Product.ProviderCode == MobileRecharge_Provider.AIIN.ToString();

            // Early exit
            if (!hasAirtelProvider)
            {
                isRatesExperimentInitiated = false;
                return Task.FromResult(mobileRechargeTransaction);
            }
            */

            // Json config should always be available for users that are part of the experiment.
            if (jsonConfig is null)
            {
                isRatesExperimentInitiated = false;
                _logger.LogError("RatesExperiment: JSON config was null or empty.");
                return Task.FromResult(mobileRechargeTransaction);
            }

            var experimentConfig = JsonConvert.DeserializeObject<RatesExperiment>(jsonConfig);

            var config = experimentConfig.ProductConfig.FirstOrDefault(x => x.ProductCode == mobileRechargeTransaction.Product.Code);

            if (config is null)
            {
                isRatesExperimentInitiated = false;
                _logger.LogError($"RatesExperiment: Product with the ID {mobileRechargeTransaction.Product.Code} could not be found.");
                return Task.FromResult(mobileRechargeTransaction);
            }

            // Set total Amount 
            mobileRechargeTransaction.SendAmount = config.Rate;
            mobileRechargeTransaction.TotalAmount = (decimal)mobileRechargeTransaction.SendAmount + (decimal)mobileRechargeTransaction.Fee;
            isRatesExperimentInitiated = true;

            return Task.FromResult(mobileRechargeTransaction);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<ProductEstimatePriceDingModel>> GetProductEstimateRate(string productCode, string corporateId, MobileApplicationId applicationId)
        {
            var passedProductCode = productCode;
            // Special Check for NCell - Nepal  
            var resp_ProductCode = ExtractProductCodeForAdjustablePriceProduct(productCode);
            productCode = resp_ProductCode;

            // Get product details.
            var product = await _unitOfWork.MobileRechargeProducts.FirstOrDefaultAsync(x => x.Code == productCode && x.IsActive.HasValue && x.IsActive.Value);
            if (product is null)
            {
                _logger.LogWarning($"ERROR:DB-NotFound. Table: 'MobileRechargeProducts'. Key: Code = '{productCode}'");
                return new ServiceResponse<ProductEstimatePriceDingModel>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());
            }

            // Get estimates for this prodict.
            decimal thisProductMaxSendValue = product.MaxSendValue.GetValueOrDefault();
            string thisProductMaxSendCurrencyIso = product.MaxSendCurrencyIso;

            // To Support Dynamic Packages 
            if (product.IsAdjustablePrice)
            {
                IList<MobileRechargeProduct> newProducts = null;
                newProducts = ConvertToDynamicPackages(newProducts, product);

                var selectedProduct = newProducts.FirstOrDefault(a => a.Code == passedProductCode);
                if (selectedProduct != null)
                {
                    thisProductMaxSendValue = selectedProduct.MaxSendValue.GetValueOrDefault();
                    thisProductMaxSendCurrencyIso = selectedProduct.MaxSendCurrencyIso;
                }
            }

            var tryGetEstimatePrice = await _dingService.GetEstimatePriceForProduct(productCode, thisProductMaxSendValue, thisProductMaxSendCurrencyIso);
            if (tryGetEstimatePrice.IsSuccessful == false)
            {
                _logger.LogWarning($"ERROR:Provider-Api. ErrorMessage: {tryGetEstimatePrice.ErrorMessage}");
                return new ServiceResponse<ProductEstimatePriceDingModel>(false, RechargeStatusValidationMessage.DingConnectionIssue.ToString());
            }

            var estimatePrice = tryGetEstimatePrice.Data.Data;
            if (tryGetEstimatePrice.IsSuccessful && estimatePrice[0].PricePayment == null)
            {
                _logger.LogWarning($"ERROR:Provider-Api. Key: Code = '{productCode}'. ErrorMessage: Empty response.");
                return new ServiceResponse<ProductEstimatePriceDingModel>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());
            }

            if (product.CustomFee != null && product.CustomFee > 0)
            {
                estimatePrice[0].PricePayment.CustomerFee = estimatePrice[0].PricePayment.CustomerFee + product.CustomFee.GetValueOrDefault();
                estimatePrice[0].PricePayment.SendValue = estimatePrice[0].PricePayment.SendValue + product.CustomFee.GetValueOrDefault();
            }

            switch (applicationId)
            {
                // Based on mobile recharge fee settings, check if we need to add an extra fee.
                // We only need to add a fee for international mobile recharge.
                case MobileApplicationId.C3Pay:
                    if (product.CountryCode != ConstantParam.DefaultCountryCode)
                    {
                        switch (this._mobileRechargeServiceSettings.C3FeeMode)
                        {
                            case MobileRechargeFeeMode.NoFee:
                                break;
                            case MobileRechargeFeeMode.OnlyOnSelectedCorporates:
                                // If the current user's corporate ID is in the list, we need to add a fee.
                                var selectedCorporatesWithFee = this._mobileRechargeServiceSettings.SelectedCorporatesWithFee.Split(',').ToList();
                                if (string.IsNullOrEmpty(corporateId) == false && selectedCorporatesWithFee.Contains(corporateId))
                                {
                                    estimatePrice[0].PricePayment.CustomerFee += this._mobileRechargeServiceSettings.FeeAmount;
                                    estimatePrice[0].PricePayment.SendValue += this._mobileRechargeServiceSettings.FeeAmount;
                                }
                                break;
                            case MobileRechargeFeeMode.All:
                                estimatePrice[0].PricePayment.CustomerFee += this._mobileRechargeServiceSettings.FeeAmount;
                                estimatePrice[0].PricePayment.SendValue += this._mobileRechargeServiceSettings.FeeAmount;
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                case MobileApplicationId.MySalary:
                    if (product.CountryCode != ConstantParam.DefaultCountryCode)
                    {
                        switch (this._mobileRechargeServiceSettings.MySalaryFeeMode)
                        {
                            case MobileRechargeFeeMode.NoFee:
                                break;
                            case MobileRechargeFeeMode.All:
                                estimatePrice[0].PricePayment.CustomerFee += this._mobileRechargeServiceSettings.FeeAmount;
                                estimatePrice[0].PricePayment.SendValue += this._mobileRechargeServiceSettings.FeeAmount;
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                default: break;
            }

            if (estimatePrice[0] != null && product.IsAdjustablePrice)
            {
                estimatePrice[0].ProductCode = passedProductCode;
            }
            return new ServiceResponse<ProductEstimatePriceDingModel>(estimatePrice[0]);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="Operator"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<MobileRechargeProduct>>> GetCallingCardProducts(string callingCardOperator, BaseEnums.MobileApplicationId applicationId, string corporateId)
        {
            var operatorCode = string.Empty;

            if (!string.IsNullOrEmpty(callingCardOperator))
            {
                if (callingCardOperator == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.FUAE))
                {
                    operatorCode = BaseEnums.CallingCardOperator.FUAE.ToString();
                }
                else if (callingCardOperator == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CallingCardOperator.HAAE))
                {
                    operatorCode = BaseEnums.CallingCardOperator.HAAE.ToString();
                }
            }


            var products = await _unitOfWork.MobileRechargeProducts.FindAsync(param => param.Benefits == BaseEnums.MobileRechargeType.CALLINGCARDS.ToString()
                                                                            && (operatorCode == string.Empty || param.ProviderCode == operatorCode) && param.CountryCode == BaseEnums.CountryEnable.AE.ToString()
                                                                            && param.RedemptionMechanism == Enums.DingProductRedemption.ReadReceipt.ToString()
                                                                            && (param.IsActive.HasValue && param.IsActive.Value)
                                                                            && (param.Provider.IsProcessingTransfers.HasValue && param.Provider.IsProcessingTransfers.Value)
                                                                            && (param.Provider.IsActive.HasValue && param.Provider.IsActive.Value),
                                                                            orderby => orderby.MinSendValue, false,
                                                                            null, null, false,
                                                                            x => x.Provider,
                                                                            x => x.SupportedCountry);
            foreach (var product in products)
            {
                switch (applicationId)
                {
                    // Based on mobile recharge fee settings, check if we need to add an extra fee.
                    // We only need to add a fee for international mobile recharge.
                    case MobileApplicationId.C3Pay:
                        if (product.CountryCode != ConstantParam.DefaultCountryCode)
                        {
                            switch (this._mobileRechargeServiceSettings.C3FeeMode)
                            {
                                case MobileRechargeFeeMode.NoFee:
                                    break;
                                case MobileRechargeFeeMode.OnlyOnSelectedCorporates:
                                    // If the current user's corporate ID is in the list, we need to add a fee.
                                    var selectedCorporatesWithFee = this._mobileRechargeServiceSettings.SelectedCorporatesWithFee.Split(',').ToList();
                                    if (string.IsNullOrEmpty(corporateId) == false && selectedCorporatesWithFee.Contains(corporateId))
                                    {
                                        if (product.CustomFee is null) { product.CustomFee = 0; }
                                        product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
                                    }
                                    break;
                                case MobileRechargeFeeMode.All:
                                    if (product.CustomFee is null) { product.CustomFee = 0; }
                                    product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    case MobileApplicationId.MySalary:
                        if (product.CountryCode != ConstantParam.DefaultCountryCode)
                        {
                            switch (this._mobileRechargeServiceSettings.MySalaryFeeMode)
                            {
                                case MobileRechargeFeeMode.NoFee:
                                    break;
                                case MobileRechargeFeeMode.All:
                                    if (product.CustomFee is null) { product.CustomFee = 0; }
                                    product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
                                    break;
                                default:
                                    break;
                            }
                        }
                        break;
                    default: break;
                }
            }

            return new ServiceResponse<IEnumerable<MobileRechargeProduct>>(products);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="countryCode"></param>
        /// <param name="phoneNumber"></param>
        /// <param name="nickName"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<bool>> GetRechargeBeneficiaryDetailsEligibility(Guid userId, string countryCode, string phoneNumber, string nickName)
        {
            //check nick name length
            if (nickName.Length > _mobileRechargeServiceSettings.NickNameLength)
            {
                _logger.LogWarning($"Due to nickname length exceeded, beneficiary creation failed for {nickName}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.ExceedNicknameLength.ToString());
            }

            if ((phoneNumber.Substring(0, 1).ToString() != "+" && phoneNumber.Substring(0, 2).ToString() != "00") || !phoneNumber.All(char.IsDigit))
            {
                _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {phoneNumber}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            }

            //check User exists or not
            var currentUser = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == userId);

            if (currentUser is null)
            {
                _logger.LogWarning($"Due to invalid user id, beneficiary creation failed for {nickName}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.UserNotExists.ToString());
            }

            if (currentUser.PhoneNumber == phoneNumber)
            {
                _logger.LogWarning($"Due to beneficiary exists, beneficiary creation failed for {nickName}");
                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            //set default format for phone number
            phoneNumber = phoneNumber.ToShortPhoneNumber();

            //Checking beneficiary if exists
            var beneficiaryExists = await _unitOfWork.MobileRechargeBeneficiaries.Any(param => param.UserId == userId && (param.AccountNumber == phoneNumber)
                                                        && !param.IsDeleted);

            if (beneficiaryExists || nickName.ToLower() == "myself")
            {
                _logger.LogWarning($"Due to beneficiary exists, beneficiary creation failed for {nickName}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            //Read other bank country code
            if (countryCode != BaseEnums.CountryEnable.AE.ToString())
            {
                var countryExists = await _unitOfWork.Countries.Any(z => z.Code == countryCode && z.MobileRechargeEnabled && z.MobileRechargeEnabledForPartner);

                if (!countryExists)
                {
                    _logger.LogWarning($"Due to invalid country code, failed to check eligibility with country {countryCode}");

                    return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }
            }

            //To check mobile validity
            if (!this.PhoneNumberIsEligible(countryCode, phoneNumber.ToZeroPrefixedPhoneNumber()))
            {
                _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {phoneNumber}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            }

            //update provider to phone number
            var dingLookupResponse = await this._dingService.GetAccountLookup(phoneNumber);

            if (!dingLookupResponse.IsSuccessful || (dingLookupResponse.IsSuccessful && (dingLookupResponse.Data == null || (dingLookupResponse.Data != null && dingLookupResponse.Data.Data.Count == 0))))
            {
                _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {phoneNumber}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            }

            //Adding new beneficiary provider 
            if (dingLookupResponse.Data.CountryCode != countryCode)
            {
                _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {phoneNumber}");

                return new ServiceResponse<bool>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            }

            return new ServiceResponse<bool>(true);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="newBeneficiary"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MobileRechargeBeneficiary>> AddMobileRechargeBeneficiary(
            MobileRechargeBeneficiary newBeneficiary, string userSelectedProviderCode)
        {
            //check nick name length
            if (newBeneficiary.NickName.Length > _mobileRechargeServiceSettings.NickNameLength)
            {
                _logger.LogWarning($"Due to nickname length exceeded, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.ExceedNicknameLength.ToString());
            }

            //check valid mobile number
            if ((newBeneficiary.AccountNumber.Substring(0, 1).ToString() != "+" && newBeneficiary.AccountNumber.Substring(0, 2).ToString() != "00") || !newBeneficiary.AccountNumber.All(char.IsDigit))
            {
                _logger.LogWarning($"Due to invalid phone number, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            }

            //check User exists or not
            var currentUser = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == newBeneficiary.UserId);

            if (currentUser is null)
            {
                _logger.LogWarning($"Due to invalid user id, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.UserNotExists.ToString());
            }

            if (currentUser.PhoneNumber == newBeneficiary.AccountNumber)
            {
                _logger.LogWarning($"Due to beneficiary exists, beneficiary creation failed for {newBeneficiary.NickName}");
                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            //set default format for country code
            newBeneficiary.AccountNumber = newBeneficiary.AccountNumber.ToShortPhoneNumber();

            //Apr 12- 2024 - user story #16580 
            var nickName = newBeneficiary.NickName.Trim();
            var regex = new Regex($@"^{Regex.Escape(nickName)}(\(\d+\)){Regex.Escape(newBeneficiary.NickName.Substring(nickName.Length))}?$");

            var matchedBeneficiaries = (await _unitOfWork.MobileRechargeBeneficiaries
                .FindAsync(b => b.UserId == currentUser.Id && !b.IsDeleted))
                .Where(b => regex.IsMatch(b.NickName))
                .ToList();

            // If match found, append the next number to the newBeneficiary.Nickname and update it
            if (matchedBeneficiaries.Any())
            {
                newBeneficiary.NickName += $"({matchedBeneficiaries.Count})";
            }
            //Checking beneficiary if exists
            var existingBeneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(param => param.UserId == newBeneficiary.UserId && param.AccountNumber == newBeneficiary.AccountNumber && !param.IsDeleted);

            if (existingBeneficiary != null && newBeneficiary.RechargeType != BaseEnums.MobileRechargeType.MYNUMBER)
            {
                _logger.LogWarning($"Due to beneficiary exists, beneficiary creation failed for {newBeneficiary.NickName}");
                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryAlreadyExists.ToString());
            }

            //Read other bank country code
            if (newBeneficiary.CountryCode.ToUpper() != CountryEnable.AE.ToString())
            {
                var countryExists = await _unitOfWork.Countries.Any(z => z.Code == newBeneficiary.CountryCode && z.MobileRechargeEnabled && z.MobileRechargeEnabledForPartner);

                if (!countryExists)
                {
                    _logger.LogWarning($"Due to invalid country code failed for beneficiary creation with the country {newBeneficiary.CountryCode} and name {newBeneficiary.NickName}");

                    return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
                }
            }

            if (!this.PhoneNumberIsEligible(newBeneficiary.CountryCode, newBeneficiary.AccountNumber.ToZeroPrefixedPhoneNumber()))
            {
                _logger.LogWarning($"Due to invalid phone number, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.InvalidPhoneNumber.ToString());
            }

            //country validation check            
            if ((newBeneficiary.RechargeType == BaseEnums.MobileRechargeType.LOCAL || newBeneficiary.RechargeType == BaseEnums.MobileRechargeType.MYNUMBER) && newBeneficiary.CountryCode.ToUpper() != BaseEnums.CountryEnable.AE.ToString().ToUpper())
            {
                _logger.LogWarning($"Due to invalid country, beneficiary creation failed for {newBeneficiary.NickName} ");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            }

            else if (newBeneficiary.RechargeType == BaseEnums.MobileRechargeType.INTERNATIONAL && newBeneficiary.CountryCode.ToUpper() == BaseEnums.CountryEnable.AE.ToString().ToUpper())
            {
                _logger.LogWarning($"Due to invalid country, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            }

            //Check Beneficiary account is in blocked list.
            var beneficiaryIsBlackListed = await _unitOfWork.BlackListedEntities.Any(z => z.EntityType == BaseEnums.BlackListedEntityType.MobileRechargePhoneNumber && z.CountryCode == newBeneficiary.CountryCode && z.Identifier == newBeneficiary.AccountNumber && z.IsActive);

            if (beneficiaryIsBlackListed)
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == newBeneficiary.UserId);

                user.Block(UserBlockType.MobileRechargeBeneficiaryBlackListed);

                await _unitOfWork.CommitAsync();

                //Block the user from Edenred
                this._logger.LogWarning(ConstantParam.BlockuserBeneficiartyCreatedProcess, user.Id, user.PhoneNumber);

                var blockUserResult = await _identityService.LockUserAccountAsync(user.PhoneNumber);

                if (!blockUserResult.IsSuccessful)
                {
                    this._logger.LogWarning(ConstantParam.BlockuserBeneficiartyCreatedFailed, user.Id, blockUserResult.ErrorMessage);
                }


                _logger.LogWarning($"Due to block listed beneficiary, beneficiary creation failed for {newBeneficiary.NickName} and User Id is : {newBeneficiary.UserId}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.UserBlocked.ToString());
            }

            //update provider to phone number
            var dingLookUpResponse = await this._dingService.GetAccountLookup(newBeneficiary.AccountNumber);

            //update status
            if (!dingLookUpResponse.IsSuccessful || (dingLookUpResponse.IsSuccessful && (dingLookUpResponse.Data == null || (dingLookUpResponse.Data != null && dingLookUpResponse.Data.Data.Count == 0))))
            {
                _logger.LogWarning($"Due to invalid provider, beneficiary creation failed for {newBeneficiary.NickName}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString());
            }

            //Adding new beneficiary provider             
            if (dingLookUpResponse.Data.CountryCode != newBeneficiary.CountryCode.ToUpper())
            {
                _logger.LogWarning($"Due to invalid phone number for beneficiary phone eligibility check {newBeneficiary.AccountNumber}");

                return new ServiceResponse<MobileRechargeBeneficiary>(false, BaseEnums.RechargeStatusValidationMessage.CountryNotSupported.ToString());
            }

            //If mobile user already exists, delete the beneficiary and add as a new beneficiary
            var userBeneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.UserId == newBeneficiary.UserId && x.RechargeType == BaseEnums.MobileRechargeType.MYNUMBER && !x.IsDeleted);

            if (newBeneficiary.RechargeType == BaseEnums.MobileRechargeType.MYNUMBER && userBeneficiary != null)
            {
                await this.DeleteMobileRechargeBeneficiary(userBeneficiary.Id, null);
            }

            //Adding new beneficiary provider
            newBeneficiary.BeneficiaryProviders = new List<MobileRechargeBeneficiaryProvider>();

            if (!string.IsNullOrWhiteSpace(userSelectedProviderCode))
            {
                if (!callingCardOperators.Contains(userSelectedProviderCode))
                {
                    newBeneficiary.IsProviderSelected = true;
                    newBeneficiary.BeneficiaryProviders.Add(new MobileRechargeBeneficiaryProvider
                    {
                        BeneficiaryId = newBeneficiary.Id,
                        CreatedDate = DateTime.Now,
                        ProviderCode = userSelectedProviderCode,
                        IsActive = true,
                        UpdatedDate = DateTime.Now,
                    });
                }
            }
            else
            {
                foreach (var item in dingLookUpResponse.Data.Data)
                {
                    if (!callingCardOperators.Contains(item.ProviderCode))
                    {
                        newBeneficiary.BeneficiaryProviders.Add(new MobileRechargeBeneficiaryProvider
                        {
                            BeneficiaryId = newBeneficiary.Id,
                            CreatedDate = DateTime.Now,
                            ProviderCode = item.ProviderCode,
                            IsActive = true,
                            UpdatedDate = DateTime.Now,
                        });
                    }
                }
            }

            newBeneficiary.Status = BaseEnums.Status.APPROVED;

            newBeneficiary.UpdatedDate = DateTime.Now;

            await _unitOfWork.MobileRechargeBeneficiaries.AddAsync(newBeneficiary);

            await _unitOfWork.CommitAsync();

            //get beneficiary details
            var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FindWithExternalProviderAsync(param => param.Id == newBeneficiary.Id,
                                                                                       null,
                                                                                       null,
                                                                                       null,
                                                                                       null);

            return new ServiceResponse<MobileRechargeBeneficiary>(beneficiary.FirstOrDefault());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="RechargeType"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<List<MobileRechargeBeneficiaryDetails>>> GetMobileRechargeBeneficiaries(Guid userId, string rechargeType)
        {
            MobileRechargeType? mappedRechargeType = null;

            if (!string.IsNullOrEmpty(rechargeType))
            {
                if (!Enum.IsDefined(typeof(BaseEnums.MobileRechargeType), rechargeType.ToUpper()))
                {
                    return new ServiceResponse<List<MobileRechargeBeneficiaryDetails>>(false, ConstantParam.InvalidRechargeType);
                }

                mappedRechargeType = (MobileRechargeType)Enum.Parse(typeof(BaseEnums.MobileRechargeType), rechargeType.ToUpper());
            }

            //get the list of mobiel recharge beneficiary
            var result = await _unitOfWork.MobileRechargeBeneficiaries
                    .GetUserBeneficiaries(userId, mappedRechargeType);


            // Add self beneficiary to the list
            // only if they didn't add themself already.
            var currentUser = await this._unitOfWork.Users.FirstOrDefaultAsync(
            u => u.IsDeleted == false
            && u.Id == userId);

            if (currentUser != null)
            {
                if (result.Any(b => b.PhoneNumber == currentUser.PhoneNumber.ToShortPhoneNumber()) == false)
                {
                    // Get the list of providers available for the user's phone number.
                    var availableProviders = await this._dingService.GetAccountLookup(currentUser.PhoneNumber);

                    // Checking provider status.
                    if (availableProviders.IsSuccessful == false || (availableProviders.IsSuccessful && (availableProviders.Data == null || (availableProviders.Data != null && availableProviders.Data.Data.Count == 0))))
                    {
                        _logger.LogWarning($"Error: No providers found");
                        return new ServiceResponse<List<MobileRechargeBeneficiaryDetails>>(result);
                    }

                    // Check if provider country is UAE.          
                    if (availableProviders.Data.CountryCode != ConstantParam.DefaultCountryCode)
                    {
                        _logger.LogWarning($"Error: country for self beneficiary is not UAE.");
                        return new ServiceResponse<List<MobileRechargeBeneficiaryDetails>>(result);
                    }

                    // Get provider codes.
                    var providerCodes = availableProviders.Data.Data.Select(p => p.ProviderCode).ToList();

                    if (providerCodes.Count == 0)
                    {
                        _logger.LogWarning(ConstantParam.NotExistsProductsForBeneficiary);
                        return new ServiceResponse<List<MobileRechargeBeneficiaryDetails>>(result);
                    }
                    var provider = await this._unitOfWork.MobileRechargeProviders.FirstOrDefaultAsync(p => providerCodes.Contains(p.Code));

                    MobileRechargeBeneficiaryDetails self = new MobileRechargeBeneficiaryDetails()
                    {
                        BeneficiaryId = userId,
                        CountryCode = ConstantParam.DefaultCountryCode,
                        CountryName = ConstantParam.DefaultCountryCode,
                        FullName = "Myself",
                        PhoneNumber = currentUser.PhoneNumber,
                        RechargeType = MobileRechargeType.LOCAL,
                        Status = Status.APPROVED,
                        ProviderLogo = provider.LogoUrl,
                        ProviderName = provider.Name
                    };
                    result.Insert(0, self);
                }
            }

            return new ServiceResponse<List<MobileRechargeBeneficiaryDetails>>(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="countryCode"></param>
        /// <param name="phoneNumber"></param>
        /// <returns></returns>
        private bool PhoneNumberIsEligible(string countryCode, string phoneNumber)
        {
            var phoneNumberLength = phoneNumber.Length;
            var operatorCode = phoneNumber.Substring(5, 2);
            var acceptableOperatorCodes = new string[] { "50", "54", "56", "52", "55", "58" };

            if (countryCode == BaseEnums.CountryEnable.AE.ToString() && (phoneNumberLength != 14 || !acceptableOperatorCodes.Contains(operatorCode)))
            {
                return false;
            }
            else if ((countryCode == BaseEnums.CountryEnable.PK.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.PK).Length, 1) != "3") ||
                    (countryCode == BaseEnums.CountryEnable.IN.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.IN).Length, 1) != "7"
                                                                                         && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.IN).Length, 1) != "8"
                                                                                         && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.IN).Length, 1) != "6"
                                                                                         && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.IN).Length, 1) != "9") ||
                    (countryCode == BaseEnums.CountryEnable.BD.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.BD).Length, 1) != "1") ||
                    (countryCode == BaseEnums.CountryEnable.LK.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.LK).Length, 1) != "7") ||
                    (countryCode == BaseEnums.CountryEnable.NP.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.NP).Length, 2) != "97"
                                                                                        && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.NP).Length, 2) != "98") ||
                    (countryCode == BaseEnums.CountryEnable.PH.ToString() && phoneNumber.Substring(EnumUtility.GetDescriptionFromEnumValue(BaseEnums.CountryEnable.PH).Length, 1) != "9"))

            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> UpdateProvidersForMobileRechargeBeneficiary(Guid beneficiaryId)
        {
            //Read benificairy            
            var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId, x => x.BeneficiaryProviders);

            if (beneficiary == null)
            {
                _logger.LogWarning($"Due to invlaid beneficiary Id, provider updates failed for {beneficiaryId.ToString()}");

                return new ServiceResponse(false, $"{ConstantParam.InvalidMobileRechargeBeneficiary}");
            }

            if (string.IsNullOrEmpty(beneficiary.AccountNumber))
            {
                _logger.LogWarning($"Due to invalid phone number,  provider updates  failed for {beneficiary.NickName}");

                return new ServiceResponse(false, string.Format(ConstantParam.InvalidPhoneNumberForCountry, beneficiary.AccountNumber, beneficiary.CountryCode));
            }

            var dingLookupResponse = await this._dingService.GetAccountLookup(beneficiary.AccountNumber);

            //update status
            if (!dingLookupResponse.IsSuccessful && dingLookupResponse.Data == null)
            {
                beneficiary.Status = BaseEnums.Status.FAILED;
                beneficiary.Remarks = $"Providers not found for this phone number {beneficiary.AccountNumber}";

                _logger.LogWarning($"Due to provider not found, Provider list updates failed for the beneficiary {beneficiary.NickName} ");
            }

            //get the list of provider exists for the user
            var providers = beneficiary.BeneficiaryProviders.Select(z => z.ProviderCode).Distinct().ToList();

            foreach (var item in dingLookupResponse.Data.Data)
            {
                if (!providers.Contains(item.ProviderCode) &&
                    !callingCardOperators.Contains(item.ProviderCode))
                {
                    beneficiary.BeneficiaryProviders.Add(new MobileRechargeBeneficiaryProvider
                    {
                        BeneficiaryId = beneficiary.Id,
                        CreatedDate = DateTime.Now,
                        ProviderCode = item.ProviderCode,
                        IsActive = true,
                        UpdatedDate = DateTime.Now,
                    });
                }
            }

            beneficiary.Status = BaseEnums.Status.APPROVED;

            beneficiary.UpdatedDate = DateTime.Now;

            await _unitOfWork.CommitAsync();

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="beneficiaryId"></param>
        /// <param name="portalUserId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse> DeleteMobileRechargeBeneficiary(Guid beneficiaryId, Guid? portalUserId = null, string portalEmailId = null)
        {
            //Read benificairy         
            var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiaryId);

            if (beneficiary == null)
            {
                _logger.LogWarning($"Due to invalid beneficiary id, beneficiary deletion failed for beneficiary id {beneficiaryId.ToString()}");

                return new ServiceResponse(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
            }

            var beneficiaryHasTransactions = await _unitOfWork.MobileRechargeTransactions.Any(x => x.BeneficiaryId == beneficiaryId && x.Status == BaseEnums.Status.PENDING);

            if (beneficiaryHasTransactions)
            {
                _logger.LogWarning($"Due to pending transaction, beneficiary deletion failed for {beneficiary.NickName}");

                return new ServiceResponse(false, BaseEnums.RechargeStatusValidationMessage.TransactionIsInProgress.ToString());
            }

            beneficiary.MarkAsDeleted();

            beneficiary.Status = BaseEnums.Status.DELETED;

            await _unitOfWork.CommitAsync();

            if (portalUserId != null)
            {
                var user = await this._unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == beneficiary.UserId);

                if (user != null)
                {
                    var saveAuditTrail = await this._auditTrailService.AddAuditTrail(portalUserId, portalEmailId, user.Id, ConstantParam.AuditTrailDeleteBeneficiary, user.PhoneNumber, null);
                }
            }

            return new ServiceResponse();
        }

        #endregion

        #region Transaction APIs

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mobileRechargeTransaction"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<CallingCardResponseDingModel>> SendTransfer(MobileRechargeTransaction mobileRechargeTransaction,
            MobileApplicationId mobileApplicationId, string phoneNumber = null, string corporateId = null)
        {
            MobileRechargeBeneficiary beneficiary = null;
            bool rechargeForMyself = false;

            // Special Check for NCell - Nepal   
            var passedProductCode = mobileRechargeTransaction.ProductCode;
            var resp_ProductCode = ExtractProductCodeForAdjustablePriceProduct(mobileRechargeTransaction.ProductCode);
            mobileRechargeTransaction.ProductCode = resp_ProductCode;

            // Check if user is trying to recharge their account.
            if (mobileRechargeTransaction.BeneficiaryId.HasValue && mobileRechargeTransaction.BeneficiaryId.Value == mobileRechargeTransaction.UserId)
            {
                mobileRechargeTransaction.RechargeType = MobileRechargeType.LOCAL;
                mobileRechargeTransaction.AccountNumber = phoneNumber;
                mobileRechargeTransaction.BeneficiaryId = null;
                rechargeForMyself = true;
            }

            else if (mobileRechargeTransaction.BeneficiaryId.HasValue)
            {
                //Read benificairy
                beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == mobileRechargeTransaction.BeneficiaryId.Value && x.Status == BaseEnums.Status.APPROVED && !x.IsDeleted);

                if (beneficiary == null)
                {
                    _logger.LogWarning($"Due to invalid beneficiary, mobile recharge transfer failed for {mobileRechargeTransaction.BeneficiaryId.Value}");

                    return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                }

                mobileRechargeTransaction.RechargeType = beneficiary.RechargeType;

                mobileRechargeTransaction.AccountNumber = beneficiary.AccountNumber;
            }
            else
            {
                mobileRechargeTransaction.RechargeType = BaseEnums.MobileRechargeType.CALLINGCARDS;
            }

            //Checking product information
            var product = await _unitOfWork.MobileRechargeProducts.FirstOrDefaultAsync(x => x.Code == mobileRechargeTransaction.ProductCode && (x.IsActive.HasValue && x.IsActive.Value));

            if (product == null)
            {
                _logger.LogWarning($"Due to invalid product code, mobile recharge transfer failed for the product : {mobileRechargeTransaction.ProductCode}");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.ProductNotExists.ToString());
            }
            else if (product.Benefits == BaseEnums.MobileRechargeType.CALLINGCARDS.ToString() && mobileRechargeTransaction.BeneficiaryId.HasValue)
            {
                _logger.LogWarning($"Due to invalid beneficiary, mobile recharge transfer failed for {mobileRechargeTransaction.BeneficiaryId.Value}");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidBeneficiaryForCallingCards.ToString());
            }

            //check User exists or not
            var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == mobileRechargeTransaction.UserId, z => z.CardHolder);

            if (user == null)
            {
                _logger.LogWarning($"Due to invalid user id, mobile transaction failed");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.UserNotExists.ToString());
            }

            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;

            //Checking user information
            if (string.IsNullOrEmpty(cardNumber))
            {
                _logger.LogWarning($"Due to invalid card number, mobile recharge transfer failed for the user {user.CardHolder.FirstName} {user.CardHolder.LastName}");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidCardNumber.ToString());
            }

            else if (string.IsNullOrEmpty(cardSerialNumber))
            {
                _logger.LogWarning($"Due to invalid card serial number, mobile recharge transfer failed for the user {user.CardHolder.FirstName} {user.CardHolder.LastName} ");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidCardSerialNumber.ToString());
            }

            //checking transaction limit
            var today = DateTime.Today;
            var beginningOfTheMonth = new DateTime(today.Year, today.Month, 1);
            var beginningOfNextMonth = beginningOfTheMonth.AddMonths(1);

            var rechargesAmountSum = await this._unitOfWork.MobileRechargeTransactions
                .DecimalSumAsync(record => record.UserId == mobileRechargeTransaction.UserId
                && record.CreatedDate >= beginningOfTheMonth
                && record.CreatedDate < beginningOfNextMonth
                && record.Status != BaseEnums.Status.FAILED, recharge => recharge.SendAmount);

            var totalAmountSum = rechargesAmountSum + mobileRechargeTransaction.SendAmount;

            var monthlyLimit = user.IsVerified ? this._mobileRechargeServiceSettings.MonthlyAmountLimitVerified : this._mobileRechargeServiceSettings.MonthlyAmountLimitNotVerified;

            if (totalAmountSum > monthlyLimit)
            {
                this._logger.LogWarning("Max amount reached for non verified user, previous recharge amount: {0} AED, total recharge amount: {1} AED, monthly limit {2} AED", rechargesAmountSum, totalAmountSum, monthlyLimit);

                return new ServiceResponse<CallingCardResponseDingModel>(false, user.IsVerified ? BaseEnums.RechargeStatusValidationMessage.RechargeAmountMonthlyLimitReached.ToString() : BaseEnums.RechargeStatusValidationMessage.RechargeAmountLimitReachedWithoutEmiratesId.ToString());
            }

            await _unitOfWork.MobileRechargeTransactions.AddAsync(mobileRechargeTransaction);
            await _unitOfWork.CommitAsync();

            var rechargeResponse = await this.AddExternalTransferTransaction(mobileRechargeTransaction, user, beneficiary, mobileApplicationId, rechargeForMyself);

            if (!rechargeResponse.IsSuccessful)
            {
                return new ServiceResponse<CallingCardResponseDingModel>(false, rechargeResponse.ErrorMessage);
            }

            //If success response.
            return new ServiceResponse<CallingCardResponseDingModel>(rechargeResponse.Data);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="rechargeType"></param>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<MobileRechargeDetails>>> GetRecentMobileRechargeTransactions(Guid userId, MobileRechargeType mobileRechargeType, int? count)
        {
            if (count.HasValue == false)
            {
                count = 3;
            }

            var recentTransactions = await _unitOfWork.MobileRechargeTransactions.GetRecentMobileRechargeTransactions(userId, mobileRechargeType, count);
            return new ServiceResponse<IEnumerable<MobileRechargeDetails>>(recentTransactions);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="mobileRechargeTransaction"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<CallingCardResponseDingModel>> RepeatTransfer(Guid transactionId, MobileApplicationId mobileApplicationId, string phoneNumber = null)
        {
            var mobileRechargeTransaction = await this._unitOfWork.MobileRechargeTransactions.FirstOrDefaultAsync(
                t => t.Id == transactionId);

            var newTransaction = new MobileRechargeTransaction()
            {
                ProductCode = mobileRechargeTransaction.ProductCode,
                SendAmount = mobileRechargeTransaction.SendAmount,
                TotalAmount = mobileRechargeTransaction.TotalAmount,
                SendCurrency = mobileRechargeTransaction.SendCurrency,
                Fee = mobileRechargeTransaction.Fee,
                ReceiveAmount = mobileRechargeTransaction.ReceiveAmount,
                ReceiveCurrency = mobileRechargeTransaction.ReceiveCurrency,
                UserId = mobileRechargeTransaction.UserId,
                BeneficiaryId = mobileRechargeTransaction.BeneficiaryId,
                IsRepeat = true
            };

            return await this.SendTransfer(newTransaction, mobileApplicationId, phoneNumber);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mobileRechargeId"></param>
        /// <returns></returns>
        private async Task<ServiceResponse<CallingCardResponseDingModel>> AddExternalTransferTransaction(MobileRechargeTransaction mobileRechargeTransaction, User user, MobileRechargeBeneficiary beneficiary, MobileApplicationId mobileApplicationId, bool rechargeForMyself = false, bool isRatesExpInitiated = false, bool isTargetedExpInitiated = false, bool isAutoRenewalJobExecution = false,
            bool hasDiscountOnRenewals = false)
        {
            var CallingCardResponseDingModel = new CallingCardResponseDingModel();

            // Have Discount  
            bool hasDiscount = Convert.ToDecimal(mobileRechargeTransaction.DiscountAmount) > 0;
            if (hasDiscount)
            {
                mrTransacationPrefix = TransactionPrefix.MOBMK;
            }

            mobileRechargeTransaction.IsDeferredRecharge = false;

            //check beneficiary valid or not
            if (rechargeForMyself == false && beneficiary == null && mobileRechargeTransaction.RechargeType != MobileRechargeType.CALLINGCARDS)
            {
                _logger.LogWarning($"Due to invalid external beneficiary, mobilre recharge transfer failed for {mobileRechargeTransaction.ReferenceNumber}");

                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;

                mobileRechargeTransaction.Remarks = BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString();

                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
            }

            //Calculate the transaction amount
            var transactionDebitAmount = mobileRechargeTransaction.TotalAmount;

            var ppsDebitAmount = transactionDebitAmount * 100M;

            var cardNumber = user.CardHolder.CardNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);
            var cardSerialNumber = user.CardHolder.CardSerialNumber;

            //-------------------------------------------------------------------
            //---------- Check Balance from PPS-----------------------------------
            //--------------------------------------------------------------------

            //Get the current balance
            var balanceResponse = await _ppsService.GetCardBalance(new BalanceRequest()
            {
                CardPanNumber = cardPanNumber,
                CardSerialNumber = cardSerialNumber,
                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.BalanceEnquiry),
                ReferenceNumber = TypeUtility.GetReferenceNumber(BaseEnums.TransactionPrefix.BAL.ToString(), 12)
            });

            int triescount = mobileRechargeTransaction.TriesCount.GetValueOrDefault() + 1;
            mobileRechargeTransaction.TriesCount = triescount;

            //If PPS API Call failed
            if (!balanceResponse.IsSuccessful)
            {
                _logger.LogWarning($" Due to PPS service connection issue, mobile recharge transaction failed for {mobileRechargeTransaction.ReferenceNumber} ");

                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;
                mobileRechargeTransaction.Remarks = ConstantParam.UnableToConnectToPPSWebAuthService;
                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.PPSConnectionIssue.ToString());
            }

            //If PPS data validation failed
            if (balanceResponse.Data.StatusCode != "00")
            {
                _logger.LogWarning($"Due to {balanceResponse.Data.Message.ToString()}, mobile recharge transaction failed for {mobileRechargeTransaction.ReferenceNumber} ");

                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;

                if (balanceResponse.Data.Message == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSResponseStatus.CARDNOTACTIVATED))
                {
                    mobileRechargeTransaction.Remarks = BaseEnums.RechargeStatusValidationMessage.ActivateYourCard.ToString();
                }
                else if (balanceResponse.Data.Message == EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSResponseStatus.MAXPINEXCEEDED))
                {
                    mobileRechargeTransaction.Remarks = BaseEnums.RechargeStatusValidationMessage.UnblockYourCard.ToString();
                }
                else
                {
                    mobileRechargeTransaction.Remarks = balanceResponse.Data.Message.ToString();
                }

                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<CallingCardResponseDingModel>(false, mobileRechargeTransaction.Remarks);
            }

            //Check the current balance
            var balance = TypeUtility.GetDecimalFromString(balanceResponse.Data.EndBalanace.Amt) / 100;

            if (balance < transactionDebitAmount)
            {
                _logger.LogWarning($"Due to less balance of {transactionDebitAmount}, transaction failed for {mobileRechargeTransaction.ReferenceNumber} ");

                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;

                mobileRechargeTransaction.Remarks = BaseEnums.RechargeStatusValidationMessage.InsufficientBalance.ToString();

                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InsufficientBalance.ToString());
            }

            //----------------------------------------------------------------------------------------------------
            //---------- do the redemption from PPS & update PPS transaction in DB--------------------------------
            //-----------------------------------------------------------------------------------------------------        
            if (mobileRechargeTransaction.Transaction == null)
            {
                var rechargeIsLocal = mobileRechargeTransaction.RechargeType == MobileRechargeType.LOCAL || mobileRechargeTransaction.RechargeType == MobileRechargeType.MYNUMBER;

                var narration = rechargeIsLocal ? EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.LocalMobileRecharge) : EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.InternationalMobileRecharge);

                var referenceNumber = TypeUtility.GetReferenceNumber(mrTransacationPrefix.ToString(), 12);

                while (await this._unitOfWork.Transactions.Any(t => t.ReferenceNumber == referenceNumber))
                {
                    referenceNumber = TypeUtility.GetReferenceNumber(mrTransacationPrefix.ToString(), 12);
                }

                // If the current user is a part of Rates Experiment.
                if (isRatesExpInitiated)
                    referenceNumber = TypeUtility.GetReferenceNumber(mrTransacationPrefix.ToString() + "REXP", 12);

                // If the current user is a part of Targeted Discount Experiment.
                if ((isTargetedExpInitiated || hasDiscountOnRenewals)
                    && mobileRechargeTransaction.RechargeType == MobileRechargeType.INTERNATIONAL)
                    referenceNumber = TypeUtility.GetReferenceNumber(mrTransacationPrefix.ToString() + "TDXP", 12);


                var redemptionResponse = await _ppsService.DoRedemption(new RedemptionRequest()
                {
                    Amount = decimal.Truncate(ppsDebitAmount),
                    CardSerialNumber = cardSerialNumber,
                    CardPanNumber = cardPanNumber,
                    Narration = narration,
                    MerchantLoopCode = "5000",
                    TerminalId = TransactionMerchantCodeService.GetMerchantCode(user.CardHolder.BelongsToExchangeHouse, user.ApplicationId, TransactionMerchantCodeFeature.MobileRecharge),
                    ReferenceNumber = referenceNumber,
                    Description = narration
                });

                //PPS connection technical issue
                if (!redemptionResponse.IsSuccessful)
                {
                    _logger.LogWarning($"Due to pps service connection, Redemption failed for {referenceNumber} and the redemption amount :{ppsDebitAmount} ");
                    mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;
                    mobileRechargeTransaction.Remarks = BaseEnums.RechargeStatusValidationMessage.PPSConnectionIssue.ToString();
                    mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.PPSConnectionIssue.ToString());
                }

                //PPS response failed
                if (redemptionResponse.Data.StatusCode != "00")
                {
                    _logger.LogWarning($"Due to {redemptionResponse.Data.Message}, Redemption failed for {referenceNumber} and the redemption amount :{ppsDebitAmount}");

                    mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;
                    mobileRechargeTransaction.Remarks = redemptionResponse.Data.Message.ToString();
                    mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                    await _unitOfWork.CommitAsync();

                    return new ServiceResponse<CallingCardResponseDingModel>(false, redemptionResponse.Data.Message.ToString());
                }

                //Create/update PPS Transaction
                mobileRechargeTransaction.Transaction = new Core.Models.Transaction
                {
                    BillPayType = EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSBillPayType.MOB),
                    CreatedDate = DateTime.Now,
                    UserId = user.Id,
                    PayeeId = redemptionResponse.Data.PayeeId,
                    ServiceProvider = BaseEnums.CardPaymentServiceProvider.PPS,
                    CardNumber = redemptionResponse.Data.Account.No,
                    CardSerialNumber = redemptionResponse.Data.Account.Serial,
                    CardAccountTerminalAddress = redemptionResponse.Data.TerminalAddress,
                    CardAccountTerminalId = redemptionResponse.Data.TerminalId,
                    Amount = TypeUtility.GetDecimalFromString(redemptionResponse.Data.Value.Amt) / 100,
                    StatusCode = BaseEnums.Status.PENDING.ToString(),
                    StatusDescription = redemptionResponse.Data.Message,
                    Date = string.IsNullOrEmpty(redemptionResponse.Data.TimeStamp.Val) ? string.Empty : redemptionResponse.Data.TimeStamp.Val.ToString().Split(' ').First(),
                    Time = string.IsNullOrEmpty(redemptionResponse.Data.TimeStamp.Val) ? string.Empty : redemptionResponse.Data.TimeStamp.Val.ToString().Split(' ').Last(),
                    MacValue = redemptionResponse.Data.HashValue,
                    Origin = redemptionResponse.Data.Origin,
                    AuthenticationCode = redemptionResponse.Data.AuthCode,
                    ReferenceNumber = redemptionResponse.Data.ReferenceId,
                    EndBalance = TypeUtility.GetDecimalFromString(redemptionResponse.Data.EndBalanace.Amt),
                    CardAccountId = BaseEnums.PPSAccountIdPrefix.MOB.GetHashCode().ToString() + BaseEnums.PPSMerchantType.MOB.GetHashCode().ToString() + EnumUtility.GetDescriptionFromEnumValue(BaseEnums.PPSBillPayType.MOB)
                };

                mobileRechargeTransaction.Status = BaseEnums.Status.PENDING;

                await _unitOfWork.CommitAsync();

            }
            //----------------------------------------------------------------------------------------------------
            //---------- Send Mobile Recharge Transfer to Ding ---------------------------------------
            //-----------------------------------------------------------------------------------------------------


            if (mobileRechargeTransaction.ExternalTransaction == null)
            {
                string accountNumber = string.Empty;

                //set Account Number to process.
                if (rechargeForMyself && _mobileRechargeServiceSettings.TransactionEnvironment == TransactionEnvironment.LIVE.ToString())
                {
                    accountNumber = user.PhoneNumber.ToShortPhoneNumber();
                }
                else if (_mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.LIVE.ToString() && mobileRechargeTransaction.RechargeType == BaseEnums.MobileRechargeType.CALLINGCARDS)
                {
                    accountNumber = _mobileRechargeServiceSettings.CallingCardAccountNumberLive;
                }
                else if (mobileRechargeTransaction.Beneficiary != null && _mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.LIVE.ToString() && mobileRechargeTransaction.RechargeType != BaseEnums.MobileRechargeType.CALLINGCARDS)
                {
                    accountNumber = mobileRechargeTransaction.Beneficiary.AccountNumber;
                }
                else
                {
                    accountNumber = mobileRechargeTransaction.Product.UATNumber;
                }

                // Build Ding Request

                decimal fee = 0;

                if (mobileRechargeTransaction.Product.CustomFee.HasValue)
                {
                    fee = mobileRechargeTransaction.Product.CustomFee.Value;
                }
                else if (mobileRechargeTransaction.RechargeType == MobileRechargeType.INTERNATIONAL)
                {
                    var userCorporateId = mobileRechargeTransaction.User.CardHolder.CorporateId;
                    var applicationId = mobileRechargeTransaction.User.ApplicationId;

                    switch (applicationId)
                    {
                        case MobileApplicationId.C3Pay:
                            switch (this._mobileRechargeServiceSettings.C3FeeMode)
                            {
                                case MobileRechargeFeeMode.NoFee:
                                    break;
                                case MobileRechargeFeeMode.OnlyOnSelectedCorporates:
                                    var selectedCorporatesWithFee = this._mobileRechargeServiceSettings.SelectedCorporatesWithFee.Split(',').ToList();
                                    if (string.IsNullOrEmpty(userCorporateId) == false && selectedCorporatesWithFee.Contains(userCorporateId))
                                    {
                                        fee = this._mobileRechargeServiceSettings.FeeAmount;
                                    }
                                    break;
                                case MobileRechargeFeeMode.All:
                                    fee = this._mobileRechargeServiceSettings.FeeAmount;
                                    break;
                                default:
                                    break;
                            }

                            break;
                        case MobileApplicationId.MySalary:
                            switch (this._mobileRechargeServiceSettings.MySalaryFeeMode)
                            {
                                case MobileRechargeFeeMode.NoFee:
                                    break;
                                case MobileRechargeFeeMode.All:
                                    fee = this._mobileRechargeServiceSettings.FeeAmount;
                                    break;
                                default:
                                    break;
                            }
                            break;
                        default: break;
                    }

                }

                var sendMobileRechargeRequest = new SendTransferRequestDingModel()
                {
                    ProductCode = mobileRechargeTransaction.ProductCode,
                    SendValue = mobileRechargeTransaction.TotalAmount - fee + Convert.ToDecimal(mobileRechargeTransaction.DiscountAmount),
                    SendCurrencyIso = mobileRechargeTransaction.SendCurrency,
                    TransferReferenceId = mobileRechargeTransaction.ReferenceNumber,
                    AccountNumber = accountNumber,
                    BatchItemReference = false,
                    BillReference = BaseEnums.MobileRechargeReference.myc3card.ToString(),
                };

                // If the current user is a part of Rates or Targeted Discount Experiment.
                // We can not change the SendValue for a ding request, it should be the same as received from Ding.
                if ((isRatesExpInitiated || isTargetedExpInitiated)
                    && mobileRechargeTransaction.Product.MinSendValue.HasValue)
                    sendMobileRechargeRequest.SendValue = mobileRechargeTransaction.Product.MinSendValue.Value;

                //Call Ding API
                ServiceResponse<SendTransferResponseDingModel> sendMobileRechargeResult = null;
                try
                {
                    var crossCheckBeneficiaryTestingEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableCrossCheckBeneficiaryTesting);
                    if (crossCheckBeneficiaryTestingEnabled && (accountNumber == "************" || accountNumber == "************"))
                    {
                        sendMobileRechargeResult = new ServiceResponse<SendTransferResponseDingModel>(false, "ProviderError###ProviderTemporarilyUnavailable");
                    }
                    else
                    {
                        sendMobileRechargeResult = await this._dingService.SendTransfer(sendMobileRechargeRequest, (Enums.MobileApplicationId)(int)mobileApplicationId);
                    }

                    var mobileRechargeRetrialTestingEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRechargeWithDingRetrial);
                    if (mobileRechargeRetrialTestingEnabled && accountNumber == "************")
                    {
                        mobileRechargeTransaction.IsDeferredRecharge = true;
                        sendMobileRechargeResult = await this._dingService.SendTransferV2(sendMobileRechargeRequest, (Enums.MobileApplicationId)(int)mobileApplicationId, mobileRechargeTransaction.Id);
                    }

                    //**********************************************************************************************
                    //Enable mobile recharge with retrials in Ding as long as it is not from AutoRenewalJobExecution
                    //**********************************************************************************************
                    var targetingContext = new TargetingContext { UserId = user.CardHolderId };
                    var enableRechargeWithDingRetrials = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRechargeWithDingRetrial, targetingContext);

                    // Determine whether to retry with deferred transfer:
                    // Extract transfer status safely
                    var transferStatus = sendMobileRechargeResult.Data?.TransferDataResponse?.TransferStatus;

                    // Determine initial retry condition based on result or specific failed states
                    bool shouldRetry =
                        !sendMobileRechargeResult.IsSuccessful ||
                        transferStatus is not null &&
                        (
                            transferStatus == Enums.ExternalDingStatus.Failed.ToString() ||
                            transferStatus == Enums.ExternalDingStatus.Cancelled.ToString() ||
                            transferStatus == Enums.ExternalDingStatus.Cancelling.ToString()
                        );

                    // Prepare remarks and context from error message if retry is initially required
                    string remarks = string.Empty;
                    string errorContext = string.Empty;

                    if (shouldRetry && !string.IsNullOrWhiteSpace(sendMobileRechargeResult.ErrorMessage))
                    {
                        var errorParts = sendMobileRechargeResult.ErrorMessage.Split("###");

                        if (errorParts.Length == 2)
                        {
                            remarks = errorParts[0];
                            errorContext = errorParts[1];

                            var transientErrorContexts = new HashSet<string>
                            {
                                Enums.DingErrorContext.AccountTemporarilyUnavailable.ToString(),
                                Enums.DingErrorContext.ProviderTemporarilyUnavailable.ToString(),
                                Enums.DingErrorContext.ProviderTimedOut.ToString()
                            };

                            // Retry only if the error context is known to be transient
                            shouldRetry = transientErrorContexts.Contains(errorContext);
                        }
                        else
                        {
                            // Error message format is invalid — avoid retry
                            shouldRetry = false;
                        }
                    }


                    if (shouldRetry && enableRechargeWithDingRetrials && !isAutoRenewalJobExecution)
                    {
                        // Retry using the secondary method that includes additional headers for deferred transfer.
                        mobileRechargeTransaction.IsDeferredRecharge = true;
                        sendMobileRechargeResult = await this._dingService.SendTransferV2(sendMobileRechargeRequest, (Enums.MobileApplicationId)(int)mobileApplicationId, mobileRechargeTransaction.Id);
                    }
                }
                catch (Exception exception)
                {
                    var getRechargeStatusResult = await this._dingService.GetTransferStatus(new GetTransferStatusRequestDingModel()
                    {
                        TransferReferenceId = mobileRechargeTransaction.ReferenceNumber,
                        AccountNumber = mobileRechargeTransaction.AccountNumber,
                        Skip = 0,
                        Take = 1,
                    });

                    var recharge = getRechargeStatusResult.Data.Data.FirstOrDefault()?.TransferDataResponse;

                    if (!getRechargeStatusResult.IsSuccessful || recharge == null || (getRechargeStatusResult.Data != null && _failedStatuses.Contains(recharge.TransferStatus)))
                    {
                        //Do the redemption reversal            
                        if (!mobileRechargeTransaction.Transaction.IsReversed())
                        {
                            await this.ReverseTransaction(mobileRechargeTransaction);

                            //update status in transaction table
                            mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;
                            mobileRechargeTransaction.Remarks = string.Join(" : ", "Exception", exception.Message);
                            mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                            await _unitOfWork.CommitAsync();

                            CallingCardResponseDingModel.Status = mobileRechargeTransaction.Status.ToString();
                            CallingCardResponseDingModel.Remarks = string.Empty;

                            _logger.LogError(BaseEnums.RechargeStatusValidationMessage.DingResponseFailed.ToString(), exception);

                            return new ServiceResponse<CallingCardResponseDingModel>(CallingCardResponseDingModel);
                        }
                    }

                    sendMobileRechargeResult = new ServiceResponse<SendTransferResponseDingModel>(true, null)
                    {
                        Data = new SendTransferResponseDingModel()
                        {
                            TransferDataResponse = recharge
                        }
                    };
                }

                if (!sendMobileRechargeResult.IsSuccessful || (sendMobileRechargeResult.Data != null && (sendMobileRechargeResult.Data.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Failed.ToString() ||
                    sendMobileRechargeResult.Data.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelled.ToString() ||
                    sendMobileRechargeResult.Data.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelling.ToString())))
                {
                    _logger.LogWarning($"Due to {sendMobileRechargeResult.ErrorMessage}, Transaction to Ding failed for {mobileRechargeTransaction.ReferenceNumber} ");

                    //Do the redemption reversal            
                    if (!mobileRechargeTransaction.Transaction.IsReversed())
                    {
                        await this.ReverseTransaction(mobileRechargeTransaction);
                    }

                    //update status in transaction table
                    mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;

                    List<string> errorMessagesWithContext = sendMobileRechargeResult.ErrorMessage.Split("###").ToList();
                    if (errorMessagesWithContext.Count() == 2)
                    {
                        mobileRechargeTransaction.Remarks = errorMessagesWithContext[0];
                        mobileRechargeTransaction.ErrorContext = errorMessagesWithContext[1];
                    }

                    mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                    // Update the Discount if any
                    if (mobileRechargeTransaction.UserDiscount != null)
                    {
                        mobileRechargeTransaction.UserDiscount.IsUsed = false;
                        mobileRechargeTransaction.UserDiscount.MobileRechargeTransactionId = null;
                        mobileRechargeTransaction.UserDiscount.UpdatedDate = DateTime.Now;
                        mobileRechargeTransaction.UserDiscountId = null;
                        mobileRechargeTransaction.DiscountAmount = 0;
                    }

                    await _unitOfWork.CommitAsync();

                    // Trigger for Cross Checking Beneficiary 
                    if (beneficiary != null && mobileRechargeTransaction != null && !string.IsNullOrEmpty(mobileRechargeTransaction.ErrorContext))
                    {
                        var message = new MobileRechargeFailedTransactionMessageDto()
                        {
                            BeneficiaryCountryCode = beneficiary.CountryCode,
                            BeneficiaryId = beneficiary.Id,
                            TransactionId = mobileRechargeTransaction.Id,
                            TransactionErrorContext = mobileRechargeTransaction.ErrorContext
                        };

                        //Pushing message to service bus - # MobileRechargeFailedTransactioEvent 
                        await _messagingQueueService.SendEventToTopicAsync(message,
                              _mobileRechargeServiceSettings.ServiceBusConnectionString,
                              _mobileRechargeServiceSettings.ServiceBusTopicName,
                              nameof(OutboxMessageTypeEnum.MRFailedTransactionEvent),
                              Guid.NewGuid().ToString());

                    }
                    CallingCardResponseDingModel.Status = mobileRechargeTransaction.Status.ToString();
                    CallingCardResponseDingModel.Remarks = string.Empty;
                    return new ServiceResponse<CallingCardResponseDingModel>(CallingCardResponseDingModel);
                }

                CallingCardResponseDingModel.CustomerCareNumber = string.Empty;

                //getting PIN number from receipt params
                if (mobileRechargeTransaction.RechargeType == BaseEnums.MobileRechargeType.CALLINGCARDS)
                {
                    if (!string.IsNullOrEmpty(sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptText))
                    {
                        var receiptTextParams = sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptText.Split(new[] { "\r\n", "\r", "\n" },
                            StringSplitOptions.None);

                        if (_mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.UAT.ToString())
                        {
                            var customerCareNumber = Array.Find(receiptTextParams, element => element.StartsWith("Control", StringComparison.Ordinal));

                            if (customerCareNumber != null && customerCareNumber.Contains(":"))
                            {
                                CallingCardResponseDingModel.CustomerCareNumber = customerCareNumber.Split(":")[1];
                            }
                        }
                        else
                        {
                            if (receiptTextParams.Length > 2)
                            {
                                var keyWord = "Customer Care: ";
                                int index = receiptTextParams[2].IndexOf(keyWord);
                                if (index != -1)
                                    CallingCardResponseDingModel.CustomerCareNumber = receiptTextParams[2][(index + keyWord.Length)..];
                                else
                                    CallingCardResponseDingModel.CustomerCareNumber = string.Empty;
                            }
                        }
                    }
                    CallingCardResponseDingModel.PinNumber = sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptParamText?.PinText;
                }

                if (mobileRechargeTransaction.IsDeferredRecharge == false)
                {
                    //Create the external transaction in DB
                    mobileRechargeTransaction.ExternalTransaction = new MobileRechargeExternalTransaction
                    {
                        ExternalStatus = sendMobileRechargeResult.Data.TransferDataResponse.TransferStatus,
                        CommisionRate = sendMobileRechargeResult.Data.TransferDataResponse.CommissionApplied,
                        ExternalTransactionId = sendMobileRechargeResult.Data.TransferDataResponse.TransferReference.ExternalReferenceId,
                        StartDate = sendMobileRechargeResult.Data.TransferDataResponse.TransferStartDate(),
                        EndDate = sendMobileRechargeResult.Data.TransferDataResponse.TransferEndDate(),
                        SendCurrency = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.SendCurrencyIso,
                        SendAmount = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.SendValue,
                        CustomerFee = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.CustomerFee,
                        ReceiveCurrency = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.ReceiveCurrencyIso,
                        ReceiveAmount = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.ReceiveValue,
                        ReceiveAmountWithTax = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.ReceiveValueExcludingTax,
                        DistributorFee = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.DistributorFee,
                        TaxName = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.TaxName,
                        TaxRate = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.TaxRate,
                        TaxCalculation = sendMobileRechargeResult.Data.TransferDataResponse.PricePayment.TaxCalculation,
                        CallingCardPin = sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptParamText?.PinText,
                        AccountNumber = sendMobileRechargeResult.Data.TransferDataResponse.AccountNumber,
                        ReceiptText = sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptParamText?.PinText,
                        ReceiptParams = sendMobileRechargeResult?.Data?.TransferDataResponse?.ReceiptText,
                        LastStatusDate = sendMobileRechargeResult.Data.TransferDataResponse.TransferEndDate(),
                        StatusDate = DateTime.Now
                    };

                    mobileRechargeTransaction.Transaction.StatusCode = "00";
                }

                if (sendMobileRechargeResult.Data.TransferDataResponse.TransferStatus.ToUpper() == Enums.ExternalDingStatus.Complete.ToString().ToUpper())
                {
                    mobileRechargeTransaction.Status = BaseEnums.Status.SUCCESSFUL;
                }
                else
                {
                    mobileRechargeTransaction.Status = BaseEnums.Status.PENDING;
                }
            }

            //----------------------------------------------------------------------------------------------------
            //---------- Update the Mobile Recharge Transfer Transaction in DB ---------------------------------------------
            //-----------------------------------------------------------------------------------------------------    
            mobileRechargeTransaction.Remarks = string.Empty;
            mobileRechargeTransaction.UpdatedDate = DateTime.Now;

            if (isTargetedExpInitiated)
            {
                //await _unitOfWork.MobileRechargeTargetedDiscounts.UpdateOfferStatus(user.CardHolder.Id, isActive: false);
                var activeTargetedDiscOffer = await _unitOfWork.MobileRechargeTargetedDiscounts.GetActiveOfferAsync(user.CardHolder.Id);
                if (activeTargetedDiscOffer is null)
                {
                    _logger.LogWarning($"Mobile recharge: Targeted discount offer not found for the user: {user.Id}");
                }

                activeTargetedDiscOffer.IsActive = false;
                activeTargetedDiscOffer.OfferStatus = (int)MobileRecharge_TargetedDiscountOfferStatus.Claimed;
            }

            await _unitOfWork.CommitAsync();

            CallingCardResponseDingModel.Status = mobileRechargeTransaction.Status.ToString();

            CallingCardResponseDingModel.Remarks = string.Empty;

            CallingCardResponseDingModel.TransactionId = mobileRechargeTransaction.Status == BaseEnums.Status.PENDING ? mobileRechargeTransaction.Id.ToString() : string.Empty;

            return new ServiceResponse<CallingCardResponseDingModel>(CallingCardResponseDingModel);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        private async Task ReverseTransaction(MobileRechargeTransaction mobileRechargeTransaction)
        {
            if (string.IsNullOrEmpty(mobileRechargeTransaction.Transaction.ReferenceNumber))
            {
                _logger.LogWarning($"Reference Id missed for the Redemption reversal of mobile recharge transaction id : {mobileRechargeTransaction.ReferenceNumber}");

                return;
            }

            // PPS call for reversal 
            var transactionDebitAmount = mobileRechargeTransaction.TotalAmount;

            var rechargeIsLocal = mobileRechargeTransaction.RechargeType == MobileRechargeType.LOCAL || mobileRechargeTransaction.RechargeType == MobileRechargeType.MYNUMBER;

            var narration = string.Join(" ", rechargeIsLocal ? EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.LocalMobileRecharge) : EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.InternationalMobileRecharge), "Reversal");

            var ppsCreditAmount = transactionDebitAmount * 100;

            var user = await _unitOfWork.Users.GetUserById(mobileRechargeTransaction.UserId);

            var reversalRequest = new RedemptionReversalRequest()
            {
                Amount = decimal.Truncate(ppsCreditAmount),
                CardPanNumber = mobileRechargeTransaction.Transaction.CardNumber,
                CardSerialNumber = mobileRechargeTransaction.Transaction.CardSerialNumber,
                RedemptionId = mobileRechargeTransaction.Transaction.ReferenceNumber,
                Narration = narration,
                Description = narration,
                ReferenceNumber = mobileRechargeTransaction.Transaction.ReferenceNumber,
                TerminalId = TransactionMerchantCodeService.GetMerchantCode(user.CardHolder.BelongsToExchangeHouse, user.ApplicationId, TransactionMerchantCodeFeature.MobileRecharge),
                MerchantLoopCode = "5000"
            };

            var redemptionReversalResponse = await _ppsService.DoRedemptionReversal(reversalRequest);

            if (!redemptionReversalResponse.IsSuccessful)
            {
                _logger.LogWarning($"Redemption reversal failed for the mobile recharge transaction : {mobileRechargeTransaction.ReferenceNumber} and the Reference Id :{mobileRechargeTransaction.Transaction.ReferenceNumber} ");

                mobileRechargeTransaction.Transaction.Reversal.StatusCode = BaseEnums.Status.FAILED.ToString();
                mobileRechargeTransaction.Transaction.Reversal.StatusDescription = "Reversal failed";
            }
            else
            {

                mobileRechargeTransaction.Transaction.AuthenticationCode = redemptionReversalResponse.Data.AuthCode;
                mobileRechargeTransaction.Transaction.Reversal.Amount = TypeUtility.GetDecimalFromString(redemptionReversalResponse.Data.Value.Amt);
                mobileRechargeTransaction.Transaction.Reversal.EndBalance = TypeUtility.GetDecimalFromString(redemptionReversalResponse.Data.EndBalanace.Amt);
                mobileRechargeTransaction.Transaction.Reversal.Date = DateTime.Now;
                mobileRechargeTransaction.Transaction.Reversal.ReferenceNumber = reversalRequest.ReferenceNumber;
                mobileRechargeTransaction.Transaction.Reversal.StatusCode = redemptionReversalResponse.Data.StatusCode;
                mobileRechargeTransaction.Transaction.Reversal.StatusDescription = redemptionReversalResponse.Data.Message;
            }

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="UserId"></param>
        /// <param name="RechargeType"></param>
        /// <param name="PageSize"></param>
        /// <param name="PageNumber"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<IEnumerable<MobileRechargeDetails>>> GetUserMobileRechargeTransactions(Guid userId, string rechargeType, int? pageSize, int? pageNumber)
        {
            int? skipValue;
            int? pageRecords;
            MobileRechargeType? mappedRechargeType = null;

            if (!string.IsNullOrEmpty(rechargeType))
            {
                if (!Enum.IsDefined(typeof(BaseEnums.MobileRechargeType), rechargeType.ToUpper()))
                {
                    return new ServiceResponse<IEnumerable<MobileRechargeDetails>>(false, ConstantParam.InvalidRechargeType);
                }

                mappedRechargeType = (MobileRechargeType)Enum.Parse(typeof(BaseEnums.MobileRechargeType), rechargeType.ToUpper());
            }

            if (pageSize > 0 && pageNumber > 0)
            {
                skipValue = pageNumber == 1 ? 0 : (pageNumber - 1) * pageSize;
                pageRecords = pageSize;
            }
            else
            {
                skipValue = null;
                pageRecords = null;
            }

            var mobileRechargeTransactions = await _unitOfWork.MobileRechargeTransactions
                .GetUserTransactionsDetails(userId, mappedRechargeType, skipValue, pageRecords);

            return new ServiceResponse<IEnumerable<MobileRechargeDetails>>(mobileRechargeTransactions);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MobileRechargeTransaction>> GetMobileRechargeTransactionReceipt(Guid transactionId)
        {
            var mobileRechargeTransaction = await _unitOfWork.MobileRechargeTransactions
                .FirstOrDefaultAsync(x => x.Id == transactionId,
                x => x.User,
                x => x.Beneficiary,
                x => x.ExternalTransaction,
                x => x.Product.Provider,
                x => x.Product,
                x => x.User.CardHolder);

            return new ServiceResponse<MobileRechargeTransaction>(mobileRechargeTransaction);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MobileRechargeTransaction>> GetMobileRechargeTransactionReceiptReadOnly(Guid transactionId)
        {
            var mrtransaction = await _unitOfWorkReadOnly.MobileRechargeTransactions.FirstOrDefaultAsync(x => x.Id == transactionId, x => x.User, x => x.Beneficiary, x => x.ExternalTransaction, x => x.Product.Provider, x => x.Product, x => x.User.CardHolder);
            return new ServiceResponse<MobileRechargeTransaction>(mrtransaction);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<ServiceResponse> UpdateStatusForPendingTransactions(bool isRatesExperimentEnabled)
        {
            var pendingMobileRecharges = await _unitOfWork.MobileRechargeTransactions.FindAsync(x => x.Status == BaseEnums.Status.PENDING, x => x.ExternalTransaction);

            _logger.LogWarning($"{pendingMobileRecharges.Count} transaction status updation started ");

            //If status is pending and retrieve from Ding 
            foreach (var recharge in pendingMobileRecharges)
            {
                if (recharge.Status == BaseEnums.Status.PENDING)
                {
                    await this.UpdateTransactionStatusFromExternal(recharge, isRatesExperimentEnabled);
                }
            }

            _logger.LogWarning($"{pendingMobileRecharges.Count} transaction status updation finished ");

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="transactionId"></param>
        /// <returns></returns>
        private async Task<ServiceResponse> UpdateTransactionStatusFromExternal(MobileRechargeTransaction mobileRechargeTransaction, bool isRatesExperimentEnabled)
        {
            // Build Ding Request
            var getTransferStatusRequest = new GetTransferStatusRequestDingModel()
            {
                ExternalReference = mobileRechargeTransaction.ExternalTransaction.ExternalTransactionId,
                TransferReferenceId = mobileRechargeTransaction.ReferenceNumber,
                AccountNumber = mobileRechargeTransaction.ExternalTransaction.AccountNumber,
                Skip = 0,
                Take = 50,
            };

            //Call Ding API          
            var getTransferStatusResult = await this._dingService.GetTransferStatus(getTransferStatusRequest);

            if (!getTransferStatusResult.IsSuccessful || getTransferStatusResult.Data == null || getTransferStatusResult.Data.Data == null)
            {
                _logger.LogWarning($"Due to {getTransferStatusResult.ErrorMessage}, status updation failed for {mobileRechargeTransaction.Id} ");

                return new ServiceResponse<MobileRechargeTransaction>(false, getTransferStatusResult.ErrorMessage);
            }

            //Read status from ding
            var statusResult = getTransferStatusResult.Data.Data.OrderBy(x => x.TransferDataResponse.TransferEndDate()).LastOrDefault();

            //Update Mobile recharge Transfer transaction in DB  
            if (statusResult.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Failed.ToString() ||
                    statusResult.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelled.ToString() ||
                    statusResult.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelling.ToString())
            {
                _logger.LogWarning($"Due to {getTransferStatusResult.ErrorMessage}, Transaction to Ding failed for {mobileRechargeTransaction.ReferenceNumber} ");

                //update status in transaction table
                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;
                mobileRechargeTransaction.Remarks = getTransferStatusResult.ErrorMessage;
                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                await _unitOfWork.CommitAsync();

                // Send failed-cleavertap event for rates experiment transaction
                if (isRatesExperimentEnabled
                    && TryValidateUserForRatesExperiment(mobileRechargeTransaction.User.CardHolderId, out string jConfig)
                    && await IsProductPartOfTheRatesExp(mobileRechargeTransaction.ProductCode, jConfig))
                {
                    await _analyticsPublisherService.PublishMobileRechargeFailedEvent(mobileRechargeTransaction.User.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeEvent
                    {
                        UserId = mobileRechargeTransaction.UserId,
                        ProductCode = mobileRechargeTransaction.ProductCode
                    });
                }

                return new ServiceResponse(false, getTransferStatusResult.ErrorMessage);
            }

            var status = statusResult.TransferDataResponse.TransferStatus.ToUpper();

            if (status == Enums.ExternalDingStatus.Complete.ToString().ToUpper() || status == "Success".ToUpper())
            {
                mobileRechargeTransaction.Status = BaseEnums.Status.SUCCESSFUL;
            }
            else
            {
                mobileRechargeTransaction.Status = BaseEnums.Status.PENDING;
            }

            mobileRechargeTransaction.Remarks = string.Empty;
            mobileRechargeTransaction.UpdatedDate = DateTime.Now;

            mobileRechargeTransaction.ExternalTransaction.ExternalStatus = statusResult.TransferDataResponse.TransferStatus;
            mobileRechargeTransaction.ExternalTransaction.StartDate = statusResult.TransferDataResponse.TransferStartDate();
            mobileRechargeTransaction.ExternalTransaction.EndDate = statusResult.TransferDataResponse.TransferEndDate();
            mobileRechargeTransaction.ExternalTransaction.LastStatusDate = statusResult.TransferDataResponse.TransferEndDate();
            mobileRechargeTransaction.ExternalTransaction.StatusDate = DateTime.Now;

            await _unitOfWork.CommitAsync();

            // Send successful-cleavertap event for rates experiment transaction
            if (mobileRechargeTransaction.Status == Status.SUCCESSFUL
                && isRatesExperimentEnabled
                && TryValidateUserForRatesExperiment(mobileRechargeTransaction.User.CardHolderId, out string jsonConfig)
                && await IsProductPartOfTheRatesExp(mobileRechargeTransaction.ProductCode, jsonConfig))
            {
                await _analyticsPublisherService.PublishMobileRechargeSucessEvent(mobileRechargeTransaction.User.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeEvent
                {
                    UserId = mobileRechargeTransaction.UserId,
                    ProductCode = mobileRechargeTransaction.ProductCode
                });
            }

            //Activate Auto Renewal
            if (mobileRechargeTransaction.IsAutoRenewalEnabled == true && mobileRechargeTransaction.Status == BaseEnums.Status.SUCCESSFUL)
            {
                await UpdateRenewal(userId: mobileRechargeTransaction.UserId,
                                    productCode: mobileRechargeTransaction.ProductCode,
                                    beneficiaryId: mobileRechargeTransaction.BeneficiaryId.Value,
                                    cardHolderId: mobileRechargeTransaction.User.CardHolderId,
                                    deactivationCode: null,
                                    hasClaimedTargetedDiscount: mobileRechargeTransaction.ReferenceNumber.Contains("TDXP") ? true : false);
            }


            _logger.LogWarning($"Status updated successfully for {mobileRechargeTransaction.ReferenceNumber} and the user id {mobileRechargeTransaction.UserId} ");

            return new ServiceResponse();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="searchMobileRechargeParameters"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>> GetAllMobileRechargeBeneficiaryReadOnly(SearchMobileRechargeBeneficiary searchMobileRechargeParameters)
        {
            var filters = MobileRechargeBeneficiaryFilter.GetMobileRechargeBeneficiaryFilter(searchMobileRechargeParameters);
            var responeMobileRechargeBeneficiary = await this._unitOfWorkReadOnly.MobileRechargeBeneficiaries.FindAsync(filters, x => x.CreatedDate, true, searchMobileRechargeParameters.Page.HasValue ? (searchMobileRechargeParameters.Page - 1) * searchMobileRechargeParameters.Size : null, searchMobileRechargeParameters.Size.HasValue ? searchMobileRechargeParameters.Size : null, true, x => x.User.CardHolder);
            return new ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>(responeMobileRechargeBeneficiary);
        }

        #endregion

        /// <summary>
        /// Search User Recharge Beneficiaries
        /// </summary>
        /// <param name="id"></param>
        /// <param name="searchBeneficiaryParameters"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>> SearchUserRechargeBeneficiariesReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MobileRechargeBeneficiaryFilter.GetFilterById(id, searchBeneficiaryParameters);
            var responseBeneficiary = await this._unitOfWorkReadOnly.MobileRechargeBeneficiaries.FindAsync(filters, x => x.CreatedDate, true, searchBeneficiaryParameters.Page.HasValue ? (searchBeneficiaryParameters.Page - 1) * searchBeneficiaryParameters.Size : null, searchBeneficiaryParameters.Size.HasValue ? searchBeneficiaryParameters.Size : null, true, x => x.User.CardHolder);
            return new ServiceResponse<Tuple<IList<MobileRechargeBeneficiary>, int>>(responseBeneficiary);
        }

        /// <summary>
        /// Search User Recharge Beneficiaries Transaction
        /// </summary>
        /// <param name="id"></param>
        /// <param name="searchBeneficiaryParameters"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<Tuple<IList<MobileRechargeTransaction>, int>>> SearchUserRechargeBeneficiariesTransactionReadOnly(Guid id, SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MobileRechargeTransactionFilter.GetFilterById(id, searchBeneficiaryParameters);
            var responseBeneficiary = await this._unitOfWorkReadOnly.MobileRechargeTransactions.FindAsync(filters, x => x.CreatedDate, true, searchBeneficiaryParameters.Page.HasValue ? (searchBeneficiaryParameters.Page - 1) * searchBeneficiaryParameters.Size : null, searchBeneficiaryParameters.Size.HasValue ? searchBeneficiaryParameters.Size : null, true, x => x.User, x => x.User.CardHolder, x => x.Beneficiary, x => x.ExternalTransaction);
            return new ServiceResponse<Tuple<IList<MobileRechargeTransaction>, int>>(responseBeneficiary);
        }

        public async Task<ServiceResponse<Tuple<List<MobileRechargeTransactionStruct>, int>>> SearchRechargeBeneficiariesTransactionReadOnly(SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var filters = MobileRechargeTransactionFilter.GetMobileRechargeTransactionFilter(searchBeneficiaryParameters);
            var responseBeneficiary = await this._unitOfWorkReadOnly.MobileRechargeTransactions.Search(filters, searchBeneficiaryParameters.Page, searchBeneficiaryParameters.Size);
            return new ServiceResponse<Tuple<List<MobileRechargeTransactionStruct>, int>>(responseBeneficiary);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="searchBeneficiaryParameters"></param>
        /// <returns></returns>
        public async Task<IList<Guid>> GetUserIdsByBeneficiary(SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var responseBeneficiary = await this._unitOfWork.MobileRechargeBeneficiaries.Search(searchBeneficiaryParameters);
            return (IList<Guid>)responseBeneficiary;
        }
        public async Task<string> GetCountryCode(string accountNumber)
        {
            var responseBeneficiary = await this._unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.AccountNumber == accountNumber && !x.IsDeleted);
            return responseBeneficiary.CountryCode;
        }

        public async Task<ServiceResponse<TargetedDiscountConfigDto>> GetTargetedDiscountConfig(User user)
        {
            var response = new ServiceResponse<TargetedDiscountConfigDto>();

            var activeOffer = await _unitOfWork.MobileRechargeTargetedDiscounts.GetActiveOfferAsync(user.CardHolder.Id);

            if (activeOffer is null)
            {
                _logger.LogInformation($"Mobile recharge targeted discount: The user does not have an active offer. UserId: {user.Id}");
                return response;
            }

            var bannerUrl = await _lookupService.GetMultimediaResources(identifier: "MobileRecharge_TargetedDiscount", type: MultimediaType.Image, feature: (int)FeatureType.MobileRecharge);

            if (!bannerUrl.IsSuccessful || bannerUrl.Data?.Count < 1)
            {
                _logger.LogInformation($"Mobile recharge targeted discount: Could not obtain the banner image URL. UserId: {user.Id}");
                return response;
            }

            var remainingTime = activeOffer.OfferStartDateTime?.AddHours(12) - DateTime.Now;

            // Expire the offer if 12 hours have been passed
            if (remainingTime?.TotalMinutes < 0)
            {
                await ChangeTargetedDiscountOfferStatus(activeOffer, MobileRecharge_TargetedDiscountOfferStatus.Expired, true);
                _logger.LogInformation($"Mobile recharge targeted discount: Offer is expired. UserId: {user.Id}");

                return response;
            }

            response.Data = new TargetedDiscountConfigDto
            {
                DiscountImageUrl = bannerUrl.Data?[0].Url,
                RemainingTime = !remainingTime.HasValue ? "00:00" : remainingTime.Value.ToString("hh\\:mm\\:ss")
            };

            return response;
        }

        /// <summary>
        /// A wrapper-method around Get Summary Details to apply experiments rates
        /// </summary>
        public async Task<ServiceResponse<MobileRechargeTransaction>> GetSummary(User user, string productCode, MobileApplicationId applicationId, MobileRechargeTransaction mobileRechargeTransaction)
        {
            var summary = await GetSummaryDetails(user, productCode, applicationId, mobileRechargeTransaction);

            // Execute rates experiment if applicable
            var isRatesExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRatesExperiment);
            if (isRatesExperimentEnabled
                && !(summary.Data is null)
                && TryValidateUserForRatesExperiment(user.CardHolderId, out string jsonConfig))
            {
                summary.Data = await ExecuteRatesExperiment(summary.Data, jsonConfig, out _);
                summary.Data.Product.MinSendValue = summary.Data.SendAmount;

                //Exclude users from auto-renewal that are part of Rates Experiment
                summary.Data.Product.IsAutoRenewalEnabled = false;

                //Exclude users from targeted discount that are part of Rates Experiment
                summary.Data.TargetedDiscountAmount = 0.0M;
                return summary;
            }

            //Execute Targeted Discount Offer if applicable
            var targetedDiscountOfferEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableTargetedDiscount);
            if (targetedDiscountOfferEnabled)
            {
                // Check if the user is doing a international MR.
                if (mobileRechargeTransaction.RechargeType == MobileRechargeType.INTERNATIONAL)
                {
                    var hasActiveTargetedDiscountOffer = await _unitOfWork.MobileRechargeTargetedDiscounts.UserHasActiveDiscountOffer(user.Id);

                    if (hasActiveTargetedDiscountOffer && !(summary.Data is null))
                    {

                        //temp fix - only offer targeted discount to products that have fixed price
                        var isFixedPrice = await IsFixedPriceProductAsync(productCode);
                        if (isFixedPrice)
                        {
                            summary.Data.TargetedDiscountAmount = summary.Data.SendAmount * 0.20M;
                        }
                        else
                        {
                            summary.Data.TargetedDiscountAmount = 0.0M;
                        }

                        //Exclude users from auto-renewal that have active user discount
                        //Commented IsAutoRenewalEnabled to allow autorenewals for users with ActiveTargetedDiscountOffer 
                        //summary.Data.Product.IsAutoRenewalEnabled = false;

                        return summary;
                    }
                }
            }
            summary.Data.TargetedDiscountAmount = 0.0M;
            return summary;
        }

        /// <summary>
        /// Get Summary Details - Step Before Making Recharge
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="productCode"></param>
        /// <returns></returns>
        public async Task<ServiceResponse<MobileRechargeTransaction>> GetSummaryDetails(User user, string productCode,
            MobileApplicationId applicationId, MobileRechargeTransaction transaction)
        {
            // **** Special Check for Dynamic Packages  
            var passedProductCode = productCode;
            productCode = ExtractProductCodeForAdjustablePriceProduct(passedProductCode);

            //------------------------------------------------------------------------------------
            // Get Product Details 
            //------------------------------------------------------------------------------------

            var thisProduct = await _unitOfWork.MobileRechargeProducts.GetByIdAsync(productCode);
            if (thisProduct == null)
                return new ServiceResponse<MobileRechargeTransaction>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());

            //------------------------------------------------------------------------------------
            // Custom Check - Dynamic Packages 
            //------------------------------------------------------------------------------------
            if (thisProduct.IsAdjustablePrice)
            {
                IList<MobileRechargeProduct> newProducts = null;
                newProducts = ConvertToDynamicPackages(newProducts, thisProduct);

                var thisDynamicProduct = newProducts.FirstOrDefault(a => a.Code == passedProductCode);
                if (thisDynamicProduct != null)
                {
                    thisProduct.MaxSendValue = thisDynamicProduct.MaxSendValue.GetValueOrDefault();
                    thisProduct.MaxSendCurrencyIso = thisDynamicProduct.MaxSendCurrencyIso;

                    thisProduct.MaxReceiveValue = thisDynamicProduct.MaxReceiveValue.GetValueOrDefault();
                    thisProduct.MaxReceiveCurrencyIso = thisDynamicProduct.MaxReceiveCurrencyIso;

                    thisProduct.Code = thisDynamicProduct.Code;
                    thisProduct.DefaultDisplayText = thisDynamicProduct.DefaultDisplayText;
                }
            }
            transaction.Product = thisProduct;


            // Populate Transaction 
            transaction.Fee = Convert.ToDecimal(thisProduct.CustomFee > 0 ? thisProduct.CustomFee : thisProduct.MaxCustomerFee);
            transaction.ReceiveAmount = Convert.ToDecimal(thisProduct.MaxReceiveValue);
            transaction.ReceiveCurrency = thisProduct.MaxReceiveCurrencyIso;
            transaction.SendAmount = Convert.ToDecimal(thisProduct.MaxSendValue);

            //------------------------------------------------------------------------------------
            // Check for Product Estimate Rate - Ding (External Call)  
            //------------------------------------------------------------------------------------
            if (!thisProduct.IsAdjustablePrice)
            {
                // External Ding Call  
                var tryGetEstimateRate = await GetProductEstimateRateV2(thisProduct);

                if (tryGetEstimateRate.Data != null)
                {
                    transaction.Fee = Convert.ToDecimal(tryGetEstimateRate.Data.PricePayment.CustomerFee);
                    transaction.ReceiveAmount = Convert.ToDecimal(tryGetEstimateRate.Data.PricePayment.ReceiveValue);
                    transaction.ReceiveCurrency = tryGetEstimateRate.Data.PricePayment.ReceiveCurrencyIso;
                    transaction.SendAmount = tryGetEstimateRate.Data.PricePayment.SendValue - Convert.ToDecimal(tryGetEstimateRate.Data.PricePayment.CustomerFee);
                }
            }
            // Set Transaction 
            transaction.SendCurrency = string.IsNullOrEmpty(transaction.SendCurrency) ? ConstantParam.DefaultCurrency : transaction.SendCurrency;

            //------------------------------------------------------------------------------------
            // Apply 'FEE' From settings
            //------------------------------------------------------------------------------------
            transaction.Fee = _mobileRechargeServiceSettings.FeeAmount;
            transaction.SendAmount = Convert.ToDecimal(transaction.SendAmount);

            //------------------------------------------------------------------------------------
            // Check for Discounts for this Product - Only for International Recharge
            //------------------------------------------------------------------------------------

            // Check Predefined Discounts for this User 
            decimal finalDiscountAmount = 0;
            if (thisProduct.CountryCode != ConstantParam.DefaultCountryCode && thisProduct.Benefits != MobileRechargeType.CALLINGCARDS.ToString())
            {
                var userDiscount = await _unitOfWork.MobileRechargeUserDiscounts
                .FirstOrDefaultAsync(a => a.UserId == user.Id && !a.IsUsed, a => a.Discount);

                if (userDiscount != null && !userDiscount.Discount.IsDeleted)
                {
                    switch (userDiscount.Discount.Type)
                    {
                        case MobileRechargeDiscountType.Percentage:
                            var totalAmount = transaction.Fee + transaction.SendAmount;
                            decimal calculatedAmount = Math.Round(((decimal)totalAmount * (userDiscount.Discount.DiscountValue / 100)) / 0.01m, MidpointRounding.AwayFromZero) * 0.01m;
                            finalDiscountAmount = Math.Min(calculatedAmount, userDiscount.Discount.CapValue);
                            break;
                        default:
                            break;
                    }
                    transaction.UserDiscount = userDiscount;
                    transaction.UserDiscountId = userDiscount.Id;
                    transaction.UserDiscount = userDiscount;
                    userDiscount.MobileRechargeTransactionId = transaction.Id;

                    // Mark User Discount as Used
                    userDiscount.IsUsed = true;
                    transaction.UserDiscount = userDiscount;
                }
                transaction.RechargeType = MobileRechargeType.INTERNATIONAL;
            }

            //------------------------------------------------------------------------------------
            // If Discounts - Then Applying it in following way
            // First the Discount will be deducted from Fee and then
            // further deduct from the Send Amount (Amount user have to Pay) 
            //------------------------------------------------------------------------------------ 
            // Set the Discount Amount for Transaction 
            transaction.DiscountAmount = finalDiscountAmount;
            transaction.DiscountCurrency = ConstantParam.DefaultCurrency;

            // Set total Amount 
            transaction.TotalAmount = (decimal)transaction.SendAmount + (decimal)transaction.Fee - (decimal)transaction.DiscountAmount;

            return new ServiceResponse<MobileRechargeTransaction>(transaction);
        }

        public async Task<ServiceResponse<CallingCardResponseDingModel>> SendTransferV2(User user, MobileRechargeTransaction mobileRechargeTransaction,
           MobileApplicationId mobileApplicationId, string phoneNumber = null, bool isAutoRenewalEnabled = false, bool isAutoRenewalJobExecution = false, bool hasDiscountOnRenewals = false)
        {
            MobileRechargeBeneficiary beneficiary = null;
            bool rechargeForMyself = false;

            // Check if user is trying to recharge their account.
            if (mobileRechargeTransaction.BeneficiaryId.HasValue && mobileRechargeTransaction.BeneficiaryId.Value == mobileRechargeTransaction.UserId)
            {
                mobileRechargeTransaction.RechargeType = MobileRechargeType.LOCAL;
                mobileRechargeTransaction.AccountNumber = phoneNumber;
                mobileRechargeTransaction.BeneficiaryId = null;
                rechargeForMyself = true;
            }
            else if (mobileRechargeTransaction.BeneficiaryId.HasValue)
            {
                //Read beneficiary
                beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == mobileRechargeTransaction.BeneficiaryId.Value
                                                                                    && x.Status == BaseEnums.Status.APPROVED);

                if (beneficiary == null)
                {
                    _logger.LogWarning($"Due to invalid beneficiary, mobile recharge transfer failed for {mobileRechargeTransaction.BeneficiaryId.Value}");

                    return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                }

                // check if beneficiary is cross-checked  
                if (beneficiary.IsDeleted == true && beneficiary.CrossCheckedBeneficiaryId != null)
                {
                    mobileRechargeTransaction.BeneficiaryId = beneficiary.CrossCheckedBeneficiaryId;
                    beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == beneficiary.CrossCheckedBeneficiaryId
                                                                                    && x.Status == BaseEnums.Status.APPROVED && !x.IsDeleted);

                    if (beneficiary == null)
                    {
                        _logger.LogWarning($"Due to invalid beneficiary, mobile recharge transfer failed for {mobileRechargeTransaction.BeneficiaryId.Value}");
                        return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.BeneficiaryNotExists.ToString());
                    }
                }
                mobileRechargeTransaction.RechargeType = beneficiary.RechargeType;
                mobileRechargeTransaction.AccountNumber = beneficiary.AccountNumber;
            }
            else
            {
                mobileRechargeTransaction.RechargeType = BaseEnums.MobileRechargeType.CALLINGCARDS;
            }


            var summaryResponse = await GetSummaryDetails(user, mobileRechargeTransaction.ProductCode, mobileApplicationId, mobileRechargeTransaction);
            if (!summaryResponse.IsSuccessful)
                return new ServiceResponse<CallingCardResponseDingModel>(false, summaryResponse.ErrorMessage);

            // Get Product From Db
            var thisProductCode = mobileRechargeTransaction.ProductCode;
            var passedProductCode = thisProductCode;
            thisProductCode = ExtractProductCodeForAdjustablePriceProduct(passedProductCode);
            mobileRechargeTransaction.Product = await _unitOfWork.MobileRechargeProducts.FirstOrDefaultAsync(a => a.Code == thisProductCode && a.IsActive == true);

            if (mobileRechargeTransaction.Product == null)
                return new ServiceResponse<CallingCardResponseDingModel>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());

            // Execute rates experiment if applicable
            var isRatesExperimentEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableRatesExperiment);
            bool isRatesExpInitiated = false;
            if (isRatesExperimentEnabled
                && TryValidateUserForRatesExperiment(user.CardHolderId, out string jsonConfig))
            {
                mobileRechargeTransaction = await ExecuteRatesExperiment(mobileRechargeTransaction, jsonConfig, out bool isRatesExpInit);
                isRatesExpInitiated = isRatesExpInit;
            }

            // Execute targeted discount experiment if applicable
            var isTargetedDiscountOfferEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableTargetedDiscount);
            bool isTargetedDiscExpInitiated = false;
            if (isTargetedDiscountOfferEnabled
                && mobileRechargeTransaction.RechargeType == MobileRechargeType.INTERNATIONAL
                && await HasTargetedDiscountOffer(user.Id))
            {
                //temp fix - only offer targeted discount to products that have fixed price
                var isFixedPrice = await IsFixedPriceProductAsync(thisProductCode);
                if (isFixedPrice)
                {
                    mobileRechargeTransaction = await ExecuteTargetedDiscExperiment(mobileRechargeTransaction);
                    isTargetedDiscExpInitiated = true;
                }

            }

            if (hasDiscountOnRenewals)
                mobileRechargeTransaction = await ExecuteTargetedDiscExperiment(mobileRechargeTransaction);


            //Checking product information 
            if (mobileRechargeTransaction.Product.Benefits == BaseEnums.MobileRechargeType.CALLINGCARDS.ToString() && mobileRechargeTransaction.BeneficiaryId.HasValue)
            {
                _logger.LogWarning($"Due to invalid beneficiary, mobile recharge transfer failed for {mobileRechargeTransaction.BeneficiaryId.Value}");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidBeneficiaryForCallingCards.ToString());
            }


            var cardNumber = user.CardHolder.CardNumber;
            var cardSerialNumber = user.CardHolder.CardSerialNumber;

            //Checking user information
            if (string.IsNullOrEmpty(cardNumber))
            {
                _logger.LogWarning($"Due to invalid card number, mobile recharge transfer failed for the user {user.CardHolder.FirstName} {user.CardHolder.LastName}");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidCardNumber.ToString());
            }

            else if (string.IsNullOrEmpty(cardSerialNumber))
            {
                _logger.LogWarning($"Due to invalid card serial number, mobile recharge transfer failed for the user {user.CardHolder.FirstName} {user.CardHolder.LastName} ");

                return new ServiceResponse<CallingCardResponseDingModel>(false, BaseEnums.RechargeStatusValidationMessage.InvalidCardSerialNumber.ToString());
            }

            //checking transaction limit
            var today = DateTime.Today;
            var beginningOfTheMonth = new DateTime(today.Year, today.Month, 1);
            var beginningOfNextMonth = beginningOfTheMonth.AddMonths(1);

            var rechargesAmountSum = await this._unitOfWork.MobileRechargeTransactions
                .DecimalSumAsync(record => record.UserId == mobileRechargeTransaction.UserId
                && record.CreatedDate >= beginningOfTheMonth
                && record.CreatedDate < beginningOfNextMonth
                && record.Status != BaseEnums.Status.FAILED, recharge => recharge.SendAmount);

            var totalAmountSum = rechargesAmountSum + mobileRechargeTransaction.SendAmount;

            var monthlyLimit = user.IsVerified ? this._mobileRechargeServiceSettings.MonthlyAmountLimitVerified : this._mobileRechargeServiceSettings.MonthlyAmountLimitNotVerified;

            if (totalAmountSum > monthlyLimit)
            {
                this._logger.LogWarning("Max amount reached for non verified user, previous recharge amount: {0} AED, total recharge amount: {1} AED, monthly limit {2} AED", rechargesAmountSum, totalAmountSum, monthlyLimit);

                return new ServiceResponse<CallingCardResponseDingModel>(false, user.IsVerified ? BaseEnums.RechargeStatusValidationMessage.RechargeAmountMonthlyLimitReached.ToString() : BaseEnums.RechargeStatusValidationMessage.RechargeAmountLimitReachedWithoutEmiratesId.ToString());
            }

            mobileRechargeTransaction.IsAutoRenewalEnabled = isAutoRenewalEnabled;

            var hasPendingRechargeTransaction = await this._unitOfWorkReadOnly.MobileRechargeTransactions.Any(x => x.UserId == mobileRechargeTransaction.UserId
                                                    && x.BeneficiaryId == mobileRechargeTransaction.BeneficiaryId
                                                    && x.ProductCode == mobileRechargeTransaction.ProductCode
                                                    && x.Status == BaseEnums.Status.PENDING
                                                    && x.IsDeferredRecharge == true);

            if (hasPendingRechargeTransaction)
            {
                return new ServiceResponse<CallingCardResponseDingModel>(false, ConstantParam.MobileRechagrePendingRetrial.ToString());
            }

            await _unitOfWork.MobileRechargeTransactions.AddAsync(mobileRechargeTransaction);

            await _unitOfWork.CommitAsync();

            var rechargeResponse = await this.AddExternalTransferTransaction(mobileRechargeTransaction, user, beneficiary, mobileApplicationId, rechargeForMyself, isRatesExpInitiated, isTargetedDiscExpInitiated, isAutoRenewalJobExecution, hasDiscountOnRenewals);

            // Send successful-cleavertap event for rates experiment transaction
            if (mobileRechargeTransaction.Status == Status.SUCCESSFUL
                && isRatesExperimentEnabled
                && isRatesExpInitiated)
            {
                await _analyticsPublisherService.PublishMobileRechargeSucessEvent(mobileRechargeTransaction.User.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeEvent
                {
                    UserId = mobileRechargeTransaction.UserId,
                    ProductCode = mobileRechargeTransaction.ProductCode
                });
            }

            // Send failed-cleavertap event for rates experiment transaction
            if ((mobileRechargeTransaction.Status == Status.FAILED || mobileRechargeTransaction.Status == Status.CANCELED
                || mobileRechargeTransaction.Status == Status.REJECTED)
                && isRatesExperimentEnabled
                && isRatesExpInitiated)
            {
                await _analyticsPublisherService.PublishMobileRechargeFailedEvent(mobileRechargeTransaction.User.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeEvent
                {
                    UserId = mobileRechargeTransaction.UserId,
                    ProductCode = mobileRechargeTransaction.ProductCode
                });
            }

            //Send failed MR recharge event
            if (mobileRechargeTransaction.Status == Status.FAILED)
            {
                await _analyticsPublisherService.PublishMobileRechargeFailureEvent(mobileRechargeTransaction.User.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeFailureEvent
                {
                    UserId = mobileRechargeTransaction.UserId,
                    ProductCode = mobileRechargeTransaction.ProductCode,
                    ErrorReason = string.Join("-", mobileRechargeTransaction.Remarks, mobileRechargeTransaction.ErrorContext)
                });
            }

            if (!rechargeResponse.IsSuccessful)
            {
                return new ServiceResponse<CallingCardResponseDingModel>(false, rechargeResponse.ErrorMessage);
            }

            //if isTargetedDiscExpInitiated == true and isAutoRenewalJobExecution == false
            //UpdateRenewal to tag it as  HasClaimedTargetedDiscount
            bool claimedTargetedDiscount = isTargetedDiscExpInitiated && !isAutoRenewalJobExecution;

            //Activate Auto Renewal
            if (isAutoRenewalEnabled && mobileRechargeTransaction.Status == BaseEnums.Status.SUCCESSFUL)
            {
                await UpdateRenewal(userId: mobileRechargeTransaction.UserId,
                                   productCode: mobileRechargeTransaction.ProductCode,
                                   beneficiaryId: mobileRechargeTransaction.BeneficiaryId.Value,
                                   cardHolderId: mobileRechargeTransaction.User.CardHolderId,
                                   deactivationCode: null,
                                   IsActive: true,
                                   hasClaimedTargetedDiscount: claimedTargetedDiscount);
            }


            //If success response.
            return new ServiceResponse<CallingCardResponseDingModel>(rechargeResponse.Data);
        }

        public async Task<ServiceResponse<ValidateSendTransferResponseDto>> ValidateSendTransferAsync(
                 MobileRechargeTransaction request,
                 MobileApplicationId mobileApplicationId)
        {

            if (request is null)
                return new ServiceResponse<ValidateSendTransferResponseDto>(false, "Bad request");

            if (request.ProductCode == null || !request.BeneficiaryId.HasValue)
                return new ServiceResponse<ValidateSendTransferResponseDto>(false, "Bad request");

            var productCacheKey = $"mobile_recharge_product_{request.ProductCode}";

            var cachedProducts = await _cache.GetRecordAsync<MobileRechargeProduct>(productCacheKey);
            MobileRechargeProduct product;

            if (cachedProducts != null)
            {
                _logger.LogDebug("Cache hit for product code: {ProductCode}", request.ProductCode);
                product = cachedProducts;
            }
            else
            {
                _logger.LogDebug("Cache miss for product code: {ProductCode}. Fetching from database.", request.ProductCode);

                product = (await _unitOfWork.MobileRechargeProducts
                    .FirstOrDefaultAsync(p => p.Code == request.ProductCode));

                if (product != null)
                {
                    await _cache.SetRecordAsync(productCacheKey, product,
                        absoluteExpiryTime: TimeSpan.FromMinutes(30),
                        unusedExpiryTime: TimeSpan.FromMinutes(10));

                    _logger.LogDebug("Cached products for product code: {ProductCode}", request.ProductCode);
                }
            }

            if (product is null)
                return new ServiceResponse<ValidateSendTransferResponseDto>(false, "Product not found.");

            var beneficiaryAccountNumberCacheKey = $"mobile_recharge_beneficiary_account_number_{request.BeneficiaryId.Value}";
            var cachedBeneficiaryAccountNumber = await _cache.GetRecordAsync<string>(beneficiaryAccountNumberCacheKey);
            if (string.IsNullOrEmpty(cachedBeneficiaryAccountNumber) == false)
            {
                _logger.LogInformation("Cache hit for beneficiary ID: {BeneficiaryId}", request.BeneficiaryId.Value);
            }
            else
            {
                _logger.LogInformation("Cache miss for beneficiary ID: {BeneficiaryId}. Fetching from database.", request.BeneficiaryId.Value);

                var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(b => b.Id == request.BeneficiaryId.Value);

                _logger.LogInformation("DB call done to mobileRechargeBeneficiaries");

                if (beneficiary != null)
                {
                    cachedBeneficiaryAccountNumber = beneficiary.AccountNumber;
                    await _cache.SetRecordAsync(beneficiaryAccountNumberCacheKey, cachedBeneficiaryAccountNumber,
                        absoluteExpiryTime: TimeSpan.FromMinutes(15),
                        unusedExpiryTime: TimeSpan.FromMinutes(5));

                    _logger.LogInformation("Cached beneficiaries for beneficiary ID: {BeneficiaryId}", request.BeneficiaryId.Value);
                }
            }

            if (string.IsNullOrEmpty(cachedBeneficiaryAccountNumber))
            {
                _logger.LogInformation("Exiting ValidateSendTransferAsync: Beneficiary not found. BeneficiaryId={BeneficiaryId}", request.BeneficiaryId.Value);
                return new ServiceResponse<ValidateSendTransferResponseDto>(false, "Beneficiary not found.");
            }

            var validateTransferTestingEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableValidateTransferTesting);

            if (_mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.UAT.ToString() && validateTransferTestingEnabled && !string.IsNullOrWhiteSpace(product.UATNumber))
            {
                cachedBeneficiaryAccountNumber = product.UATNumber;
            }

            var dingResponse = new ServiceResponse<SendTransferResponseDingModel>();

            if (cachedBeneficiaryAccountNumber == "************" || (cachedBeneficiaryAccountNumber == "************" && _mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.LIVE.ToString()))
            {
                dingResponse = new ServiceResponse<SendTransferResponseDingModel>
                {
                    IsSuccessful = false,
                    ErrorMessage = "AccountNumberInvalid###ProviderRefusedRequest"
                };
            }
            else if (cachedBeneficiaryAccountNumber == "************"|| (cachedBeneficiaryAccountNumber == "************" && _mobileRechargeServiceSettings.TransactionEnvironment == BaseEnums.TransactionEnvironment.LIVE.ToString()))
            {
                dingResponse = new ServiceResponse<SendTransferResponseDingModel>
                {
                    IsSuccessful = false,
                    ErrorMessage = "ProviderError###ProviderRefusedRequest"
                };
            }
            else
            {
                var dingRequest = new SendTransferRequestDingModel
                {
                    ProductCode = product.Code,
                    SendValue = product.MinSendValue ?? 0m,
                    SendCurrencyIso = product.MinSendCurrencyIso ?? "AED",
                    AccountNumber = cachedBeneficiaryAccountNumber,
                    TransferReferenceId = TypeUtility.GetReferenceNumber(mrTransacationPrefix.ToString(), 12),
                    BatchItemReference = true,
                    BillReference = "MyC3Card"
                };

                dingResponse = await _dingService.SendTransfer(
                   dingRequest, (Enums.MobileApplicationId)mobileApplicationId);
            }



            if (dingResponse.IsSuccessful)
            {
                var dto = new ValidateSendTransferResponseDto
                {
                    ValidateSuccess = true,
                    ValidationMessage = "Transaction validated successfully."
                };

                return new ServiceResponse<ValidateSendTransferResponseDto>(true, dto.ValidationMessage)
                {
                    Data = dto
                };
            }

            // Safe-parse Ding error
            var error = dingResponse.ErrorMessage ?? string.Empty;
            var parts = error.Split(new[] { "###" }, StringSplitOptions.None);
            var errorCode = parts.ElementAtOrDefault(0) ?? string.Empty;
            var errorContext = parts.ElementAtOrDefault(1) ?? string.Empty;

            // AccountNumberInvalid###ProviderRefusedRequest
            if (errorCode == "AccountNumberInvalid" && errorContext == "ProviderRefusedRequest")
            {
                var dto = new ValidateSendTransferResponseDto
                {
                    ValidateSuccess = false,
                    ValidationMessage = "AccountNumberInvalid"
                };

                return new ServiceResponse<ValidateSendTransferResponseDto>(false, dto.ValidationMessage)
                {
                    Data = dto
                };
            }

            // ProviderError###ProviderRefusedRequest
            if (errorCode == "ProviderError" && errorContext == "ProviderRefusedRequest")
            {
                //var videos = await _unitOfWork.Mult
                var dto = new ValidateSendTransferResponseDto
                {
                    ValidateSuccess = false,
                    ValidationMessage = "ProviderError",
                    ScreenContent = new ScreenContentDto
                    {
                        Title = "Please change your pack",
                        SubTitle = "This pack cannot be used with the selected number. Please choose a different pack",
                        PrimaryButton = new ButtonDto { Text = "Change Pack" },
                        SecondaryButton = new ButtonDto { Text = "Need Help?" }
                    },
                };

                return new ServiceResponse<ValidateSendTransferResponseDto>(false, dto.ValidationMessage)
                {
                    Data = dto
                };
            }

            // Fallback
            {
                var dto = new ValidateSendTransferResponseDto
                {
                    ValidateSuccess = false,
                    ValidationMessage = "Ding validation failed"
                };

                return new ServiceResponse<ValidateSendTransferResponseDto>(false, dto.ValidationMessage)
                {
                    Data = dto
                };
            }
        }

        public async Task<ServiceResponse> UpdateOperator(Guid beneficiaryId)
        {
            if (beneficiaryId == Guid.Empty)
                return new ServiceResponse(false, "Invalid request ID.");

            var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.GetByIdAsync(beneficiaryId);
            if (beneficiary is null)
                return new ServiceResponse(false, "Beneficiary not found.");

            var accountNumber = beneficiary.AccountNumber?.Trim();
            if (string.IsNullOrWhiteSpace(accountNumber))
                return new ServiceResponse(false, "Beneficiary account number is missing.");

            var productsResp = await _dingService.GetProductsByAccountNumber(accountNumber);
            if (!productsResp.IsSuccessful)
                return new ServiceResponse(false, productsResp.ErrorMessage ?? "Failed to retrieve products.");

            var products = productsResp.Data?.ToList() ?? new List<ProductResponseDingModel>();

            var providerCodes = products
                .Select(p => p?.ProviderCode)
                .Where(code => !string.IsNullOrWhiteSpace(code))
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .ToList();

            if (providerCodes.Count == 0)
                return new ServiceResponse(false, "No provider code found.");

            if (providerCodes.Count > 1)
                return new ServiceResponse(false, "Multiple provider codes found. Unable to determine the correct one.");

            var chosenProviderCode = providerCodes.First();

            var beneficiaryProvider = await _unitOfWork.MobileRechargeBeneficiaryProviders.FirstOrDefaultAsync(x => x.BeneficiaryId == beneficiaryId && x.IsActive == true);
            if (beneficiaryProvider is null)
            {
                return new ServiceResponse(false, "Beneficiary provider record not found.");
            }

            if (beneficiaryProvider.ProviderCode.ToUpper() != chosenProviderCode.ToUpper())
            {
                var codeAvailable = await this._unitOfWork.MobileRechargeProviders.FirstOrDefaultAsync(x => x.Code == chosenProviderCode);
                if (codeAvailable is null)
                {
                    return new ServiceResponse(false, "No provider code found.");
                }
                else
                {
                    // disabled the old one.
                    beneficiaryProvider.IsActive = false;
                    beneficiaryProvider.IsDeleted = true;

                    var mobileRechargeBeneficiaryProvider = new MobileRechargeBeneficiaryProvider()
                    {
                        BeneficiaryId = beneficiaryId,
                        ProviderCode = chosenProviderCode,
                        IsActive = true,
                        IsDeleted = false,
                    };

                    await this._unitOfWork.MobileRechargeBeneficiaryProviders.AddAsync(mobileRechargeBeneficiaryProvider);
                    await this._unitOfWork.CommitAsync();
                }
            }
            return new ServiceResponse(true, "Operator updated successfully.");
        }



        public async Task<ServiceResponse<bool>> CheckForBeneficiaryModification(Guid userId, Guid beneficiaryId)
        {
            var isFailed = await _unitOfWork.MobileRechargeTransactions.CheckLastTransactionFailedWithErrorAsync(userId, beneficiaryId, "ProviderUnknownError");

            return new ServiceResponse<bool>(isFailed);
        }

        #region NCell (Nepal Provider) - Dynamic Changes 

        private decimal RoundToNearest(decimal number, decimal multiple)
        {
            return Math.Round(number * (1 / multiple)) / (1 / multiple);
        }

        private List<MobileRechargeProduct> ConvertToDynamicPackages(IList<MobileRechargeProduct> products, MobileRechargeProduct thisProduct)
        {
            products = products == null ? new List<MobileRechargeProduct>() : products;
            double exchangeRate = (double)thisProduct.MaxSendValue / (double)thisProduct.MaxReceiveValue;
            List<double> dynamicRange = FetchDynamicPriceRange((double)thisProduct.MinReceiveValue, (double)thisProduct.MaxReceiveValue);

            if (dynamicRange.Any())
            {
                if (products != null && products.Count() > 0)
                    products.RemoveAt(products.IndexOf(thisProduct) >= 0 && products.IndexOf(thisProduct) < products.Count ? products.IndexOf(thisProduct) : -1);

                for (int i = 0; i < dynamicRange.Count; i++)
                {
                    var thisMinReceiveValue = (decimal)dynamicRange[i];
                    var thisMinReceiveValueExcludingTax = ((decimal)dynamicRange[i]);
                    var thisMaxSendValue = thisMinReceiveValue * (decimal)exchangeRate;
                    var thisMaxReceiveValue = thisMinReceiveValue;

                    var _newProduct = new MobileRechargeProduct()
                    {
                        Code = $"{thisProduct.Code}{this._mobileRechargeServiceSettings.DynamicPackageSeperator}{i.ToString()}",
                        ProviderCode = thisProduct.ProviderCode,
                        CountryCode = thisProduct.CountryCode,
                        LocalizationKey = $"{thisProduct.Code}{this._mobileRechargeServiceSettings.DynamicPackageSeperator}{i.ToString()}",
                        MinCustomerFee = thisProduct.MinCustomerFee,
                        MinDistributorFee = thisProduct.MinDistributorFee,
                        MinReceiveValue = RoundToNearest(thisMinReceiveValue, 0.05m),
                        MinReceiveCurrencyIso = thisProduct.MinReceiveCurrencyIso,
                        MinReceiveValueExcludingTax = RoundToNearest(thisMinReceiveValueExcludingTax, 0.05m),
                        MinTaxRate = thisProduct.MinTaxRate,
                        MinTaxName = thisProduct.MinTaxName,
                        MinTaxCalculation = thisProduct.MinTaxCalculation,
                        MinSendValue = thisProduct.MinSendValue,
                        MinSendCurrencyIso = thisProduct.MinSendCurrencyIso,
                        MaxCustomerFee = thisProduct.MaxCustomerFee,
                        MaxDistributorFee = thisProduct.MaxDistributorFee,
                        MaxReceiveValue = RoundToNearest(thisMaxReceiveValue, 0.05m),
                        MaxReceiveCurrencyIso = thisProduct.MaxReceiveCurrencyIso,
                        MaxReceiveValueExcludingTax = RoundToNearest(thisMaxReceiveValue, 0.05m),
                        MaxTaxRate = thisProduct.MaxTaxRate,
                        MaxTaxName = thisProduct.MaxTaxName,
                        MaxTaxCalculation = thisProduct.MaxTaxCalculation,
                        MaxSendValue = RoundToNearest(thisMaxSendValue, 0.05m),
                        MaxSendCurrencyIso = thisProduct.MaxSendCurrencyIso,
                        CommissionRate = thisProduct.CommissionRate,
                        ProcessingMode = thisProduct.ProcessingMode,
                        RedemptionMechanism = thisProduct.RedemptionMechanism,
                        Benefits = thisProduct.Benefits,
                        ValidityPeriodIso = thisProduct.ValidityPeriodIso,
                        UATNumber = thisProduct.UATNumber,
                        AdditionalInformation = thisProduct.AdditionalInformation,
                        DefaultDisplayText = $"{thisProduct.MaxReceiveCurrencyIso} {RoundToNearest(thisMaxReceiveValue, 0.05m)}",
                        RegionCode = thisProduct.RegionCode,
                        LookupBillsRequired = thisProduct.LookupBillsRequired,
                        CustomFee = thisProduct.CustomFee,
                        IsActive = thisProduct.IsActive,
                        LastUpdatedDate = DateTime.Now,
                        Provider = thisProduct.Provider,
                        SupportedCountry = thisProduct.SupportedCountry
                    };

                    products.Add(_newProduct);
                    products = products.OrderBy(a => a.MaxReceiveValue).ToList();
                }
            }

            return products.ToList();
        }

        private List<double> FetchDynamicPriceRange(double start, double end)
        {
            int numberOfElements = 15;

            double range = end - start;
            double[] percentages = { 0.05, 0.05, 0.05, 0.05, 0.05, 0.10, 0.10, 0.10, 0.10, 0.10, 0.15, 0.15, 0.15, 0.15, 0.15 };

            List<double> series = new List<double>(numberOfElements) { start + 40 };

            double remainingRange = range;
            double remainingElements = numberOfElements - 1;

            for (int i = 0; i < numberOfElements - 1; i++)
            {
                double targetPercentage = (percentages[0] - percentages[percentages.Length - 1]) * remainingElements / (numberOfElements - 1) + percentages[percentages.Length - 1];
                int difference = (int)Math.Round(remainingRange * targetPercentage);

                double currentNumber = series[i] + difference;
                series.Add(currentNumber);

                remainingRange -= difference;
                remainingElements--;
            }

            series[numberOfElements - 1] = end; // Set the last element to the end number

            return series;
        }

        private string ExtractProductCodeForAdjustablePriceProduct(string productCode)
        {
            bool isDynamicProduct = productCode.IndexOf(this._mobileRechargeServiceSettings.DynamicPackageSeperator) >= 0;
            if (isDynamicProduct)
            {
                int delimiterIndex = productCode.IndexOf(this._mobileRechargeServiceSettings.DynamicPackageSeperator);
                productCode = delimiterIndex >= 0 ? productCode.Substring(0, delimiterIndex) : productCode;
            }
            return productCode;
        }

        private async Task<ServiceResponse<MobileRechargeProduct>> GetProductByIdAsync(string productCode)
        {
            var product = await _unitOfWork.MobileRechargeProducts.GetByIdAsync(productCode);
            if (product != null)
            {
                if (product.CustomFee is null) { product.CustomFee = 0; }
                product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
                return new ServiceResponse<MobileRechargeProduct>(product);
            }

            return new ServiceResponse<MobileRechargeProduct>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());
        }

        public async Task<ServiceResponse<ProductEstimatePriceDingModel>> GetProductEstimateRateV2(MobileRechargeProduct product)
        {
            var tryGetEstimatePrice = await _dingService.GetEstimatePriceForProduct(product.Code, product.MaxSendValue.GetValueOrDefault(), product.MaxSendCurrencyIso);
            if (tryGetEstimatePrice.IsSuccessful == false)
            {
                _logger.LogWarning($"ERROR:Provider-Api. ErrorMessage: {tryGetEstimatePrice.ErrorMessage}");
                return new ServiceResponse<ProductEstimatePriceDingModel>(false, RechargeStatusValidationMessage.DingConnectionIssue.ToString());
            }

            var estimatePrice = tryGetEstimatePrice.Data.Data;
            if (tryGetEstimatePrice.IsSuccessful && estimatePrice[0].PricePayment == null)
            {
                _logger.LogWarning($"ERROR:Provider-Api. Key: Code = '{product.Code}'. ErrorMessage: Empty response.");
                return new ServiceResponse<ProductEstimatePriceDingModel>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());
            }

            return new ServiceResponse<ProductEstimatePriceDingModel>(estimatePrice[0]);
        }

        /// <summary>
        /// Get Local Calling Card Products
        /// </summary>
        /// <param name="c3Pay"></param>
        /// <returns></returns>
        public async Task<List<MobileRechargeProduct>> GetLocalCallingCardProductsAsync(CancellationToken cancellationToken)
        {
            var products = await _unitOfWork.MobileRechargeProducts.GetLocalCallingCardOperatorsAsync(BaseEnums.CallingCardOperator.VRAE.ToString(), cancellationToken);

            // --- Apply Fee - For Local, International & Calling Cards - Same Fee (From App Settings)
            // -------------------------------------------------------------------------------------------------------------
            products.ForEach(product =>
            {
                product.CustomFee ??= 0;
                product.CustomFee += this._mobileRechargeServiceSettings.FeeAmount;
            });

            return products;
        }
        #endregion 
        public async Task<ServiceResponse<string>> CrossCheckBeneficiaryForFailedTransaction(Guid transactionId, Guid beneficiaryId,
                                        string beneficiaryCountryCode, string externalErrorMessage)
        {
            var crossCheckBeneficiaryEnabled = await this._featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableCrossCheckBeneficiary,
                                    new MRCrossCheckBeneficiaryContext() { CountryCode = beneficiaryCountryCode, ExternalErrorMessage = externalErrorMessage });

            if (crossCheckBeneficiaryEnabled)
            {
                _logger.LogWarning($"MR Beneficiary - Cross Check Entered for {beneficiaryId.ToString()}");
                //---- Fetch Transaction 
                var thisTransaction = await _unitOfWork.MobileRechargeTransactions.GetTransactionAsync(transactionId);
                if (thisTransaction == null)
                    return new ServiceResponse<string>(false, "NoTransactionFoundForThisBeneficiary");
                if (thisTransaction.Status != Status.FAILED && thisTransaction.Status == Status.SUCCESSFUL)
                    return new ServiceResponse<string>(false, "LastTransactionIsSuccessful");

                //---- Fetch Existing Beneficiary 
                if (beneficiaryId == null)
                    return new ServiceResponse<string>(false, "InvalidBeneficiary");

                var existingBeneficiary = await _unitOfWork.MobileRechargeBeneficiaries.GetBeneficiaryAsync(beneficiaryId);
                if (existingBeneficiary == null)
                    return new ServiceResponse<string>(false, "InvalidBeneficiary");
                if (existingBeneficiary.CrossCheckedBeneficiaryId != null)
                    return new ServiceResponse<string>(false, "AlreadyCrossChecked");

                //----------------------------------------------------------------------------------
                // If Last Transaction is Failed - Call GetAccountLookup (External API) - Delete Existing 
                // Beneficiary - Add new Beneficiary with same details but with Provider we get from 
                // GetAccountLookup() - If both Providers are same then no action needed.
                //----------------------------------------------------------------------------------
                string phoneNumber = existingBeneficiary.AccountNumber.StartsWith("00") ? existingBeneficiary.AccountNumber.Substring(2) : existingBeneficiary.AccountNumber;
                var externalAccountLookupResult = await _dingService.GetAccountLookup(phoneNumber);
                if (externalAccountLookupResult.IsSuccessful == false ||
                            (externalAccountLookupResult.IsSuccessful && (externalAccountLookupResult.Data == null ||
                            (externalAccountLookupResult.Data != null && externalAccountLookupResult.Data.Data.Count == 0))))
                    return new ServiceResponse<string>(false, "ExternalGetAccountLookupFailed");

                var externalProviderCodes = externalAccountLookupResult.Data.Data.Select(p => p.ProviderCode).ToList();
                var existingProviderCode = existingBeneficiary.BeneficiaryProviders.Select(a => a.ProviderCode).FirstOrDefault();

                //--- Only if existing Beneficiary doesn't have this External Provider Code
                if (!externalProviderCodes.Contains(existingProviderCode))
                {
                    //--- Delete Existing Benficiary (Soft Delete) & Create new Beneficiary with Same Details 
                    string actionResult = await _unitOfWork.MobileRechargeBeneficiaries.MarkAsDeletedAndCreateNewAsync(existingBeneficiary.Id, externalProviderCodes.FirstOrDefault());
                    if (actionResult == "success")
                    {
                        return new ServiceResponse<string>($"MR Beneficiary - Delete And Create for {existingBeneficiary.Id.ToString()}");
                    }
                    else
                    {
                        _logger.LogError($"MR Beneficiary - Delete And Create FAILED for {existingBeneficiary.Id.ToString()}");
                        return new ServiceResponse<string>(false, $"MR Beneficiary - Delete And Create FAILED for {existingBeneficiary.Id.ToString()} with error: {actionResult}");
                    }
                }
                else
                {
                    _logger.LogWarning($"MR Beneficiary - Already Mapped with correct Provider for {existingBeneficiary.Id.ToString()}");
                    return new ServiceResponse<string>(false, $"MR Beneficiary - Already Mapped with correct Provider for {existingBeneficiary.Id.ToString()}");
                }
            }
            return new ServiceResponse<string>(false, $"BeneficiaryCrossChecked_FeatureFlagOffForBeneficiary_{beneficiaryId.ToString()}");
        }

        public async Task<ServiceResponse<bool>> CheckForMobileRechargeDiscount(Guid userId)
        {
            var isExists = await _unitOfWork.MobileRechargeUserDiscounts.Any(a => a.UserId == userId && !a.IsUsed);
            return new ServiceResponse<bool>(isExists);
        }

        public async Task<ServiceResponse<bool>> UpdateRenewal(Guid userId, string productCode, Guid beneficiaryId, string cardHolderId, MobileRecharge_AutoRenewalInactiveCode? deactivationCode, bool IsActive = true, bool hasClaimedTargetedDiscount = false)
        {
            var isAutoRenewalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewal);
            if (!isAutoRenewalEnabled)
            {
                return new ServiceResponse<bool>(false, RechargeStatusValidationMessage.AutoRenewalDisabled.ToString());
            }

            var isTargetedRenewalDiscountEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewalTargetedDiscount);

            //Exclude users that are part of Rates Experiment
            if (TryValidateUserForRatesExperiment(cardHolderId, out string jsonConfig)
                && await IsProductPartOfTheRatesExp(productCode, jsonConfig))
            {
                return new ServiceResponse<bool>(false, RechargeStatusValidationMessage.AutoRenewalDisabledForUser.ToString());
            }

            // Retrieve the product by product code
            var product = await _unitOfWorkReadOnly.MobileRechargeProducts.GetByIdNoActiveFilterAsync(productCode);
            if (product == null || product.IsAutoRenewalEnabled != true)
            {
                _logger.LogError($"MobileRechargeAutorenewal: Invalid Product for: {userId}");
                return new ServiceResponse<bool>(false, RechargeStatusValidationMessage.ProductNotExists.ToString());
            }

            // Check if there is already an active renewal
            var activeRenewal = await _unitOfWork.MobileRechargeRenewals.GetActiveRenewal(userId, beneficiaryId, productCode);

            if (activeRenewal != null && !IsActive)
            {
                //Auto Renewal Deactivation
                activeRenewal.IsActive = false;
                activeRenewal.InactiveCode = deactivationCode.HasValue ? (int)deactivationCode : (int)MobileRecharge_AutoRenewalInactiveCode.DeactivatedByUser;
                activeRenewal.IsDeleted = true;

                // Stop low balance checks.
                activeRenewal.IsCheckingForLowBalanceRenewal = null;

                activeRenewal.MarkAsUpdated();
            }
            else if (activeRenewal != null && IsActive)
            {
                //increment renewal date
                activeRenewal.RenewalDate = CalculateRenewalDate(product.ValidityPeriodIso);


                // Stop low balance checks.
                activeRenewal.IsCheckingForLowBalanceRenewal = null;
                activeRenewal.LowBalanceRetriesFailedCount = null;
                activeRenewal.LastLowBalanceRetryOn = null;

                activeRenewal.MarkAsUpdated();
            }
            else
            {
                var renewalRecord = new MobileRechargeRenewal
                {
                    UserId = userId,
                    BeneficiaryId = beneficiaryId,
                    ProductCode = productCode,
                    IsActive = true,
                    RenewalDate = CalculateRenewalDate(product.ValidityPeriodIso),
                    HasDiscountOnRenewals = isTargetedRenewalDiscountEnabled ? hasClaimedTargetedDiscount : false,
                };

                // Add the renewal record to the user
                await _unitOfWork.MobileRechargeRenewals.AddAsync(renewalRecord);
            }

            await _unitOfWork.CommitAsync();

            return new ServiceResponse<bool>(true);
        }

        public async Task<ServiceResponse<List<MobileRechargeRenewal>>> GetAllActivateRenewals(User user)
        {
            // Proceeds only if the feature flag is on.
            var isAuotRenewalEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewal);
            if (!isAuotRenewalEnabled)
            {
                return new ServiceResponse<List<MobileRechargeRenewal>>();
            }

            var activeRenewals = await _unitOfWork.MobileRechargeRenewals.GetAllActiveRenewals(user.Id);

            return new ServiceResponse<List<MobileRechargeRenewal>>(activeRenewals);
        }

        public async Task<ServiceResponse<bool>> ProcessScheduledRenewals(bool isTargetedRenewalDiscountEnabled)
        {
            var currentDate = DateTime.Now;

            // If feature "MobileRecharge_RetryDueToLowBalance" is enabled,
            // then we need to not retrive renewals marked for low balance retry.
            var retryDueToLowBalanceEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRecharge_RetryDueToLowBalance);
            var scheduledRenewals = await _unitOfWork.MobileRechargeRenewals.GetScheduledRenewals(currentDate, retryDueToLowBalanceEnabled);


            foreach (var scheduledRenewal in scheduledRenewals)
            {
                try
                {
                    var user = await _unitOfWork.Users.FirstOrDefaultAsync(record => record.Id == scheduledRenewal.UserId && record.ApplicationId == MobileApplicationId.C3Pay && !record.IsDeleted, record => record.CardHolder);

                    if (user is null)
                    {
                        // Continue for this renewal because the user is not found.
                        // We should also consider deleting the renewal record for this user.
                        continue;
                    }

                    var newTransaction = new MobileRechargeTransaction()
                    {
                        ProductCode = scheduledRenewal.ProductCode,
                        UserId = user.Id,
                        BeneficiaryId = scheduledRenewal.BeneficiaryId
                    };
                    //do recharge, set for next auto renewal
                    var isAutoRenewalEnabled = true;
                    var isAutoRenewalJobExecution = true;

                    bool hasDiscountOnRenewals = isTargetedRenewalDiscountEnabled && (scheduledRenewal.HasDiscountOnRenewals ?? false);

                    var sendTransfer = await SendTransferV2(user, newTransaction, BaseEnums.MobileApplicationId.C3Pay, user.PhoneNumber, isAutoRenewalEnabled, isAutoRenewalJobExecution, hasDiscountOnRenewals);
                    if (!sendTransfer.IsSuccessful)
                    {
                        var product = await _unitOfWorkReadOnly.MobileRechargeProducts.GetByIdNoActiveFilterAsync(scheduledRenewal.ProductCode);

                        var errorMessage = sendTransfer.ErrorMessage;

                        var deactivationCode = errorMessage.Contains(RechargeStatusValidationMessage.InsufficientBalance.ToString())
                                            ? MobileRecharge_AutoRenewalInactiveCode.CancelledDueToLowBalance
                                            : MobileRecharge_AutoRenewalInactiveCode.CancelledDueToPackUnavailability;

                        bool dueToLowBalance = (deactivationCode == MobileRecharge_AutoRenewalInactiveCode.CancelledDueToLowBalance);


                        if (dueToLowBalance == true && retryDueToLowBalanceEnabled == true)
                        {
                            // Here, we need to mark this renewal for low balance check.
                            scheduledRenewal.IsCheckingForLowBalanceRenewal = true;
                            await this._unitOfWork.CommitAsync();

                            // Send confirmation SMS.
                            await _textMessageSenderService.SendMRLowBalanceStartSms(user.PhoneNumber.ToZeroPrefixedPhoneNumber(), user.CardHolder.FirstName);
                        }

                        else
                        {
                            var failureReason = MapErrorToFailureReason(errorMessage);

                            _logger.LogInformation("MR Auto-renewal failed for user {UserId}, product {ProductCode}. Original error: {ErrorMessage}, Mapped failure reason: {FailureReason}",
                                user.Id, product.Code, errorMessage, failureReason);

                            var eventData = new MRAutoRenewalFailureEvent()
                            {
                                UserId = user.Id,
                                ProductCode = product.Code,
                                FailureReason = failureReason,
                            };

                            await UpdateRenewal(user.Id, scheduledRenewal.ProductCode, scheduledRenewal.BeneficiaryId, user.CardHolderId, deactivationCode, false);
                            await _textMessageSenderService.SendMRAutoRenewalDeactivationMessage(user.PhoneNumber.ToZeroPrefixedPhoneNumber(), dueToLowBalance, product.DefaultDisplayText);
                            await _analyticsPublisherService.PublishMRAutoRenewalFailureEvent(user.CardHolderId, eventData);
                        }
                    }

                }
                catch (Exception exception)
                {
                    _logger.LogError(ConstantParam.MobileRechargeAutoRenewalFailure, scheduledRenewal.Id, exception.Message.ToString());
                }

            }
            return new ServiceResponse<bool>(true);
        }

        private string MapErrorToFailureReason(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return "Failure_unknownerror";

            // Map each RechargeStatusValidationMessage enum value to appropriate failure reason
            if (errorMessage.Contains(RechargeStatusValidationMessage.InsufficientBalance.ToString()))
                return "Failure_insufficientfunds";

            if (errorMessage.Contains(RechargeStatusValidationMessage.ProductNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ProductCodeNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ProductsNotExistsForBeneficiary.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.InvalidProductCode.ToString()))
                return "Failure_unavailablepack";

            if (errorMessage.Contains(RechargeStatusValidationMessage.DingConnectionIssue.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.DingResponseFailed.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.PPSConnectionIssue.ToString()))
                return "Failure_serviceunavailable";

            if (errorMessage.Contains(RechargeStatusValidationMessage.UserBlocked.ToString()))
                return "Failure_userblocked";

            if (errorMessage.Contains(RechargeStatusValidationMessage.BeneficiaryNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.InvalidBeneficiaryForCallingCards.ToString()))
                return "Failure_beneficiarynotfound";

            if (errorMessage.Contains(RechargeStatusValidationMessage.TransactionIsInProgress.ToString()))
                return "Failure_transactioninprogress";

            if (errorMessage.Contains(RechargeStatusValidationMessage.InvalidCardNumber.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.InvalidCardSerialNumber.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ActivateYourCard.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.UnblockYourCard.ToString()))
                return "Failure_cardinvalid";

            if (errorMessage.Contains(RechargeStatusValidationMessage.RechargeAmountMonthlyLimitReached.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.RechargeAmountLimitReachedWithoutEmiratesId.ToString()))
                return "Failure_limitreached";

            if (errorMessage.Contains(RechargeStatusValidationMessage.InvalidPhoneNumber.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.phoneNumberNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ProviderNotExistsforPhoneNumber.ToString()))
                return "Failure_invalidphonenumber";

            if (errorMessage.Contains(RechargeStatusValidationMessage.CountryNotSupported.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.CountryNotExists.ToString()))
                return "Failure_countrynotsupported";

            if (errorMessage.Contains(RechargeStatusValidationMessage.UserNotExists.ToString()))
                return "Failure_usernotfound";

            if (errorMessage.Contains(RechargeStatusValidationMessage.EmiratesIdNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.EmiratesIdExpired.ToString()))
                return "Failure_emiratesidissue";

            if (errorMessage.Contains(RechargeStatusValidationMessage.OperatorNotExists.ToString()))
                return "Failure_operatornotexists";

            if (errorMessage.Contains(RechargeStatusValidationMessage.RechargeTypeNotExists.ToString()))
                return "Failure_invalidrechargetype";

            if (errorMessage.Contains(RechargeStatusValidationMessage.SendCurrencyNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ReceiveCurrencyNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.SendAmountNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ReceiveAmountNotExists.ToString()))
                return "Failure_currencyamountissue";

            if (errorMessage.Contains(RechargeStatusValidationMessage.TransactionIdNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.InvalidRechargeTransaction.ToString()))
                return "Failure_invalidtransaction";

            if (errorMessage.Contains(RechargeStatusValidationMessage.AutoRenewalDisabled.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.AutoRenewalDisabledForUser.ToString()))
                return "Failure_autorenewalsdisabled";

            if (errorMessage.Contains(RechargeStatusValidationMessage.NickNameNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.FullNameNotExists.ToString()) ||
                errorMessage.Contains(RechargeStatusValidationMessage.ExceedNicknameLength.ToString()))
                return "Failure_namingissue";

            if (errorMessage.Contains(RechargeStatusValidationMessage.BeneficiaryAlreadyExists.ToString()))
                return "Failure_beneficiaryalreadyexists";

            // Default fallback - if error contains "InsufficientBalance" pattern, treat as insufficient funds
            // Otherwise treat as pack unavailability (maintaining backward compatibility)
            return errorMessage.ToLower().Contains("insufficient") ? "Failure_insufficientfunds" : "Failure_unavailablepack";
        }

        public async Task ProcessTargetedDiscount(string citizenId, string phoneNumber)
        {
            if (string.IsNullOrEmpty(citizenId))
            {
                _logger.LogError("Mobile recharge targeted discount: CitizenId is null. cannot proceed.");
                return;
            }

            var activeOffer = await _unitOfWork.MobileRechargeTargetedDiscounts.GetActiveOfferAsync(citizenId);

            if (!(activeOffer is null))
            {
                _logger.LogInformation($"Mobile recharge targeted discount: The user already has an active offer. CitizenId: {citizenId}");
                return;
            }

            var offerUser = await _unitOfWork.Users.GetActiveUserAsync(phoneNumber.ToZeroPrefixedPhoneNumber());

            if (offerUser is null)
            {
                _logger.LogError($"Mobile recharge targeted discount: User with the phone number {phoneNumber} does not exist.");
                return;
            }

            var offer = new MobileRechargeTargetedDiscount
            {
                Id = Guid.NewGuid(),
                CitizenId = citizenId,
                UserId = offerUser.Id,
                IsActive = true,
                OfferStatus = (int)MobileRecharge_TargetedDiscountOfferStatus.Initiated
            };

            await _unitOfWork.MobileRechargeTargetedDiscounts.AddAsync(offer);

            await _unitOfWork.CommitAsync();

            _logger.LogInformation($"Mobile recharge: Targeted discount offer for the the user has been initiated. Phonenumber: {phoneNumber}.");


            // Send offer_sent cleavertap event
            await _analyticsPublisherService.PublishMobileRechargeTargetedOfferEvent(offerUser.CardHolderId, new Core.Models.Messages.MobileRecharge.Analytics.MobileRechargeEvent
            {
                UserId = offerUser.Id,
                ProductCode = null
            });
        }

        private async Task ChangeTargetedDiscountOfferStatus(MobileRechargeTargetedDiscount activeOffer, MobileRecharge_TargetedDiscountOfferStatus offerStatus, bool makeInActive = false)
        {
            activeOffer.IsActive = !makeInActive;
            activeOffer.OfferStatus = (int)offerStatus;
            await _unitOfWork.CommitAsync();
        }

        private Task<bool> IsProductPartOfTheRatesExp(string productCode, string jsonConfig)
        {
            // Json config should always be available for users that are part of the experiment.
            if (jsonConfig is null)
            {
                _logger.LogError("RatesExperiment: JSON config was null or empty.");
                return Task.FromResult(false);
            }

            var experimentConfig = JsonConvert.DeserializeObject<RatesExperiment>(jsonConfig);

            var config = experimentConfig.ProductConfig.FirstOrDefault(x => x.ProductCode == productCode);

            if (config is null)
            {
                _logger.LogError($"RatesExperiment: Product with the ID {productCode} is not assosiated with the experiment.");
                return Task.FromResult(false);
            }

            return Task.FromResult(true);
        }

        private static DateTime CalculateRenewalDate(string validityPeriodIso)
        {
            // Define a regular expression pattern to match "P" followed by digits and "D" or "M"
            string pattern = @"P(\d+)([DM])"; // Added ([DM]) to capture the last character

            // Use Regex.Match to find the first match in the input string
            Match match = Regex.Match(validityPeriodIso, pattern);

            if (match.Success)
            {
                string number = match.Groups[1].Value;
                string lastChar = match.Groups[2].Value;

                if (double.TryParse(number, out double days))
                {
                    if (lastChar.Equals("D"))
                    {
                        return System.DateTime.Now.AddDays(days);
                    }
                    else if (lastChar.Equals("M"))
                    {
                        return System.DateTime.Now.AddMonths((int)days);
                    }
                }
            }
            return DateTime.Now;
        }

        private Task<MobileRechargeTransaction> ExecuteTargetedDiscExperiment(MobileRechargeTransaction mobileRechargeTransaction)
        {
            // Set total Amount
            var discountedAmount = mobileRechargeTransaction.SendAmount * 0.20M;
            mobileRechargeTransaction.SendAmount = mobileRechargeTransaction.SendAmount - discountedAmount;
            mobileRechargeTransaction.TotalAmount = (decimal)mobileRechargeTransaction.SendAmount + (decimal)mobileRechargeTransaction.Fee;

            return Task.FromResult(mobileRechargeTransaction);
        }

        private async Task<bool> HasTargetedDiscountOffer(Guid id)
        {
            return await _unitOfWork.MobileRechargeTargetedDiscounts.UserHasActiveDiscountOffer(id);
        }

        public async Task<ServiceResponse> ReceiveDeferredRechargeResponseFromDing(string dingResponse, string correlationId, CancellationToken cancellationToken)
        {
            var result = JsonConvert.DeserializeObject<SendTransferResponseDingModel>(dingResponse);

            if (result == null)
            {
                _logger.LogError($"Deferred Transaction {correlationId} has invalid Json not found.");
                return new ServiceResponse(false, "Invalid Json not found");
            }

            var transactionId = new Guid(correlationId);

            var mobileRechargeTransaction = await _unitOfWork.MobileRechargeTransactions.GetTransactionByIdAsync(transactionId);

            if (mobileRechargeTransaction == null)
            {
                _logger.LogError($"Deferred Transaction {correlationId} from Ding not found.");
                return new ServiceResponse(false, $"Deferred Transaction {correlationId} from Ding not found.");
            }

            var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Id == mobileRechargeTransaction.UserId && !u.IsDeleted);

            if (user == null)
            {
                _logger.LogError($"User for deferred transaction {correlationId} from Ding not found.");
                return new ServiceResponse(false, $"User for deferred transaction {correlationId} from Ding not found.");
            }

            if (result.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Failed.ToString() ||
                    result.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelled.ToString() ||
                    result.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Cancelling.ToString())
            {
                //************************
                //Failed Result from Ding
                //************************

                //Do the redemption reversal            
                if (!mobileRechargeTransaction.Transaction.IsReversed())
                {
                    await this.ReverseTransaction(mobileRechargeTransaction);
                }

                //update status in transaction table
                mobileRechargeTransaction.Status = BaseEnums.Status.FAILED;

                var errorMessage = string.Empty;
                if (result.ErrorCodes != null && result.ErrorCodes.Count > 0)
                {
                    errorMessage = result.ErrorCodes[0].Code.ToString() + "###" + (!string.IsNullOrWhiteSpace(result.ErrorCodes[0].Context) ? result.ErrorCodes[0].Context.ToString() : "");
                }
                else
                {
                    errorMessage = string.Format(SystemMessages.PostTransferToDingFailed, "General Exception");
                }


                List<string> errorMessagesWithContext = errorMessage.Split("###").ToList();
                if (errorMessagesWithContext.Count() == 2)
                {
                    mobileRechargeTransaction.Remarks = errorMessagesWithContext[0];
                    mobileRechargeTransaction.ErrorContext = errorMessagesWithContext[1];
                }

                mobileRechargeTransaction.UpdatedDate = DateTime.Now;

                // Update the Discount if any
                if (mobileRechargeTransaction.UserDiscount != null)
                {
                    mobileRechargeTransaction.UserDiscount.IsUsed = false;
                    mobileRechargeTransaction.UserDiscount.MobileRechargeTransactionId = null;
                    mobileRechargeTransaction.UserDiscount.UpdatedDate = DateTime.Now;
                    mobileRechargeTransaction.UserDiscountId = null;
                    mobileRechargeTransaction.DiscountAmount = 0;
                }

                await _unitOfWork.CommitAsync();

                // Trigger for Cross Checking Beneficiary 
                if (mobileRechargeTransaction.BeneficiaryId.HasValue && mobileRechargeTransaction != null && !string.IsNullOrEmpty(mobileRechargeTransaction.ErrorContext))
                {
                    var beneficiary = await _unitOfWork.MobileRechargeBeneficiaries.FirstOrDefaultAsync(x => x.Id == mobileRechargeTransaction.BeneficiaryId.Value && x.Status == BaseEnums.Status.APPROVED && !x.IsDeleted);

                    var message = new MobileRechargeFailedTransactionMessageDto()
                    {
                        BeneficiaryCountryCode = beneficiary.CountryCode,
                        BeneficiaryId = beneficiary.Id,
                        TransactionId = mobileRechargeTransaction.Id,
                        TransactionErrorContext = mobileRechargeTransaction.ErrorContext
                    };

                    //Pushing message to service bus - # MobileRechargeFailedTransactioEvent 
                    await _messagingQueueService.SendEventToTopicAsync(message,
                          _mobileRechargeServiceSettings.ServiceBusConnectionString,
                          _mobileRechargeServiceSettings.ServiceBusTopicName,
                          nameof(OutboxMessageTypeEnum.MRFailedTransactionEvent),
                          Guid.NewGuid().ToString());

                }
                //Send Push Notification
                await _pushNotificationSenderService.SendMobileRechargeResultNotification(user.DeviceToken, mobileRechargeTransaction.Status);

            }
            else if (result.TransferDataResponse.TransferStatus == Enums.ExternalDingStatus.Complete.ToString())
            {
                //Successful Transaction

                //Create the external transaction in DB
                mobileRechargeTransaction.ExternalTransaction = new MobileRechargeExternalTransaction
                {
                    ExternalStatus = result.TransferDataResponse.TransferStatus,
                    CommisionRate = result.TransferDataResponse.CommissionApplied,
                    ExternalTransactionId = result.TransferDataResponse.TransferReference.ExternalReferenceId,
                    StartDate = result.TransferDataResponse.TransferStartDate(),
                    EndDate = result.TransferDataResponse.TransferEndDate(),
                    SendCurrency = result.TransferDataResponse.PricePayment.SendCurrencyIso,
                    SendAmount = result.TransferDataResponse.PricePayment.SendValue,
                    CustomerFee = result.TransferDataResponse.PricePayment.CustomerFee,
                    ReceiveCurrency = result.TransferDataResponse.PricePayment.ReceiveCurrencyIso,
                    ReceiveAmount = result.TransferDataResponse.PricePayment.ReceiveValue,
                    ReceiveAmountWithTax = result.TransferDataResponse.PricePayment.ReceiveValueExcludingTax,
                    DistributorFee = result.TransferDataResponse.PricePayment.DistributorFee,
                    TaxName = result.TransferDataResponse.PricePayment.TaxName,
                    TaxRate = result.TransferDataResponse.PricePayment.TaxRate,
                    TaxCalculation = result.TransferDataResponse.PricePayment.TaxCalculation,
                    CallingCardPin = result?.TransferDataResponse?.ReceiptParamText?.PinText,
                    AccountNumber = result.TransferDataResponse.AccountNumber,
                    ReceiptText = result?.TransferDataResponse?.ReceiptParamText?.PinText,
                    ReceiptParams = result?.TransferDataResponse?.ReceiptText,
                    LastStatusDate = result.TransferDataResponse.TransferEndDate(),
                    StatusDate = DateTime.Now
                };

                mobileRechargeTransaction.Transaction.StatusCode = "00";

                mobileRechargeTransaction.Status = Status.SUCCESSFUL;

                await _unitOfWork.CommitAsync();

                //Activate Auto Renewal
                if (mobileRechargeTransaction.IsAutoRenewalEnabled == true && mobileRechargeTransaction.Status == BaseEnums.Status.SUCCESSFUL)
                {
                    await UpdateRenewal(userId: mobileRechargeTransaction.UserId,
                                       productCode: mobileRechargeTransaction.ProductCode,
                                       beneficiaryId: mobileRechargeTransaction.BeneficiaryId.Value,
                                       cardHolderId: mobileRechargeTransaction.User.CardHolderId,
                                       deactivationCode: null,
                                       IsActive: true,
                                       hasClaimedTargetedDiscount: mobileRechargeTransaction.ReferenceNumber.Contains("TDXP") ? true : false);
                }

                //Send Push Notification
                await _pushNotificationSenderService.SendMobileRechargeResultNotification(user.DeviceToken, mobileRechargeTransaction.Status);
            }
            else
            {

                mobileRechargeTransaction.Status = Status.PENDING;

                await _unitOfWork.CommitAsync();
            }

            return new ServiceResponse();
        }

        public async Task<ServiceResponse<bool>> ProcessLowBalanceScheduledRenewals()
        {
            // Here, we will process all the pending renewals for low balance.
            var lowBalanceScheduledRenewals = await _unitOfWork.MobileRechargeRenewals.GetLowBalanceScheduledRenewals();

            foreach (var lowBalanceScheduledRenewal in lowBalanceScheduledRenewals)
            {
                try
                {
                    // First, find the user.
                    var user = await _unitOfWork.Users.FirstOrDefaultAsync(x => x.Id == lowBalanceScheduledRenewal.UserId
                                                                                     && x.ApplicationId == MobileApplicationId.C3Pay
                                                                                     && x.IsDeleted == false, i => i.CardHolder);

                    if (user is null)
                    {
                        // Continue for this renewal because the user is not found.
                        // We should also consider deleting the renewal record for this user.
                        continue;
                    }

                    // Next, we need to make sure that we have not exceeded the max number of retries.
                    var lowBalanceRetryMaxThresholdInDays = Convert.ToInt32(this._mobileRechargeServiceSettings.LowBalanceRetryMaxThresholdInDays);
                    if (lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount.HasValue && lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount.Value >= lowBalanceRetryMaxThresholdInDays)
                    {
                        // Here, we know that we have retried for the max number of allowed retires
                        // and still could not perform the renewal, so we will mark it as failed.
                        await UpdateRenewal(userId: user.Id,
                                            productCode: lowBalanceScheduledRenewal.ProductCode,
                                            beneficiaryId: lowBalanceScheduledRenewal.BeneficiaryId,
                                            cardHolderId: user.CardHolderId,
                                            deactivationCode: MobileRecharge_AutoRenewalInactiveCode.CancelledDueToExceededNumberOfLowBalanceChecks,
                                            IsActive: false);
                    }
                    else
                    {
                        // Prepare to call the mobile recharge again.
                        var newTransaction = new MobileRechargeTransaction()
                        {
                            ProductCode = lowBalanceScheduledRenewal.ProductCode,
                            UserId = user.Id,
                            BeneficiaryId = lowBalanceScheduledRenewal.BeneficiaryId
                        };

                        var isAutoRenewalEnabled = true;
                        var isAutoRenewalJobExecution = true;

                        var isTargetedRenewalDiscountEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.MobileRechargeEnableAutoRenewalTargetedDiscount);

                        bool hasDiscountOnRenewals = isTargetedRenewalDiscountEnabled && (lowBalanceScheduledRenewal.HasDiscountOnRenewals ?? false);

                        var trySendTransferV2 = await SendTransferV2(user, newTransaction, MobileApplicationId.C3Pay, user.PhoneNumber, isAutoRenewalEnabled, isAutoRenewalJobExecution, hasDiscountOnRenewals);
                        if (trySendTransferV2.IsSuccessful == true)
                        {
                            // If the mobile recharge was successful, we don't have to remove the low balance flags here since they are
                            // already removed inside SendTransferV2. We just need to send a confirmation SMS.
                            await _textMessageSenderService.SendMRLowBalanceEndSms(user.PhoneNumber.ToZeroPrefixedPhoneNumber());
                        }
                        else
                        {
                            // Here, the mobile recharge has failed again.
                            var product = await _unitOfWorkReadOnly.MobileRechargeProducts.GetByIdNoActiveFilterAsync(lowBalanceScheduledRenewal.ProductCode);
                            var errorMessage = trySendTransferV2.ErrorMessage;

                            var deactivationCode = errorMessage.Contains(RechargeStatusValidationMessage.InsufficientBalance.ToString())
                                                ? MobileRecharge_AutoRenewalInactiveCode.CancelledDueToLowBalance
                                                : MobileRecharge_AutoRenewalInactiveCode.CancelledDueToPackUnavailability;

                            bool dueToLowBalance = deactivationCode == MobileRecharge_AutoRenewalInactiveCode.CancelledDueToLowBalance;
                            bool dueToPackUnavailability = deactivationCode == MobileRecharge_AutoRenewalInactiveCode.CancelledDueToPackUnavailability;

                            if (dueToLowBalance == true)
                            {
                                // If the error was due to low balance again, we need to increment the retries counter.
                                if (lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount.HasValue == false)
                                {
                                    lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount = 0;
                                }
                                else
                                {
                                    lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount = lowBalanceScheduledRenewal.LowBalanceRetriesFailedCount.Value + 1;
                                }

                                lowBalanceScheduledRenewal.LastLowBalanceRetryOn = DateTime.Now;
                                await this._unitOfWork.CommitAsync();
                            }
                            else if (dueToPackUnavailability == true)
                            {
                                await UpdateRenewal(userId: user.Id,
                                                    productCode: lowBalanceScheduledRenewal.ProductCode,
                                                    beneficiaryId: lowBalanceScheduledRenewal.BeneficiaryId,
                                                    cardHolderId: user.CardHolderId,
                                                    deactivationCode: MobileRecharge_AutoRenewalInactiveCode.CancelledDueToPackUnavailability,
                                                    IsActive: false);
                            }
                            else
                            {
                                // Here, the mobile recharge has failed for a different reason other than low balance.
                                // No need to do anything.
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    _logger.LogError(ConstantParam.MobileRechargeAutoRenewalFailure, lowBalanceScheduledRenewal.Id, exception.Message.ToString());
                    continue;
                }
            }
            return new ServiceResponse<bool>(true);
        }

        private async Task<bool> IsFixedPriceProductAsync(string productCode)
        {
            var product = await _unitOfWork.MobileRechargeProducts.GetByIdAsync(productCode);
            return product != null && product.MinSendValue == product.MaxSendValue;
        }
    }
}