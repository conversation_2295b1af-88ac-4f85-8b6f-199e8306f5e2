﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class DashboardTags : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DashboardQuickActionElementTags",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    TextContentCode = table.Column<string>(type: "nvarchar(25)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardQuickActionElementTags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DashboardQuickActionElementTags_TextContents_TextContentCode",
                        column: x => x.TextContentCode,
                        principalTable: "TextContents",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "TextContents",
                columns: new[] { "Code", "LanguageCode", "Order", "Text", "Type" },
                values: new object[,]
                {
                    { "da_mt_tag_1", "en", 99, "No Charges", "da_mt_tag" },
                    { "da_mt_tag_2", "en", 99, "From AED 5", "da_mt_tag" },
                    { "da_mt_tag_3", "en", 99, "Get 2.5%", "da_mt_tag" },
                    { "da_mt_tag_4", "en", 99, "0 Deduction", "da_mt_tag" },
                    { "da_mt_tag_5", "en", 99, "AED 10 Only", "da_mt_tag" }
                });

            migrationBuilder.InsertData(
                table: "DashboardQuickActionElementTags",
                columns: new[] { "Id", "TextContentCode" },
                values: new object[,]
                {
                    { 1, "da_mt_tag_1" },
                    { 3, "da_mt_tag_3" },
                    { 5, "da_mt_tag_4" },
                    { 4, "da_mt_tag_5" },
                    { 2, "da_mt_tag_2" }
                });

            migrationBuilder.InsertData(
                table: "Translations",
                columns: new[] { "Id", "LanguageCode", "Text", "TextContentCode" },
                values: new object[,]
                {
                    { 642, "ur-en", "Get 2.5%", "da_mt_tag_3" },
                    { 643, "en", "0 Deduction", "da_mt_tag_4" },
                    { 644, "bn", "0 Deduction", "da_mt_tag_4" },
                    { 645, "hi", "0 Deduction", "da_mt_tag_4" },
                    { 646, "hi-en", "0 Deduction", "da_mt_tag_4" },
                    { 648, "ta", "0 Deduction", "da_mt_tag_4" },
                    { 641, "ta", "Get 2.5%", "da_mt_tag_3" },
                    { 649, "ur-en", "0 Deduction", "da_mt_tag_4" },
                    { 650, "en", "AED 10 Only", "da_mt_tag_5" },
                    { 651, "bn", "AED 10 Only", "da_mt_tag_5" },
                    { 652, "hi", "AED 10 Only", "da_mt_tag_5" },
                    { 653, "hi-en", "AED 10 Only", "da_mt_tag_5" },
                    { 654, "ml", "AED 10 Only", "da_mt_tag_5" },
                    { 647, "ml", "0 Deduction", "da_mt_tag_4" },
                    { 640, "ml", "Get 2.5%", "da_mt_tag_3" },
                    { 638, "hi", "Get 2.5%", "da_mt_tag_3" },
                    { 655, "ta", "AED 10 Only", "da_mt_tag_5" },
                    { 622, "en", "No Charges", "da_mt_tag_1" },
                    { 623, "bn", "No Charges", "da_mt_tag_1" },
                    { 624, "hi", "No Charges", "da_mt_tag_1" },
                    { 625, "hi-en", "No Charges", "da_mt_tag_1" },
                    { 626, "ml", "No Charges", "da_mt_tag_1" },
                    { 627, "ta", "No Charges", "da_mt_tag_1" },
                    { 628, "ur-en", "No Charges", "da_mt_tag_1" },
                    { 639, "hi-en", "Get 2.5%", "da_mt_tag_3" },
                    { 629, "en", "From AED 5", "da_mt_tag_2" },
                    { 631, "hi", "From AED 5", "da_mt_tag_2" },
                    { 632, "hi-en", "From AED 5", "da_mt_tag_2" },
                    { 633, "ml", "From AED 5", "da_mt_tag_2" },
                    { 634, "ta", "From AED 5", "da_mt_tag_2" },
                    { 635, "ur-en", "From AED 5", "da_mt_tag_2" },
                    { 636, "en", "Get 2.5%", "da_mt_tag_3" },
                    { 637, "bn", "Get 2.5%", "da_mt_tag_3" },
                    { 630, "bn", "From AED 5", "da_mt_tag_2" },
                    { 656, "ur-en", "AED 10 Only", "da_mt_tag_5" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_DashboardQuickActionElementTags_TextContentCode",
                table: "DashboardQuickActionElementTags",
                column: "TextContentCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DashboardQuickActionElementTags");

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 622);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 623);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 624);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 625);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 626);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 627);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 628);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 629);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 630);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 631);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 632);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 633);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 634);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 635);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 636);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 637);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 638);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 639);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 640);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 641);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 642);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 643);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 644);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 645);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 646);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 647);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 648);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 649);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 650);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 651);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 652);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 653);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 654);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 655);

            migrationBuilder.DeleteData(
                table: "Translations",
                keyColumn: "Id",
                keyValue: 656);

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mt_tag_1");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mt_tag_2");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mt_tag_3");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mt_tag_4");

            migrationBuilder.DeleteData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_mt_tag_5");
        }
    }
}
