﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideoSeeding : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "LoginVideoTypes",
                columns: new[] { "Id", "Type" },
                values: new object[] { 1, "MoneyTransfer" });

            migrationBuilder.InsertData(
                table: "LoginVideoTypes",
                columns: new[] { "Id", "Type" },
                values: new object[] { 2, "C3PayPlus" });

            migrationBuilder.InsertData(
                table: "LoginVideoSlots",
                columns: new[] { "Id", "DisplayOrder", "VideoTypeId" },
                values: new object[,]
                {
                    { 1, 1, 1 },
                    { 2, 2, 2 },
                    { 3, 3, 2 }
                });

            migrationBuilder.InsertData(
                table: "LoginVideoUrls",
                columns: new[] { "Id", "LanguageCode", "Url", "VideoTypeId" },
                values: new object[,]
                {
                    { 1, "en", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_en.mp4", 1 },
                    { 2, "hi", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_hi.mp4", 1 },
                    { 3, "en", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Eng_10AED.mp4", 2 },
                    { 4, "hi", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Hi_10AED.mp4", 2 }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "LoginVideoSlots",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "LoginVideoUrls",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideoUrls",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "LoginVideoUrls",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "LoginVideoUrls",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "LoginVideoTypes",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "LoginVideoTypes",
                keyColumn: "Id",
                keyValue: 2);
        }
    }
}
