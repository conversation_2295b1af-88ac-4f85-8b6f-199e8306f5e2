﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class C3PayPlusMembershipInsuranceNomineeTypeUpdates : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "UserPhoneNumber",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.AddColumn<Guid>(
                name: "C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                column: "C3PayPlusMembershipUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                column: "C3PayPlusMembershipUserId",
                principalTable: "C3PayPlusMembershipUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.DropIndex(
                name: "IX_C3PayPlusMembershipLifeInsuranceNominees_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.DropColumn(
                name: "C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLifeInsuranceNominees");

            migrationBuilder.AddColumn<string>(
                name: "UserPhoneNumber",
                table: "C3PayPlusMembershipLifeInsuranceNominees",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
