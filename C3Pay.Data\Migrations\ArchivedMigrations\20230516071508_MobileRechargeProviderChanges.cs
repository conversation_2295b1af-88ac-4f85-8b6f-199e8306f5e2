﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class MobileRechargeProviderChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "MobileRechargeProviders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<bool>(
                name: "IsVisible",
                table: "MobileRechargeProviders",
                type: "bit",
                nullable: true,
                defaultValue: false);

            var sql = @" 
                                
                    Update MobileRechargeProviders set IsVisible = 1 
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1AIN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1DIN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1SIN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1UIN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1VIN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'DNNP'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '1KPH'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IQBJ'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'F9BF'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'CICM'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IACA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'CJTD'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'ZYGM'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'L2IE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '6AKE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'Z9KW'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '6CMW'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'SDMR'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '6EMZ'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'HQNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '4XNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'NSNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WNNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WQNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WRNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WUNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WVNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WXNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'WYNG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'PNSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'NYSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'VRSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'XUSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'Z9SA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'GPSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IASA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '2ASA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '3GSA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'W5SN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'PZSN'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'GYSL'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '6GZA'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'GZTG'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '2AAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '3GAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '3NAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '3OAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'GPAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'I7AE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IAAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'NYAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'PNAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'Z9AE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'ZOAE'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IAGB'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'S7US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'RZUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'QTUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'PNUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'PGUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'O6US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'Z9US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'ZQUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'XUUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '9CUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '9SUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '2AUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '2VUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = '4RUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'L3US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'I2US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'I3US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'I4US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'IAUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'GPUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'E7US'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'FHUS'
                    Update MobileRechargeProviders set IsVisible = 0 where Code = 'FIUS'
                    Update MobileRechargeProviders set DisplayOrder = 0 where Code = '6FZW'

                    Update MobileRechargeProviders set DisplayOrder = 4 where Code = 'AXBD'
                    Update MobileRechargeProviders set DisplayOrder = 3 where Code = 'BGBD'
                    Update MobileRechargeProviders set DisplayOrder = 1 where Code = 'GEBD'
                    Update MobileRechargeProviders set DisplayOrder = 2 where Code = 'RLBD'
                    Update MobileRechargeProviders set DisplayOrder = 5 where Code = 'TQBD'
                    Update MobileRechargeProviders set DisplayOrder = 2 where Code = 'RJIN'
                    Update MobileRechargeProviders set DisplayOrder = 3 where Code = 'VFIN'
                    Update MobileRechargeProviders set DisplayOrder = 5 where Code = 'MNIN'
                    Update MobileRechargeProviders set DisplayOrder = 4 where Code = 'BLIN'
                    Update MobileRechargeProviders set DisplayOrder = 1 where Code = 'AIIN'
                    Update MobileRechargeProviders set DisplayOrder = 2 where Code = 'NCNP'
                    Update MobileRechargeProviders set DisplayOrder = 1 where Code = 'NENP'
                    Update MobileRechargeProviders set DisplayOrder = 3 where Code = 'NQNP'
                    Update MobileRechargeProviders set DisplayOrder = 1 where Code = 'MBPK'
                    Update MobileRechargeProviders set DisplayOrder = 5 where Code = 'WPPK'
                    Update MobileRechargeProviders set DisplayOrder = 2 where Code = 'ZNPK'
                    Update MobileRechargeProviders set DisplayOrder = 3 where Code = 'TPPK'
                    Update MobileRechargeProviders set DisplayOrder = 4 where Code = 'UFPK'
                    Update MobileRechargeProviders set DisplayOrder = 2 where Code = 'SMPH'
                    Update MobileRechargeProviders set DisplayOrder = 3 where Code = 'STPH'
                    Update MobileRechargeProviders set DisplayOrder = 4 where Code = 'TMPH'
                    Update MobileRechargeProviders set DisplayOrder = 6 where Code = 'PWPH'
                    Update MobileRechargeProviders set DisplayOrder = 5 where Code = 'DWPH'
                    Update MobileRechargeProviders set DisplayOrder = 1 where Code = 'GBPH'
            ";
            migrationBuilder.Sql(sql);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "MobileRechargeProviders");

            migrationBuilder.DropColumn(
                name: "IsVisible",
                table: "MobileRechargeProviders");
        }
    }
}
