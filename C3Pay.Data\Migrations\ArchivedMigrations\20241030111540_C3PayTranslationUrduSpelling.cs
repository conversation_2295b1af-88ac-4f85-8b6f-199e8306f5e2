﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace C3Pay.Data.Migrations
{
    /// <inheritdoc />
    public partial class C3PayTranslationUrduSpelling : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UruthuEnglish",
                table: "C3PayPlusMembershipTranslations",
                newName: "UrduEnglish");

            migrationBuilder.AlterColumn<string>(
                name: "TextCode",
                table: "C3PayPlusMembershipTranslations",
                type: "nvarchar(450)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipTranslations_TextCode",
                table: "C3PayPlusMembershipTranslations",
                column: "TextCode",
                unique: true,
                filter: "[TextCode] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_C3PayPlusMembershipTranslations_TextCode",
                table: "C3PayPlusMembershipTranslations");

            migrationBuilder.RenameColumn(
                name: "UrduEnglish",
                table: "C3PayPlusMembershipTranslations",
                newName: "UruthuEnglish");

            migrationBuilder.AlterColumn<string>(
                name: "TextCode",
                table: "C3PayPlusMembershipTranslations",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)",
                oldNullable: true);
        }
    }
}
