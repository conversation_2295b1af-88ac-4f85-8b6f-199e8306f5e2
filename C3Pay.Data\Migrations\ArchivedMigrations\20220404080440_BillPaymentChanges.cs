﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class BillPaymentChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Reason",
                table: "BillPaymentTransactions",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "BillAmount",
                table: "BillPaymentBillers",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "BillAmountCurrency",
                table: "BillPaymentBillers",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "BillPaymentBillers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            var query = @"  
                Update BillPaymentBillers set Status =  1;

                Update BillPaymentBillers set Status = 6 where Id in (
                select distinct bill.Id from BillPaymentBillers bill
                left join BillPaymentProviders prov on prov.Id = bill.ProviderId
                left join BillPaymentProducts prod on prod.ProviderId = prov.Id
                where prod.InquiryAvailable = 0)
            ";

            migrationBuilder.Sql(query);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Reason",
                table: "BillPaymentTransactions");

            migrationBuilder.DropColumn(
                name: "BillAmount",
                table: "BillPaymentBillers");

            migrationBuilder.DropColumn(
                name: "BillAmountCurrency",
                table: "BillPaymentBillers");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "BillPaymentBillers");
        }
    }
}
