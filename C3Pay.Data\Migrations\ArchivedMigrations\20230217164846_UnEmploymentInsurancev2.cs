﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnEmploymentInsurancev2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var start = @"   
                    delete from UnEmpInsurancePayments; 
                    delete from UnEmpInsurancePaymentOptions;
                    delete from MultimediaResources where Id in (19,20);
                    delete from Features where Id = 7;
                    drop table UnEmpInsurancePayments;
                    drop table UnEmpInsurancePaymentOptions;

                    DECLARE @ConstraintName nvarchar(200)
                    SELECT @ConstraintName = Name FROM SYS.DEFAULT_CONSTRAINTS WHERE PARENT_OBJECT_ID = OBJECT_ID('MultimediaResources') AND PARENT_COLUMN_ID = (SELECT column_id FROM sys.columns WHERE NAME = N'Language' AND object_id = OBJECT_ID(N'MultimediaResources'))
                    IF @ConstraintName IS NOT NULL
                    EXEC('ALTER TABLE MultimediaResources DROP CONSTRAINT ' + @ConstraintName)
                    IF EXISTS (SELECT * FROM syscolumns WHERE id=object_id('MultimediaResources') AND name='Language')
                    EXEC('ALTER TABLE MultimediaResources DROP COLUMN Language') 

                    DECLARE @ConstraintName1 nvarchar(200)
                    SELECT @ConstraintName1 = Name FROM SYS.DEFAULT_CONSTRAINTS WHERE PARENT_OBJECT_ID = OBJECT_ID('CardHolders') AND PARENT_COLUMN_ID = (SELECT column_id FROM sys.columns WHERE NAME = N'IsFreeZoneEmployee' AND object_id = OBJECT_ID(N'CardHolders'))
                    IF @ConstraintName1 IS NOT NULL
                    EXEC('ALTER TABLE CardHolders DROP CONSTRAINT ' + @ConstraintName1)
                    IF EXISTS (SELECT * FROM syscolumns WHERE id=object_id('CardHolders') AND name='IsFreeZoneEmployee')
                    EXEC('ALTER TABLE CardHolders DROP COLUMN IsFreeZoneEmployee') 
            ";
            migrationBuilder.Sql(start);

            migrationBuilder.AddColumn<string>(
                name: "Language",
                table: "MultimediaResources",
                type: "varchar(3)",
                maxLength: 3,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsFreeZoneEmployee",
                table: "CardHolders",
                type: "bit",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "UnEmpInsurancePaymentOptions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Description = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    AmountVAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountVATCurrency = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FeeCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Frequency = table.Column<int>(type: "int", nullable: false),
                    PartnerCode = table.Column<int>(type: "int", maxLength: 150, nullable: false),
                    Category = table.Column<int>(type: "int", maxLength: 1, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnEmpInsurancePaymentOptions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UnEmpInsurancePayments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PolicyNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OptionId = table.Column<int>(type: "int", nullable: true),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    AmountVAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    AmountVATCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    FeeCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    TotalAmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    InceptionDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpiryDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsSubscribedExternally = table.Column<bool>(type: "bit", nullable: false),
                    TotalInstallments = table.Column<int>(type: "int", nullable: true),
                    PaidInstallments = table.Column<int>(type: "int", nullable: true),
                    NextDueDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    NextDueAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    NextDueAmountCurrency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
                    PaidDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsAutoPayment = table.Column<bool>(type: "bit", nullable: false),
                    Source = table.Column<int>(type: "int", nullable: false),
                    Category = table.Column<int>(type: "int", maxLength: 1, nullable: false),
                    LastSyncDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UnEmpInsurancePayments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UnEmpInsurancePayments_UnEmpInsurancePaymentOptions_OptionId",
                        column: x => x.OptionId,
                        principalTable: "UnEmpInsurancePaymentOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UnEmpInsurancePayments_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Features",
                columns: new[] { "Id", "Name" },
                values: new object[] { 7, "UnEmpInsurance" });

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 1,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 2,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 3,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 4,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 5,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 6,
                column: "Language",
                value: "bn");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 7,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 8,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 9,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 10,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 11,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 12,
                column: "Language",
                value: "bn");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 13,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 14,
                column: "Language",
                value: "hi");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 15,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 16,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 17,
                column: "Language",
                value: "en");

            migrationBuilder.UpdateData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 18,
                column: "Language",
                value: "bn");

            migrationBuilder.InsertData(
                table: "UnEmpInsurancePaymentOptions",
                columns: new[] { "Id", "Amount", "AmountCurrency", "AmountVAT", "AmountVATCurrency", "Category", "Description", "Fee", "FeeCurrency", "Frequency", "PartnerCode" },
                values: new object[,]
                {
                    { 7, 10m, "AED", 6m, "AED", 2, "AED 10/Month", 1.5m, "AED", 1, 2 },
                    { 6, 60m, "AED", 3m, "AED", 1, "AED 60 (1 Year)", 7m, "AED", 4, 2 },
                    { 5, 5m, "AED", 3m, "AED", 1, "AED 6/Month", 0.75m, "AED", 1, 2 },
                    { 4, 120m, "AED", 6m, "AED", 2, "AED 120 (1 Year)", 14m, "AED", 1, 1 },
                    { 3, 10m, "AED", 6m, "AED", 2, "AED 10/Month", 1.5m, "AED", 1, 1 },
                    { 2, 60m, "AED", 3m, "AED", 1, "AED 60 (1 Year)", 7m, "AED", 4, 1 },
                    { 8, 120m, "AED", 6m, "AED", 2, "AED 120 (1 Year)", 14m, "AED", 1, 2 },
                    { 1, 5m, "AED", 3m, "AED", 1, "AED 6/Month", 0.75m, "AED", 1, 1 }
                });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 19, 7, "hi", "IND", "", "Video", "https://player.vimeo.com/progressive_redirect/playback/786255453/rendition/360p/file.mp4?loc=external&signature=20c3820b7d2a6f9836b3b90e41ee727394a74f0810174109b97c24707c9258eb" });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 20, 7, "en", "PHL", "", "Video", "https://player.vimeo.com/progressive_redirect/playback/786493764/rendition/360p/file.mp4?loc=external&signature=8e65c18fcdd646448fe799c83c2668dca72799c5cdbf53f2af9f7296189ae125" });

            migrationBuilder.CreateIndex(
                name: "IX_UnEmpInsurancePayments_CreatedDate",
                table: "UnEmpInsurancePayments",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_UnEmpInsurancePayments_OptionId",
                table: "UnEmpInsurancePayments",
                column: "OptionId");

            migrationBuilder.CreateIndex(
                name: "IX_UnEmpInsurancePayments_UserId",
                table: "UnEmpInsurancePayments",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UnEmpInsurancePayments");

            migrationBuilder.DropTable(
                name: "UnEmpInsurancePaymentOptions");

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 19);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 20);

            migrationBuilder.DeleteData(
                table: "Features",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DropColumn(
                name: "Language",
                table: "MultimediaResources");

            migrationBuilder.DropColumn(
                name: "IsFreeZoneEmployee",
                table: "CardHolders");
        }
    }
}
