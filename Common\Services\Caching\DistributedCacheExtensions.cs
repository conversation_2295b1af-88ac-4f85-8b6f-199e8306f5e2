﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace Edenred.Common.Services
{
    public static class DistributedCacheExtensions
    {
        private static readonly ActivitySource ActivitySource = new("C3Pay.Cache");

        public static async Task SetRecordAsync<T>(this IDistributedCache cache, string recordId, T data, 
            TimeSpan? absoluteExpiryTime = null, TimeSpan? unusedExpiryTime = null, CancellationToken cancellationToken = default)
        {
            using var activity = ActivitySource.StartActivity("Cache.Set");
            activity?.SetTag("cache.key", recordId);
            activity?.SetTag("cache.operation", "Set");

            try
            {
                var options = new DistributedCacheEntryOptions()
                {
                    AbsoluteExpirationRelativeToNow = absoluteExpiryTime ?? TimeSpan.FromMinutes(60),
                    SlidingExpiration = unusedExpiryTime
                };

                var jsonData = JsonSerializer.Serialize(data);
                activity?.SetTag("cache.data_size", jsonData.Length);

                await cache.SetStringAsync(recordId, jsonData, options, cancellationToken);
                activity?.SetStatus(ActivityStatusCode.Ok);
            }
            catch (TaskCanceledException ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, "Operation was canceled or timed out");
                activity?.SetTag("exception.type", ex.GetType().Name);
                activity?.SetTag("exception.message", ex.Message);
                throw;
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.SetTag("exception.type", ex.GetType().Name);
                activity?.SetTag("exception.message", ex.Message);
                throw;
            }
        }

        public static async Task<T> GetRecordAsync<T>(this IDistributedCache cache, string recordId, CancellationToken cancellationToken = default)
        {
            using var activity = ActivitySource.StartActivity("Cache.Get");
            activity?.SetTag("cache.key", recordId);
            activity?.SetTag("cache.operation", "Get");

            try
            {
                var jsonData = await cache.GetStringAsync(recordId, cancellationToken);

                if (jsonData is null)
                {
                    activity?.SetTag("cache.hit", false);
                    activity?.SetStatus(ActivityStatusCode.Ok, "Cache miss");
                    return default(T);
                }

                activity?.SetTag("cache.hit", true);
                activity?.SetTag("cache.data_size", jsonData.Length);
                activity?.SetStatus(ActivityStatusCode.Ok);
                
                return JsonSerializer.Deserialize<T>(jsonData);
            }
            catch (TaskCanceledException ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, "Operation was canceled or timed out");
                activity?.SetTag("exception.type", ex.GetType().Name);
                activity?.SetTag("exception.message", ex.Message);
                return default(T); // Return default instead of re-throwing for cache misses
            }
            catch (Exception ex)
            {
                activity?.SetStatus(ActivityStatusCode.Error, ex.Message);
                activity?.SetTag("exception.type", ex.GetType().Name);
                activity?.SetTag("exception.message", ex.Message);
                return default(T);
            }
        }
    }
}
