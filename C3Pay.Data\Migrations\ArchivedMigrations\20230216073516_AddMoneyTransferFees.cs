﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddMoneyTransferFees : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "MoneyTransferFees",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CountryCode = table.Column<string>(type: "varchar(2)", maxLength: 2, nullable: false),
                    TransferMethod = table.Column<int>(type: "int", nullable: false),
                    LowerLimit = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    UpperLimit = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    Fees = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferFees", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferFees",
                columns: new[] { "Id", "CountryCode", "Fees", "IsActive", "LowerLimit", "TransferMethod", "UpperLimit" },
                values: new object[,]
                {
                    { 1, "IN", 5.25m, true, 0m, 2, 500m },
                    { 17, "LK", 15.75m, true, 0m, 1, 1000m },
                    { 16, "LK", 31.5m, true, 125000.01m, 2, null },
                    { 15, "LK", 21m, true, 1000.01m, 2, 125000m },
                    { 14, "LK", 15.75m, true, 0m, 2, 1000m },
                    { 13, "NP", 10m, true, 0m, 1, null },
                    { 12, "NP", 10m, true, 0m, 2, null },
                    { 11, "BD", 15m, true, 1000.01m, 2, null },
                    { 18, "LK", 21m, true, 1000.01m, 1, 125000m },
                    { 10, "BD", 15m, true, 0m, 2, 1000m },
                    { 8, "PH", 21m, true, 0m, 1, 2000m },
                    { 7, "PH", 20m, true, 2000.01m, 2, null },
                    { 6, "PH", 15m, true, 0m, 2, 2000m },
                    { 5, "PK", 0m, true, 0m, 1, null },
                    { 4, "PK", 0m, true, 0m, 2, null },
                    { 3, "IN", 15.75m, true, 1000.01m, 2, null },
                    { 2, "IN", 10.5m, true, 500.01m, 2, 1000m },
                    { 9, "PH", 21m, true, 2000.01m, 1, null },
                    { 19, "LK", 31.5m, true, 125000.01m, 1, null }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferFees");
        }
    }
}
