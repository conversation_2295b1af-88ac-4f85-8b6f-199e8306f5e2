﻿// <auto-generated />
using System;
using C3Pay.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace C3Pay.Data.Migrations
{
    [DbContext(typeof(C3PayContext))]
    [Migration("20221216071212_DashboardTags")]
    partial class DashboardTags
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.6")
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.HasSequence<int>("OrderNumbers")
                .StartsAt(1001L);

            modelBuilder.Entity("C3Pay.Core.Models.AuditTrail", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Action")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ActionOn")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("PortalUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("PortalUserId");

                    b.HasIndex("UserId");

                    b.ToTable("AuditTrails");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentBiller", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<decimal>("BillAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BillAmountCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("NickName")
                        .HasMaxLength(350)
                        .HasColumnType("nvarchar(350)");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("ProviderId");

                    b.HasIndex("UserId");

                    b.ToTable("BillPaymentBillers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentBillerIO", b =>
                {
                    b.Property<int>("ProductIOId")
                        .HasColumnType("int");

                    b.Property<Guid>("BillerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FieldValue")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductIOId", "BillerId");

                    b.HasIndex("BillerId");

                    b.ToTable("BillPaymentBillerIOs");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IconUrl")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("BillPaymentCategories");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentCategoryProvider", b =>
                {
                    b.Property<int>("SubCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("SubCategoryId", "ProviderId");

                    b.HasIndex("ProviderId");

                    b.ToTable("BillPaymentCategoryProviders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentFee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Fee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FeeCurrency")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("ProviderCode")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("BillPaymentFees");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("BusinessDays")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<int>("DaysToPost")
                        .HasColumnType("int");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("ExcessPaymentAllowed")
                        .HasColumnType("bit");

                    b.Property<bool>("InquiryAvailable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("MaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MinAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("PartialPaymentAllowed")
                        .HasColumnType("bit");

                    b.Property<bool>("PastDuePaymentAllowed")
                        .HasColumnType("bit");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProviderId");

                    b.ToTable("BillPaymentProducts");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProductIO", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DataType")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("IOId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("MaxLength")
                        .HasColumnType("int");

                    b.Property<int>("MinLength")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Operation")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ProductId");

                    b.ToTable("BillPaymentProductIOs");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProvider", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CatalogVersion")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Code")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IconUrl")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Type")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.ToTable("BillPaymentProviders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProviderFee", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("MandatoryFee")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MandatoryFeeType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MaxUserFee")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MaxUserFreeType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "ProviderId");

                    b.HasIndex("ProviderId");

                    b.ToTable("BillPaymentProviderFees");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentSubCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("BillPaymentSubCategories");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<decimal>("BillAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BillAmountCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<Guid>("BillerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("ConvertedBillAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ConvertedBillAmountCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DaysToPost")
                        .HasColumnType("int");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("ExternalFxRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FeeAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FeeAmountCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<decimal>("FxRatePercent")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("ModifiedFxRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ProviderCharge")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProviderChargeCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ReferenceNumber")
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("TotalAmountCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("BillerId");

                    b.HasIndex("ReferenceNumber")
                        .IsUnique()
                        .HasFilter("[ReferenceNumber] IS NOT NULL");

                    b.ToTable("BillPaymentTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BlackListedEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<string>("Identifier")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastFailedAttemptTimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.ToTable("BlackListedEntities");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPaymentCategoryFee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("FxRatePercentage")
                        .HasMaxLength(50)
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("MandatoryFee")
                        .HasMaxLength(50)
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MandatoryFeeCurrency")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CountryCode");

                    b.ToTable("BillPaymentCategoryFees");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPaymentDailyFxRate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("BillerType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FXDate")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("FxRate")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SettlementCurrency")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("BillPaymentDailyFxRates");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPaymentExternalTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("BaseCurrency")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<Guid>("BillPaymentTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConfirmationNumber")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("EntityTransactionId")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)");

                    b.Property<string>("ExternalTransactionId")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("FXRate")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ResponseCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ResponseDateTime")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ResponseMessage")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<string>("SettlementCurrency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("TicketCaption")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("BillPaymentTransactionId")
                        .IsUnique();

                    b.HasIndex("ExternalTransactionId");

                    b.ToTable("BillPaymentExternalTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.Lookup.UserRegistrationRejectionReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.ToTable("UserRegistrationRejectionReasons");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Glare"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Unclear image"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Not original EID photo"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Wearing a mask"
                        },
                        new
                        {
                            Id = 5,
                            Name = "Mismatch name"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Mismatch selfie"
                        },
                        new
                        {
                            Id = 7,
                            Name = "Selfie not clear"
                        },
                        new
                        {
                            Id = 8,
                            Name = "Missing images"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBank", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int?>("AccountNumberLengthLimitType")
                        .HasColumnType("int");

                    b.Property<string>("AccountNumberLengthLimitValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPopular")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int?>("MoneyTransferPartnerId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PrimaryIdentifierCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("RequiresBranch")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("MoneyTransferPartnerId");

                    b.ToTable("MoneyTransferBanks");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBranch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("BankId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PrimaryAddress")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PrimaryIdentifierCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SecondaryAddress")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("SecondaryIdentifierCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("BankId");

                    b.ToTable("MoneyTransferBranches");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferCorridor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<decimal>("BankTransferLatestRate")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("BankTransferMaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("BankTransferMaxMonthlyAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("BankTransferMaxMonthlyCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("BankTransferMinAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("BankTransferRateLastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("CashPickUpLatestRate")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("CashPickUpMaxAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CashPickUpMaxMonthlyAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("CashPickUpMaxMonthlyCount")
                        .HasColumnType("int");

                    b.Property<decimal?>("CashPickUpMinAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("CashPickUpRateLastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<bool>("EligibleForBankTransfer")
                        .HasColumnType("bit");

                    b.Property<bool>("EligibleForCashPickUp")
                        .HasColumnType("bit");

                    b.Property<decimal>("Fee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("MoneyTransferEnabled")
                        .HasColumnType("bit");

                    b.Property<int>("MoneyTransferPartnerId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("VAT")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("MoneyTransferPartnerId");

                    b.ToTable("MoneyTransferCorridors");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 15000m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CountryCode = "BD",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = false,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        },
                        new
                        {
                            Id = 2,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 15000m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CashPickUpMaxAmount = 15000m,
                            CashPickUpMaxMonthlyAmount = 30000m,
                            CashPickUpMaxMonthlyCount = 7,
                            CashPickUpMinAmount = 25m,
                            CountryCode = "PK",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = true,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        },
                        new
                        {
                            Id = 3,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 10000m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CountryCode = "IN",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = false,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        },
                        new
                        {
                            Id = 4,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 29900m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CashPickUpMaxAmount = 3600m,
                            CashPickUpMaxMonthlyAmount = 30000m,
                            CashPickUpMaxMonthlyCount = 7,
                            CashPickUpMinAmount = 25m,
                            CountryCode = "PH",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = true,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        },
                        new
                        {
                            Id = 5,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 15000m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CashPickUpMaxAmount = 3000m,
                            CashPickUpMaxMonthlyAmount = 30000m,
                            CashPickUpMaxMonthlyCount = 7,
                            CashPickUpMinAmount = 25m,
                            CountryCode = "NP",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = true,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        },
                        new
                        {
                            Id = 6,
                            BankTransferLatestRate = 0m,
                            BankTransferMaxAmount = 19823m,
                            BankTransferMaxMonthlyAmount = 30000m,
                            BankTransferMaxMonthlyCount = 7,
                            BankTransferMinAmount = 25m,
                            CashPickUpLatestRate = 0m,
                            CashPickUpMaxAmount = 15000m,
                            CashPickUpMaxMonthlyAmount = 30000m,
                            CashPickUpMaxMonthlyCount = 7,
                            CashPickUpMinAmount = 25m,
                            CountryCode = "LK",
                            EligibleForBankTransfer = true,
                            EligibleForCashPickUp = true,
                            Fee = 0m,
                            IsActive = true,
                            MoneyTransferEnabled = true,
                            MoneyTransferPartnerId = 2,
                            VAT = 0m
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("MoneyTransferPartners");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "RAK Bank",
                            Type = 1
                        },
                        new
                        {
                            Id = 2,
                            Name = "Index",
                            Type = 2
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartnerReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<int>("MoneyTransferPartnerId")
                        .HasColumnType("int");

                    b.Property<int>("MoneyTransferReasonId")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("MoneyTransferPartnerId");

                    b.HasIndex("MoneyTransferReasonId");

                    b.ToTable("MoneyTransferPartnerReasons");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CountryCode = "BD",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "FAMILY MAINTENANCE"
                        },
                        new
                        {
                            Id = 2,
                            CountryCode = "BD",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "EDUCATIONAL EXPENSES"
                        },
                        new
                        {
                            Id = 3,
                            CountryCode = "BD",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "MEDICAL EXPENSE"
                        },
                        new
                        {
                            Id = 4,
                            CountryCode = "BD",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "INSURANCE PAYMENT"
                        },
                        new
                        {
                            Id = 5,
                            CountryCode = "PK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "FAMILY MAINTENANCE"
                        },
                        new
                        {
                            Id = 6,
                            CountryCode = "PK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "EDUCATIONAL EXPENSES"
                        },
                        new
                        {
                            Id = 7,
                            CountryCode = "PK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "MEDICAL EXPENSE"
                        },
                        new
                        {
                            Id = 8,
                            CountryCode = "PK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "INSURANCE PAYMENT"
                        },
                        new
                        {
                            Id = 9,
                            CountryCode = "IN",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS"
                        },
                        new
                        {
                            Id = 10,
                            CountryCode = "IN",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "PAYMENT TO SCHOOLS AND COLLEGES"
                        },
                        new
                        {
                            Id = 11,
                            CountryCode = "IN",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "PAYMENT FOR MEDICAL TREATMENT"
                        },
                        new
                        {
                            Id = 12,
                            CountryCode = "IN",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS"
                        },
                        new
                        {
                            Id = 13,
                            CountryCode = "PH",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "FAMILY MAINTENANCE"
                        },
                        new
                        {
                            Id = 14,
                            CountryCode = "PH",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "EDUCATIONAL EXPENSES"
                        },
                        new
                        {
                            Id = 15,
                            CountryCode = "PH",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "MEDICAL EXPENSE"
                        },
                        new
                        {
                            Id = 16,
                            CountryCode = "PH",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "INSURANCE PAYMENT"
                        },
                        new
                        {
                            Id = 17,
                            CountryCode = "NP",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "FAMILY MAINTENANCE"
                        },
                        new
                        {
                            Id = 18,
                            CountryCode = "NP",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "EDUCATIONAL EXPENSES"
                        },
                        new
                        {
                            Id = 19,
                            CountryCode = "NP",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "MEDICAL EXPENSE"
                        },
                        new
                        {
                            Id = 20,
                            CountryCode = "NP",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "INSURANCE PAYMENT"
                        },
                        new
                        {
                            Id = 21,
                            CountryCode = "LK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 8,
                            Reason = "FAMILY MAINTENANCE"
                        },
                        new
                        {
                            Id = 22,
                            CountryCode = "LK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 22,
                            Reason = "EDUCATIONAL EXPENSES"
                        },
                        new
                        {
                            Id = 23,
                            CountryCode = "LK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 24,
                            Reason = "MEDICAL EXPENSE"
                        },
                        new
                        {
                            Id = 24,
                            CountryCode = "LK",
                            MoneyTransferPartnerId = 2,
                            MoneyTransferReasonId = 21,
                            Reason = "INSURANCE PAYMENT"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferProfileModel", b =>
                {
                    b.Property<string>("CorporateId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CorporateName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmiratesId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TotalCount")
                        .HasColumnType("int");

                    b
                        .HasAnnotation("Relational:SqlQuery", "GetMoneyTransferProfile");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.OutboxMessage.OutboxMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("Processed")
                        .HasColumnType("datetime2");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("OutboxMessage");
                });

            modelBuilder.Entity("C3Pay.Core.Models.CardHolder", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<bool?>("BelongsToExchangeHouse")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("Birthdate")
                        .HasColumnType("datetime2");

                    b.Property<string>("C3RegistrationId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("CardSerialNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CorporateId")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CorporateName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("EmiratesId")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime?>("EmiratesIdExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmployeeId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("EmployeeStatus")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Gender")
                        .HasMaxLength(6)
                        .HasColumnType("int");

                    b.Property<string>("LabourCardId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("PassportNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PpsAccountNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("CardSerialNumber");

                    b.HasIndex("CorporateId");

                    b.HasIndex("EmiratesId");

                    b.HasIndex("FirstName");

                    b.HasIndex("LastName");

                    b.ToTable("CardHolders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.City", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<bool>("IsStoreEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.ToTable("Cities");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Dubai"
                        },
                        new
                        {
                            Id = 2,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Abu Dhabi"
                        },
                        new
                        {
                            Id = 3,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Sharjah"
                        },
                        new
                        {
                            Id = 4,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Ajman"
                        },
                        new
                        {
                            Id = 5,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Ras Al Khaimah"
                        },
                        new
                        {
                            Id = 6,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Fujairah"
                        },
                        new
                        {
                            Id = 7,
                            CountryCode = "AE",
                            IsStoreEnabled = true,
                            Name = "Umm Al-Quwain"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.Country", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<decimal>("BankTransferLatestRate")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<bool>("BillPaymentEnabled")
                        .HasColumnType("bit");

                    b.Property<decimal>("CashPickUpLatestRate")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("CashPickUpProvider")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("CashPickUpProviderLocationsURL")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Code3")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("Currency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<bool>("EligibleForBankTransfer")
                        .HasColumnType("bit");

                    b.Property<bool>("EligibleForCashPickUp")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPapularCountry")
                        .HasColumnType("bit");

                    b.Property<string>("LongName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("MobileRechargeEnabled")
                        .HasColumnType("bit");

                    b.Property<bool>("MobileRechargeEnabledForPartner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("MobileRechargeLastSynchronizedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("MoneyTransferEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("RatesLastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("STDCode")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.HasKey("Code");

                    b.ToTable("Countries");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardElement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeepLinkUrl")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("SectionId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SectionId");

                    b.ToTable("DashboardElements");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/moneytransfer",
                            DisplayOrder = 1,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 2,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/mobilerecharge",
                            DisplayOrder = 2,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 3,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/mobilerecharge",
                            DisplayOrder = 3,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 4,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/salaryadvance",
                            DisplayOrder = 4,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 5,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/moneytransfer/c3toc3",
                            DisplayOrder = 5,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 6,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/billpayments",
                            DisplayOrder = 6,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 7,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/transactions",
                            DisplayOrder = 7,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 8,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/pinreveal",
                            DisplayOrder = 8,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 9,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/billpayments",
                            DisplayOrder = 6,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        },
                        new
                        {
                            Id = 10,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DeepLinkUrl = "/securityalerts",
                            DisplayOrder = 8,
                            IsDeleted = false,
                            SectionId = 1,
                            Type = 1
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardQuickActionElement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ElementId")
                        .HasColumnType("int");

                    b.Property<string>("IconUrl")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("TextContentCode")
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("ElementId");

                    b.HasIndex("TextContentCode");

                    b.ToTable("DashboardQuickActionElements");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 2,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 3,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 4,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 5,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 6,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 7,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 1,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_1.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 8,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 9,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 10,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 11,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 12,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 13,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 14,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 2,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_2.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 15,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 16,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 17,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 18,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 19,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 20,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 21,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 3,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_3.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 22,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 23,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 24,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 25,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 26,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 27,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 28,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 4,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_4.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 29,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 30,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 31,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 32,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 33,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 34,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 35,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 5,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_5.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 36,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 37,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 38,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 39,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 40,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 41,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 42,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 6,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_6.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 43,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 44,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_7.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 45,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 46,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 47,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 48,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 49,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 7,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_7.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 50,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 51,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_ind_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 52,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 53,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 54,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 55,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 56,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 8,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_8.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 57,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_9.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 58,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_action_9.png",
                            IsActive = false,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 59,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_pak_9.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 60,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_lka_9.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 61,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_phl_9.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 62,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_bgd_9.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 63,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 9,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_npl_9.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 64,
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 65,
                            CountryCode = "IN",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 66,
                            CountryCode = "PK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 67,
                            CountryCode = "LK",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 68,
                            CountryCode = "PH",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 69,
                            CountryCode = "BD",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 70,
                            CountryCode = "NP",
                            CreatedDate = new DateTime(2022, 10, 10, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ElementId = 10,
                            IconUrl = "https://eaec3sharedsp.blob.core.windows.net/dashboard-icons/da_qck_act_10.png",
                            IsActive = true,
                            IsDeleted = false,
                            TextContentCode = "da_qck_act_10"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardQuickActionElementTag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("TextContentCode")
                        .HasColumnType("nvarchar(25)");

                    b.HasKey("Id");

                    b.HasIndex("TextContentCode");

                    b.ToTable("DashboardQuickActionElementTags");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 2,
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 3,
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 5,
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 4,
                            TextContentCode = "da_mt_tag_5"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardSection", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("DashboardSections");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            IsActive = true,
                            Name = "Quick Action"
                        },
                        new
                        {
                            Id = 2,
                            IsActive = true,
                            Name = "Subscriptions"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.Document", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Documents");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Experiment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<DateTime>("EstimatedEndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EstimatedStartDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("HaveUniqueUser")
                        .HasColumnType("bit");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("TotalUserCount")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Experiments");

                    b.HasData(
                        new
                        {
                            Id = new Guid("388863e6-c871-4089-99c3-f88907add739"),
                            CreatedDate = new DateTime(2022, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Subscription ABTesting",
                            EstimatedEndDate = new DateTime(2022, 9, 30, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            EstimatedStartDate = new DateTime(2022, 9, 5, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            HaveUniqueUser = false,
                            IsActive = true,
                            IsDeleted = false,
                            Status = 0,
                            TotalUserCount = 20000,
                            Type = 2
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Label")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)");

                    b.Property<string>("RulesJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId");

                    b.ToTable("ExperimentGroups");

                    b.HasData(
                        new
                        {
                            Id = new Guid("2e713d85-c4d6-4839-97d6-ca4bf4466a06"),
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ExperimentId = new Guid("388863e6-c871-4089-99c3-f88907add739"),
                            IsDeleted = false,
                            Label = "A",
                            RulesJson = ""
                        },
                        new
                        {
                            Id = new Guid("df767c68-424d-4efe-994c-78d27fefdf63"),
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            ExperimentId = new Guid("388863e6-c871-4089-99c3-f88907add739"),
                            IsDeleted = false,
                            Label = "B",
                            RulesJson = ""
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentGroupUser", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("UserId", "GroupId");

                    b.HasIndex("GroupId");

                    b.ToTable("ExperimentGroupUsers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId");

                    b.ToTable("ExperimentLogs");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Feature", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Features");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "MobileRecharge"
                        },
                        new
                        {
                            Id = 2,
                            Name = "MoneyTransfer"
                        },
                        new
                        {
                            Id = 3,
                            Name = "C3PayToC3Pay"
                        },
                        new
                        {
                            Id = 4,
                            Name = "BillPayments"
                        },
                        new
                        {
                            Id = 5,
                            Name = "SalaryAdvance"
                        },
                        new
                        {
                            Id = 6,
                            Name = "ReferralProgram"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.Identification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("BackScanFileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Birthdate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(9)
                        .HasColumnType("nvarchar(9)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("FaceMatchIsIdeal")
                        .HasColumnType("bit");

                    b.Property<decimal>("FaceMatchPercentage")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FrontScanFileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Gender")
                        .HasMaxLength(6)
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("NameMatchIsIdeal")
                        .HasColumnType("bit");

                    b.Property<decimal>("NameMatchPercentage")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<int?>("OriginalVerifierId")
                        .HasColumnType("int");

                    b.Property<string>("PostRemarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Posted")
                        .HasColumnType("bit");

                    b.Property<string>("SelfieFileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ServiceReference")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Type")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("UpdateType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("UserRegistrationRejectionReasonId")
                        .HasColumnType("int");

                    b.Property<int>("Vendor")
                        .HasColumnType("int");

                    b.Property<DateTime?>("VerificationDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("VerificationStatus")
                        .HasColumnType("int");

                    b.Property<Guid?>("VerifierId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("UserRegistrationRejectionReasonId");

                    b.HasIndex("VerifierId");

                    b.ToTable("Identifications");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Language", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Description")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("NativeName")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.HasKey("Code");

                    b.HasIndex("CountryCode");

                    b.ToTable("Languages");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("NickName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("RechargeType")
                        .HasColumnType("int");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AccountNumber");

                    b.HasIndex("CountryCode");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("NickName");

                    b.HasIndex("UserId");

                    b.ToTable("MobileRechargeBeneficiaries");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiaryProvider", b =>
                {
                    b.Property<Guid>("BeneficiaryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ProviderCode")
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("BeneficiaryId", "ProviderCode");

                    b.HasIndex("ProviderCode");

                    b.ToTable("MobileRechargeBeneficiaryProviders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeExternalTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("CallingCardPin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("CommisionRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CustomerFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DistributorFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalStatus")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ExternalTransactionId")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<DateTime?>("LastStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("MobileRechargeTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ReceiptParams")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("ReceiptText")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<decimal>("ReceiveAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ReceiveAmountWithTax")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceiveCurrency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal>("SendAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SendCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StatusDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StatusDescription")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TaxCalculation")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TaxName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("TaxRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ExternalTransactionId");

                    b.HasIndex("MobileRechargeTransactionId")
                        .IsUnique();

                    b.ToTable("MobileRechargeExternalTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("AdditionalInformation")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Benefits")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<decimal?>("CommissionRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<decimal?>("CustomFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("DefaultDisplayText")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool?>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LocalizationKey")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool?>("LookupBillsRequired")
                        .HasColumnType("bit");

                    b.Property<decimal?>("MaxCustomerFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MaxDistributorFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MaxReceiveCurrencyIso")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MaxReceiveValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MaxReceiveValueExcludingTax")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MaxSendCurrencyIso")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MaxSendValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MaxTaxCalculation")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("MaxTaxName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MaxTaxRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinCustomerFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinDistributorFee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MinReceiveCurrencyIso")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MinReceiveValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinReceiveValueExcludingTax")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MinSendCurrencyIso")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MinSendValue")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("MinTaxCalculation")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("MinTaxName")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("MinTaxRate")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProcessingMode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ProviderCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("RedemptionMechanism")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("RegionCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("UATNumber")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("ValidityPeriodIso")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Code");

                    b.HasIndex("CountryCode");

                    b.HasIndex("ProviderCode");

                    b.ToTable("MobileRechargeProducts");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("CustomerCareNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool?>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool?>("IsProcessingTransfers")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LogoUrl")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PaymentTypes")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ProcessStatusMessage")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RegionCodes")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("ShortName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ValidationRegex")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Code");

                    b.HasIndex("CountryCode");

                    b.ToTable("MobileRechargeProviders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeSupportedCountry", b =>
                {
                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Code")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastUpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("CountryCode");

                    b.ToTable("MobileRechargeSupportedCountries");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<Guid?>("BeneficiaryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorContext")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<decimal?>("Fee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRepeat")
                        .HasColumnType("bit");

                    b.Property<string>("ProductCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("ReceiveAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceiveCurrency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("RechargeType")
                        .HasColumnType("int");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal>("SendAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SendCurrency")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("TriesCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("BeneficiaryId");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("ProductCode");

                    b.HasIndex("ReferenceNumber")
                        .IsUnique()
                        .HasFilter("[ReferenceNumber] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.ToTable("MobileRechargeTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Address1")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Address2")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("BankBranchName")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("BankName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DocumentNumber")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("DocumentType")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IdentifierCode1")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IdentifierCode2")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool?>("IsBranchSelected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LastTransferReferenceNumber")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<Guid?>("LinkedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("MoneyTransferReasonId")
                        .HasColumnType("int");

                    b.Property<string>("NationalityCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("PostDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Posted")
                        .HasColumnType("bit");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("RequiresApproval")
                        .HasColumnType("bit");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StatusDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("TransferType")
                        .HasMaxLength(30)
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("FirstName");

                    b.HasIndex("LastName");

                    b.HasIndex("LinkedUserId");

                    b.HasIndex("MoneyTransferReasonId");

                    b.HasIndex("UserId");

                    b.ToTable("MoneyTransferBeneficiaries");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreationMessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DeletionMessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ExternalId")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("MoneyTransferBeneficiaryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("MoneyTransferBeneficiaryId")
                        .IsUnique();

                    b.ToTable("MoneyTransferExternalBeneficiaries");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<decimal?>("ChargeVat")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ChargesAmount")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("ChargesCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreditCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("CustomerAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DebitCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ExternalTransactionId")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<decimal?>("FxRate")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("IDExpDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IDIssueDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IDPlaceOfIssue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InvoiceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastStatusDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("MessageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("MoneyTransferTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StatusDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StatusDescription")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TTNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("TotalCharges")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal?>("TotalCreditAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalDebitAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("UniqueIdentificationNo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("WaivedCharge")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("ExternalTransactionId");

                    b.HasIndex("MoneyTransferTransactionId")
                        .IsUnique();

                    b.ToTable("MoneyTransferExternalTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferLimit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CountryCode")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode")
                        .IsUnique()
                        .HasFilter("[CountryCode] IS NOT NULL");

                    b.ToTable("MoneyTransferLimits");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferProfile", b =>
                {
                    b.Property<string>("EmiratesId")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("BatchId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DataFilename")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FileDate")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Status")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("EmiratesId");

                    b.ToTable("MoneyTransferProfiles");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TextContentCode")
                        .HasColumnType("nvarchar(25)");

                    b.HasKey("Id");

                    b.HasIndex("TextContentCode");

                    b.ToTable("MoneyTransferReasons");

                    b.HasData(
                        new
                        {
                            Id = 8,
                            IsActive = true,
                            Reason = "Family maintenance and savings"
                        },
                        new
                        {
                            Id = 21,
                            IsActive = false,
                            Reason = "Personal or other travel"
                        },
                        new
                        {
                            Id = 22,
                            IsActive = false,
                            Reason = "Education services"
                        },
                        new
                        {
                            Id = 24,
                            IsActive = false,
                            Reason = "Medical or health"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("CashPickUpPoint")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<string>("CashPickupPin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("ChargeVat")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("ChargesAmount")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("ChargesCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<decimal?>("ConversionRate")
                        .HasPrecision(18, 6)
                        .HasColumnType("decimal(18,6)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("MoneyTransferBeneficiaryId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("MoneyTransferReasonId")
                        .HasColumnType("int");

                    b.Property<decimal>("ReceiveAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReceiveCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ReferralCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Remarks")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ReversalDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("SendAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SendCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal?>("TotalCharges")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TransferType")
                        .HasColumnType("int");

                    b.Property<int?>("TriesCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("UsedForReferral")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UserId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("WaiveType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<decimal?>("WaivedCharge")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("MoneyTransferBeneficiaryId");

                    b.HasIndex("MoneyTransferReasonId");

                    b.HasIndex("ReferenceNumber")
                        .IsUnique()
                        .HasFilter("[ReferenceNumber] IS NOT NULL");

                    b.HasIndex("ReferralCode");

                    b.HasIndex("UserId");

                    b.ToTable("MoneyTransferTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MultimediaResource", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<int>("FeatureId")
                        .HasColumnType("int");

                    b.Property<string>("NationalityCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("varchar(3)");

                    b.Property<string>("ThumbnailUrl")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("varchar(5)");

                    b.Property<string>("Url")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.ToTable("MultimediaResources");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Order", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DeliveryDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("InstallmentAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("InstallmentsCount")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid?>("OrderAddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("OrderNumber")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValueSql("NEXT VALUE FOR OrderNumbers");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("ProductPrice")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReferenceNumber")
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal>("TransactionAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("OrderAddressId")
                        .IsUnique()
                        .HasFilter("[OrderAddressId] IS NOT NULL");

                    b.HasIndex("ProductId");

                    b.HasIndex("ReferenceNumber")
                        .IsUnique()
                        .HasFilter("[ReferenceNumber] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.OrderAddress", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Area")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("Building")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int?>("CityId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FloorNumber")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("RoomNumber")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CityId");

                    b.ToTable("OrderAddresses");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Popup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("CountryCode")
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ImageFileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.ToTable("Popups");
                });

            modelBuilder.Entity("C3Pay.Core.Models.PortalUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("RequiresPasswordReset")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Username")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("PortalUsers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("AttributesJson")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Currency")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ProductType")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Products");

                    b.HasDiscriminator<int>("ProductType");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Rating", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Comments")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FeatureId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("ShowedStoreRating")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Value")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("FeatureId");

                    b.HasIndex("UserId");

                    b.ToTable("Ratings");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ReferrerCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(6)
                        .HasColumnType("nvarchar(6)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ReferrerCodes");
                });

            modelBuilder.Entity("C3Pay.Core.Models.SecretAnswer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Answer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("SecurityQuestionId")
                        .HasColumnType("int");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SecurityQuestionId");

                    b.HasIndex("UserId");

                    b.ToTable("SecretAnswers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.SecurityQuestion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Question")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("SecurityQuestions");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Question = "What is your mother's first name?"
                        },
                        new
                        {
                            Id = 2,
                            Question = "What is your father's first name?"
                        },
                        new
                        {
                            Id = 3,
                            Question = "What was the name of your school?"
                        },
                        new
                        {
                            Id = 4,
                            Question = "What is the first name of your first child?"
                        },
                        new
                        {
                            Id = 5,
                            Question = "In what city or town was your first job?"
                        },
                        new
                        {
                            Id = 6,
                            Question = "Who was your childhood's sports hero?"
                        },
                        new
                        {
                            Id = 7,
                            Question = "What is the first name of your best friend in school?"
                        },
                        new
                        {
                            Id = 8,
                            Question = "What was the name of the company where you had your first job?"
                        },
                        new
                        {
                            Id = 9,
                            Question = "What is your wife or husband's first name?"
                        },
                        new
                        {
                            Id = 10,
                            Question = "What was your first car?"
                        },
                        new
                        {
                            Id = 11,
                            Question = "What is your eldest brother or sister's name?"
                        },
                        new
                        {
                            Id = 12,
                            Question = "What is your youngest brother or sister's name?"
                        },
                        new
                        {
                            Id = 13,
                            Question = "What was the name of your favorite school teacher?"
                        },
                        new
                        {
                            Id = 14,
                            Question = "What was your father's last job?"
                        },
                        new
                        {
                            Id = 15,
                            Question = "What city did you fly for the first time by plane?"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.StoreExperimentUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CardHolderId")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("Id");

                    b.ToTable("StoreExperimentUsers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<decimal>("Fee")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FullDescription")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsAvailable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("PaymentFrequency")
                        .HasColumnType("int");

                    b.Property<string>("ShortDescription")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TextContentCode")
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("Title")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("TextContentCode");

                    b.ToTable("Subscriptions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770"),
                            Code = "BE",
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayOrder = 1,
                            Fee = 1m,
                            FullDescription = "View your current balance, save on ATM balance enquiry fees and view your current balance at any time as often as you want on this app",
                            IsAvailable = true,
                            IsDeleted = false,
                            PaymentFrequency = 1,
                            ShortDescription = "View your current balance at any time.",
                            Title = "Balance Enquiry"
                        },
                        new
                        {
                            Id = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"),
                            Code = "T",
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayOrder = 2,
                            Fee = 3m,
                            FullDescription = "Secure your card from being used without your knowledge. Keep your account safe by getting an SMS whenever:",
                            IsAvailable = true,
                            IsDeleted = false,
                            PaymentFrequency = 1,
                            ShortDescription = "Receive an SMS after every transaction.",
                            Title = "Security Alerts"
                        },
                        new
                        {
                            Id = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b"),
                            Code = "S",
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            DisplayOrder = 3,
                            Fee = 0.5m,
                            FullDescription = "Receive an SMS as soon as your salary is deposited into your account:",
                            IsAvailable = true,
                            IsDeleted = false,
                            PaymentFrequency = 2,
                            ShortDescription = "Receive an SMS with your salary.",
                            Title = "SMS Salary Alert"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.SubscriptionFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsAvailable")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TextContentCode")
                        .HasColumnType("nvarchar(25)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("TextContentCode");

                    b.ToTable("SubscriptionFeatures");

                    b.HasData(
                        new
                        {
                            Id = new Guid("94b6c134-5e52-4019-acb1-9c0884c28e31"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "View your balance",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
                        },
                        new
                        {
                            Id = new Guid("596a0b4c-9c68-48ab-b574-4a8fc3431edd"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "View your transactions",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
                        },
                        new
                        {
                            Id = new Guid("170fd6ae-7c74-4604-bc5e-f0f79ea6398a"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Anytime & anywhere",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
                        },
                        new
                        {
                            Id = new Guid("3e569303-038e-413e-b3e2-3dfe3f3b2b76"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Avoid fees on ATMs",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("30a85d94-3485-4d6f-87dd-e115e67f5770")
                        },
                        new
                        {
                            Id = new Guid("38f76250-0e8b-4985-adb1-7852368d6884"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "You receive your salary",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
                        },
                        new
                        {
                            Id = new Guid("09fb418d-6219-4c5e-87df-990c8b050919"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used at the ATM",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
                        },
                        new
                        {
                            Id = new Guid("0156f81c-a76f-43e8-9a95-b8244c6cd725"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used in shops",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
                        },
                        new
                        {
                            Id = new Guid("36f0989d-ddf5-474c-b18c-e9343d35edb5"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used online",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2")
                        },
                        new
                        {
                            Id = new Guid("5a35e788-6c84-4d40-9d12-03490902a21e"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "You receive your salary",
                            IsAvailable = true,
                            IsDeleted = false,
                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
                        },
                        new
                        {
                            Id = new Guid("76990d20-b275-4ba6-8b99-d9cc27205421"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used at the ATM",
                            IsAvailable = false,
                            IsDeleted = false,
                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
                        },
                        new
                        {
                            Id = new Guid("7e14e718-7981-4bae-a08f-f146a5408642"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used in shops",
                            IsAvailable = false,
                            IsDeleted = false,
                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
                        },
                        new
                        {
                            Id = new Guid("f760a7bf-3f2c-45be-9f23-0bb3e0d96e50"),
                            CreatedDate = new DateTime(2020, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Description = "Your card is used online",
                            IsAvailable = false,
                            IsDeleted = false,
                            SubscriptionId = new Guid("9a79dd89-2fcc-43b8-801b-8261be81c97b")
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.TextContent", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("LanguageCode")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Text")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Code");

                    b.HasIndex("LanguageCode");

                    b.ToTable("TextContents");

                    b.HasData(
                        new
                        {
                            Code = "da_qck_act_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_ind_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_ind"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_pak_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_pak"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_lka_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_lka"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_phl_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_phl"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_bgd_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_bgd"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Send Money",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge Local",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Recharge International",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Loan",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "C3Pay to C3Pay",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_6",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay Bills",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_7",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "History & Settlements",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_8",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "View ATM Pin",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_npl_9",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Pay International Bills",
                            Type = "da_qck_act_npl"
                        },
                        new
                        {
                            Code = "da_qck_act_10",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get Security Alerts",
                            Type = "da_qck_act"
                        },
                        new
                        {
                            Code = "da_mt_tag_1",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "No Charges",
                            Type = "da_mt_tag"
                        },
                        new
                        {
                            Code = "da_mt_tag_2",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "From AED 5",
                            Type = "da_mt_tag"
                        },
                        new
                        {
                            Code = "da_mt_tag_3",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "Get 2.5%",
                            Type = "da_mt_tag"
                        },
                        new
                        {
                            Code = "da_mt_tag_4",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "0 Deduction",
                            Type = "da_mt_tag"
                        },
                        new
                        {
                            Code = "da_mt_tag_5",
                            IsActive = false,
                            LanguageCode = "en",
                            Order = 99,
                            Text = "AED 10 Only",
                            Type = "da_mt_tag"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
                {
                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("Amount")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AuthenticationCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("BillPayType")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("CardAccountId")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CardAccountTerminalAddress")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CardAccountTerminalId")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CardNumber")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("CardSerialNumber")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Date")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("EndBalance")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("MacValue")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Origin")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PayeeId")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("RelativeSequenceNumber")
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)");

                    b.Property<int>("ServiceProvider")
                        .HasColumnType("int");

                    b.Property<string>("StatusCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("StatusDescription")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Time")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ReferenceNumber");

                    b.HasIndex("UserId");

                    b.ToTable("Transactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Translation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Text")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("TextContentCode")
                        .IsRequired()
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.HasKey("Id");

                    b.HasIndex("LanguageCode");

                    b.HasIndex("TextContentCode");

                    b.ToTable("Translations");

                    b.HasData(
                        new
                        {
                            Id = 164,
                            LanguageCode = "en",
                            Text = "Send Money",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 165,
                            LanguageCode = "bn",
                            Text = "টাকা পাঠান",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 166,
                            LanguageCode = "hi",
                            Text = "घर पैसे भेजें",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 167,
                            LanguageCode = "hi-en",
                            Text = "Ghar Paisa bhejiye",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 168,
                            LanguageCode = "ml",
                            Text = "പണം അയക്കുക",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 169,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள்",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 170,
                            LanguageCode = "ur-en",
                            Text = "Send Money",
                            TextContentCode = "da_qck_act_1"
                        },
                        new
                        {
                            Id = 171,
                            LanguageCode = "en",
                            Text = "Send Money To India",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 172,
                            LanguageCode = "bn",
                            Text = "নেপালে  টাকা পাঠান",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 173,
                            LanguageCode = "hi",
                            Text = "इंडिया पैसे भेजें",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 174,
                            LanguageCode = "hi-en",
                            Text = "India Paisa Bhejiye",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 175,
                            LanguageCode = "ml",
                            Text = "ഇന്ത്യയിലേക്ക് പണം അയയ്ക്കുക",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 176,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் இந்தியாவிற்கு",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 177,
                            LanguageCode = "ur-en",
                            Text = "Send Money To India",
                            TextContentCode = "da_qck_act_ind_1"
                        },
                        new
                        {
                            Id = 178,
                            LanguageCode = "en",
                            Text = "Send Money To Pakistan",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 179,
                            LanguageCode = "bn",
                            Text = "পাকিস্তানে টাকা পাঠান",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 180,
                            LanguageCode = "hi",
                            Text = "पाकिस्तान पैसे भेजें",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 181,
                            LanguageCode = "hi-en",
                            Text = "Pakistan Paise Bhejiye",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 182,
                            LanguageCode = "ml",
                            Text = "പാക്കിസ്ഥാനിലേക്ക് പണം അയയ്ക്കു",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 183,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் பாகிஸ்தானுக்கு",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 184,
                            LanguageCode = "ur-en",
                            Text = "Send Money To Pakistan",
                            TextContentCode = "da_qck_act_pak_1"
                        },
                        new
                        {
                            Id = 185,
                            LanguageCode = "en",
                            Text = "Send Money To SriLanka",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 186,
                            LanguageCode = "bn",
                            Text = "শ্রীলংকা  টাকা পাঠান",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 187,
                            LanguageCode = "hi",
                            Text = "श्री लंका पैसे भेजें",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 188,
                            LanguageCode = "hi-en",
                            Text = "Sri Lanka Paisa Bhejiye",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 189,
                            LanguageCode = "ml",
                            Text = "ശ്രീലങ്കയിലേക്ക് പണം അയയ്ക്കുക",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 190,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் இலங்கைக்கு",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 191,
                            LanguageCode = "ur-en",
                            Text = "Send Money To SriLanka",
                            TextContentCode = "da_qck_act_lka_1"
                        },
                        new
                        {
                            Id = 192,
                            LanguageCode = "en",
                            Text = "Send Money To Philippines",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 193,
                            LanguageCode = "bn",
                            Text = "ফিলিপিন্স টাকা পাঠান",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 194,
                            LanguageCode = "hi",
                            Text = "फिलीपींस पैसे भेजें",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 195,
                            LanguageCode = "hi-en",
                            Text = "Philippines Paise Bhejiye",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 196,
                            LanguageCode = "ml",
                            Text = "ഫിലിപ്പീൻസിലേക്ക് പണം അയയ്ക്കുക",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 197,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் பிலிப்பைன்ஸுக்கு",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 198,
                            LanguageCode = "ur-en",
                            Text = "Send Money To Philippines",
                            TextContentCode = "da_qck_act_phl_1"
                        },
                        new
                        {
                            Id = 199,
                            LanguageCode = "en",
                            Text = "Send Money To Bangladesh",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 200,
                            LanguageCode = "bn",
                            Text = "বাংলাদেশে টাকা পাঠান",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 201,
                            LanguageCode = "hi",
                            Text = "बांग्लादेश पैसे भेजें",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 202,
                            LanguageCode = "hi-en",
                            Text = "Bangladesh Paisa Bhejiye",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 203,
                            LanguageCode = "ml",
                            Text = "ബംഗ്ലാദേശിലേക്ക് പണം അയയ്ക്കുക",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 204,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் பங்களாதேஷுக்கு",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 205,
                            LanguageCode = "ur-en",
                            Text = "Send Money To Bangladesh",
                            TextContentCode = "da_qck_act_bgd_1"
                        },
                        new
                        {
                            Id = 206,
                            LanguageCode = "en",
                            Text = "Send Money To Nepal",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 207,
                            LanguageCode = "bn",
                            Text = "নেপালে  টাকা পাঠান",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 208,
                            LanguageCode = "hi",
                            Text = "नेपाल पैसे भेजें",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 209,
                            LanguageCode = "hi-en",
                            Text = "Nepal Paise Bhejiye",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 210,
                            LanguageCode = "ml",
                            Text = "നേപ്പാളിലേക്ക് പണം അയയ്ക്കുക",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 211,
                            LanguageCode = "ta",
                            Text = "பணம் அனுப்புங்கள் நேபாளத்திற்கு",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 212,
                            LanguageCode = "ur-en",
                            Text = "Send Money To Nepal",
                            TextContentCode = "da_qck_act_npl_1"
                        },
                        new
                        {
                            Id = 213,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 214,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 215,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 216,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 217,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 218,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 219,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_2"
                        },
                        new
                        {
                            Id = 220,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 221,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 222,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 223,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 224,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 225,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 226,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_ind_2"
                        },
                        new
                        {
                            Id = 227,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 228,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 229,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 230,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 231,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 232,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 233,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_pak_2"
                        },
                        new
                        {
                            Id = 234,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 235,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 236,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 237,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 238,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 239,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 240,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_lka_2"
                        },
                        new
                        {
                            Id = 241,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 242,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 243,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 244,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 245,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 246,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 247,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_phl_2"
                        },
                        new
                        {
                            Id = 248,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 249,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 250,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 251,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 252,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 253,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 254,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_bgd_2"
                        },
                        new
                        {
                            Id = 255,
                            LanguageCode = "en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 256,
                            LanguageCode = "bn",
                            Text = "দু  এটিসালাত রিচার্জ করুন",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 257,
                            LanguageCode = "hi",
                            Text = "रिचार्ज Du Etisalat",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 258,
                            LanguageCode = "hi-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 259,
                            LanguageCode = "ml",
                            Text = "ഡു എത്തിസലാത്ത് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 260,
                            LanguageCode = "ta",
                            Text = "Du Etisalat  ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 261,
                            LanguageCode = "ur-en",
                            Text = "Recharge Du Etisalat",
                            TextContentCode = "da_qck_act_npl_2"
                        },
                        new
                        {
                            Id = 262,
                            LanguageCode = "en",
                            Text = "Recharge International",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 263,
                            LanguageCode = "bn",
                            Text = "ইন্টারন্যাশনাল রিচার্জ",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 264,
                            LanguageCode = "hi",
                            Text = "अंतर्राष्ट्रीय रिचार्ज",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 265,
                            LanguageCode = "hi-en",
                            Text = "Recharge International",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 266,
                            LanguageCode = "ml",
                            Text = "അന്താരാഷ്ട്ര റീചാർജ്",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 267,
                            LanguageCode = "ta",
                            Text = "சர்வதேச ரீசார்ஜ்",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 268,
                            LanguageCode = "ur-en",
                            Text = "Recharge International",
                            TextContentCode = "da_qck_act_3"
                        },
                        new
                        {
                            Id = 269,
                            LanguageCode = "en",
                            Text = "Recharge India",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 270,
                            LanguageCode = "bn",
                            Text = "রিচার্জ ভারত",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 271,
                            LanguageCode = "hi",
                            Text = "रिचार्ज इंडिया",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 272,
                            LanguageCode = "hi-en",
                            Text = "Recharge India",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 273,
                            LanguageCode = "ml",
                            Text = "ഇന്ത്യയിലേക്ക്  റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 274,
                            LanguageCode = "ta",
                            Text = "இந்தியாவிற்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 275,
                            LanguageCode = "ur-en",
                            Text = "Recharge India",
                            TextContentCode = "da_qck_act_ind_3"
                        },
                        new
                        {
                            Id = 276,
                            LanguageCode = "en",
                            Text = "Recharge Pakistan",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 277,
                            LanguageCode = "bn",
                            Text = "রিচার্জ পাকিস্তান",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 278,
                            LanguageCode = "hi",
                            Text = "रिचार्ज पाकिस्तान",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 279,
                            LanguageCode = "hi-en",
                            Text = "Recharge Pakistan",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 280,
                            LanguageCode = "ml",
                            Text = "പാകിസ്ഥാനിലേക്ക് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 281,
                            LanguageCode = "ta",
                            Text = "பாகிஸ்தானுக்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 282,
                            LanguageCode = "ur-en",
                            Text = "Recharge Pakistan",
                            TextContentCode = "da_qck_act_pak_3"
                        },
                        new
                        {
                            Id = 283,
                            LanguageCode = "en",
                            Text = "Recharge SriLanka",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 284,
                            LanguageCode = "bn",
                            Text = "রিচার্জ শ্রীলঙ্কা",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 285,
                            LanguageCode = "hi",
                            Text = "रिचार्ज श्री लंका",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 286,
                            LanguageCode = "hi-en",
                            Text = "Recharge SriLanka",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 287,
                            LanguageCode = "ml",
                            Text = "ശ്രീലങ്കയിലേക്ക് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 288,
                            LanguageCode = "ta",
                            Text = "இலங்கைக்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 289,
                            LanguageCode = "ur-en",
                            Text = "Recharge SriLanka",
                            TextContentCode = "da_qck_act_lka_3"
                        },
                        new
                        {
                            Id = 290,
                            LanguageCode = "en",
                            Text = "Recharge Philippines",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 291,
                            LanguageCode = "bn",
                            Text = "রিচার্জ ফিলিপনিএস",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 292,
                            LanguageCode = "hi",
                            Text = "रिचार्ज फिलीपींस",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 293,
                            LanguageCode = "hi-en",
                            Text = "Recharge Philippines",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 294,
                            LanguageCode = "ml",
                            Text = "ഫിലിപ്പീൻസിലേക്ക് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 295,
                            LanguageCode = "ta",
                            Text = "பிலிப்பைன்ஸுக்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 296,
                            LanguageCode = "ur-en",
                            Text = "Recharge Philippines",
                            TextContentCode = "da_qck_act_phl_3"
                        },
                        new
                        {
                            Id = 297,
                            LanguageCode = "en",
                            Text = "Recharge Bangladesh",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 298,
                            LanguageCode = "bn",
                            Text = "রিচার্জ  বাংলাদেশ",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 299,
                            LanguageCode = "hi",
                            Text = "रिचार्ज बांग्लादेश",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 300,
                            LanguageCode = "hi-en",
                            Text = "Recharge Bangladesh",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 301,
                            LanguageCode = "ml",
                            Text = "ബംഗ്ലാദേശിലേക്ക് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 302,
                            LanguageCode = "ta",
                            Text = "பங்களாதேஷுக்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 303,
                            LanguageCode = "ur-en",
                            Text = "Recharge Bangladesh",
                            TextContentCode = "da_qck_act_bgd_3"
                        },
                        new
                        {
                            Id = 304,
                            LanguageCode = "en",
                            Text = "Recharge Nepal",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 305,
                            LanguageCode = "bn",
                            Text = "রিচার্জ  নেপাল",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 306,
                            LanguageCode = "hi",
                            Text = "रिचार्ज नेपाल",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 307,
                            LanguageCode = "hi-en",
                            Text = "Recharge Nepal",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 308,
                            LanguageCode = "ml",
                            Text = "നേപ്പാളിലേക്ക് റീചാർജ് ചെയ്യുക",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 309,
                            LanguageCode = "ta",
                            Text = "நேபாளத்திற்கு ரீசார்ஜ் செய்ய",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 310,
                            LanguageCode = "ur-en",
                            Text = "Recharge Nepal",
                            TextContentCode = "da_qck_act_npl_3"
                        },
                        new
                        {
                            Id = 311,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 312,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 313,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 314,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 315,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 316,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 317,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_4"
                        },
                        new
                        {
                            Id = 318,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 319,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 320,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 321,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 322,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 323,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 324,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_ind_4"
                        },
                        new
                        {
                            Id = 325,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 326,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 327,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 328,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 329,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 330,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 331,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_pak_4"
                        },
                        new
                        {
                            Id = 332,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 333,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 334,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 335,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 336,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 337,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 338,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_lka_4"
                        },
                        new
                        {
                            Id = 339,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 340,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 341,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 342,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 343,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 344,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 345,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_phl_4"
                        },
                        new
                        {
                            Id = 346,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 347,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 348,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 349,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 350,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 351,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 352,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_bgd_4"
                        },
                        new
                        {
                            Id = 353,
                            LanguageCode = "en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 354,
                            LanguageCode = "bn",
                            Text = "লোন পান",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 355,
                            LanguageCode = "hi",
                            Text = "लोन पायें",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 356,
                            LanguageCode = "hi-en",
                            Text = "Loan Paiye",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 357,
                            LanguageCode = "ml",
                            Text = "ലോൺ നേടുക",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 358,
                            LanguageCode = "ta",
                            Text = "கடன் பெறுங்கள்",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 359,
                            LanguageCode = "ur-en",
                            Text = "Get Loan",
                            TextContentCode = "da_qck_act_npl_4"
                        },
                        new
                        {
                            Id = 360,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 361,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 362,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 363,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 364,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 365,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 366,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_5"
                        },
                        new
                        {
                            Id = 367,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 368,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 369,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 370,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 371,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 372,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 373,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_ind_5"
                        },
                        new
                        {
                            Id = 374,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 375,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 376,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 377,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 378,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 379,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 380,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_pak_5"
                        },
                        new
                        {
                            Id = 381,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 382,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 383,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 384,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 385,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 386,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 387,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_lka_5"
                        },
                        new
                        {
                            Id = 388,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 389,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 390,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 391,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 392,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 393,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 394,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_phl_5"
                        },
                        new
                        {
                            Id = 395,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 396,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 397,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 398,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 399,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 400,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 401,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_bgd_5"
                        },
                        new
                        {
                            Id = 402,
                            LanguageCode = "en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 403,
                            LanguageCode = "bn",
                            Text = "C3Pay থেকে C3Pay",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 404,
                            LanguageCode = "hi",
                            Text = "C3Pay से C3Pay",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 405,
                            LanguageCode = "hi-en",
                            Text = "C3 Pay se C3 Pay",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 406,
                            LanguageCode = "ml",
                            Text = "C3Pay മുതൽ C3Pay വരെ",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 407,
                            LanguageCode = "ta",
                            Text = "C3Pay இல் இருந்து C3Pay இற்கு",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 408,
                            LanguageCode = "ur-en",
                            Text = "C3Pay to C3Pay",
                            TextContentCode = "da_qck_act_npl_5"
                        },
                        new
                        {
                            Id = 409,
                            LanguageCode = "en",
                            Text = "Pay Bills",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 410,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 411,
                            LanguageCode = "hi",
                            Text = "अंतर्राष्ट्रीय बिल चुकाए",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 412,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 413,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 414,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 415,
                            LanguageCode = "ur-en",
                            Text = "Pay Bills",
                            TextContentCode = "da_qck_act_6"
                        },
                        new
                        {
                            Id = 416,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 417,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 418,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 419,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 420,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 421,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 422,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_ind_6"
                        },
                        new
                        {
                            Id = 423,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 424,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন ",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 425,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 426,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 427,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 428,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 429,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_pak_6"
                        },
                        new
                        {
                            Id = 430,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 431,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন ",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 432,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 433,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 434,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 435,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 436,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_lka_6"
                        },
                        new
                        {
                            Id = 437,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 438,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন ",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 439,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 440,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 441,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 442,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 443,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_phl_6"
                        },
                        new
                        {
                            Id = 444,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 445,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন ",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 446,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 447,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 448,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 449,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 450,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_bgd_6"
                        },
                        new
                        {
                            Id = 451,
                            LanguageCode = "en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 452,
                            LanguageCode = "bn",
                            Text = "লোকাল বিলাস পে করুন ",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 453,
                            LanguageCode = "hi",
                            Text = "लोकल बिल चुकाए",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 454,
                            LanguageCode = "hi-en",
                            Text = "Local Bills Pay Karein",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 455,
                            LanguageCode = "ml",
                            Text = "പ്രാദേശിക ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 456,
                            LanguageCode = "ta",
                            Text = "உள்ளூர் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 457,
                            LanguageCode = "ur-en",
                            Text = "Pay Local Bills",
                            TextContentCode = "da_qck_act_npl_6"
                        },
                        new
                        {
                            Id = 458,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 459,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 460,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 461,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 462,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 463,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 464,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_7"
                        },
                        new
                        {
                            Id = 465,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 466,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 467,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 468,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 469,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 470,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 471,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_ind_7"
                        },
                        new
                        {
                            Id = 472,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 473,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 474,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 475,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 476,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 477,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 478,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_pak_7"
                        },
                        new
                        {
                            Id = 479,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 480,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 481,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 482,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 483,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 484,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 485,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_lka_7"
                        },
                        new
                        {
                            Id = 486,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 487,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 488,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 489,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 490,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 491,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 492,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_phl_7"
                        },
                        new
                        {
                            Id = 493,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 494,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 495,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 496,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 497,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 498,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 499,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_bgd_7"
                        },
                        new
                        {
                            Id = 500,
                            LanguageCode = "en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 501,
                            LanguageCode = "bn",
                            Text = "হিস্ট্রি বা স্টেটমেন্টস",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 502,
                            LanguageCode = "hi",
                            Text = "हिस्ट्री और स्टेटमेंट्स",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 503,
                            LanguageCode = "hi-en",
                            Text = "History aur statements",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 504,
                            LanguageCode = "ml",
                            Text = "വിശദാംശങ്ങളും , പ്രസ്താവനകളും",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 505,
                            LanguageCode = "ta",
                            Text = "வரலாறு மற்றும் அறிக்கைகள்",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 506,
                            LanguageCode = "ur-en",
                            Text = "History and Statements",
                            TextContentCode = "da_qck_act_npl_7"
                        },
                        new
                        {
                            Id = 507,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 508,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 509,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 510,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 511,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 512,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 513,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_8"
                        },
                        new
                        {
                            Id = 514,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 515,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 516,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 517,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 518,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 519,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 520,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_ind_8"
                        },
                        new
                        {
                            Id = 521,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 522,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 523,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 524,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 525,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 526,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 527,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_pak_8"
                        },
                        new
                        {
                            Id = 528,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 529,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 530,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 531,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 532,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 533,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 534,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_lka_8"
                        },
                        new
                        {
                            Id = 535,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 536,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 537,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 538,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 539,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 540,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 541,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_phl_8"
                        },
                        new
                        {
                            Id = 542,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 543,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 544,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 545,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 546,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 547,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 548,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_bgd_8"
                        },
                        new
                        {
                            Id = 549,
                            LanguageCode = "en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 550,
                            LanguageCode = "bn",
                            Text = "এটিএম পিন দেখুন",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 551,
                            LanguageCode = "hi",
                            Text = "ATM PIN देखें",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 552,
                            LanguageCode = "hi-en",
                            Text = "ATM Pin Dekhein",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 553,
                            LanguageCode = "ml",
                            Text = "എടിഎം പിൻ കാണുക",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 554,
                            LanguageCode = "ta",
                            Text = "ATM PIN பார்க்க",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 555,
                            LanguageCode = "ur-en",
                            Text = "View ATM Pin",
                            TextContentCode = "da_qck_act_npl_8"
                        },
                        new
                        {
                            Id = 556,
                            LanguageCode = "en",
                            Text = "Pay International Bill",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 557,
                            LanguageCode = "bn",
                            Text = "ইন্টারন্যাশনাল বিল পে করুন",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 558,
                            LanguageCode = "hi",
                            Text = "अंतर्राष्ट्रीय बिल चुकाए",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 559,
                            LanguageCode = "hi-en",
                            Text = "International Bills Pay Karein",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 560,
                            LanguageCode = "ml",
                            Text = "അന്താരാഷ്ട്ര ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 561,
                            LanguageCode = "ta",
                            Text = "சர்வதேச பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 562,
                            LanguageCode = "ur-en",
                            Text = "Pay International Bill",
                            TextContentCode = "da_qck_act_9"
                        },
                        new
                        {
                            Id = 563,
                            LanguageCode = "en",
                            Text = "Pay India Bills",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 564,
                            LanguageCode = "bn",
                            Text = "ভারত এ বিল পে করুন",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 565,
                            LanguageCode = "hi",
                            Text = "इंडिया के बिल चुकाए",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 566,
                            LanguageCode = "hi-en",
                            Text = "India Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 567,
                            LanguageCode = "ml",
                            Text = "ഇന്ത്യയിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 568,
                            LanguageCode = "ta",
                            Text = "இந்தியாவுக்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 569,
                            LanguageCode = "ur-en",
                            Text = "Pay India Bills",
                            TextContentCode = "da_qck_act_ind_9"
                        },
                        new
                        {
                            Id = 570,
                            LanguageCode = "en",
                            Text = "Pay Pakistan Bills",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 571,
                            LanguageCode = "bn",
                            Text = "পাকিস্তান এ বিল পে করুন",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 572,
                            LanguageCode = "hi",
                            Text = "पाकिस्तान के बिल चुकाए",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 573,
                            LanguageCode = "hi-en",
                            Text = "Pakistan Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 574,
                            LanguageCode = "ml",
                            Text = "പാകിസ്ഥാനിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 575,
                            LanguageCode = "ta",
                            Text = "பாகிஸ்தானுக்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 576,
                            LanguageCode = "ur-en",
                            Text = "Pay Pakistan Bills",
                            TextContentCode = "da_qck_act_pak_9"
                        },
                        new
                        {
                            Id = 577,
                            LanguageCode = "en",
                            Text = "Pay Srilanka Bills",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 578,
                            LanguageCode = "bn",
                            Text = "শ্রী লঙ্কা এ বিল পে করুন",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 579,
                            LanguageCode = "hi",
                            Text = "श्री लंका के बिल चुकाए",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 580,
                            LanguageCode = "hi-en",
                            Text = "Srilanka Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 581,
                            LanguageCode = "ml",
                            Text = "ശ്രീലങ്കയിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 582,
                            LanguageCode = "ta",
                            Text = "இலங்கைக்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 583,
                            LanguageCode = "ur-en",
                            Text = "Pay Srilanka Bills",
                            TextContentCode = "da_qck_act_lka_9"
                        },
                        new
                        {
                            Id = 584,
                            LanguageCode = "en",
                            Text = "Pay Philippines Bills",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 585,
                            LanguageCode = "bn",
                            Text = "ফিলিপিন্স  এ বিল পে করুন",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 586,
                            LanguageCode = "hi",
                            Text = "फिलीपींस के बिल चुकाए",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 587,
                            LanguageCode = "hi-en",
                            Text = "Philippines Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 588,
                            LanguageCode = "ml",
                            Text = "ഫിലിപ്പീൻസിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 590,
                            LanguageCode = "ta",
                            Text = "பிலிப்பைன்ஸிற்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 600,
                            LanguageCode = "ur-en",
                            Text = "Pay Philippines Bills",
                            TextContentCode = "da_qck_act_phl_9"
                        },
                        new
                        {
                            Id = 601,
                            LanguageCode = "en",
                            Text = "Pay Bangladesh Bills",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 602,
                            LanguageCode = "bn",
                            Text = "বাংলাদেশ এ বিল পে করুন",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 603,
                            LanguageCode = "hi",
                            Text = "बांग्लादेश के बिल चुकाए",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 604,
                            LanguageCode = "hi-en",
                            Text = "Bangladesh Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 605,
                            LanguageCode = "ml",
                            Text = "ബംഗ്ലാദേശിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 606,
                            LanguageCode = "ta",
                            Text = "பங்களாதேஷிற்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 607,
                            LanguageCode = "ur-en",
                            Text = "Pay Bangladesh Bills",
                            TextContentCode = "da_qck_act_bgd_9"
                        },
                        new
                        {
                            Id = 608,
                            LanguageCode = "en",
                            Text = "Pay Nepal Bills",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 609,
                            LanguageCode = "bn",
                            Text = "নেপাল এ বিল পে করুন",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 610,
                            LanguageCode = "hi",
                            Text = "नेपाल के बिल चुकाए",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 611,
                            LanguageCode = "hi-en",
                            Text = "Nepal Ke Bills Pay Karein",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 612,
                            LanguageCode = "ml",
                            Text = "നേപ്പാളിലേക്ക് ബില്ലുകൾ അടയ്ക്കുക",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 613,
                            LanguageCode = "ta",
                            Text = "நேபாளத்திற்கான உங்கள் பில்களை செலுத்துங்கள்",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 614,
                            LanguageCode = "ur-en",
                            Text = "Pay Nepal Bills",
                            TextContentCode = "da_qck_act_npl_9"
                        },
                        new
                        {
                            Id = 615,
                            LanguageCode = "en",
                            Text = "Get Security Alerts",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 616,
                            LanguageCode = "bn",
                            Text = "সিকিউরিটি এলার্টস পান",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 617,
                            LanguageCode = "hi",
                            Text = "सिक्योरिटी अलर्टस पाएं",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 618,
                            LanguageCode = "hi-en",
                            Text = "Security Alerts Payein",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 619,
                            LanguageCode = "ml",
                            Text = "സുരക്ഷാ അലേർട്ടുകൾ നേടുക",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 620,
                            LanguageCode = "ta",
                            Text = "பாதுகாப்பு அலெர்ட் பெறுங்கள்",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 621,
                            LanguageCode = "ur-en",
                            Text = "Get Security Alerts",
                            TextContentCode = "da_qck_act_10"
                        },
                        new
                        {
                            Id = 622,
                            LanguageCode = "en",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 623,
                            LanguageCode = "bn",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 624,
                            LanguageCode = "hi",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 625,
                            LanguageCode = "hi-en",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 626,
                            LanguageCode = "ml",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 627,
                            LanguageCode = "ta",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 628,
                            LanguageCode = "ur-en",
                            Text = "No Charges",
                            TextContentCode = "da_mt_tag_1"
                        },
                        new
                        {
                            Id = 629,
                            LanguageCode = "en",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 630,
                            LanguageCode = "bn",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 631,
                            LanguageCode = "hi",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 632,
                            LanguageCode = "hi-en",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 633,
                            LanguageCode = "ml",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 634,
                            LanguageCode = "ta",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 635,
                            LanguageCode = "ur-en",
                            Text = "From AED 5",
                            TextContentCode = "da_mt_tag_2"
                        },
                        new
                        {
                            Id = 636,
                            LanguageCode = "en",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 637,
                            LanguageCode = "bn",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 638,
                            LanguageCode = "hi",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 639,
                            LanguageCode = "hi-en",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 640,
                            LanguageCode = "ml",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 641,
                            LanguageCode = "ta",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 642,
                            LanguageCode = "ur-en",
                            Text = "Get 2.5%",
                            TextContentCode = "da_mt_tag_3"
                        },
                        new
                        {
                            Id = 643,
                            LanguageCode = "en",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 644,
                            LanguageCode = "bn",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 645,
                            LanguageCode = "hi",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 646,
                            LanguageCode = "hi-en",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 647,
                            LanguageCode = "ml",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 648,
                            LanguageCode = "ta",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 649,
                            LanguageCode = "ur-en",
                            Text = "0 Deduction",
                            TextContentCode = "da_mt_tag_4"
                        },
                        new
                        {
                            Id = 650,
                            LanguageCode = "en",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 651,
                            LanguageCode = "bn",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 652,
                            LanguageCode = "hi",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 653,
                            LanguageCode = "hi-en",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 654,
                            LanguageCode = "ml",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 655,
                            LanguageCode = "ta",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        },
                        new
                        {
                            Id = 656,
                            LanguageCode = "ur-en",
                            Text = "AED 10 Only",
                            TextContentCode = "da_mt_tag_5"
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.UploadedDocument", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Url")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("UserId")
                        .IsRequired()
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UploadedDocuments");
                });

            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime?>("ATMPinBlockEndDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("BlockDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("BlockType")
                        .HasColumnType("int");

                    b.Property<string>("CardHolderId")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeviceToken")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("ExternalId")
                        .HasColumnType("int");

                    b.Property<bool>("HasOldTransactions")
                        .HasColumnType("bit");

                    b.Property<bool>("IsBlocked")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVerified")
                        .HasColumnType("bit");

                    b.Property<int>("MoneyTransferProfileStatus")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)");

                    b.Property<string>("ReferralCode")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<bool>("RequiresPasswordReset")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("UnblockDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("UserBlockReasonId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CardHolderId");

                    b.HasIndex("CreatedDate");

                    b.HasIndex("ExternalId");

                    b.HasIndex("PhoneNumber");

                    b.HasIndex("ReferralCode");

                    b.HasIndex("UserBlockReasonId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("C3Pay.Core.Models.UserBlockReason", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Name")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.ToTable("UserBlockReasons");
                });

            modelBuilder.Entity("C3Pay.Core.Models.VerificationComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int?>("OriginalAdminId")
                        .HasColumnType("int");

                    b.Property<Guid?>("PortalUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("PortalUserId");

                    b.HasIndex("UserId");

                    b.ToTable("VerificationComments");
                });

            modelBuilder.Entity("C3Pay.Core.UserSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasDefaultValueSql("NEWSEQUENTIALID()");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("PaymentWaved")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("UserId");

                    b.ToTable("UserSubscriptions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Phone", b =>
                {
                    b.HasBaseType("C3Pay.Core.Models.Product");

                    b.HasDiscriminator().HasValue(1);

                    b.HasData(
                        new
                        {
                            Id = new Guid("27826ac1-9d05-45df-8076-0579a5885ae0"),
                            AttributesJson = "{\"Brand\":\"Samsung\",\"Model\":\"A13\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA134GB 128GB.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 660m
                        },
                        new
                        {
                            Id = new Guid("71eb3e4f-c494-4d2d-af71-1003818c54a8"),
                            AttributesJson = "{\"Brand\":\"Samsung\",\"Model\":\"A23\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Samsung A23 4GB128GB.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 760m
                        },
                        new
                        {
                            Id = new Guid("ef8eaca8-5878-44b5-bd47-7fb1f63ad3b1"),
                            AttributesJson = "{\"Brand\":\"Samsung\",\"Model\":\"A33 \",\"BackCamera\":\"48MP\",\"FrontCamera\":\"13MP\",\"ScreenSize\":\"6.4 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/SamsungA336GB128GB5g.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 1040m
                        },
                        new
                        {
                            Id = new Guid("8c1c9bb2-94c5-4713-99a5-7355eeb32add"),
                            AttributesJson = "{\"Brand\":\"Oppo\",\"Model\":\"A74\",\"BackCamera\":\"48MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OPPORenoA74.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 920m
                        },
                        new
                        {
                            Id = new Guid("b3f63af3-1394-46b9-be5c-bc50d8dc5865"),
                            AttributesJson = "{\"Brand\":\"Oppo\",\"Model\":\"RENO 8Z \",\"BackCamera\":\"64MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Gold\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoReno8z.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 1520m
                        },
                        new
                        {
                            Id = new Guid("43976d37-4cae-4f1f-9c44-5487576d4481"),
                            AttributesJson = "{\"Brand\":\"Apple\",\"Model\":\"Iphone 11\",\"BackCamera\":\"12MP\",\"FrontCamera\":\"12MP\",\"ScreenSize\":\"6.1 inches\",\"Sim\":\"4G Single SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/Apple11.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 2220m
                        },
                        new
                        {
                            Id = new Guid("404a3b6f-8181-40e2-90e6-31ef551d115d"),
                            AttributesJson = "{\"Brand\":\"Vivo\",\"Model\":\"Y33S\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.58 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Blue\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY33s.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 920m
                        },
                        new
                        {
                            Id = new Guid("9d3ba1d9-1c38-4ead-8cfc-8438df74c4ab"),
                            AttributesJson = "{\"Brand\":\"Vivo\",\"Model\":\"Y22S\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.55 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Blue\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/VivoY22s.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 840m
                        },
                        new
                        {
                            Id = new Guid("854e5c74-4203-4c17-a760-ddc3a943a2be"),
                            AttributesJson = "{\"Brand\":\"Oppo\",\"Model\":\"A77\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.56 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Black\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/OppoA77Black.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 780m
                        },
                        new
                        {
                            Id = new Guid("dd43d743-c359-4ea1-9d8c-4927404db06b"),
                            AttributesJson = "{\"Brand\":\"Redmi\",\"Model\":\"Note 11\",\"BackCamera\":\"50MP\",\"FrontCamera\":\"8MP\",\"ScreenSize\":\"6.5 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 720m
                        },
                        new
                        {
                            Id = new Guid("fc90b41e-bd5c-4d93-aba5-5414412da7dd"),
                            AttributesJson = "{\"Brand\":\"Redmi\",\"Model\":\"Note11 Pro 4G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.67 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro4g.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 960m
                        },
                        new
                        {
                            Id = new Guid("3f9d0f43-7996-4103-b796-b9054fd47ded"),
                            AttributesJson = "{\"Brand\":\"Redmi\",\"Model\":\"Note11 Pro 5G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.67 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11pro5g.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 1060m
                        },
                        new
                        {
                            Id = new Guid("869b6ab6-488b-469a-94a7-33ff84974542"),
                            AttributesJson = "{\"Brand\":\"Redmi\",\"Model\":\"Note 11S 4G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"16MP\",\"ScreenSize\":\"6.43 inches\",\"Sim\":\"4G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S4G.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 840m
                        },
                        new
                        {
                            Id = new Guid("59f640ca-90df-466f-8492-23d258cb3b67"),
                            AttributesJson = "{\"Brand\":\"Redmi\",\"Model\":\"Note 11S 5G\",\"BackCamera\":\"108MP\",\"FrontCamera\":\"13MP\",\"ScreenSize\":\"6.6 inches\",\"Sim\":\"5G Dual SIM\",\"Memory\":\"128 GB\",\"Warranty\":\"1 Year \",\"Colour\":\"Grey\",\"ImageUrl\":\"https://eaec3sharedsp.blob.core.windows.net/store-phone-images/RedmiNote11S5g.png\"}",
                            CreatedDate = new DateTime(2022, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Currency = "AED",
                            IsDeleted = false,
                            Price = 1000m
                        });
                });

            modelBuilder.Entity("C3Pay.Core.Models.AuditTrail", b =>
                {
                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
                        .WithMany("AuditTrails")
                        .HasForeignKey("PortalUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("AuditTrails")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PortalUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentBiller", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentCategory", "Category")
                        .WithMany("Billers")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.BillPaymentProvider", "Provider")
                        .WithMany("Billers")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("BillPaymentBillers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Provider");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentBillerIO", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentBiller", "Biller")
                        .WithMany("BillerIOs")
                        .HasForeignKey("BillerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.BillPaymentProductIO", "ProductIO")
                        .WithMany("BillerIOs")
                        .HasForeignKey("ProductIOId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Biller");

                    b.Navigation("ProductIO");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentCategoryProvider", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentProvider", "Provider")
                        .WithMany("CategoryProviders")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.BillPaymentSubCategory", "SubCategory")
                        .WithMany("CategoryProviders")
                        .HasForeignKey("SubCategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Provider");

                    b.Navigation("SubCategory");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProduct", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentProvider", "Provider")
                        .WithMany("Products")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProductIO", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentProduct", "Product")
                        .WithMany("IOs")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProvider", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("BillPaymentProviders")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProviderFee", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentProduct", "Product")
                        .WithMany("ProviderFees")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.BillPaymentProvider", "Provider")
                        .WithMany("ProviderFees")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Product");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentSubCategory", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentCategory", "Category")
                        .WithMany("SubCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentBiller", "Biller")
                        .WithMany("Transactions")
                        .HasForeignKey("BillerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
                        .WithOne("BillPaymentTransaction")
                        .HasForeignKey("C3Pay.Core.Models.BillPaymentTransaction", "ReferenceNumber");

                    b.Navigation("Biller");

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BlackListedEntity", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("BlackListedEntities")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPaymentCategoryFee", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentCategory", "Category")
                        .WithMany("CategoryFees")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("CategoryFees")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Category");

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.BillPaymentExternalTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.BillPaymentTransaction", "BillPaymentTransaction")
                        .WithOne("ExternalTransaction")
                        .HasForeignKey("C3Pay.Core.Models.C3Pay.BillPaymentExternalTransaction", "BillPaymentTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BillPaymentTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBank", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("Banks")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", "MoneyTransferPartner")
                        .WithMany("MoneyTransferBanks")
                        .HasForeignKey("MoneyTransferPartnerId");

                    b.Navigation("Country");

                    b.Navigation("MoneyTransferPartner");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBranch", b =>
                {
                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBank", "Bank")
                        .WithMany("Branches")
                        .HasForeignKey("BankId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Bank");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferCorridor", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryCode");

                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", "MoneyTransferPartner")
                        .WithMany("MoneyTransferCorridors")
                        .HasForeignKey("MoneyTransferPartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("MoneyTransferPartner");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartnerReason", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryCode");

                    b.HasOne("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", "MoneyTransferPartner")
                        .WithMany()
                        .HasForeignKey("MoneyTransferPartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
                        .WithMany()
                        .HasForeignKey("MoneyTransferReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("MoneyTransferPartner");

                    b.Navigation("MoneyTransferReason");
                });

            modelBuilder.Entity("C3Pay.Core.Models.City", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("Cities")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardElement", b =>
                {
                    b.HasOne("C3Pay.Core.Models.DashboardSection", "Section")
                        .WithMany("Elements")
                        .HasForeignKey("SectionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Section");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardQuickActionElement", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("DashboardQuickActionElements")
                        .HasForeignKey("CountryCode");

                    b.HasOne("C3Pay.Core.Models.DashboardElement", "DashboardElement")
                        .WithMany("QuickActionElements")
                        .HasForeignKey("ElementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany()
                        .HasForeignKey("TextContentCode");

                    b.Navigation("Country");

                    b.Navigation("DashboardElement");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardQuickActionElementTag", b =>
                {
                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany()
                        .HasForeignKey("TextContentCode");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Document", b =>
                {
                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Documents")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentGroup", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Experiment", "Experiment")
                        .WithMany("ExperimentGroups")
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentGroupUser", b =>
                {
                    b.HasOne("C3Pay.Core.Models.ExperimentGroup", "Group")
                        .WithMany("ExperimentGroupUsers")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("ExperimentGroupUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentLog", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Experiment", "Experiment")
                        .WithMany("ExperimentLogs")
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Identification", b =>
                {
                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Identifications")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.C3Pay.Lookup.UserRegistrationRejectionReason", "UserRegistrationRejectionReason")
                        .WithMany("Identifications")
                        .HasForeignKey("UserRegistrationRejectionReasonId");

                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
                        .WithMany("VerifiedIdentifications")
                        .HasForeignKey("VerifierId");

                    b.Navigation("PortalUser");

                    b.Navigation("User");

                    b.Navigation("UserRegistrationRejectionReason");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Language", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("Languages")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("MobileRechargeBeneficiaries")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("MobileRechargeBeneficiaries")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiaryProvider", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MobileRechargeBeneficiary", "Beneficiary")
                        .WithMany("BeneficiaryProviders")
                        .HasForeignKey("BeneficiaryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.MobileRechargeProvider", "Provider")
                        .WithMany("BeneficiaryProviders")
                        .HasForeignKey("ProviderCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Beneficiary");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeExternalTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MobileRechargeTransaction", "MobileRechargeTransaction")
                        .WithOne("ExternalTransaction")
                        .HasForeignKey("C3Pay.Core.Models.MobileRechargeExternalTransaction", "MobileRechargeTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MobileRechargeTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MobileRechargeSupportedCountry", "SupportedCountry")
                        .WithMany("Products")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.MobileRechargeProvider", "Provider")
                        .WithMany("Products")
                        .HasForeignKey("ProviderCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Provider");

                    b.Navigation("SupportedCountry");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MobileRechargeSupportedCountry", "SupportedCountry")
                        .WithMany("Providers")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SupportedCountry");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MobileRechargeBeneficiary", "Beneficiary")
                        .WithMany("MobileRechargeTransactions")
                        .HasForeignKey("BeneficiaryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("C3Pay.Core.Models.MobileRechargeProduct", "Product")
                        .WithMany("MobileRechargeTransactions")
                        .HasForeignKey("ProductCode")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
                        .WithOne("MobileRechargeTransaction")
                        .HasForeignKey("C3Pay.Core.Models.MobileRechargeTransaction", "ReferenceNumber");

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("MobileRechargeTransactions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Beneficiary");

                    b.Navigation("Product");

                    b.Navigation("Transaction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("MoneyTransferBeneficiaries")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "LinkedUser")
                        .WithMany("MoneyTransferDirectBeneficiaries")
                        .HasForeignKey("LinkedUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
                        .WithMany("MoneyTransferBeneficiaries")
                        .HasForeignKey("MoneyTransferReasonId");

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("MoneyTransferBeneficiaries")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Country");

                    b.Navigation("LinkedUser");

                    b.Navigation("MoneyTransferReason");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MoneyTransferBeneficiary", "MoneyTransferBeneficiary")
                        .WithOne("ExternalBeneficiary")
                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferExternalBeneficiary", "MoneyTransferBeneficiaryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MoneyTransferBeneficiary");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferExternalTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MoneyTransferTransaction", "MoneyTransferTransaction")
                        .WithOne("ExternalTransaction")
                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferExternalTransaction", "MoneyTransferTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MoneyTransferTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferLimit", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithOne("MoneyTransferLimit")
                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferLimit", "CountryCode");

                    b.OwnsOne("C3Pay.Core.Models.BankTransferLimit", "BankTransferLimit", b1 =>
                        {
                            b1.Property<int>("MoneyTransferLimitId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int")
                                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                            b1.Property<decimal?>("MaxAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<decimal?>("MaxMonthlyAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<int?>("MaxMonthlyCount")
                                .HasColumnType("int");

                            b1.Property<decimal?>("MinAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.HasKey("MoneyTransferLimitId");

                            b1.ToTable("MoneyTransferLimits");

                            b1.WithOwner()
                                .HasForeignKey("MoneyTransferLimitId");
                        });

                    b.OwnsOne("C3Pay.Core.Models.CashPickUpLimit", "CashPickUpLimit", b1 =>
                        {
                            b1.Property<int>("MoneyTransferLimitId")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int")
                                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                            b1.Property<decimal?>("MaxAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<decimal?>("MaxMonthlyAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<int?>("MaxMonthlyCount")
                                .HasColumnType("int");

                            b1.Property<decimal?>("MinAmount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.HasKey("MoneyTransferLimitId");

                            b1.ToTable("MoneyTransferLimits");

                            b1.WithOwner()
                                .HasForeignKey("MoneyTransferLimitId");
                        });

                    b.Navigation("BankTransferLimit");

                    b.Navigation("CashPickUpLimit");

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferReason", b =>
                {
                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany()
                        .HasForeignKey("TextContentCode");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.MoneyTransferBeneficiary", "MoneyTransferBeneficiary")
                        .WithMany("MoneyTransferTransactions")
                        .HasForeignKey("MoneyTransferBeneficiaryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.MoneyTransferReason", "MoneyTransferReason")
                        .WithMany("MoneyTransferTransactions")
                        .HasForeignKey("MoneyTransferReasonId");

                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
                        .WithOne("MoneyTransferTransaction")
                        .HasForeignKey("C3Pay.Core.Models.MoneyTransferTransaction", "ReferenceNumber");

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("MoneyTransferTransactions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MoneyTransferBeneficiary");

                    b.Navigation("MoneyTransferReason");

                    b.Navigation("Transaction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MultimediaResource", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Feature", "Feature")
                        .WithMany("MultimediaResources")
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Feature");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Order", b =>
                {
                    b.HasOne("C3Pay.Core.Models.OrderAddress", "OrderAddress")
                        .WithOne("Order")
                        .HasForeignKey("C3Pay.Core.Models.Order", "OrderAddressId");

                    b.HasOne("C3Pay.Core.Models.Product", "Product")
                        .WithMany("Orders")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.Transaction", "Transaction")
                        .WithOne("Order")
                        .HasForeignKey("C3Pay.Core.Models.Order", "ReferenceNumber");

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Orders")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("OrderAddress");

                    b.Navigation("Product");

                    b.Navigation("Transaction");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.OrderAddress", b =>
                {
                    b.HasOne("C3Pay.Core.Models.City", "City")
                        .WithMany("OrderAddresses")
                        .HasForeignKey("CityId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("City");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Popup", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Country", "Country")
                        .WithMany("Popups")
                        .HasForeignKey("CountryCode")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Country");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Rating", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Feature", "Feature")
                        .WithMany("Ratings")
                        .HasForeignKey("FeatureId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Ratings")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Feature");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.SecretAnswer", b =>
                {
                    b.HasOne("C3Pay.Core.Models.SecurityQuestion", "SecurityQuestion")
                        .WithMany("SecretAnswers")
                        .HasForeignKey("SecurityQuestionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("SecretAnswers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("SecurityQuestion");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Subscription", b =>
                {
                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany()
                        .HasForeignKey("TextContentCode");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.SubscriptionFeature", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Subscription", "Subscription")
                        .WithMany("Features")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany()
                        .HasForeignKey("TextContentCode");

                    b.Navigation("Subscription");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.TextContent", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Language", "Language")
                        .WithMany()
                        .HasForeignKey("LanguageCode");

                    b.Navigation("Language");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
                {
                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Transactions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("C3Pay.Core.Models.TranscationReversal", "Reversal", b1 =>
                        {
                            b1.Property<string>("TransactionReferenceNumber")
                                .HasColumnType("nvarchar(20)");

                            b1.Property<decimal?>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<string>("AuthenticationCode")
                                .HasMaxLength(10)
                                .HasColumnType("nvarchar(10)");

                            b1.Property<DateTime?>("Date")
                                .HasColumnType("datetime2");

                            b1.Property<decimal?>("EndBalance")
                                .HasPrecision(18, 2)
                                .HasColumnType("decimal(18,2)");

                            b1.Property<string>("ReferenceNumber")
                                .HasMaxLength(30)
                                .HasColumnType("nvarchar(30)");

                            b1.Property<string>("StatusCode")
                                .HasMaxLength(10)
                                .HasColumnType("nvarchar(10)");

                            b1.Property<string>("StatusDescription")
                                .HasColumnType("nvarchar(max)");

                            b1.HasKey("TransactionReferenceNumber");

                            b1.ToTable("Transactions");

                            b1.WithOwner()
                                .HasForeignKey("TransactionReferenceNumber");
                        });

                    b.Navigation("Reversal");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Translation", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Language", "Language")
                        .WithMany("Translations")
                        .HasForeignKey("LanguageCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.TextContent", "TextContent")
                        .WithMany("Translations")
                        .HasForeignKey("TextContentCode")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Language");

                    b.Navigation("TextContent");
                });

            modelBuilder.Entity("C3Pay.Core.Models.UploadedDocument", b =>
                {
                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("UploadedDocuments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
                {
                    b.HasOne("C3Pay.Core.Models.CardHolder", "CardHolder")
                        .WithMany("Users")
                        .HasForeignKey("CardHolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.UserBlockReason", "UserBlockReason")
                        .WithMany("Users")
                        .HasForeignKey("UserBlockReasonId");

                    b.OwnsOne("C3Pay.Core.Models.C3Pay.User.UserAutoUnblock", "AutoUnblock", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<DateTime?>("LastWrongAttemptDate")
                                .HasColumnType("datetime2");

                            b1.Property<int>("WrongAttemptsCount")
                                .HasColumnType("int");

                            b1.HasKey("UserId");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.OwnsOne("C3Pay.Core.Models.C3Pay.User.UserPreferences", "Preferences", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool>("ATMPinPopupEnabled")
                                .HasColumnType("bit");

                            b1.HasKey("UserId");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.OwnsOne("C3Pay.Core.Models.C3Pay.User.UserSpendPolicy", "SpendPolicy", b1 =>
                        {
                            b1.Property<Guid>("UserId")
                                .HasColumnType("uniqueidentifier");

                            b1.Property<bool?>("IsActive")
                                .HasColumnType("bit");

                            b1.Property<string>("PolicyId")
                                .HasMaxLength(50)
                                .HasColumnType("nvarchar(50)");

                            b1.HasKey("UserId");

                            b1.ToTable("Users");

                            b1.WithOwner()
                                .HasForeignKey("UserId");
                        });

                    b.Navigation("AutoUnblock");

                    b.Navigation("CardHolder");

                    b.Navigation("Preferences");

                    b.Navigation("SpendPolicy");

                    b.Navigation("UserBlockReason");
                });

            modelBuilder.Entity("C3Pay.Core.Models.VerificationComment", b =>
                {
                    b.HasOne("C3Pay.Core.Models.PortalUser", "PortalUser")
                        .WithMany("VerificationComments")
                        .HasForeignKey("PortalUserId");

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("VerificationComments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PortalUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.UserSubscription", b =>
                {
                    b.HasOne("C3Pay.Core.Models.Subscription", "Subscription")
                        .WithMany("UserSubscriptions")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("C3Pay.Core.Models.User", "User")
                        .WithMany("Subscriptions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Subscription");

                    b.Navigation("User");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentBiller", b =>
                {
                    b.Navigation("BillerIOs");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentCategory", b =>
                {
                    b.Navigation("Billers");

                    b.Navigation("CategoryFees");

                    b.Navigation("SubCategories");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProduct", b =>
                {
                    b.Navigation("IOs");

                    b.Navigation("ProviderFees");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProductIO", b =>
                {
                    b.Navigation("BillerIOs");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentProvider", b =>
                {
                    b.Navigation("Billers");

                    b.Navigation("CategoryProviders");

                    b.Navigation("Products");

                    b.Navigation("ProviderFees");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentSubCategory", b =>
                {
                    b.Navigation("CategoryProviders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.BillPaymentTransaction", b =>
                {
                    b.Navigation("ExternalTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.Lookup.UserRegistrationRejectionReason", b =>
                {
                    b.Navigation("Identifications");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferBank", b =>
                {
                    b.Navigation("Branches");
                });

            modelBuilder.Entity("C3Pay.Core.Models.C3Pay.MoneyTransfer.MoneyTransferPartner", b =>
                {
                    b.Navigation("MoneyTransferBanks");

                    b.Navigation("MoneyTransferCorridors");
                });

            modelBuilder.Entity("C3Pay.Core.Models.CardHolder", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("C3Pay.Core.Models.City", b =>
                {
                    b.Navigation("OrderAddresses");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Country", b =>
                {
                    b.Navigation("Banks");

                    b.Navigation("BillPaymentProviders");

                    b.Navigation("BlackListedEntities");

                    b.Navigation("CategoryFees");

                    b.Navigation("Cities");

                    b.Navigation("DashboardQuickActionElements");

                    b.Navigation("Languages");

                    b.Navigation("MobileRechargeBeneficiaries");

                    b.Navigation("MoneyTransferBeneficiaries");

                    b.Navigation("MoneyTransferLimit");

                    b.Navigation("Popups");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardElement", b =>
                {
                    b.Navigation("QuickActionElements");
                });

            modelBuilder.Entity("C3Pay.Core.Models.DashboardSection", b =>
                {
                    b.Navigation("Elements");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Experiment", b =>
                {
                    b.Navigation("ExperimentGroups");

                    b.Navigation("ExperimentLogs");
                });

            modelBuilder.Entity("C3Pay.Core.Models.ExperimentGroup", b =>
                {
                    b.Navigation("ExperimentGroupUsers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Feature", b =>
                {
                    b.Navigation("MultimediaResources");

                    b.Navigation("Ratings");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Language", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeBeneficiary", b =>
                {
                    b.Navigation("BeneficiaryProviders");

                    b.Navigation("MobileRechargeTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProduct", b =>
                {
                    b.Navigation("MobileRechargeTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeProvider", b =>
                {
                    b.Navigation("BeneficiaryProviders");

                    b.Navigation("Products");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeSupportedCountry", b =>
                {
                    b.Navigation("Products");

                    b.Navigation("Providers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MobileRechargeTransaction", b =>
                {
                    b.Navigation("ExternalTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferBeneficiary", b =>
                {
                    b.Navigation("ExternalBeneficiary");

                    b.Navigation("MoneyTransferTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferReason", b =>
                {
                    b.Navigation("MoneyTransferBeneficiaries");

                    b.Navigation("MoneyTransferTransactions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.MoneyTransferTransaction", b =>
                {
                    b.Navigation("ExternalTransaction");
                });

            modelBuilder.Entity("C3Pay.Core.Models.OrderAddress", b =>
                {
                    b.Navigation("Order");
                });

            modelBuilder.Entity("C3Pay.Core.Models.PortalUser", b =>
                {
                    b.Navigation("AuditTrails");

                    b.Navigation("VerificationComments");

                    b.Navigation("VerifiedIdentifications");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Product", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("C3Pay.Core.Models.SecurityQuestion", b =>
                {
                    b.Navigation("SecretAnswers");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Subscription", b =>
                {
                    b.Navigation("Features");

                    b.Navigation("UserSubscriptions");
                });

            modelBuilder.Entity("C3Pay.Core.Models.TextContent", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("C3Pay.Core.Models.Transaction", b =>
                {
                    b.Navigation("BillPaymentTransaction");

                    b.Navigation("MobileRechargeTransaction");

                    b.Navigation("MoneyTransferTransaction");

                    b.Navigation("Order");
                });

            modelBuilder.Entity("C3Pay.Core.Models.User", b =>
                {
                    b.Navigation("AuditTrails");

                    b.Navigation("BillPaymentBillers");

                    b.Navigation("Documents");

                    b.Navigation("ExperimentGroupUsers");

                    b.Navigation("Identifications");

                    b.Navigation("MobileRechargeBeneficiaries");

                    b.Navigation("MobileRechargeTransactions");

                    b.Navigation("MoneyTransferBeneficiaries");

                    b.Navigation("MoneyTransferDirectBeneficiaries");

                    b.Navigation("MoneyTransferTransactions");

                    b.Navigation("Orders");

                    b.Navigation("Ratings");

                    b.Navigation("SecretAnswers");

                    b.Navigation("Subscriptions");

                    b.Navigation("Transactions");

                    b.Navigation("UploadedDocuments");

                    b.Navigation("VerificationComments");
                });

            modelBuilder.Entity("C3Pay.Core.Models.UserBlockReason", b =>
                {
                    b.Navigation("Users");
                });
#pragma warning restore 612, 618
        }
    }
}
