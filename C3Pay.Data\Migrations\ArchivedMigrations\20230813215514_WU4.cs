﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU4 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AdditionalBeneficiaryFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FieldCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferBeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdditionalBeneficiaryFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdditionalBeneficiaryFields_MoneyTransferBeneficiaries_MoneyTransferBeneficiaryId",
                        column: x => x.MoneyTransferBeneficiaryId,
                        principalTable: "MoneyTransferBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdditionalBeneficiaryFields_MoneyTransferBeneficiaryId",
                table: "AdditionalBeneficiaryFields",
                column: "MoneyTransferBeneficiaryId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdditionalBeneficiaryFields");
        }
    }
}
