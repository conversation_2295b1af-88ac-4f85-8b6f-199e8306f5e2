﻿using Edenred.Common.Core;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace C3Pay.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UatController : Controller
    {
        private readonly IPushNotificationService _notificationService;

        public UatController(IPushNotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpGet("send-notifications")]

        public async Task<ActionResult> SendNotifications()
        {
            var token = "cw2w8M1QSz2Xx4UyLNmGGl:APA91bGD3qx4p_YCbcrp3d5EGqLrZgvzcRwu6Qm_Yg2oze6BREgqZ63zh2v7_bZYX7Tp2mWYQLgUYU1ypHnZQMwi-WV_6kLGlwqVkDkEhCO5UBLxljHV2wEVLJcENbKWcMtzAAnO7Amb";
            var result = await _notificationService.SendPushNotification(new SendPushNotificationRequest()
            {
                MessageTitle = "Title",
                MessageBody = "Body",
                UserRegistrationToken = token
            });
            if (result.IsSuccessful)
            {
                return Ok();
            }
            else
            {
                return BadRequest();
            }
        }
    }
}
