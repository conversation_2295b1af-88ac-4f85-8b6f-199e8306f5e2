﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SecuritySMSChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "IconUrl",
                table: "SubscriptionFeatures",
                type: "nvarchar(800)",
                maxLength: 800,
                nullable: true);

            migrationBuilder.InsertData(
                table: "Features",
                columns: new[] { "Id", "Name" },
                values: new object[] { 9, "Subscription_SecuritySMS" });

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("0156f81c-a76f-43e8-9a95-b8244c6cd725"),
                columns: new[] { "Description", "IconUrl" },
                values: new object[] { "Get SMS for shopping", "https://eaec3sharedsp.blob.core.windows.net/sms-security-icons/shopping.png" });

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("09fb418d-6219-4c5e-87df-990c8b050919"),
                columns: new[] { "Description", "IconUrl" },
                values: new object[] { "Get ATM SMS", "https://eaec3sharedsp.blob.core.windows.net/sms-security-icons/atm.png" });

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("38f76250-0e8b-4985-adb1-7852368d6884"),
                columns: new[] { "Description", "IconUrl" },
                values: new object[] { "Get Salary SMS", "https://eaec3sharedsp.blob.core.windows.net/sms-security-icons/salary.png" });

            migrationBuilder.UpdateData(
                table: "Subscriptions",
                keyColumn: "Id",
                keyValue: Guid.Parse("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"),
                column: "Title",
                value: "Security SMS");

            migrationBuilder.UpdateData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_10",
                column: "Text",
                value: "Get Security SMS");

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 44, 9, null, "hi", "IND", null, 1, " https://player.vimeo.com/progressive_redirect/playback/816877470/rendition/360p/file.mp4?loc=external&signature=18aa360dc76d720d73d150c8655ca9ec38d2464a51995fc6997053884044fa1d" });

            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "Identifier", "Language", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[] { 45, 9, null, "en", "PHL", null, 1, "https://player.vimeo.com/progressive_redirect/playback/816877509/rendition/360p/file.mp4?loc=external&signature=60f788ad44ab2c74a6792b40d43f19d286be4f64c25c0b48626e40ff2a797389" });

            var sql = @" 
                    Update Translations set Text  = 'Get Salary SMS' where Id = 72;
                    Update Translations set Text  = 'Get ATM SMS' where Id = 73;
                    Update Translations set Text  = 'Get SMS for shopping' where Id = 74;
                    Update Translations set Text = 'Security SMS' where Id in (615, 621);
            ";
            migrationBuilder.Sql(sql);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 44);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 45);

            migrationBuilder.DeleteData(
                table: "Features",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DropColumn(
                name: "IconUrl",
                table: "SubscriptionFeatures");

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("0156f81c-a76f-43e8-9a95-b8244c6cd725"),
                column: "Description",
                value: "Your card is used in shops");

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("09fb418d-6219-4c5e-87df-990c8b050919"),
                column: "Description",
                value: "Your card is used at the ATM");

            migrationBuilder.UpdateData(
                table: "SubscriptionFeatures",
                keyColumn: "Id",
                keyValue: Guid.Parse("38f76250-0e8b-4985-adb1-7852368d6884"),
                column: "Description",
                value: "You receive your salary");

            migrationBuilder.UpdateData(
                table: "Subscriptions",
                keyColumn: "Id",
                keyValue: Guid.Parse("c5ca013e-54dc-4de6-b2a4-b567bc0a2ad2"),
                column: "Title",
                value: "Security Alerts");

            migrationBuilder.UpdateData(
                table: "TextContents",
                keyColumn: "Code",
                keyValue: "da_qck_act_10",
                column: "Text",
                value: "Get Security Alerts");
        }
    }
}
