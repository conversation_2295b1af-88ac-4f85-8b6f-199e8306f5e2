﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Updated_MR_ProductAdjustablePriceFlag : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAdjustablePrice",
                table: "MobileRechargeProducts",
                type: "bit",
                nullable: false,
                defaultValue: false);

            var query = @"  
                	Update MobileRechargeProducts set IsAdjustablePrice = 1 where Code = 'NP_NC_TopUp';
	                Update MobileRechargeProducts set IsAdjustablePrice = 1 where Code = 'PK_UF_TopUp';

            ";

            migrationBuilder.Sql(query);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsAdjustablePrice",
                table: "MobileRechargeProducts"); 
        }
    }
}
