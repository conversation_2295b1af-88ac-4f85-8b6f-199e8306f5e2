{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"templatesLocation": {"type": "string", "metadata": {"description": "Templates Location URL"}}, "sasToken": {"type": "securestring", "metadata": {"description": "Shared access signature token"}}, "environment": {"type": "string", "metadata": {"description": "Target environment"}}, "entityCode": {"type": "string", "metadata": {"description": "BU code"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location where resource will be deployed to"}}, "secondaryLocation": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location where resource will be deployed to"}}, "appName": {"type": "string", "metadata": {"description": "Application name"}}, "appShortName": {"type": "string", "metadata": {"description": "Short Application name usec for KV"}}, "dbName": {"type": "string", "metadata": {"description": "Database name"}}, "dbSkuName": {"type": "string", "defaultValue": "S0", "metadata": {"description": "Describes database's pricing tier and instance size. Check details at  https://azure.microsoft.com/en-us/pricing/details/sql-database/single/"}, "allowedValues": ["S0", "S1", "S2", "S3", "S4", "S6", "S7", "S9", "S12", "P1", "P2", "P4", "P6", "P11", "P15"]}, "identityDbSkuName": {"type": "string", "defaultValue": "S0", "metadata": {"description": "Describes database's pricing tier and instance size. Check details at  https://azure.microsoft.com/en-us/pricing/details/sql-database/single/"}, "allowedValues": ["S0", "S1", "S2", "S3", "S4", "S6", "S7", "S9", "S12", "P1", "P2", "P4", "P6", "P11", "P15"]}, "farmSkuName": {"type": "string", "defaultValue": "B1", "allowedValues": ["F1", "D1", "B1", "B2", "B3", "S1", "S2", "S3", "P1v2", "P2v2", "P3v2"], "metadata": {"description": "Describes plan's pricing tier and instance size. Check details at https://azure.microsoft.com/en-us/pricing/details/app-service/"}}, "webJobFarmSkuName": {"type": "string", "defaultValue": "S1", "allowedValues": ["F1", "D1", "B1", "B2", "B3", "S1", "S2", "S3", "P1v2", "P2v2", "P3v2"], "metadata": {"description": "Describes plan's pricing tier and instance size. Check details at https://azure.microsoft.com/en-us/pricing/details/app-service/"}}, "farmSkuCapacity": {"type": "int", "defaultValue": 1, "minValue": 1, "metadata": {"description": "Describes plan's instance count"}}, "sqlsrvAdministratorLogin": {"type": "string"}, "sqlsrvAdministratorPassword": {"type": "securestring"}, "serviceBusSkuName": {"type": "string", "defaultValue": "Basic", "allowedValues": ["Basic", "Standard", "Premium"], "metadata": {"description": "SKU of the redis to deploy"}}, "serviceBusSkuCapcity": {"type": "int", "defaultValue": 1, "allowedValues": [1, 2, 4, 8], "metadata": {"description": "# of messaging units. Valid only for Premium : 2, 4, 8"}}, "healthCheckPath": {"type": "string", "defaultValue": "/api/health", "metadata": {"description": "The path to check for unhealthy instances."}}, "existingFarmName": {"type": "string", "defaultValue": "none", "metadata": {"description": "Existing App Service Plan"}}, "existingWebjobFarmName": {"type": "string", "defaultValue": "none", "metadata": {"description": "Existing Webjob App Service Plan"}}}, "variables": {"dbName": "C3Pay", "identityDbName": "C3Pay.Identity", "jobComponentName": "job", "portalComponentName": "portal", "storageJobAppName": "[concat(parameters('appName'), 'job')]"}, "resources": [{"apiVersion": "2017-05-10", "name": "sqlserver", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'sqlsrv.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "sqlsrvAdministratorLogin": {"value": "[parameters('sqlsrvAdministratorLogin')]"}, "sqlsrvAdministratorPassword": {"value": "[parameters('sqlsrvAdministratorPassword')]"}}}}, {"apiVersion": "2017-05-10", "name": "sqleb<PERSON><PERSON>b", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'sqldb.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "dbName": {"value": "[parameters('dbName')]"}, "dbSkuName": {"value": "[parameters('dbSkuName')]"}}}, "dependsOn": ["sqlserver"]}, {"apiVersion": "2017-05-10", "name": "identitysqldb", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'sqldb.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "dbName": {"value": "[variables('identityDbName')]"}, "dbSkuName": {"value": "[parameters('identityDbSkuName')]"}}}, "dependsOn": ["sqlserver"]}, {"apiVersion": "2017-05-10", "name": "appInsight", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'appInsights.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('secondaryLocation')]"}}}}, {"apiVersion": "2017-05-10", "name": "<PERSON><PERSON><PERSON>", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'vault.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "appShortName": {"value": "[parameters('appShortName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "region": {"value": "uaen1"}}}}, {"apiVersion": "2017-05-10", "name": "webApp", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'webApp.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('secondaryLocation')]"}, "farmSkuName": {"value": "[parameters('farmSkuName')]"}, "farmSkuCapacity": {"value": "[parameters('farmSkuCapacity')]"}, "healthCheckPath": {"value": "[parameters('healthCheckPath')]"}, "existingFarmName": {"value": "[parameters('existingFarmName')]"}}}}, {"apiVersion": "2017-05-10", "name": "portalWebApp", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'webApp.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "component": {"value": "[variables('portalComponentName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('secondaryLocation')]"}, "farmSkuName": {"value": "[parameters('farmSkuName')]"}, "farmSkuCapacity": {"value": "[parameters('farmSkuCapacity')]"}, "healthCheckPath": {"value": "[parameters('healthCheckPath')]"}, "existingFarmName": {"value": "[parameters('existingFarmName')]"}}}}, {"apiVersion": "2017-05-10", "name": "webJob", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'webApp.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "component": {"value": "[variables('jobComponentName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('secondaryLocation')]"}, "farmSkuName": {"value": "[parameters('webJobFarmSkuName')]"}, "farmSkuCapacity": {"value": 1}, "existingFarmName": {"value": "[parameters('existingWebjobFarmName')]"}}}}, {"apiVersion": "2017-05-10", "name": "serviceBus", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'), 'serviceBus.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "skuName": {"value": "[parameters('serviceBusSkuName')]"}, "skuCapcity": {"value": "[parameters('serviceBusSkuCapcity')]"}}}}, {"apiVersion": "2017-05-10", "name": "storage", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'),'storage.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[parameters('appName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}, "networkRulesDefaultAction": {"value": "Allow"}, "softDeleteEnabled": {"value": true}, "softDeleteDuration": {"value": 30}}}}, {"apiVersion": "2017-05-10", "name": "storageWebJob", "type": "Microsoft.Resources/deployments", "properties": {"mode": "incremental", "templateLink": {"uri": "[concat(uri(parameters('templatesLocation'),'storage.json'), parameters('sasToken'))]", "contentVersion": "*******"}, "parameters": {"entityCode": {"value": "[parameters('entityCode')]"}, "appName": {"value": "[variables('storageJobAppName')]"}, "environment": {"value": "[parameters('environment')]"}, "location": {"value": "[parameters('location')]"}}}}], "outputs": {"farmName": {"type": "string", "value": "[reference('webApp').outputs.farmName.value]"}, "webAppName": {"type": "string", "value": "[reference('webApp').outputs.name.value]"}, "portalWebAppName": {"type": "string", "value": "[reference('portalWebApp').outputs.name.value]"}, "webJobName": {"type": "string", "value": "[reference('webJob').outputs.name.value]"}, "sqlName": {"type": "string", "value": "[reference('sqlserver').outputs.name.value]"}, "dbName": {"type": "string", "value": "[parameters('dbName')]"}, "identityDbName": {"type": "string", "value": "[variables('identityDbName')]"}, "aiName": {"type": "string", "value": "[reference('appInsight').outputs.name.value]"}, "keyvaultName": {"type": "string", "value": "[reference('keyVault').outputs.name.value]"}, "serviceBusName": {"type": "string", "value": "[reference('serviceBus').outputs.name.value]"}, "storageName": {"type": "string", "value": "[reference('storage').outputs.name.value]"}, "storageWebJobName": {"type": "string", "value": "[reference('storageWebJob').outputs.name.value]"}}}