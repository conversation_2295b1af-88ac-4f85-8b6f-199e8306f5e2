﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Reset_WU_3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AdditionalBeneficiaryFields");

            migrationBuilder.DropTable(
                name: "BeneficiaryAdditionFields");

            migrationBuilder.DropTable(
                name: "BeneficiaryAdditionFieldGroups");

            migrationBuilder.DropTable(
                name: "RemittanceDeliveryMethods");

            migrationBuilder.DropTable(
                name: "RemittanceDestinations");

            migrationBuilder.DropColumn(
                name: "EligibleForWallet",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "RatesLastUpdateAttemptSuccessful",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "WalletLatestRate",
                table: "Countries");

            migrationBuilder.CreateTable(
                name: "MoneyTransferSmartDefaults",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", maxLength: 2, nullable: false),
                    NeverSentMoneyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    NeverSentToBeneficiaryAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "varchar(3)", maxLength: 3, nullable: false),
                    Type = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferSmartDefaults", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferSmartDefaults_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "MoneyTransferSmartDefaults",
                columns: new[] { "Id", "CountryCode", "Currency", "NeverSentMoneyAmount", "NeverSentToBeneficiaryAmount", "Type" },
                values: new object[] { 1, "IN", "INR", 1000m, 10000m, "Receive" });

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferSmartDefaults_CountryCode",
                table: "MoneyTransferSmartDefaults",
                column: "CountryCode");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MoneyTransferSmartDefaults");

            migrationBuilder.AddColumn<bool>(
                name: "EligibleForWallet",
                table: "Countries",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "RatesLastUpdateAttemptSuccessful",
                table: "Countries",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "WalletLatestRate",
                table: "Countries",
                type: "decimal(18,2)",
                precision: 18,
                scale: 2,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "AdditionalBeneficiaryFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FieldCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferBeneficiaryId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AdditionalBeneficiaryFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AdditionalBeneficiaryFields_MoneyTransferBeneficiaries_MoneyTransferBeneficiaryId",
                        column: x => x.MoneyTransferBeneficiaryId,
                        principalTable: "MoneyTransferBeneficiaries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "RemittanceDestinations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Alpha2Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Alpha3Code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsPopular = table.Column<bool>(type: "bit", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RemittanceDestinations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RemittanceDeliveryMethods",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Alpha2Code = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    CurrencyCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DetailsUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    ProviderLogoUrl = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProviderName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RemittanceDeliveryType = table.Column<int>(type: "int", nullable: false),
                    RemittanceDestinationId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RemittanceDeliveryMethods", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RemittanceDeliveryMethods_Countries_Alpha2Code",
                        column: x => x.Alpha2Code,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_RemittanceDeliveryMethods_RemittanceDestinations_RemittanceDestinationId",
                        column: x => x.RemittanceDestinationId,
                        principalTable: "RemittanceDestinations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BeneficiaryAdditionFieldGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    RemittanceDeliveryMethodId = table.Column<int>(type: "int", nullable: true),
                    RemittanceDeliveryType = table.Column<int>(type: "int", nullable: true),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryAdditionFieldGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryAdditionFieldGroups_RemittanceDeliveryMethods_RemittanceDeliveryMethodId",
                        column: x => x.RemittanceDeliveryMethodId,
                        principalTable: "RemittanceDeliveryMethods",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "BeneficiaryAdditionFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BeneficiaryAdditionFieldGroupId = table.Column<int>(type: "int", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    DropdownReference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldType = table.Column<int>(type: "int", nullable: false),
                    Hint = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    MaxLength = table.Column<int>(type: "int", nullable: false),
                    MinLength = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryAdditionFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryAdditionFields_BeneficiaryAdditionFieldGroups_BeneficiaryAdditionFieldGroupId",
                        column: x => x.BeneficiaryAdditionFieldGroupId,
                        principalTable: "BeneficiaryAdditionFieldGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AdditionalBeneficiaryFields_MoneyTransferBeneficiaryId",
                table: "AdditionalBeneficiaryFields",
                column: "MoneyTransferBeneficiaryId");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryAdditionFieldGroups_RemittanceDeliveryMethodId",
                table: "BeneficiaryAdditionFieldGroups",
                column: "RemittanceDeliveryMethodId");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryAdditionFields_BeneficiaryAdditionFieldGroupId",
                table: "BeneficiaryAdditionFields",
                column: "BeneficiaryAdditionFieldGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_RemittanceDeliveryMethods_Alpha2Code",
                table: "RemittanceDeliveryMethods",
                column: "Alpha2Code");

            migrationBuilder.CreateIndex(
                name: "IX_RemittanceDeliveryMethods_RemittanceDestinationId",
                table: "RemittanceDeliveryMethods",
                column: "RemittanceDestinationId");
        }
    }
}
