﻿using C3Pay.Core.Models.C3Pay.Membership.C3PayPlus.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace C3Pay.Data.EntityConfigurations.Membership
{
    public class Log_C3PayPlusMembershipRenewalsConfiguration : IEntityTypeConfiguration<Log_C3PayPlusMembershipRenewals>
    {
        public void Configure(EntityTypeBuilder<Log_C3PayPlusMembershipRenewals> builder)
        {
            builder.ToTable("Logs_C3PayPlusMembershipRenewals");
            builder.<PERSON><PERSON>ey(e => e.Id);
        }
    }
}
