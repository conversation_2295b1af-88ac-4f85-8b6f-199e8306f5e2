﻿using System.Threading.Tasks;

namespace Edenred.Common.Core
{
    public interface ITableStorageService
    {
        Task<ServiceResponse> SetEntityAsync(string tableName, ValueEntity entity);
        Task<ServiceResponse<ValueEntity>> GetEntityAsync(string tableName, string partitionKey, string rowKey);
        Task<ServiceResponse> DeleteEntityAsync(string tableName, ValueEntity entity);
        Task<ServiceResponse> InsertEntityAsync(string tableName, ValueEntity entity);

    }
}
