﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_LuckyDrawWinners_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipLuckyDrawWinners",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TicketNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CorporateName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Location = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    WinningDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    WinningMonth = table.Column<int>(type: "int", nullable: false),
                    CanWinAfter = table.Column<DateTime>(type: "datetime2", nullable: false),
                    C3PayPlusMembershipUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipLuckyDrawWinners", x => x.Id);
                    table.ForeignKey(
                        name: "FK_C3PayPlusMembershipLuckyDrawWinners_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                        column: x => x.C3PayPlusMembershipUserId,
                        principalTable: "C3PayPlusMembershipUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipLuckyDrawWinners_C3PayPlusMembershipUserId",
                table: "C3PayPlusMembershipLuckyDrawWinners",
                column: "C3PayPlusMembershipUserId");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipLuckyDrawWinners_CanWinAfter",
                table: "C3PayPlusMembershipLuckyDrawWinners",
                column: "CanWinAfter");

            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipLuckyDrawWinners_WinningDate",
                table: "C3PayPlusMembershipLuckyDrawWinners",
                column: "WinningDate");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipLuckyDrawWinners");
        }
    }
}
