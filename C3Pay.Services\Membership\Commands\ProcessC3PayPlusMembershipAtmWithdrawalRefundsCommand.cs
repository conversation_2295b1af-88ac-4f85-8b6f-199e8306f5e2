﻿using C3Pay.Core;
using C3Pay.Core.Common;
using C3Pay.Core.Models.Messages.Membership;
using C3Pay.Core.Services;
using C3Pay.Services.Helper;
using Common.Core.Models.Messages.Integration.Transactions;
using Edenred.Common.Core;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Membership.Commands
{
    public class ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommand : IRequest<Result>
    {
        public string CardholderId { get; set; }
        public string TransactionId { get; set; }
    }


    public class ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommandHandler : IRequestHandler<ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommand, Result>
    {
        private readonly IAnalyticsPublisherService _analyticsPublisherService;
        private readonly ITextMessageSenderService _textMessageSenderService;
        private readonly ITransactionsB2CService _transactionsB2CService;
        private readonly IPPSWebAuthService _ppsWebAuthService;
        private readonly IFeatureManager _featureManager;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger _logger;

        public ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommandHandler(ILogger<ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommandHandler> logger,
                                                                            ITextMessageSenderService textMessageSenderService,
                                                                            IAnalyticsPublisherService analyticsPublisherService,
                                                                            ITransactionsB2CService transactionsB2CService,
                                                                            IPPSWebAuthService ppsWebAuthService,
                                                                            IFeatureManager featureManager,
                                                                            IUnitOfWork unitOfWork)
        {
            _analyticsPublisherService = analyticsPublisherService;
            _textMessageSenderService = textMessageSenderService;
            _transactionsB2CService = transactionsB2CService;
            _ppsWebAuthService = ppsWebAuthService;
            _featureManager = featureManager;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Result> Handle(ProcessC3PayPlusMembershipAtmWithdrawalRefundsCommand request, CancellationToken ct)
        {
            try
            {
                // If ATM fee reversals is unavailable, exit.
                var isEnabled = await _featureManager.IsEnabledAsync(FeatureFlags.C3PayPlus_Event_ProcessAtmWithdrawalRefunds);
                if (isEnabled == false)
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_FeatureNotEnabled.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_FeatureNotEnabled);
                }


                // Check params.
                if (string.IsNullOrWhiteSpace(request.CardholderId))
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_CardholderIdNotSent.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_CardholderIdNotSent);
                }

                // Find user.
                var user = await this._unitOfWork.Users.FirstOrDefaultAsync(x => x.CardHolderId == request.CardholderId && x.IsDeleted == false && x.IsBlocked == false, i => i.CardHolder);
                if (user is null)
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_UserNotFound.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_UserNotFound);
                }


                // Find membership user.
                var membershipUser = await this._unitOfWork.C3PayPlusMembershipUsers.FirstOrDefaultAsync(x => x.UserId == user.Id);
                if (membershipUser is null)
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_MembershipUserNotFound.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_MembershipUserNotFound);
                }


                if (membershipUser.IsActive == false)
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_MembershipIsNotActive.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_MembershipIsNotActive);
                }


                if (membershipUser.HasClaimedAtmWithdrawalFeeReversalForThisMonth == true)
                {
                    _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_UserAlreadyClaimedBenefit.Code);
                    return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_UserAlreadyClaimedBenefit);
                }


                // Get transaction details.
                var tryGetTransactionDetails = await this._transactionsB2CService.GetAtmTransactionDetails(new GetAtmTransactionDetailsRequest()
                {
                    TransactionId = request.TransactionId
                });


                if (tryGetTransactionDetails.IsSuccessful == false)
                {
                    _logger.LogError(Errors.C3PayPlus.CantGetAtmTransactionDetails.Code);
                    return Result.Failure(Errors.C3PayPlus.CantGetAtmTransactionDetails);
                }

                if (tryGetTransactionDetails.Data.ApprovedForRefund == false)
                {
                    _logger.LogError(Errors.C3PayPlus.CantGetAtmTransactionDetails.Code);
                    return Result.Failure(Errors.C3PayPlus.CantGetAtmTransactionDetails);
                }


                var feeAmount = Convert.ToDecimal(tryGetTransactionDetails.Data.FeeInAed); // Example: 2.5 and not 250

                // Process refunds.
                if (feeAmount == 0)
                {
                    // If the fee for this ATM withdrawal was already zero, we will count this as a free ATM withdrawal.
                    membershipUser.ClaimBenefit(C3PayPlusMonitaryBenefits.AtmWWithdrawalFeeRefund);
                    await this._unitOfWork.CommitAsync();

                    // Publish event.
                    await this.PublishAtmWithdrawalFeeReversalAnalyticsEvent(membershipUser, 0, true);

                    // Send SMS.
                    _ = await this._textMessageSenderService.SendAtmWithdrawalFeeReversalSms(membershipUser.User.PhoneNumber.ToShortPhoneNumber());
                }
                else
                {
                    // Generate reference number.
                    string referenceNumber = await GenerateAtmWithdrawalFeeReversalReferenceNumber(false);

                    // Perform the reversal.
                    var tryReverseAtmWithdrawalFee = await this.ReverseAtmWithdrawalFee(membershipUser, feeAmount);
                    if (tryReverseAtmWithdrawalFee.IsSuccessful == false)
                    {
                        // Publish event.
                        await this.PublishAtmWithdrawalFeeReversalAnalyticsEvent(membershipUser, feeAmount, false);
                    }
                    else
                    {
                        // Reverse was succesful. Save details and continue;
                        membershipUser.MarkAtmWithdrawalFeeReversalCompletion(tryReverseAtmWithdrawalFee.Data);
                        await this._unitOfWork.CommitAsync();

                        // Publish event.
                        await this.PublishAtmWithdrawalFeeReversalAnalyticsEvent(membershipUser, 0, true);

                        // Send SMS.
                        _ = await this._textMessageSenderService.SendAtmWithdrawalFeeReversalSms(membershipUser.User.PhoneNumber.ToShortPhoneNumber());

                    }
                }

                return Result.Success();

            }
            catch (Exception ex)
            {
                _logger.LogError(Errors.C3PayPlus.AtmWithdrawalRefunds_Exception.Code);
                return Result.Failure(Errors.C3PayPlus.AtmWithdrawalRefunds_Exception);
            }

        }


        private async Task<ServiceResponse> PublishAtmWithdrawalFeeReversalAnalyticsEvent(C3PayPlusMembershipUser membershipUser, decimal amount, bool reversed)
        {
            await _analyticsPublisherService.PublishAtmWithdrawalFeeReversalAnalyticsEvent(new C3PayPlusMembershipAtmWithdrawalEvent()
            {
                CitizenId = membershipUser.User.CardHolderId,
                UserId = membershipUser.User.Id.ToString(),
                AmountReversed = amount.ToString(),
                Successful = reversed ? "Yes" : "No",
            });

            return new ServiceResponse();
        }
        private async Task<string> GenerateAtmWithdrawalFeeReversalReferenceNumber(bool zeroFee)
        {
            var now = DateTime.Now;

            // Generate a reference number based on if the ATM withdrawal fee was zero or not.
            var referencePrefix = zeroFee == true ? TransactionPrefix.C3PARZ.ToString() : TransactionPrefix.C3PAR.ToString();

            // Format start date component.
            var startYear = now.Year.ToString().Substring(2);
            var startMonth = now.Month.ToString();
            var startDay = now.Day.ToString();

            // We are doing this because we want the date in this format.
            // 2022/08/24 becomes: 220824
            var dateComponent = Convert.ToDecimal(startYear.PadLeft(2, '0') + startMonth.PadLeft(2, '0') + startDay.PadLeft(2, '0'));

            referencePrefix = $"{referencePrefix}{dateComponent}X";
            var referenceDigits = 7;
            var referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            while (await this._unitOfWork.C3PayPlusMembershipTransactions.Any(t => t.ReferenceNumber == referenceNumber))
            {
                referenceNumber = TypeUtility.GetReferenceNumber(referencePrefix, referenceDigits);
            }

            return referenceNumber;
        }
        private async Task<ServiceResponse<C3PayPlusMembershipTransaction>> ReverseAtmWithdrawalFee(C3PayPlusMembershipUser membershipUser, decimal amount)
        {
            var cardNumber = membershipUser.User.CardHolder.CardNumber;
            var cardSerialNumber = membershipUser.User.CardHolder.CardSerialNumber;
            var cardPanNumber = cardNumber.Substring(cardNumber.Length - 4, 4);

            var topUpRequest = new TopUpRequest()
            {
                CardSerialNumber = cardSerialNumber,
                CardPanNumber = cardPanNumber,
                MerchantLoopCode = "5000",
                Amount = decimal.Truncate(amount * 100),

                Narration = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusAtmWithdrawalFeeReversal),
                Description = EnumUtility.GetDescriptionFromEnumValue(TransactionNarration.C3PayPlusAtmWithdrawalFeeReversal),

                TerminalId = TransactionMerchantCodeService.GetMerchantCode(false, MobileApplicationId.C3Pay, TransactionMerchantCodeFeature.C3PayPlusAtmWithdrawalFeeReversal),
                ReferenceNumber = membershipUser.LastAtmWithrawalReversalTransactionNumber
            };

            ServiceResponse<PPSWebAuthResponseModel> tryTopUpUser;

            try
            {
                tryTopUpUser = await _ppsWebAuthService.DoTopUp(topUpRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError($"{Errors.C3PayPlus.CantPerformCredit} EX: {ex}");
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            if (tryTopUpUser.IsSuccessful == false)
            {

                _logger.LogError($"{Errors.C3PayPlus.CantPerformCredit} EX: {tryTopUpUser.ErrorMessage}");
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            if (tryTopUpUser.Data.StatusCode != "00")
            {
                _logger.LogError($"{Errors.C3PayPlus.CantPerformCredit} EX: {tryTopUpUser.Data?.Message}");
                return new ServiceResponse<C3PayPlusMembershipTransaction>(false, Errors.C3PayPlus.CantPerformCredit);
            }

            var transactionDetails = new C3PayPlusMembershipTransaction()
            {
                UserId = membershipUser.User.Id,
                C3PayPlusMembershipUserId = membershipUser.Id,

                TransactionType = C3PayPlusMembershipTransactionType.AtmWithdrawalFeeReversal,

                WasAtmReversalAlreadyClaimed = false,

                CardNumber = tryTopUpUser.Data.Account.No,
                CardSerialNumber = tryTopUpUser.Data.Account.Serial,
                CardAccountTerminalAddress = tryTopUpUser.Data.TerminalAddress,
                CardAccountTerminalId = tryTopUpUser.Data.TerminalId,
                CardAccountId = PPSAccountIdPrefix.RMT.GetHashCode().ToString() + PPSMerchantType.RMT.GetHashCode().ToString() + EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.C3P),

                BillPayType = EnumUtility.GetDescriptionFromEnumValue(PPSBillPayType.C3P),
                ServiceProvider = CardPaymentServiceProvider.PPS,

                PayeeId = tryTopUpUser.Data.PayeeId,
                Amount = TypeUtility.GetDecimalFromString(tryTopUpUser.Data.Value.Amt) / 100,
                StatusDescription = tryTopUpUser.Data.Message,
                Date = string.IsNullOrEmpty(tryTopUpUser.Data.TimeStamp.Val) ? string.Empty : tryTopUpUser.Data.TimeStamp.Val.ToString().Split(' ').First(),
                Time = string.IsNullOrEmpty(tryTopUpUser.Data.TimeStamp.Val) ? string.Empty : tryTopUpUser.Data.TimeStamp.Val.ToString().Split(' ').Last(),
                TimeStamp = DateTime.Now,
                MacValue = tryTopUpUser.Data.HashValue,
                Origin = tryTopUpUser.Data.Origin,
                AuthenticationCode = tryTopUpUser.Data.AuthCode,
                ReferenceNumber = tryTopUpUser.Data.ReferenceId,
                EndBalance = TypeUtility.GetDecimalFromString(tryTopUpUser.Data.EndBalanace.Amt),

                StatusCode = Status.SUCCESSFUL.ToString(),
                Remarks = "FROM QUEUE " + Status.SUCCESSFUL.ToString()
            };

            return new ServiceResponse<C3PayPlusMembershipTransaction>(transactionDetails);
        }
    }
}