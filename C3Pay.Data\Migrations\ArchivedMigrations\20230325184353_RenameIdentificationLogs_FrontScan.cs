﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class RenameIdentificationLogs_FrontScan : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "FrontScanName",
                table: "PassportLogs",
                newName: "FrontScanUrl");

            migrationBuilder.RenameColumn(
                name: "FrontScanName",
                table: "EmiratesIdLogs",
                newName: "FrontScanUrl");

            migrationBuilder.RenameColumn(
                name: "BackScanName",
                table: "EmiratesIdLogs",
                newName: "BackScanUrl");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "FrontScanUrl",
                table: "PassportLogs",
                newName: "FrontScanName");

            migrationBuilder.RenameColumn(
                name: "FrontScanUrl",
                table: "EmiratesIdLogs",
                newName: "FrontScanName");

            migrationBuilder.RenameColumn(
                name: "BackScanUrl",
                table: "EmiratesIdLogs",
                newName: "BackScanName");
        }
    }
}
