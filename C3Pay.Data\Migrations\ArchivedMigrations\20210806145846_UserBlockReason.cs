﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UserBlockReason : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "UserBlockReasonId",
                table: "Users",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "BlackListedEntities",
                type: "bit",
                nullable: false,
                defaultValue: true,
                oldClrType: typeof(bool),
                oldType: "bit");

            migrationBuilder.CreateTable(
                name: "UserBlockReasons",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserBlockReasons", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_UserBlockReasonId",
                table: "Users",
                column: "UserBlockReasonId");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_UserBlockReasons_UserBlockReasonId",
                table: "Users",
                column: "UserBlockReasonId",
                principalTable: "UserBlockReasons",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_UserBlockReasons_UserBlockReasonId",
                table: "Users");

            migrationBuilder.DropTable(
                name: "UserBlockReasons");

            migrationBuilder.DropIndex(
                name: "IX_Users_UserBlockReasonId",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "UserBlockReasonId",
                table: "Users");

            migrationBuilder.AlterColumn<bool>(
                name: "IsActive",
                table: "BlackListedEntities",
                type: "bit",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "bit",
                oldDefaultValue: true);
        }
    }
}
