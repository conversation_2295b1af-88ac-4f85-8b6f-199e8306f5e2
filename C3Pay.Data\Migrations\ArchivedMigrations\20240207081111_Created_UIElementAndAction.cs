﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Created_UIElementAndAction : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UIElements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ElementType = table.Column<int>(type: "int", nullable: false),
                    PartnerType = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsVisible = table.Column<bool>(type: "bit", nullable: false),
                    PropertiesJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ActionJson = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UIElements", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UIElementUserActions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UIElementId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UIElementActionType = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UIElementUserActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UIElementUserActions_UIElements_UIElementId",
                        column: x => x.UIElementId,
                        principalTable: "UIElements",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                }); 

            migrationBuilder.InsertData(
                table: "UIElements",
                columns: new[] { "Id", "ActionJson", "CreatedDate", "DeletedBy", "DeletedDate", "ElementType", "IsActive", "IsDeleted", "IsVisible", "PartnerType", "PropertiesJson", "UpdatedDate" },
                values: new object[,]
                {
                    { new Guid("fdd79642-c552-49c7-aaf4-002d8a7f6320"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 2, "{\"Type\":0,\"Title\":\"Home\",\"ProductName\":\"Dashboard\",\"DeepLinkUrl\":\"/home\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_home_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_home.png\",\"UseTint\":true,\"Index\":0}", null },
                    { new Guid("069f9562-24e7-49ff-8101-d0542f8efac7"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 2, "{\"Type\":0,\"Title\":\"Recharge\",\"ProductName\":\"MobileRecharge\",\"DeepLinkUrl\":\"/mobilerecharge\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_mobilerecharge_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_mobilerecharge.png\",\"UseTint\":true,\"Index\":1}", null },
                    { new Guid("5269d6f8-5f5d-4a60-9a18-08ddb6f8dd64"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, false, 2, "{\"Type\":0,\"Title\":\"Bills\",\"ProductName\":\"Bills\",\"DeepLinkUrl\":\"/billpayments\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments.png\",\"UseTint\":true,\"Index\":2}", null },
                    { new Guid("4f987400-3dc0-4c67-ab9b-22de6b50d64e"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 2, "{\"Type\":0,\"Title\":\"Profile\",\"ProductName\":\"Profile\",\"DeepLinkUrl\":\"/profile\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_profile_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_profile.png\",\"UseTint\":true,\"Index\":3}", null },
                    { new Guid("d056625e-96ee-4ee9-9af6-9a0d8c047253"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 1, "{\"Type\":0,\"Title\":\"Home\",\"ProductName\":\"Dashboard\",\"DeepLinkUrl\":\"/home\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_home_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_home.png\",\"UseTint\":true,\"Index\":0}", null },
                    { new Guid("0329405d-f4d5-47d3-85e4-f7e9a074b2b4"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 1, "{\"Type\":0,\"Title\":\"Recharge\",\"ProductName\":\"MobileRecharge\",\"DeepLinkUrl\":\"/mobilerecharge\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_mobilerecharge_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_mobilerecharge.png\",\"UseTint\":true,\"Index\":1}", null },
                    { new Guid("635ec65d-078a-45dc-bf70-e9bcd1597d3b"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 1, "{\"Type\":0,\"Title\":\"Send Money\",\"ProductName\":\"MoneyTransfer\",\"DeepLinkUrl\":\"/moneytransfer\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_moneytransfer_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_moneytransfer.png\",\"UseTint\":true,\"Index\":2}", null },
                    { new Guid("6b1b43ea-b75f-4fdc-9a9d-893832d7dfe7"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, true, 1, "{\"Type\":0,\"Title\":\"Get Money\",\"ProductName\":\"SA\",\"DeepLinkUrl\":\"/salaryadvance\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_salaryadvance_selected_UAT.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_salaryadvance_UAT.png\",\"UseTint\":true,\"Index\":3}", null },
                    { new Guid("3746c79c-7bae-46d5-9d9f-ce8d8e28ee1c"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, false, 1, "{\"Type\":0,\"Title\":\"Bills\",\"ProductName\":\"Bills\",\"DeepLinkUrl\":\"/billpayments\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments.png\",\"UseTint\":true,\"Index\":4}", null },
                    { new Guid("4f35c408-574b-4a6a-9f42-cf0215d0af56"), null, new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, false, 1, "{\"Type\":0,\"Title\":\"Nol\",\"ProductName\":\"NolRecharge\",\"DeepLinkUrl\":\"/nol\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments.png\",\"UseTint\":true,\"Index\":4}", null },
                    { new Guid("5ba80ae3-e2a0-4e85-a159-8c9e259889e6"), "{\"Request\":{\"ActionType\":1,\"ToBeReplacedElementId\":\"4f35c408-574b-4a6a-9f42-cf0215d0af56\"}}", new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 0, true, false, false, 1, "{\"Type\":0,\"Title\":\"SMS\",\"ProductName\":\"SecuritySMS\",\"DeepLinkUrl\":\"/securityalerts\",\"SelectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments_selected.png\",\"DeselectedIconUrl\":\"https://eaec3sharedsp.blob.core.windows.net/c3pay-mobile-mainmenu-icons/mainmenu_billpayments.png\",\"UseTint\":true,\"Index\":4}", null },
                    { new Guid("9e27b089-7cf5-44d8-a999-13bc9e2ca3e1"), "{\"Request\":{\"ActionType\":2,\"ToBeReplacedElementId\":null}}", new DateTime(2024, 1, 25, 0, 0, 0, 0, DateTimeKind.Unspecified), null, null, 1, true, false, true, 1, "{\"Type\":1,\"FeeAmount\":3.15,\"ActionExpiryDays\":60,\"MaxViewCount\":2}", null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_UIElementUserActions_UIElementId",
                table: "UIElementUserActions",
                column: "UIElementId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UIElementUserActions");

            migrationBuilder.DropTable(
                name: "UIElements"); 
        }
    }
}
