﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class SeedML_MTReasonsData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var query = @"
                            update MoneyTransferReasons set TextContentCode='mt_re_1' where id=8
                            update MoneyTransferReasons set TextContentCode='mt_re_2' where id=21
                            update MoneyTransferReasons set TextContentCode='mt_re_3' where id=22
                            update MoneyTransferReasons set TextContentCode='mt_re_4' where id=24
                            ";
            migrationBuilder.Sql(query);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
