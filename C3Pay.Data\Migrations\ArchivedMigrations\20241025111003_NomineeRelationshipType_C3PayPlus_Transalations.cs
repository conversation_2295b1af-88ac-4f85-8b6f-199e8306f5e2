﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class NomineeRelationshipType_C3PayPlus_Transalations : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DisplayNameTranslationKey",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "InputQuestionTranslationKey",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "C3PayPlusMembershipTranslations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TextCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    English = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Hindi = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    HindiEnglish = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Malayalam = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Telugu = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Tamil = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Bangladesh = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UruthuEnglish = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_C3PayPlusMembershipTranslations", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 1,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeWife", "RelationShipTypeWifeQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeHusband", "RelationShipTypeHusbandQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 3,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeChild", "RelationShipTypeChildQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeParent", "RelationShipTypeParentQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeSibling", "RelationShipTypeSiblingQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 6,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeFriend", "RelationShipTypeFriendQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 7,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeFather", "RelationShipTypeFatherQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 8,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeMother", "RelationShipTypeMotherQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 9,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeSon", "RelationShipTypeSonQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 10,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeDaughter", "RelationShipTypeDaughterQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 11,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeBrother", "RelationShipTypeBrotherQuestion" });

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 12,
                columns: new[] { "DisplayNameTranslationKey", "InputQuestionTranslationKey" },
                values: new object[] { "RelationShipTypeSister", "RelationShipTypeSisterQuestion" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "C3PayPlusMembershipTranslations");

            migrationBuilder.DropColumn(
                name: "DisplayNameTranslationKey",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");

            migrationBuilder.DropColumn(
                name: "InputQuestionTranslationKey",
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes");
        }
    }
}
