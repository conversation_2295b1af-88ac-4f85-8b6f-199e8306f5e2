﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateExperimentUsers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers");

            migrationBuilder.AddColumn<string>(
                name: "NationalityCode",
                table: "ExperimentUsers",
                type: "varchar(3)",
                maxLength: 3,
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers");

            migrationBuilder.DropColumn(
                name: "NationalityCode",
                table: "ExperimentUsers");

            migrationBuilder.AddForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
