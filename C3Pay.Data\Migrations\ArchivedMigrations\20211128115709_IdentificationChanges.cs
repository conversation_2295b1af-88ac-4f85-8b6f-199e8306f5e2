﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class IdentificationChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EmiratesIdUpdates_PortalUsers_VerifierId",
                table: "EmiratesIdUpdates");

            migrationBuilder.DropForeignKey(
                name: "FK_EmiratesIdUpdates_Users_UserId",
                table: "EmiratesIdUpdates");

            migrationBuilder.DropPrimaryKey(
                name: "PK_EmiratesIdUpdates",
                table: "EmiratesIdUpdates");

            migrationBuilder.RenameTable(
                name: "EmiratesIdUpdates",
                newName: "Identifications");

            migrationBuilder.RenameColumn(
                name: "IdNumber",
                table: "Identifications",
                newName: "DocumentNumber");

            migrationBuilder.RenameIndex(
                name: "IX_EmiratesIdUpdates_VerifierId",
                table: "Identifications",
                newName: "IX_Identifications_VerifierId");

            migrationBuilder.RenameIndex(
                name: "IX_EmiratesIdUpdates_UserId",
                table: "Identifications",
                newName: "IX_Identifications_UserId");

            migrationBuilder.AddColumn<int>(
                name: "Type",
                table: "Identifications",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Identifications",
                table: "Identifications",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Identifications_PortalUsers_VerifierId",
                table: "Identifications",
                column: "VerifierId",
                principalTable: "PortalUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Identifications_Users_UserId",
                table: "Identifications",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Identifications_PortalUsers_VerifierId",
                table: "Identifications");

            migrationBuilder.DropForeignKey(
                name: "FK_Identifications_Users_UserId",
                table: "Identifications");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Identifications",
                table: "Identifications");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "Identifications");

            migrationBuilder.RenameTable(
                name: "Identifications",
                newName: "EmiratesIdUpdates");

            migrationBuilder.RenameColumn(
                name: "DocumentNumber",
                table: "EmiratesIdUpdates",
                newName: "IdNumber");

            migrationBuilder.RenameIndex(
                name: "IX_Identifications_VerifierId",
                table: "EmiratesIdUpdates",
                newName: "IX_EmiratesIdUpdates_VerifierId");

            migrationBuilder.RenameIndex(
                name: "IX_Identifications_UserId",
                table: "EmiratesIdUpdates",
                newName: "IX_EmiratesIdUpdates_UserId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EmiratesIdUpdates",
                table: "EmiratesIdUpdates",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_EmiratesIdUpdates_PortalUsers_VerifierId",
                table: "EmiratesIdUpdates",
                column: "VerifierId",
                principalTable: "PortalUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_EmiratesIdUpdates_Users_UserId",
                table: "EmiratesIdUpdates",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
