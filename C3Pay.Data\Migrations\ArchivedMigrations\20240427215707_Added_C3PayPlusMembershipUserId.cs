﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_C3PayPlusMembershipUserId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "C3PayPlusMembershipUserId",
                table: "UserSubscriptions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserSubscriptions_C3PayPlusMembershipUserId",
                table: "UserSubscriptions",
                column: "C3PayPlusMembershipUserId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserSubscriptions_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                table: "UserSubscriptions",
                column: "C3PayPlusMembershipUserId",
                principalTable: "C3PayPlusMembershipUsers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserSubscriptions_C3PayPlusMembershipUsers_C3PayPlusMembershipUserId",
                table: "UserSubscriptions");

            migrationBuilder.DropIndex(
                name: "IX_UserSubscriptions_C3PayPlusMembershipUserId",
                table: "UserSubscriptions");

            migrationBuilder.DropColumn(
                name: "C3PayPlusMembershipUserId",
                table: "UserSubscriptions");
        }
    }
}
