﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Add_Money_Transfer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
              name: "ChargeVat",
              table: "MoneyTransferTransactions",
              type: "decimal(18,2)",
              precision: 18,
              scale: 2,
              nullable: false,
              defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "LastTransferReferenceNumber",
                table: "MoneyTransferBeneficiaries",
                type: "nvarchar(250)",
                maxLength: 250,
                nullable: true);

            migrationBuilder.DropTable(
                name: "MoneyTransferCorridors");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartnerReasons");
            
            migrationBuilder.DropTable(
                name: "MoneyTransferPartners");

            migrationBuilder.CreateTable(
               name: "MoneyTransferPartners",
               columns: table => new
               {
                   Id = table.Column<int>(type: "int", nullable: false)
                       .Annotation("SqlServer:Identity", "1, 1"),
                   Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   Type = table.Column<int>(type: "int", nullable: false),
                   CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                   UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                   DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                   IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                   DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
               },
               constraints: table =>
               {
                   table.PrimaryKey("PK_MoneyTransferPartners", x => x.Id);
               });

            migrationBuilder.CreateTable(
                name: "MoneyTransferCorridors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    VAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MoneyTransferEnabled = table.Column<bool>(type: "bit", nullable: false),
                    EligibleForBankTransfer = table.Column<bool>(type: "bit", nullable: false),
                    BankTransferLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    BankTransferRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BankTransferMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    EligibleForCashPickUp = table.Column<bool>(type: "bit", nullable: false),
                    CashPickUpLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    CashPickUpRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CashPickUpMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferCorridors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_MoneyTransferPartners_MoneyTransferPartnerId",
                        column: x => x.MoneyTransferPartnerId,
                        principalTable: "MoneyTransferPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                   name: "MoneyTransferPartnerReasons",
                   columns: table => new
                   {
                       Id = table.Column<int>(type: "int", nullable: false)
                           .Annotation("SqlServer:Identity", "1, 1"),
                       Reason = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: false),
                       MoneyTransferReasonId = table.Column<int>(type: "int", nullable: false),
                       MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                       CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                       CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                       UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                       DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                       IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                       DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                   },
                   constraints: table =>
                   {
                       table.PrimaryKey("PK_MoneyTransferPartnerReasons", x => x.Id);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_Countries_CountryCode",
                           column: x => x.CountryCode,
                           principalTable: "Countries",
                           principalColumn: "Code",
                           onDelete: ReferentialAction.Restrict);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_MoneyTransferPartners_MoneyTransferPartnerId",
                           column: x => x.MoneyTransferPartnerId,
                           principalTable: "MoneyTransferPartners",
                           principalColumn: "Id",
                           onDelete: ReferentialAction.Cascade);
                       table.ForeignKey(
                           name: "FK_MoneyTransferPartnerReasons_MoneyTransferReasons_MoneyTransferReasonId",
                           column: x => x.MoneyTransferReasonId,
                           principalTable: "MoneyTransferReasons",
                           principalColumn: "Id",
                           onDelete: ReferentialAction.Cascade);
                   });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartners",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "Name", "Type", "UpdatedDate" },
                values: new object[] { 1, new DateTime(2021, 9, 22, 14, 30, 51, 407, DateTimeKind.Local).AddTicks(5738), null, null, false, "RAK Bank", 1, null });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartners",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "Name", "Type", "UpdatedDate" },
                values: new object[] { 2, new DateTime(2021, 9, 22, 14, 30, 51, 407, DateTimeKind.Local).AddTicks(6531), null, null, false, "Index", 2, null });

            migrationBuilder.InsertData(
                table: "MoneyTransferCorridors",
                columns: new[] { "Id", "BankTransferLatestRate", "BankTransferMaxAmount", "BankTransferMaxMonthlyAmount", "BankTransferMaxMonthlyCount", "BankTransferMinAmount", "BankTransferRateLastUpdatedDate", "CashPickUpLatestRate", "CashPickUpMaxAmount", "CashPickUpMaxMonthlyAmount", "CashPickUpMaxMonthlyCount", "CashPickUpMinAmount", "CashPickUpRateLastUpdatedDate", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "EligibleForBankTransfer", "EligibleForCashPickUp", "Fee", "IsActive", "IsDeleted", "MoneyTransferEnabled", "MoneyTransferPartnerId", "Name", "UpdatedDate", "VAT" },
                values: new object[,]
                {
                    { 6, 0m, 19823m, 30000m, 7, 25m, null, 0m, 15000m, 30000m, 7, 25m, null, "LK", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(7469), null, null, true, true, 0m, true, false, true, 2, null, null, 0m },
                    { 4, 0m, 29900m, 30000m, 7, 25m, null, 0m, 3600m, 30000m, 7, 25m, null, "PH", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(7437), null, null, true, true, 0m, true, false, true, 2, null, null, 0m },
                    { 3, 0m, 10000m, 30000m, 7, 25m, null, 0m, null, null, null, null, null, "IN", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(7429), null, null, true, false, 0m, true, false, true, 2, null, null, 0m },
                    { 2, 0m, 15000m, 30000m, 7, 25m, null, 0m, 15000m, 30000m, 7, 25m, null, "PK", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(6360), null, null, true, true, 0m, true, false, true, 2, null, null, 0m },
                    { 1, 0m, 15000m, 30000m, 7, 25m, null, 0m, null, null, null, null, null, "BD", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(3779), null, null, true, false, 0m, true, false, true, 2, null, null, 0m },
                    { 5, 0m, 15000m, 30000m, 7, 25m, null, 0m, 3000m, 30000m, 7, 25m, null, "NP", new DateTime(2021, 9, 22, 14, 30, 51, 409, DateTimeKind.Local).AddTicks(7466), null, null, true, true, 0m, true, false, true, 2, null, null, 0m }
          });

            migrationBuilder.InsertData(
                table: "MoneyTransferPartnerReasons",
                columns: new[] { "Id", "CountryCode", "CreatedDate", "DeletedBy", "DeletedDate", "IsDeleted", "MoneyTransferPartnerId", "MoneyTransferReasonId", "Reason", "UpdatedDate" },
                values: new object[,]
                {
                    { 24, "LK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(27), null, null, false, 2, 21, "INSURANCE PAYMENT", null },
                    { 23, "LK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(26), null, null, false, 2, 24, "MEDICAL EXPENSE", null },
                    { 22, "LK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(25), null, null, false, 2, 22, "EDUCATIONAL EXPENSES", null },
                    { 21, "LK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(23), null, null, false, 2, 8, "FAMILY MAINTENANCE", null },
                    { 20, "NP", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(22), null, null, false, 2, 21, "INSURANCE PAYMENT", null },
                    { 19, "NP", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(21), null, null, false, 2, 24, "MEDICAL EXPENSE", null },
                    { 18, "NP", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(20), null, null, false, 2, 22, "EDUCATIONAL EXPENSES", null },
                    { 17, "NP", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(19), null, null, false, 2, 8, "FAMILY MAINTENANCE", null },
                    { 16, "PH", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(18), null, null, false, 2, 21, "INSURANCE PAYMENT", null },
                    { 15, "PH", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(16), null, null, false, 2, 24, "MEDICAL EXPENSE", null },
                    { 13, "PH", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(14), null, null, false, 2, 8, "FAMILY MAINTENANCE", null },
                    { 12, "IN", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(13), null, null, false, 2, 21, "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS", null },
                    { 11, "IN", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(11), null, null, false, 2, 24, "PAYMENT FOR MEDICAL TREATMENT", null },
                    { 10, "IN", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(10), null, null, false, 2, 22, "PAYMENT TO SCHOOLS AND COLLEGES", null },
                    { 9, "IN", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(9), null, null, false, 2, 8, "PAYMENTS TOFAMILIES OF NON-RESIDENT INDIANS", null },
                    { 8, "PK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(8), null, null, false, 2, 21, "INSURANCE PAYMENT", null },
                    { 7, "PK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(7), null, null, false, 2, 24, "MEDICAL EXPENSE", null },
                    { 6, "PK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(5), null, null, false, 2, 22, "EDUCATIONAL EXPENSES", null },
                    { 5, "PK", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(4), null, null, false, 2, 8, "FAMILY MAINTENANCE", null },
                    { 4, "BD", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(3), null, null, false, 2, 21, "INSURANCE PAYMENT", null },
                    { 3, "BD", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(1), null, null, false, 2, 24, "MEDICAL EXPENSE", null },
                    { 2, "BD", new DateTime(2021, 9, 22, 14, 30, 51, 410, DateTimeKind.Local).AddTicks(9961), null, null, false, 2, 22, "EDUCATIONAL EXPENSES", null },
                    { 14, "PH", new DateTime(2021, 9, 22, 14, 30, 51, 411, DateTimeKind.Local).AddTicks(15), null, null, false, 2, 22, "EDUCATIONAL EXPENSES", null },
                    { 1, "BD", new DateTime(2021, 9, 22, 14, 30, 51, 410, DateTimeKind.Local).AddTicks(8769), null, null, false, 2, 8, "FAMILY MAINTENANCE", null }
               });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ChargeVat",
                table: "MoneyTransferTransactions");

            migrationBuilder.DropColumn(
                name: "LastTransferReferenceNumber",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.DropTable(
                name: "MoneyTransferCorridors");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartnerReasons");

            migrationBuilder.DropTable(
                name: "MoneyTransferPartners");

            migrationBuilder.CreateTable(
               name: "MoneyTransferPartners",
               columns: table => new
               {
                   Id = table.Column<int>(type: "int", nullable: false)
                       .Annotation("SqlServer:Identity", "1, 1"),
                   Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                   Type = table.Column<int>(type: "int", nullable: false),
                   CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                   UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                   DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                   IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                   DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
               },
               constraints: table =>
               {
                   table.PrimaryKey("PK_MoneyTransferPartners", x => x.Id);
               });

            migrationBuilder.CreateTable(
                name: "MoneyTransferCorridors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MoneyTransferPartnerId = table.Column<int>(type: "int", nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    VAT = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    MoneyTransferEnabled = table.Column<bool>(type: "bit", nullable: false),
                    EligibleForBankTransfer = table.Column<bool>(type: "bit", nullable: false),
                    BankTransferLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    BankTransferRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    BankTransferMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    BankTransferMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    EligibleForCashPickUp = table.Column<bool>(type: "bit", nullable: false),
                    CashPickUpLatestRate = table.Column<decimal>(type: "decimal(18,4)", precision: 18, scale: 4, nullable: false),
                    CashPickUpRateLastUpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CashPickUpMinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: true),
                    CashPickUpMaxMonthlyCount = table.Column<int>(type: "int", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferCorridors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_Countries_CountryCode",
                        column: x => x.CountryCode,
                        principalTable: "Countries",
                        principalColumn: "Code",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_MoneyTransferCorridors_MoneyTransferPartners_MoneyTransferPartnerId",
                        column: x => x.MoneyTransferPartnerId,
                        principalTable: "MoneyTransferPartners",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }
    }
}
