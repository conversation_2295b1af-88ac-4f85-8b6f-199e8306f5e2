﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class MultimediaResources_data : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "MultimediaResources",
                columns: new[] { "Id", "FeatureId", "NationalityCode", "ThumbnailUrl", "Type", "Url" },
                values: new object[,]
                {
                    { 1, 2, "IND", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638332475/rendition/360p/file.mp4?loc=external&signature=78f4cbd07481885778965da0f1098683c16bf5bd8b4b9d46312caf3ee6285c72" },
                    { 16, 4, "PHL", "***********************************************************************?sp=r&st=2022-03-17T07:08:16Z&se=2025-03-17T15:08:16Z&spr=https&sv=2020-08-04&sr=b&sig=z8bSU5YbioY%2BioXX629pV%2BF7vrMQ8xNr7LspcyOjiJY%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710823250/rendition/360p/file.mp4?loc=external&signature=97135538aeb127d8fdac343c7f7dba2ab5c705d6e4d7b3347f8ca8f58853a764" },
                    { 15, 4, "NPL", "***********************************************************************?sp=r&st=2022-03-17T07:07:46Z&se=2025-03-17T15:07:46Z&spr=https&sv=2020-08-04&sr=b&sig=Mcq041UQ7AEGJrOlS0DC8YBxzJZ2W7C7%2FVe5TwBaF%2B8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710777315/rendition/360p/file.mp4?loc=external&signature=427f91fe9f3a35a9585f630758dbb80f2a014a2f2d11599da3e080fa682aa45d" },
                    { 14, 4, "PAK", "***********************************************************************?sp=r&st=2022-03-17T07:07:13Z&se=2025-03-17T15:07:13Z&spr=https&sv=2020-08-04&sr=b&sig=VHtQmfSzUOou%2Fn1xwKrDwZQjoWhwH1fBAV05Ab6J2G8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710822242/rendition/360p/file.mp4?loc=external&signature=dd707730fc4125e547323be1d546fda1fbc7f65ca51900d4ba66fcdce9703cc8" },
                    { 13, 4, "IND", "***********************************************************************?sp=r&st=2022-03-17T07:04:24Z&se=2025-12-31T15:04:24Z&spr=https&sv=2020-08-04&sr=b&sig=6FesClJTvTEH5GWpDAmkpiG%2BiIUBgQwxbwyvxsgkp3c%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710823432/rendition/360p/file.mp4?loc=external&signature=bc5dbc45a1afd3ff9c5a7537eda576d1cbbf46c82edb222f5aa13af57b0ff936" },
                    { 12, 6, "BGD", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                    { 11, 6, "LKA", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071251/rendition/360p/file.mp4?loc=external&signature=03d7efa87ea502df255f80b0d4bc34a4958e2dde4b9f1b9690ce3b24feb744ec" },
                    { 10, 6, "PHL", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071251/rendition/360p/file.mp4?loc=external&signature=03d7efa87ea502df255f80b0d4bc34a4958e2dde4b9f1b9690ce3b24feb744ec" },
                    { 9, 6, "NPL", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                    { 8, 6, "PAK", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                    { 7, 6, "IND", "**********************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/644071052/rendition/360p/file.mp4?loc=external&signature=ba3ec9b4c997591270dc2b0e9c0d476f2585fb82c554aa7bd4be2434a7671535" },
                    { 6, 2, "BGD", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638332062/rendition/360p/file.mp4?loc=external&signature=c3d681362a341967310e3eb433ac97bb49f358a47d0512824143aa0d5493d5a9" },
                    { 5, 2, "LKA", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638333368/rendition/360p/file.mp4?loc=external&signature=3620db85a3c10c4c8e355e7bb74984149bbc5c7cb7cca020ba54f26c1ec65261" },
                    { 4, 2, "PHL", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638333304/rendition/360p/file.mp4?loc=external&signature=02ed7a9cf3acfd2fe5f238b20308f3afc1ec5c07ea098177916127732185b12f" },
                    { 3, 2, "NPL", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638332739/rendition/360p/file.mp4?loc=external&signature=c9861055f0846001b86cc2559442000a5b92a3a333e91931704810f025a8e70a" },
                    { 2, 2, "PAK", "*************************************************************************?sp=rl&st=2021-09-06T07:46:14Z&se=2025-12-31T07:46:00Z&sv=2020-08-04&sr=c&sig=Vpp4Tn8KhTACPeMguSQSGzb3REK74dig%2FV1nzDnsUR8%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/638332791/rendition/360p/file.mp4?loc=external&signature=bcdf80cb45792dd654e02b9eef808af17da01b7a6902895f721e4f22f8f10cbe" },
                    { 17, 4, "LKA", "***********************************************************************?sp=r&st=2022-03-17T12:01:41Z&se=2025-03-17T20:01:41Z&spr=https&sv=2020-08-04&sr=b&sig=1Ga10HwKBkHuQ5SpjVMmQmZis9GKDGGqPRKmy4%2FkYpE%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710822089/rendition/360p/file.mp4?loc=external&signature=d637a95a186c7831b0358870c828b57378e2afb3c5f5a3ece6a291776c133b8f" },
                    { 18, 4, "BGD", "***********************************************************************?sp=r&st=2022-03-17T07:04:24Z&se=2025-12-31T15:04:24Z&spr=https&sv=2020-08-04&sr=b&sig=6FesClJTvTEH5GWpDAmkpiG%2BiIUBgQwxbwyvxsgkp3c%3D", "Video", "https://player.vimeo.com/progressive_redirect/playback/710823432/rendition/360p/file.mp4?loc=external&signature=bc5dbc45a1afd3ff9c5a7537eda576d1cbbf46c82edb222f5aa13af57b0ff936" }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 6);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 8);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 9);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 10);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 11);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 12);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 13);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 14);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 15);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 16);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 17);

            migrationBuilder.DeleteData(
                table: "MultimediaResources",
                keyColumn: "Id",
                keyValue: 18);
        }
    }
}
