﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateMissingKycCardholders : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsUnblocked",
                table: "MissingKycCardholders");

            migrationBuilder.AlterColumn<string>(
                name: "Remarks",
                table: "MissingKycCardholders",
                type: "varchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.DropPrimaryKey(
                name: "PK_MissingKycCardholders",
                table: "MissingKycCardholders");

            migrationBuilder.AlterColumn<string>(
                name: "CitizenId",
                table: "MissingKycCardholders",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)");

            migrationBuilder.AddPrimaryKey(
                name: "PK_MissingKycCardholders",
                table: "MissingKycCardholders",
                column: "CitizenId");

            migrationBuilder.AddColumn<DateTime>(
                name: "BlockedDate",
                table: "MissingKycCardholders",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "MissingKycCardholders",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddForeignKey(
                name: "FK_MissingKycCardholders_CardHolders_CitizenId",
                table: "MissingKycCardholders",
                column: "CitizenId",
                principalTable: "CardHolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MissingKycCardholders_CardHolders_CitizenId",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "BlockedDate",
                table: "MissingKycCardholders");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "MissingKycCardholders");

            migrationBuilder.AlterColumn<string>(
                name: "Remarks",
                table: "MissingKycCardholders",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "varchar(500)",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.DropPrimaryKey(
                name: "PK_MissingKycCardholders",
                table: "MissingKycCardholders");

            migrationBuilder.AlterColumn<string>(
                name: "CitizenId",
                table: "MissingKycCardholders",
                type: "nvarchar(450)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(15)",
                oldMaxLength: 15);

            migrationBuilder.AddPrimaryKey(
                name: "PK_MissingKycCardholders",
                table: "MissingKycCardholders",
                column: "CitizenId");

            migrationBuilder.AddColumn<bool>(
                name: "IsUnblocked",
                table: "MissingKycCardholders",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
