﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BeneficiaryAdditionFieldGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    RemittanceDeliveryType = table.Column<int>(type: "int", nullable: true),
                    RemittanceDeliveryMethodId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryAdditionFieldGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryAdditionFieldGroups_RemittanceDeliveryMethods_RemittanceDeliveryMethodId",
                        column: x => x.RemittanceDeliveryMethodId,
                        principalTable: "RemittanceDeliveryMethods",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryAdditionFieldGroups_RemittanceDeliveryMethodId",
                table: "BeneficiaryAdditionFieldGroups",
                column: "RemittanceDeliveryMethodId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BeneficiaryAdditionFieldGroups");
        }
    }
}
