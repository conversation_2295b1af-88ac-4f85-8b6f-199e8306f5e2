﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class ModifyMoneyTransferReasontable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            var query = @"  
                delete from [dbo].[Translations];
                delete from [dbo].[textContents];   
            "; 
            migrationBuilder.Sql(query);

            migrationBuilder.AddColumn<string>(
                name: "TextContentCode",
                table: "MoneyTransferReasons",
                type: "nvarchar(25)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferReasons_TextContentCode",
                table: "MoneyTransferReasons",
                column: "TextContentCode");

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferReasons_TextContents_TextContentCode",
                table: "MoneyTransferReasons",
                column: "TextContentCode",
                principalTable: "TextContents",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferReasons_TextContents_TextContentCode",
                table: "MoneyTransferReasons");

            migrationBuilder.DropIndex(
                name: "IX_MoneyTransferReasons_TextContentCode",
                table: "MoneyTransferReasons");

            migrationBuilder.DropColumn(
                name: "TextContentCode",
                table: "MoneyTransferReasons");
        }
    }
}
