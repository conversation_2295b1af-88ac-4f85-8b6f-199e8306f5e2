﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Continue_Txt : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ContinueText",
                table: "Languages",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Order",
                table: "Languages",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TitleText",
                table: "Languages",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ContinueText",
                table: "Languages");

            migrationBuilder.DropColumn(
                name: "Order",
                table: "Languages");

            migrationBuilder.DropColumn(
                name: "TitleText",
                table: "Languages");
        }
    }
}
