﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class IdentificationLogs_Tbls : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "EmiratesIdLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Vendor = table.Column<int>(type: "int", nullable: false),
                    FrontScanName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BackScanName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FullName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FirstName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LastName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Nationality = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NationalityCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Gender = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DOB = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CardNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsExpired = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyFirstName = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyLastName = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyNationality = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyNationalityCode = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyGender = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyDateOfBirth = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyDateOfExpiry = table.Column<bool>(type: "bit", nullable: false),
                    IsValidFullName = table.Column<bool>(type: "bit", nullable: false),
                    IsValidBirthDate = table.Column<bool>(type: "bit", nullable: false),
                    IsValidExpiryDate = table.Column<bool>(type: "bit", nullable: false),
                    IsValidCardNumber = table.Column<bool>(type: "bit", nullable: false),
                    IsValidIdNumber = table.Column<bool>(type: "bit", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EmiratesIdLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EmiratesIdLogs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PassportLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Vendor = table.Column<int>(type: "int", nullable: false),
                    FrontScanName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Surname = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PassportNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CountryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Gender = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateOfBirth = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateOfExpiry = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsEmptyName = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptySurname = table.Column<bool>(type: "bit", nullable: false),
                    IsValidPassportNumber = table.Column<bool>(type: "bit", nullable: false),
                    IsValidCountryCode = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyGender = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyDateOfBirth = table.Column<bool>(type: "bit", nullable: false),
                    IsEmptyDateOfExpiry = table.Column<bool>(type: "bit", nullable: false),
                    IsValidFullName = table.Column<bool>(type: "bit", nullable: false),
                    IsValidBirthDate = table.Column<bool>(type: "bit", nullable: false),
                    IsValidExpiryDate = table.Column<bool>(type: "bit", nullable: false),
                    IsExpired = table.Column<bool>(type: "bit", nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PassportLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PassportLogs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_EmiratesIdLogs_UserId",
                table: "EmiratesIdLogs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_PassportLogs_UserId",
                table: "PassportLogs",
                column: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EmiratesIdLogs");

            migrationBuilder.DropTable(
                name: "PassportLogs");
        }
    }
}
