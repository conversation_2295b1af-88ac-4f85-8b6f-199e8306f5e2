﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_IsShared_MoneyTransferFieldGroups_Tbl : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.AddColumn<string>(
                name: "DropdownReference",
                table: "MoneyTransferFields",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsShared",
                table: "MoneyTransferFields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<bool>(
                name: "IsShared",
                table: "MoneyTransferFieldGroups",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId",
                principalTable: "MoneyTransferCountries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.DropColumn(
                name: "DropdownReference",
                table: "MoneyTransferFields");

            migrationBuilder.DropColumn(
                name: "IsShared",
                table: "MoneyTransferFields");

            migrationBuilder.DropColumn(
                name: "IsShared",
                table: "MoneyTransferFieldGroups");

            migrationBuilder.AlterColumn<int>(
                name: "MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_MoneyTransferFieldGroups_MoneyTransferCountries_MoneyTransferCountryId",
                table: "MoneyTransferFieldGroups",
                column: "MoneyTransferCountryId",
                principalTable: "MoneyTransferCountries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
