﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class AddExperimentGroupMultimedia : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers");

            migrationBuilder.DropTable(
                name: "GuidedWalkthroughUsers");

            migrationBuilder.DropTable(
                name: "MoneyTransferUxExperimentUsers");

            migrationBuilder.DropIndex(
                name: "IX_ExperimentUsers_UserId",
                table: "ExperimentUsers");

            migrationBuilder.DropColumn(
                name: "UserId",
                table: "ExperimentUsers");

            migrationBuilder.AddColumn<string>(
                name: "CardHolderId",
                table: "ExperimentUsers",
                type: "nvarchar(15)",
                maxLength: 15,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "ExperimentGroupMultimedias",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ExperimentId = table.Column<int>(type: "int", nullable: false),
                    GroupCode = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Url = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentGroupMultimedias", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExperimentGroupMultimedias_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentUsers_CardHolderId",
                table: "ExperimentUsers",
                column: "CardHolderId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentGroupMultimedias_ExperimentId",
                table: "ExperimentGroupMultimedias",
                column: "ExperimentId");

            migrationBuilder.Sql("DELETE FROM ExperimentUsers");

            migrationBuilder.AddForeignKey(
                name: "FK_ExperimentUsers_CardHolders_CardHolderId",
                table: "ExperimentUsers",
                column: "CardHolderId",
                principalTable: "CardHolders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ExperimentUsers_CardHolders_CardHolderId",
                table: "ExperimentUsers");

            migrationBuilder.DropTable(
                name: "ExperimentGroupMultimedias");

            migrationBuilder.DropIndex(
                name: "IX_ExperimentUsers_CardHolderId",
                table: "ExperimentUsers");

            migrationBuilder.DropColumn(
                name: "CardHolderId",
                table: "ExperimentUsers");

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                table: "ExperimentUsers",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateTable(
                name: "GuidedWalkthroughUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GuidedWalkthroughGroup = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GuidedWalkthroughUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_GuidedWalkthroughUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MoneyTransferUxExperimentUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupCode = table.Column<string>(type: "varchar(10)", maxLength: 10, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MoneyTransferUxExperimentUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MoneyTransferUxExperimentUsers_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentUsers_UserId",
                table: "ExperimentUsers",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GuidedWalkthroughUsers_UserId",
                table: "GuidedWalkthroughUsers",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MoneyTransferUxExperimentUsers_UserId",
                table: "MoneyTransferUxExperimentUsers",
                column: "UserId");

            migrationBuilder.Sql("DELETE FROM ExperimentUsers");

            migrationBuilder.AddForeignKey(
                name: "FK_ExperimentUsers_Users_UserId",
                table: "ExperimentUsers",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
