﻿using C3Pay.Core;
using C3Pay.Core.Models;
using Edenred.Common.Core;
using Edenred.Common.Core.Models.Messages.Integration.Rak.Requests;
using Edenred.Common.Core.Models.Messages.Integration.Rak.Responses;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Services.Mock
{
    public class RakMockService : IRAKService
    {
        private readonly MoneyTransferServiceSettings _moneyTransferServiceSettings;
        private readonly IUnitOfWork _unitOfWork;

        public RakMockService(IOptions<MoneyTransferServiceSettings> moneyTransferServiceSettings,
            IUnitOfWork unitOfWork)
        {
            _moneyTransferServiceSettings = moneyTransferServiceSettings.Value;
            _unitOfWork = unitOfWork;
        }

        public Task<ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>> AddRAKMoneyTransferBeneficiary(string EmiratesId, NewBeneficiaryRequestRakModel RequestObject)
        {
            var beneficiariesDetailsRakModel = new BeneficiariesDetailsRakModel();
            beneficiariesDetailsRakModel.RakBeneficiayId = GetRandomNumber().ToString();

            var rakResponse = new RakResponse<BeneficiariesDetailsRakModel>
            {
                MessageId = Guid.NewGuid(),
                Data = beneficiariesDetailsRakModel
            };

            var result = new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>();

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Rak Access token error";
                result.Data = rakResponse;
            }
            else
            {
                result = new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>(rakResponse);
            }

            return Task.FromResult(result);
        }

        public Task<ServiceResponse<RakResponse>> DeleteRAKMoneyTransferBeneficiary(string EmiratesId, string BeneficiaryId)
        {
            var rakResponse = new RakResponse
            {
                MessageId = Guid.NewGuid()
            };

            var result = new ServiceResponse<RakResponse>();

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Failed to delete";
            }
            else
            {
                result = new ServiceResponse<RakResponse>(rakResponse);
            }

            return Task.FromResult(result);
        }

        public Task<ServiceResponse<CountryRakModel>> GetBankParameters(string CountryCode, string EmiratesId)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResponse<BankDetailsRakModel>> GetBanksDetails(BanksRequestRakModel banksRequest, string EmiratesId)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<List<RakDropdownItem>>> GetDropdownItems(string EmiratesId, RakDropdownRequest request)
        {
            return new ServiceResponse<List<RakDropdownItem>>(false, "");
        }

        public async Task<ServiceResponse<List<FxRateConversionResponseRakModel>>> GetFxRate(List<FxRateRequestRakModel> RequestObject, string EmiratesId)
        {
            var countriesToUpdate = await this._unitOfWork.Countries.FindAsync(c => c.MoneyTransferEnabled && c.IsPapularCountry, false);

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                return new ServiceResponse<List<FxRateConversionResponseRakModel>>(false, "Unable fetch fx rates");
            }
            else
            {
                List<FxRateConversionResponseRakModel> response = new List<FxRateConversionResponseRakModel>();
                foreach (var item in RequestObject)
                {
                    var country = countriesToUpdate.FirstOrDefault(x => item.BankCountry == x.Code);
                    var conversionCharges = new ChargesRakModel()
                    {
                        Currency = country.Currency,
                        Amount = "0",
                        TotalCharges = "0"
                    };

                    if (item.TransferType == MoneyTransferType.OutsideUAE.ToString())
                    {
                        var convertedAmount = GetReceiverAmount(Convert.ToDecimal(item.Fxvalue.Amount), country.BankTransferLatestRate);

                        response.Add(new FxRateConversionResponseRakModel()
                        {
                            FromCurrency = item.FromCurrency,
                            ToCurrency = country.Currency,
                            TransferType = item.TransferType,
                            Fxvalue = item.Fxvalue,
                            FxConversionRates = new List<FxConversionRakModel>
                            {
                                new FxConversionRakModel() { Currency = country.Currency,
                                                             ConvertedAmount = convertedAmount,
                                                             Rate = country.BankTransferLatestRate.ToString() }
                            },
                            ConversionCharges = conversionCharges,
                            TotalDebitAmount = item.Fxvalue.Amount,
                            TotalCreditAmount = convertedAmount.ToString(),
                            
                        });
                    }
                    else if (item.TransferType == MoneyTransferType.RAKMoneyCashPayout.ToString())
                    {
                        var convertedAmount = GetReceiverAmount(Convert.ToDecimal(item.Fxvalue.Amount), country.CashPickUpLatestRate);

                        response.Add(new FxRateConversionResponseRakModel()
                        {
                            FromCurrency = item.FromCurrency,
                            ToCurrency = country.Currency,
                            TransferType = item.TransferType,
                            Fxvalue = item.Fxvalue,
                            FxConversionRates = new List<FxConversionRakModel>
                            {
                                new FxConversionRakModel() { Currency = country.Currency,
                                                             ConvertedAmount = convertedAmount,
                                                             Rate = country.CashPickUpLatestRate.ToString() }
                            },
                            ConversionCharges = conversionCharges,
                            TotalDebitAmount = item.Fxvalue.Amount,
                            TotalCreditAmount = convertedAmount.ToString()
                        });
                    }
                }

                return new ServiceResponse<List<FxRateConversionResponseRakModel>>(response);
            }
        }

        public Task<ServiceResponse<IEnumerable<BeneficiariesResponseRakModel>>> GetMoneyTransferBeneficiaries(string EmiratesId)
        {
            throw new NotImplementedException();
        }

        public async Task<ServiceResponse<BeneficiariesResponseRakModel>> GetMoneyTransferBeneficiaryById(string EmiratesId, string beneficiaryId)
        {
            var externalBeneficiary = await _unitOfWork.MoneyTransferExternalBeneficiaries.FirstOrDefaultAsync(x => x.ExternalId == beneficiaryId,
                                                                                                 y => y.MoneyTransferBeneficiary,
                                                                                                 y => y.MoneyTransferBeneficiary.Country);

            var beneficiary = externalBeneficiary.MoneyTransferBeneficiary;

            var rakResponse = new BeneficiariesResponseRakModel()
            {
                AccountNumber = beneficiary.AccountNumber,
                Address = new AddressRakModel()
                {
                    Address1 = beneficiary.Address1,
                    Address2 = beneficiary.Address2,
                    //Country = beneficiary.Address.Country
                },
                FirstName = beneficiary.FirstName,
                LastName = beneficiary.LastName,
                RakBeneficiayId = externalBeneficiary.ExternalId,
                //BankCountry = externalBeneficiary.BankCountry,
                BankName = beneficiary.BankName,
                BankBranchName = beneficiary.BankBranchName,
                //Identifier1Name = externalBeneficiary.Identifier1Name,
                IdentifierCode1 = beneficiary.IdentifierCode1,
                //Identifier2Name = externalBeneficiary.Identifier2Name,
                IdentifierCode2 = beneficiary.IdentifierCode2,
                //TransferType = externalBeneficiary.TransferType,
                Documents = new List<DocumentRakModel>()
                {
                    new DocumentRakModel() { Number = beneficiary.DocumentNumber, Type = beneficiary.DocumentType }
                }
            };

            var result = new ServiceResponse<BeneficiariesResponseRakModel>();

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Failed to fetch external beneficiary";
                result.Data = null;
            }
            else
            {
                result = new ServiceResponse<BeneficiariesResponseRakModel>(rakResponse);
            }

            return result;
        }

        public async Task<ServiceResponse<MoneyTransferResponseRakModel>> GetMoneyTransferTransactionStatus(string TransactionId, string TransactionType, string EmiratesId)
        {
            var moneyTransferTransaction = await _unitOfWork.MoneyTransferTransactions.FirstOrDefaultAsync(x => x.ReferenceNumber == TransactionId);

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock && moneyTransferTransaction.SendAmount == 31)
            {
                var response = new MoneyTransferResponseRakModel();
                response.Status = ExternalStatus.REV.ToString();
                response.StatusDesc = "Beneficiary bank did not respond";

                return new ServiceResponse<MoneyTransferResponseRakModel>(response);
            }
            else if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                return new ServiceResponse<MoneyTransferResponseRakModel>(false, "Unable to get status");
            }
            else
            {
                var response = new MoneyTransferResponseRakModel();
                response.Status = ExternalStatus.S.ToString();
                response.StatusDesc = "SUCCESSFULLY POSTED THE TRANSACTION";

                return new ServiceResponse<MoneyTransferResponseRakModel>(response);
            }
        }

        public async Task<ServiceResponse<RakResponse<MoneyTransferResponseRakModel>>> SendMoneyTransfer(string EdenTransactionId, MoneyTransferRequestRakModel RequestObject)
        {
            var result = new ServiceResponse<RakResponse<MoneyTransferResponseRakModel>>();

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock && RequestObject.Value.Amount.ToString() == "32.00")
            {
                throw new Exception("Rak Access token error");
            }
            else if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock && RequestObject.Value.Amount.ToString() == "35.00")
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "20010-Beneficiary Bank currently Not Supported.";
                result.Data = new RakResponse<MoneyTransferResponseRakModel>
                {
                    MessageId = Guid.NewGuid(),
                    Data = new MoneyTransferResponseRakModel()
                    {
                        Charges = new MoneyTransferCharges(),
                        Status = ExternalStatus.P.ToString(),
                        StatusDesc = "WAITING FOR SUSPICIOUS TRANSACTION",
                        PaymentRefId = GetRandomNumber().ToString(),
                        StartDate = DateTime.Now.ToString(),
                        EndDate = DateTime.Now.ToString(),
                        TransactionType = "RAKMoney"
                    }
                };
            }
            else
            {
                var beneficiary = await _unitOfWork.MoneyTransferExternalBeneficiaries.FirstOrDefaultAsync(x => x.ExternalId == RequestObject.To.RakBeneficiaryId,
                                                                                                 y => y.MoneyTransferBeneficiary,
                                                                                                 y => y.MoneyTransferBeneficiary.Country);

                var country = await _unitOfWork.Countries.FirstOrDefaultAsync(x => x.Currency == beneficiary.MoneyTransferBeneficiary.Country.Currency);

                result.Data = new RakResponse<MoneyTransferResponseRakModel>
                {
                    MessageId = Guid.NewGuid(),
                    Data = new MoneyTransferResponseRakModel()
                    {
                        Charges = new MoneyTransferCharges()
                        {
                            Amount = GetReceiverAmount(Convert.ToDecimal(RequestObject.Value.Amount), country.BankTransferLatestRate),
                            Currency = country.Currency
                        },
                        Status = ExternalStatus.P.ToString(),
                        StatusDesc = null,
                        PaymentRefId = GetRandomNumber().ToString(),
                        StartDate = DateTime.Now.ToString(),
                        EndDate = DateTime.Now.ToString(),
                        TransactionType = TransactionType.RAKMoney.ToString(),
                        CashPickUpPin = GetRandomNumber().ToString(),
                        CashPickUpPoint = GetRandomNumber().ToString(),
                        CreditCurrency = country.Currency,
                        DebitCurrency = RequestObject.Value.Currency
                    }
                };
            }

            return result;
        }

        public Task<ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>> UpdateRAKMoneyTransferBeneficiary(string EmiratesId, string BeneficiaryId, NewBeneficiaryRequestRakModel RequestObject)
        {
            var beneficiariesDetailsRakModel = new BeneficiariesDetailsRakModel();
            beneficiariesDetailsRakModel.RakBeneficiayId = GetRandomNumber().ToString();

            var rakResponse = new RakResponse<BeneficiariesDetailsRakModel>
            {
                MessageId = Guid.NewGuid(),
                Data = beneficiariesDetailsRakModel
            };

            var result = new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>();

            if (_moneyTransferServiceSettings.EnableRakNegativeScenarioMock)
            {
                result.IsSuccessful = false;
                result.ErrorMessage = "Update beneficiary failed";
                result.Data = rakResponse;
            }
            else
            {
                result = new ServiceResponse<RakResponse<BeneficiariesDetailsRakModel>>(rakResponse);
            }

            return Task.FromResult(result);
        }

        #region Private Methods

        private static int GetRandomNumber()
        {
            using RNGCryptoServiceProvider rngCrypt = new RNGCryptoServiceProvider();
            byte[] tokenBuffer = new byte[4];        // `int32` takes 4 bytes in C#
            rngCrypt.GetBytes(tokenBuffer);
            return Math.Abs(BitConverter.ToInt32(tokenBuffer, 0));
        }

        private static string GetReceiverAmount(decimal senderAmount, decimal conversionRate)
        {
            var result = Math.Round(senderAmount * (1 / conversionRate), 2);
            return result.ToString();
        }

        #endregion Private Methods
    }
}
