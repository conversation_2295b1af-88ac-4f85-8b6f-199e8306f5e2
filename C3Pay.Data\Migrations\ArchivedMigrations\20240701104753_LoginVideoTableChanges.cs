﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class LoginVideoTableChanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideoSlots_LoginVideoTypes_VideoTypeId",
                table: "LoginVideoSlots");

            migrationBuilder.DropTable(
                name: "LoginVideoUrls");

            migrationBuilder.DropTable(
                name: "LoginVideoTypes");

            migrationBuilder.CreateTable(
                name: "LoginVideos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Owner = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Metadata = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideos", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LoginVideResources",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LanguageCode = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LoginVideoId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideResources", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LoginVideResources_LoginVideos_LoginVideoId",
                        column: x => x.LoginVideoId,
                        principalTable: "LoginVideos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "LoginVideos",
                columns: new[] { "Id", "Metadata", "Owner", "Title" },
                values: new object[] { 1, null, "MoneyTransfer", null });

            migrationBuilder.InsertData(
                table: "LoginVideos",
                columns: new[] { "Id", "Metadata", "Owner", "Title" },
                values: new object[] { 2, null, "C3PayPlus", null });

            migrationBuilder.InsertData(
                table: "LoginVideResources",
                columns: new[] { "Id", "LanguageCode", "LoginVideoId", "Url" },
                values: new object[,]
                {
                    { 1, "en", 1, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_en.mp4" },
                    { 2, "hi", 1, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_hi.mp4" },
                    { 3, "en", 2, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Eng_10AED.mp4" },
                    { 4, "hi", 2, "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Hi_10AED.mp4" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideResources_LoginVideoId",
                table: "LoginVideResources",
                column: "LoginVideoId");

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId",
                principalTable: "LoginVideos",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LoginVideoSlots_LoginVideos_VideoTypeId",
                table: "LoginVideoSlots");

            migrationBuilder.DropTable(
                name: "LoginVideResources");

            migrationBuilder.DropTable(
                name: "LoginVideos");

            migrationBuilder.CreateTable(
                name: "LoginVideoTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Type = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "LoginVideoUrls",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LanguageCode = table.Column<string>(type: "nvarchar(5)", maxLength: 5, nullable: true),
                    Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VideoTypeId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LoginVideoUrls", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LoginVideoUrls_LoginVideoTypes_VideoTypeId",
                        column: x => x.VideoTypeId,
                        principalTable: "LoginVideoTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "LoginVideoTypes",
                columns: new[] { "Id", "Type" },
                values: new object[] { 1, "MoneyTransfer" });

            migrationBuilder.InsertData(
                table: "LoginVideoTypes",
                columns: new[] { "Id", "Type" },
                values: new object[] { 2, "C3PayPlus" });

            migrationBuilder.InsertData(
                table: "LoginVideoUrls",
                columns: new[] { "Id", "LanguageCode", "Url", "VideoTypeId" },
                values: new object[,]
                {
                    { 1, "en", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_en.mp4", 1 },
                    { 2, "hi", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Onboarding_hi.mp4", 1 },
                    { 3, "en", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Eng_10AED.mp4", 2 },
                    { 4, "hi", "https://eaec3sharedsp.blob.core.windows.net/c3pay-plus/C3PayPlus_Hi_10AED.mp4", 2 }
                });

            migrationBuilder.CreateIndex(
                name: "IX_LoginVideoUrls_VideoTypeId",
                table: "LoginVideoUrls",
                column: "VideoTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_LoginVideoSlots_LoginVideoTypes_VideoTypeId",
                table: "LoginVideoSlots",
                column: "VideoTypeId",
                principalTable: "LoginVideoTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
