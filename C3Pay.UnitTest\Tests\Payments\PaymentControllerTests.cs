﻿using C3Pay.API.Controllers;
using C3Pay.Core;
using C3Pay.Core.Models.DTOs.Payments.Requests;
using C3Pay.Core.Models.DTOs.Payments.Responses;
using C3Pay.Services.Payments.Commands;
using C3Pay.Services.Payments.Queries;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace C3Pay.UnitTest.Tests.Payments
{
    public class PaymentControllerTests
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
        private readonly PaymentController _controller;

        public PaymentControllerTests()
        {
            _mediatorMock = new Mock<IMediator>();
            _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            _controller = new PaymentController(_httpContextAccessorMock.Object, _mediatorMock.Object);
        }

        [Fact]
        public async Task GetPaymentAuthorisationRequests_ReturnOK()
        {

            // Arrange
            var cancellationToken = CancellationToken.None;
            var userPhoneNumber = "00971503145237";
            var languageCode = "en";
            var responseDto = new PaymentAuthorisationResponseDto()
            {
                Amount = new PaymentAuthAmountDto
                {
                    Amount = "10.0",
                    CurrencyCode = "AED"
                }
            };

            var successResult = Result.Success(responseDto);

            // Mock HttpContextAccessor
            _httpContextAccessorMock.Setup(x => x.HttpContext.User.FindFirst(It.IsAny<string>()))
                .Returns(new Claim(ConstantParam.Username, userPhoneNumber));

            // Mock Mediator
            _mediatorMock.Setup(x => x.Send(It.IsAny<GetPaymentAuthRequestQuery>(), cancellationToken))
                .ReturnsAsync(successResult);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.HttpContext.Request.Headers["x-lang-code"] = languageCode;

            // Act
            var actionResult = await _controller.GetPaymentAuthorisationRequests(cancellationToken);

            // Assert
            var okObjectResult = Assert.IsType<OkObjectResult>(actionResult.Result);
            var returnedDto = Assert.IsType<PaymentAuthorisationResponseDto>(okObjectResult.Value);
            Assert.Equal(responseDto, returnedDto);

        }

        [Fact]
        public async Task GetPaymentAuthorisationRequests_ReturnBadRequest()
        {

            // Arrange
            var cancellationToken = CancellationToken.None;
            var userPhoneNumber = "00971123456789";
            var languageCode = "en";

            var failureResult = Result.Failure<PaymentAuthorisationResponseDto>(Errors.InAppAuth.UserNotFound);

            // Mock HttpContextAccessor
            _httpContextAccessorMock.Setup(x => x.HttpContext.User.FindFirst(It.IsAny<string>()))
                .Returns(new Claim(ConstantParam.Username, userPhoneNumber));

            // Mock Mediator
            _mediatorMock.Setup(x => x.Send(It.IsAny<GetPaymentAuthRequestQuery>(), cancellationToken))
                .ReturnsAsync(failureResult);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.HttpContext.Request.Headers["x-lang-code"] = languageCode;

            // Act
            var actionResult = await _controller.GetPaymentAuthorisationRequests(cancellationToken);

            // Assert
            var badRequestObjectResult = Assert.IsType<BadRequestObjectResult>(actionResult.Result);
            Assert.Equal(Errors.InAppAuth.UserNotFound.Code, badRequestObjectResult.Value);

        }

        [Fact]
        public async Task PostPaymentAuthorisationDecision_ReturnOk()
        {

            // Arrange
            var cancellationToken = CancellationToken.None;
            var userPhoneNumber = "00971123456789";
            var languageCode = "en";
            var requestDto = new PaymentAuthorisationDecisionRequestDto
            {
                Id = Guid.NewGuid(),
                Decision = PaymentAuthDecision.APPROVE
            };

            var successResult = Result.Success();

            // Mock HttpContextAccessor
            _httpContextAccessorMock.Setup(x => x.HttpContext.User.FindFirst(It.IsAny<string>()))
                .Returns(new Claim(ConstantParam.Username, userPhoneNumber));

            // Mock Mediator
            _mediatorMock.Setup(x => x.Send(It.IsAny<UpdatePaymentAuthorisationDecisionCommand>(), cancellationToken))
                .ReturnsAsync(successResult);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.HttpContext.Request.Headers["x-lang-code"] = languageCode;

            // Act
            var actionResult = await _controller.PaymentAuthorisationDecision(requestDto, cancellationToken);

            // Assert
            var okObjectResult = Assert.IsType<OkResult>(actionResult);

        }

        [Fact]
        public async Task PostPaymentAuthorisationDecision_ReturnBadRequest()
        {

            // Arrange
            var cancellationToken = CancellationToken.None;
            var userPhoneNumber = "00971123456789";
            var languageCode = "en";
            var requestDto = new PaymentAuthorisationDecisionRequestDto
            {
                Id = Guid.NewGuid(),
                Decision = PaymentAuthDecision.APPROVE
            };

            var errorResult = Result.Failure(Errors.InAppAuth.UserNotFound);

            // Mock HttpContextAccessor
            _httpContextAccessorMock.Setup(x => x.HttpContext.User.FindFirst(It.IsAny<string>()))
                .Returns(new Claim(ConstantParam.Username, userPhoneNumber));

            // Mock Mediator
            _mediatorMock.Setup(x => x.Send(It.IsAny<UpdatePaymentAuthorisationDecisionCommand>(), cancellationToken))
                .ReturnsAsync(errorResult);

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext()
            };
            _controller.HttpContext.Request.Headers["x-lang-code"] = languageCode;

            // Act
            var actionResult = await _controller.PaymentAuthorisationDecision(requestDto, cancellationToken);

            // Assert
            var badRequestObjectResult = Assert.IsType<BadRequestObjectResult>(actionResult);
            Assert.Equal(Errors.InAppAuth.UserNotFound.Code, badRequestObjectResult.Value);
        }

    }
}
