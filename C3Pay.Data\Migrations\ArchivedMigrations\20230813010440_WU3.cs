﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class WU3 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Alpha2Code",
                table: "RemittanceDeliveryMethods",
                type: "nvarchar(2)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EligibleForWallet",
                table: "Countries",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "BeneficiaryAdditionFields",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Hint = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    MinLength = table.Column<int>(type: "int", nullable: false),
                    MaxLength = table.Column<int>(type: "int", nullable: false),
                    FieldId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FieldType = table.Column<int>(type: "int", nullable: false),
                    DisplayOrder = table.Column<int>(type: "int", nullable: true),
                    IsShared = table.Column<bool>(type: "bit", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DropdownReference = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BeneficiaryAdditionFieldGroupId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BeneficiaryAdditionFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BeneficiaryAdditionFields_BeneficiaryAdditionFieldGroups_BeneficiaryAdditionFieldGroupId",
                        column: x => x.BeneficiaryAdditionFieldGroupId,
                        principalTable: "BeneficiaryAdditionFieldGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RemittanceDeliveryMethods_Alpha2Code",
                table: "RemittanceDeliveryMethods",
                column: "Alpha2Code");

            migrationBuilder.CreateIndex(
                name: "IX_BeneficiaryAdditionFields_BeneficiaryAdditionFieldGroupId",
                table: "BeneficiaryAdditionFields",
                column: "BeneficiaryAdditionFieldGroupId");

            migrationBuilder.AddForeignKey(
                name: "FK_RemittanceDeliveryMethods_Countries_Alpha2Code",
                table: "RemittanceDeliveryMethods",
                column: "Alpha2Code",
                principalTable: "Countries",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RemittanceDeliveryMethods_Countries_Alpha2Code",
                table: "RemittanceDeliveryMethods");

            migrationBuilder.DropTable(
                name: "BeneficiaryAdditionFields");

            migrationBuilder.DropIndex(
                name: "IX_RemittanceDeliveryMethods_Alpha2Code",
                table: "RemittanceDeliveryMethods");

            migrationBuilder.DropColumn(
                name: "Alpha2Code",
                table: "RemittanceDeliveryMethods");

            migrationBuilder.DropColumn(
                name: "EligibleForWallet",
                table: "Countries");
        }
    }
}
