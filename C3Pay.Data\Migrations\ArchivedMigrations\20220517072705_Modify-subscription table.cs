﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Modifysubscriptiontable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TextContentCode",
                table: "Subscriptions",
                type: "nvarchar(25)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_TextContentCode",
                table: "Subscriptions",
                column: "TextContentCode");

            migrationBuilder.AddForeignKey(
                name: "FK_Subscriptions_TextContents_TextContentCode",
                table: "Subscriptions",
                column: "TextContentCode",
                principalTable: "TextContents",
                principalColumn: "Code",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Subscriptions_TextContents_TextContentCode",
                table: "Subscriptions");

            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_TextContentCode",
                table: "Subscriptions");

            migrationBuilder.DropColumn(
                name: "TextContentCode",
                table: "Subscriptions");
        }
    }
}
