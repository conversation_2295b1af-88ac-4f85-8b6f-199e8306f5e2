﻿using Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core.Models.Messages.Integration.ESMO;
using Edenred.Common.Core.Models.Messages.Integration.PPS.Card;
using Edenred.Common.Core.Services.Base;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Edenred.Common.Core
{
    public interface IESMOWebService : IESMOBaseService
    {
        Task<ServiceResponse<string>> GetCardSerialNumber(string cardNumber);
        Task<ServiceResponse> BlockCard(BlockCardRequest request);
        Task<ServiceResponse> UnblockCard(UnblockCardRequest request);
        Task<ServiceResponse<GetCardPinResult>> GetCardPin(GetCardPinRequest request);
        Task<ServiceResponse<GetCardDetailsResult>> GetCardDetails(GetCardDetailsRequest request);
        Task<ServiceResponse<GetCardHolderDetailsResponseDto>> GetCardHolderDetailsV2(GetCardHolderDetailsRequestDto getCardHolderDetailsRequest);
        Task<ServiceResponse<VerifyCvc2Result>> VerifyCvc2(VerifyCvc2Request request);
        Task<ServiceResponse<GetEmployeeDetailsResult>> GetEmployeeDetails(GetEmployeeDetailsRequest getCardHolderDetailsRequest);
        Task<ServiceResponse<GetOrianEmployeeResult>> GetEmployeeDetailsByCardSerialNumber(string CardSerialNumber);
        Task<ServiceResponse<GetOrianEmployeeResult>> GetEmployeeDetailsByPPSAccountNo(string ppsAccountNo);
        Task<ServiceResponse<GetCashBackEligibilityResult>> GetCashbackEligibility(string citizenId);
        Task<ServiceResponse<CashBackUpdateResult>> UpdateCashBackStatus(bool isCashBacked, string citizenId);
        Task<ServiceResponse<List<PendingEodFile>>> GetPendingEodFiles();
        Task<ServiceResponse<List<SearchResult>>> Search(SearchRequest request);
        Task<ServiceResponse<GetSMSSubscriptionModeResponse>> GetSMSSubscriptionMode(GetSMSSubscriptionModeRequest getSMSSubscriptionModeRequest, CancellationToken cancellationToken = default(CancellationToken));
        Task<ServiceResponse> MarkCardAsKycUnblocked(string citizenId);
        Task<ServiceResponse> MarkCardAsKycUnblockedV2(MarkUnblockCardRequest request);
        Task<ServiceResponse<GotSalaryInTheLast3MonthsResponse>> GotSalaryInLast3Months(string citizenId);
        Task<ServiceResponse> MarkEmployeeAsDormant(MarkEmployeeAsDormantRequest request);
        Task<ServiceResponse<List<CardHolderDetailsDto>>> GetCardsByPassportId(GetCardsByPassportIdRequest request);
        Task<ServiceResponse<IsCardDormantResponse>> IsCardDormant(string registrationId);
        Task<ServiceResponse> ToggleC3PayPlusSmsSubscription(ToggleC3PayPlusSmsSubscriptionRequestDto toggleC3PayPlusSmsSubscriptionRequest);
        Task<ServiceResponse<RollbackC3PayPlusSmsSubscriptionResponseDto>> RollbackC3PayPlusSmsSubscription(RollbackC3PayPlusSmsSubscriptionRequestDto rollbackC3PayPlusSmsSubscriptionRequestDto);
    }
}
