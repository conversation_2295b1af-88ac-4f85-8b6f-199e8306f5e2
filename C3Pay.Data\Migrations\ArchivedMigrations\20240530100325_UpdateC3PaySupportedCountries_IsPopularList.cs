﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UpdateC3PaySupportedCountries_IsPopularList : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 3,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 46,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 64,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 68,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 81,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 100,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 114,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 136,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 163,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 200,
                column: "IsPopular",
                value: true);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 227,
                column: "IsPopular",
                value: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 3,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 46,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 64,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 68,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 81,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 100,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 114,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 136,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 163,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 200,
                column: "IsPopular",
                value: false);

            migrationBuilder.UpdateData(
                table: "C3PayPlusMembershipSupportedCountries",
                keyColumn: "Id",
                keyValue: 227,
                column: "IsPopular",
                value: false);
        }
    }
}
