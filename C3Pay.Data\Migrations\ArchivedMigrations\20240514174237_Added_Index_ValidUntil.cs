﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_Index_ValidUntil : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_C3PayPlusMembershipUsers_ValidUntill",
                table: "C3PayPlusMembershipUsers",
                column: "ValidUntill");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_C3PayPlusMembershipUsers_ValidUntill",
                table: "C3PayPlusMembershipUsers");
        }
    }
}
