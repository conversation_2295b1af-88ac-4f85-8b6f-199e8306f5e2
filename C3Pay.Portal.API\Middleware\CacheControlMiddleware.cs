﻿using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using System.Threading.Tasks;

namespace C3Pay.Portal.API.Middleware
{
    public class CacheControlMiddleware
    {
        private readonly RequestDelegate _next;

        public CacheControlMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            context.Response.GetTypedHeaders().CacheControl = new CacheControlHeaderValue
            {
                NoCache = true,
                MustRevalidate = true
            };

            await _next(context);
        }
    }
}
