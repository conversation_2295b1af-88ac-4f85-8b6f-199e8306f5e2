﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_IsCrossTransfer_Beneficiary : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsCrossTransfer",
                table: "MoneyTransferBeneficiaries",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 5,
                column: "LogoUrl",
                value: "https://eaec3sharedsp.blob.core.windows.net/partner-logos/orient-exchange-house.png");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsCrossTransfer",
                table: "MoneyTransferBeneficiaries");

            migrationBuilder.UpdateData(
                table: "Partners",
                keyColumn: "Id",
                keyValue: 5,
                column: "LogoUrl",
                value: "https://eaec3sharedsp.blob.core.windows.net/exchange-house-logos/orient-exchange-house.png");
        }
    }
}
