﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Seeding_C3PayPlusMembershipLifeInsuranceNomineeRelationshipType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                columns: new[] { "Id", "CreatedDate", "DeletedBy", "DeletedDate", "IsActive", "IsDeleted", "Name", "UpdatedDate" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(138), null, null, true, false, "Wife", null },
                    { 2, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(566), null, null, true, false, "Husband", null },
                    { 3, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(584), null, null, true, false, "Child", null },
                    { 4, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(586), null, null, true, false, "Parent", null },
                    { 5, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(587), null, null, true, false, "Sibling", null },
                    { 6, new DateTime(2024, 5, 20, 19, 6, 32, 240, DateTimeKind.Local).AddTicks(588), null, null, true, false, "Friend", null }
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 1);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 2);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 3);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 4);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                table: "C3PayPlusMembershipLifeInsuranceNomineeRelationshipTypes",
                keyColumn: "Id",
                keyValue: 6);
        }
    }
}
