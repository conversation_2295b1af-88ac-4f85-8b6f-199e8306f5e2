﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class UnEmploymentInsurance_Update : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "UnEmpInsurancePaymentOptions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 1,
                column: "IsActive",
                value: true);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 2,
                column: "IsActive",
                value: true);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 3,
                column: "IsActive",
                value: true);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 4,
                columns: new[] { "Frequency", "IsActive" },
                values: new object[] { 4, true });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "Amount", "Description", "Frequency", "PartnerCode" },
                values: new object[] { 15m, "AED 15/Quarter", 2, 1 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 6,
                columns: new[] { "Amount", "Description", "Fee", "Frequency", "PartnerCode" },
                values: new object[] { 30m, "AED 30 (Semi-Annual)", 0.75m, 3, 1 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 7,
                columns: new[] { "Amount", "Description", "Fee", "Frequency", "PartnerCode" },
                values: new object[] { 30m, "AED 30/Quarter", 14m, 2, 1 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 8,
                columns: new[] { "Amount", "Description", "Frequency", "PartnerCode" },
                values: new object[] { 60m, "AED 60 (Semi-Annual)", 3, 1 });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "UnEmpInsurancePaymentOptions");

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 4,
                column: "Frequency",
                value: 1);

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "Amount", "Description", "Frequency", "PartnerCode" },
                values: new object[] { 5m, "AED 6/Month", 1, 2 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 6,
                columns: new[] { "Amount", "Description", "Fee", "Frequency", "PartnerCode" },
                values: new object[] { 60m, "AED 60 (1 Year)", 7m, 4, 2 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 7,
                columns: new[] { "Amount", "Description", "Fee", "Frequency", "PartnerCode" },
                values: new object[] { 10m, "AED 10/Month", 1.5m, 1, 2 });

            migrationBuilder.UpdateData(
                table: "UnEmpInsurancePaymentOptions",
                keyColumn: "Id",
                keyValue: 8,
                columns: new[] { "Amount", "Description", "Frequency", "PartnerCode" },
                values: new object[] { 120m, "AED 120 (1 Year)", 1, 2 });
        }
    }
}
