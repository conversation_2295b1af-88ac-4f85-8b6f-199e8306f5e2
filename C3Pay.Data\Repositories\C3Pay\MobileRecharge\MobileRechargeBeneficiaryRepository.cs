﻿using C3Pay.Core.Models;
using C3Pay.Core.Models.Messages;
using C3Pay.Core.Models.Portal;
using C3Pay.Core.Models.Portal.BlackList;
using C3Pay.Core.Repositories;
using Edenred.Common.Data;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using static C3Pay.Core.BaseEnums;

namespace C3Pay.Data.Repositories
{
    public class MobileRechargeBeneficiaryRepository : Repository<MobileRechargeBeneficiary>, IMobileRechargeBeneficiaryRepository
    {
        public MobileRechargeBeneficiaryRepository(C3PayContext context) : base(context)
        {

        }
        private C3PayContext C3PayContext
        {
            get { return Context as C3PayContext; }

        }


        public async Task<IList<MobileRechargeBeneficiary>> FindWithExternalProviderAsync(Expression<Func<MobileRechargeBeneficiary, bool>> predicate, Expression<Func<MobileRechargeBeneficiary, object>> order, bool? isDescOrder, int? skip, int? pageSize)
        {
            var query = base.BuildQuery(predicate, order, isDescOrder, skip, pageSize, false, x => x.Country);

            query = query.Include(x => x.BeneficiaryProviders).ThenInclude(x => x.Provider);

            return await query.ToListAsync();
        }

        public async Task<List<MobileRechargeBeneficiaryDetails>> GetUserBeneficiaries(Guid userId, MobileRechargeType? rechargeType)
        {
            var beneficairies = await C3PayContext
                .MobileRechargeBeneficiaries
                .AsNoTracking()
                .Where(t => t.UserId == userId
                && (!rechargeType.HasValue || t.RechargeType == rechargeType.Value)
                && !t.IsDeleted)
                .Take(20)
                .Select(t => new MobileRechargeBeneficiaryDetails()
                {
                    BeneficiaryId = t.Id,
                    CountryCode = t.CountryCode,
                    CountryName = t.Country.Name,
                    FullName = t.NickName,
                    PhoneNumber = t.AccountNumber,
                    Status = t.Status,
                    RechargeType = t.RechargeType,
                    Remarks = t.Remarks,
                    ProviderLogo = t.BeneficiaryProviders.FirstOrDefault(b => b.IsActive == true && b.IsDeleted == false).Provider.LogoUrl ?? "",
                    ProviderName = t.BeneficiaryProviders.FirstOrDefault(b => b.IsActive == true && b.IsDeleted == false).Provider.Name ?? "",
                    LastInteractedDate = t.MobileRechargeTransactions.Any() ? t.MobileRechargeTransactions.OrderByDescending(a => a.CreatedDate).FirstOrDefault().CreatedDate :
                        t.CreatedDate
                })
                .OrderByDescending(b => b.LastInteractedDate).ToListAsync();

            return beneficairies;
        }

        public async Task<IList<Guid>> Search(SearchBeneficiaryParameters searchBeneficiaryParameters)
        {
            var userIds = await C3PayContext
                .MobileRechargeBeneficiaries
                .AsNoTracking()
                .Where(t => t.AccountNumber == searchBeneficiaryParameters.Receiver && !t.IsDeleted && t.CountryCode == searchBeneficiaryParameters.Country)
                .Take(20)
                .Select(t => t.UserId)
                .ToListAsync();

            return userIds;
        }

        public async Task<MobileRechargeBeneficiary> GetBeneficiaryAsync(Guid beneficiaryId)
        {
            return await C3PayContext.MobileRechargeBeneficiaries.AsNoTracking()
                        .Include(a => a.BeneficiaryProviders)
                        .Where(a => a.Id == beneficiaryId && !a.IsDeleted)
                        .Select(a => new MobileRechargeBeneficiary()
                        {
                            Id = a.Id,
                            AccountNumber = a.AccountNumber,
                            CountryCode = a.CountryCode,
                            BeneficiaryProviders = a.BeneficiaryProviders.Select(b => new MobileRechargeBeneficiaryProvider()
                            {
                                ProviderCode = b.ProviderCode
                            }).ToList(),
                        }).FirstOrDefaultAsync();
        }

        public async Task<string> MarkAsDeletedAndCreateNewAsync(Guid beneficiaryId, string newProviderCode)
        {
            string result = "success";
            using (var transaction = await C3PayContext.Database.BeginTransactionAsync())
            {
                try
                {
                    var beneficiary = await C3PayContext.MobileRechargeBeneficiaries
                        .Include(a => a.BeneficiaryProviders)
                        .FirstOrDefaultAsync(a => a.Id == beneficiaryId && !a.IsDeleted);
                    if (beneficiary != null)
                    {
                        var newBeneficiary = new MobileRechargeBeneficiary()
                        {
                            AccountNumber = beneficiary.AccountNumber,
                            CountryCode = beneficiary.CountryCode,
                            CreatedDate = DateTime.Now,
                            DeletedBy = null,
                            DeletedDate = null,
                            BeneficiaryProviders = beneficiary.BeneficiaryProviders.Select(a => new MobileRechargeBeneficiaryProvider()
                            {
                                ProviderCode = newProviderCode,
                                IsActive = true,
                                IsDeleted = false,
                                CreatedDate = DateTime.Now,
                                DeletedBy = null,
                                DeletedDate = null,
                                UpdatedDate = null,
                            }).ToList(),
                            IsDeleted = false,
                            IsProviderSelected = false,
                            NickName = beneficiary.NickName,
                            RechargeType = beneficiary.RechargeType,
                            Remarks = beneficiary.Remarks,
                            Status = beneficiary.Status,
                            UserId = beneficiary.UserId,
                            ExternalId = 0,
                            ExternalUserId = 0,
                            UpdatedDate = null
                        };
                        await C3PayContext.MobileRechargeBeneficiaries.AddAsync(newBeneficiary);
                        await C3PayContext.SaveChangesAsync();

                        beneficiary.CrossCheckedBeneficiaryId = newBeneficiary.Id;
                        beneficiary.MarkAsDeleted();
                        await C3PayContext.SaveChangesAsync();
                    }


                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // If any step fails, roll back the transaction
                    await transaction.RollbackAsync();
                    result = Convert.ToString(ex.Message) + Convert.ToString(ex.StackTrace);
                    return result;
                }
            }
            return result;
        }
    }
}
