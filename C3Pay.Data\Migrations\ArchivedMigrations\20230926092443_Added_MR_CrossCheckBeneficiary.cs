﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class Added_MR_CrossCheckBeneficiary : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsCrossChecked",
                table: "MobileRechargeBeneficiaries");

            migrationBuilder.AddColumn<Guid>(
                name: "CrossCheckedBeneficiaryId",
                table: "MobileRechargeBeneficiaries",
                type: "uniqueidentifier",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CrossCheckedBeneficiaryId",
                table: "MobileRechargeBeneficiaries");

            migrationBuilder.AddColumn<bool>(
                name: "IsCrossChecked",
                table: "MobileRechargeBeneficiaries",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
