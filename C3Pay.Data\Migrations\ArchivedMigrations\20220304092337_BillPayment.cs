﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace C3Pay.Data.Migrations
{
    public partial class BillPayment : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        { }

        //        protected override void Up(MigrationBuilder migrationBuilder)
        //        {
        //                var start = @" 
        //                    delete from [dbo].[__EFMigrationsHistory] where MigrationId like '%Bill%' 

        //                    IF EXISTS (SELECT 1 
        //                               FROM INFORMATION_SCHEMA.TABLES 
        //                               WHERE TABLE_TYPE='BASE TABLE' 
        //                               AND TABLE_NAME='BillPaymentExternalTransactions') 
        //                    BEGIN 
        //                    SELECT * INTO BillPaymentExternalTransactions_temp FROM BillPaymentExternalTransactions;
        //                    SELECT * INTO BillPaymentTransactions_temp FROM BillPaymentTransactions; 
        //                    SELECT * INTO BillPaymentBillerIOs_temp FROM BillPaymentBillerIOs;
        //                    SELECT * INTO BillPaymentBillers_temp FROM BillPaymentBillers;
        //                    SELECT * INTO BillPaymentCategoryProviders_temp FROM BillPaymentCategoryProviders;
        //                    SELECT * INTO BillPaymentProviderFees_temp FROM BillPaymentProviderFees; 
        //                    SELECT * INTO BillPaymentProductIOs_temp FROM BillPaymentProductIOs;
        //                    SELECT * INTO BillPaymentProducts_temp FROM BillPaymentProducts;
        //                    SELECT * INTO BillPaymentProviders_temp FROM BillPaymentProviders;
        //                    SELECT * INTO BillPaymentCategoryFees_temp FROM BillPaymentCategoryFees; 
        //                    SELECT * INTO BillPaymentSubCategories_temp FROM BillPaymentSubCategories; 
        //                    SELECT * INTO BillPaymentCategories_temp FROM BillPaymentCategories; 
        //                    SELECT * INTO BillPaymentDailyFxRates_temp FROM BillPaymentDailyFxRates;
        //                    SELECT * INTO BillPaymentFees_temp FROM BillPaymentFees;

        //                    IF EXISTS (SELECT 1 
        //                               FROM INFORMATION_SCHEMA.TABLES 
        //                               WHERE TABLE_TYPE='BASE TABLE' 
        //                               AND TABLE_NAME='BillPaymentExternalTransactions') 
        //                    BEGIN  
        //                        drop table  BillPaymentExternalTransactions ;
        //                        drop table  BillPaymentTransactions; 
        //                        drop table  BillPaymentBillerIOs ;
        //                        drop table  BillPaymentBillers;
        //                        drop table  BillPaymentCategoryProviders;
        //                        drop table  BillPaymentProviderFees; 
        //                        drop table  BillPaymentProductIOs;
        //                        drop table  BillPaymentProducts;
        //                        drop table  BillPaymentProviders;
        //                        drop table  BillPaymentCategoryFees; 
        //                        drop table  BillPaymentSubCategories; 
        //                        drop table  BillPaymentCategories; 
        //                        drop table  BillPaymentDailyFxRates;
        //                        drop table  BillPaymentFees; 
        //                 END 

        //END 

        //                    DECLARE @ConstraintName nvarchar(200)
        //                    SELECT @ConstraintName = Name FROM SYS.DEFAULT_CONSTRAINTS WHERE PARENT_OBJECT_ID = OBJECT_ID('Countries') AND PARENT_COLUMN_ID = (SELECT column_id FROM sys.columns WHERE NAME = N'BillPaymentEnabled' AND object_id = OBJECT_ID(N'Countries'))
        //                    IF @ConstraintName IS NOT NULL
        //                    EXEC('ALTER TABLE Countries DROP CONSTRAINT ' + @ConstraintName)
        //                    IF EXISTS (SELECT * FROM syscolumns WHERE id=object_id('Countries') AND name='BillPaymentEnabled')
        //                    EXEC('ALTER TABLE Countries DROP COLUMN BillPaymentEnabled') 
        //            ";
        //            migrationBuilder.Sql(start);

        //            migrationBuilder.AddColumn<bool>(
        //                name: "BillPaymentEnabled",
        //                table: "Countries",
        //                type: "bit",
        //                nullable: false,
        //                defaultValue: false);

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentCategories",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    IconUrl = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
        //                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentCategories", x => x.Id);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentDailyFxRates",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    FXDate = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
        //                    SettlementCurrency = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    BillerType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    FxRate = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentDailyFxRates", x => x.Id);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentFees",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    ProviderCode = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    Fee = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    FeeCurrency = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentFees", x => x.Id);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentProviders",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    Code = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: true),
        //                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
        //                    Description = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
        //                    Type = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
        //                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
        //                    CatalogVersion = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
        //                    IconUrl = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentProviders", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentProviders_Countries_CountryCode",
        //                        column: x => x.CountryCode,
        //                        principalTable: "Countries",
        //                        principalColumn: "Code",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentCategoryFees",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    CountryCode = table.Column<string>(type: "nvarchar(2)", nullable: true),
        //                    CategoryId = table.Column<int>(type: "int", nullable: false),
        //                    FxRatePercentage = table.Column<decimal>(type: "decimal(18,2)", maxLength: 50, precision: 18, scale: 2, nullable: false),
        //                    MandatoryFee = table.Column<decimal>(type: "decimal(18,2)", maxLength: 50, precision: 18, scale: 2, nullable: false),
        //                    MandatoryFeeCurrency = table.Column<string>(type: "nvarchar(150)", maxLength: 150, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentCategoryFees", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentCategoryFees_BillPaymentCategories_CategoryId",
        //                        column: x => x.CategoryId,
        //                        principalTable: "BillPaymentCategories",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentCategoryFees_Countries_CountryCode",
        //                        column: x => x.CountryCode,
        //                        principalTable: "Countries",
        //                        principalColumn: "Code",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentSubCategories",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    CategoryId = table.Column<int>(type: "int", nullable: false),
        //                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentSubCategories", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentSubCategories_BillPaymentCategories_CategoryId",
        //                        column: x => x.CategoryId,
        //                        principalTable: "BillPaymentCategories",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentBillers",
        //                columns: table => new
        //                {
        //                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
        //                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
        //                    ProviderId = table.Column<int>(type: "int", nullable: false),
        //                    CategoryId = table.Column<int>(type: "int", nullable: false),
        //                    NickName = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentBillers", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentBillers_BillPaymentCategories_CategoryId",
        //                        column: x => x.CategoryId,
        //                        principalTable: "BillPaymentCategories",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentBillers_BillPaymentProviders_ProviderId",
        //                        column: x => x.ProviderId,
        //                        principalTable: "BillPaymentProviders",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentBillers_Users_UserId",
        //                        column: x => x.UserId,
        //                        principalTable: "Users",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentProducts",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    ProviderId = table.Column<int>(type: "int", nullable: false),
        //                    Code = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
        //                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
        //                    Type = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
        //                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    MinAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    MaxAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    Currency = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: true),
        //                    DaysToPost = table.Column<int>(type: "int", nullable: false),
        //                    BusinessDays = table.Column<bool>(type: "bit", nullable: false),
        //                    InquiryAvailable = table.Column<bool>(type: "bit", nullable: false),
        //                    PastDuePaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
        //                    ExcessPaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
        //                    PartialPaymentAllowed = table.Column<bool>(type: "bit", nullable: false),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentProducts", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentProducts_BillPaymentProviders_ProviderId",
        //                        column: x => x.ProviderId,
        //                        principalTable: "BillPaymentProviders",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentCategoryProviders",
        //                columns: table => new
        //                {
        //                    SubCategoryId = table.Column<int>(type: "int", nullable: false),
        //                    ProviderId = table.Column<int>(type: "int", nullable: false),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentCategoryProviders", x => new { x.SubCategoryId, x.ProviderId });
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentCategoryProviders_BillPaymentProviders_ProviderId",
        //                        column: x => x.ProviderId,
        //                        principalTable: "BillPaymentProviders",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentCategoryProviders_BillPaymentSubCategories_SubCategoryId",
        //                        column: x => x.SubCategoryId,
        //                        principalTable: "BillPaymentSubCategories",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentTransactions",
        //                columns: table => new
        //                {
        //                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
        //                    BillerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
        //                    ReferenceNumber = table.Column<string>(type: "nvarchar(20)", nullable: true),
        //                    BillAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    BillAmountCurrency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
        //                    ProviderCharge = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    ProviderChargeCurrency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
        //                    ConvertedBillAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    ConvertedBillAmountCurrency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
        //                    FeeAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    FeeAmountCurrency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
        //                    FxRatePercent = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    ExternalFxRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    ModifiedFxRate = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
        //                    TotalAmountCurrency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
        //                    DaysToPost = table.Column<int>(type: "int", nullable: false),
        //                    Status = table.Column<int>(type: "int", nullable: false),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentTransactions", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentTransactions_BillPaymentBillers_BillerId",
        //                        column: x => x.BillerId,
        //                        principalTable: "BillPaymentBillers",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentTransactions_Transactions_ReferenceNumber",
        //                        column: x => x.ReferenceNumber,
        //                        principalTable: "Transactions",
        //                        principalColumn: "ReferenceNumber",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentProductIOs",
        //                columns: table => new
        //                {
        //                    Id = table.Column<int>(type: "int", nullable: false)
        //                        .Annotation("SqlServer:Identity", "1, 1"),
        //                    ProductId = table.Column<int>(type: "int", nullable: false),
        //                    Name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
        //                    Description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    Type = table.Column<int>(type: "int", nullable: false),
        //                    DataType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
        //                    MinLength = table.Column<int>(type: "int", nullable: false),
        //                    MaxLength = table.Column<int>(type: "int", nullable: false),
        //                    IOId = table.Column<int>(type: "int", nullable: false),
        //                    Operation = table.Column<int>(type: "int", nullable: false),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentProductIOs", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentProductIOs_BillPaymentProducts_ProductId",
        //                        column: x => x.ProductId,
        //                        principalTable: "BillPaymentProducts",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentProviderFees",
        //                columns: table => new
        //                {
        //                    ProviderId = table.Column<int>(type: "int", nullable: false),
        //                    ProductId = table.Column<int>(type: "int", nullable: false),
        //                    MaxUserFreeType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    MandatoryFee = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    MaxUserFee = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    MandatoryFeeType = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentProviderFees", x => new { x.ProductId, x.ProviderId });
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentProviderFees_BillPaymentProducts_ProductId",
        //                        column: x => x.ProductId,
        //                        principalTable: "BillPaymentProducts",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentProviderFees_BillPaymentProviders_ProviderId",
        //                        column: x => x.ProviderId,
        //                        principalTable: "BillPaymentProviders",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentExternalTransactions",
        //                columns: table => new
        //                {
        //                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWSEQUENTIALID()"),
        //                    BillPaymentTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
        //                    ExternalTransactionId = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
        //                    FXRate = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
        //                    ResponseCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
        //                    SettlementCurrency = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
        //                    ResponseMessage = table.Column<string>(type: "nvarchar(800)", maxLength: 800, nullable: true),
        //                    ResponseDateTime = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
        //                    ConfirmationNumber = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
        //                    EntityTransactionId = table.Column<string>(type: "nvarchar(400)", maxLength: 400, nullable: true),
        //                    BaseCurrency = table.Column<string>(type: "nvarchar(30)", maxLength: 30, nullable: true),
        //                    TicketCaption = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentExternalTransactions", x => x.Id);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentExternalTransactions_BillPaymentTransactions_BillPaymentTransactionId",
        //                        column: x => x.BillPaymentTransactionId,
        //                        principalTable: "BillPaymentTransactions",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Cascade);
        //                });

        //            migrationBuilder.CreateTable(
        //                name: "BillPaymentBillerIOs",
        //                columns: table => new
        //                {
        //                    BillerId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
        //                    ProductIOId = table.Column<int>(type: "int", nullable: false),
        //                    FieldValue = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: true),
        //                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
        //                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
        //                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
        //                    DeletedBy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true)
        //                },
        //                constraints: table =>
        //                {
        //                    table.PrimaryKey("PK_BillPaymentBillerIOs", x => new { x.ProductIOId, x.BillerId });
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentBillerIOs_BillPaymentBillers_BillerId",
        //                        column: x => x.BillerId,
        //                        principalTable: "BillPaymentBillers",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                    table.ForeignKey(
        //                        name: "FK_BillPaymentBillerIOs_BillPaymentProductIOs_ProductIOId",
        //                        column: x => x.ProductIOId,
        //                        principalTable: "BillPaymentProductIOs",
        //                        principalColumn: "Id",
        //                        onDelete: ReferentialAction.Restrict);
        //                });

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentBillerIOs_BillerId",
        //                table: "BillPaymentBillerIOs",
        //                column: "BillerId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentBillers_CategoryId",
        //                table: "BillPaymentBillers",
        //                column: "CategoryId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentBillers_ProviderId",
        //                table: "BillPaymentBillers",
        //                column: "ProviderId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentBillers_UserId",
        //                table: "BillPaymentBillers",
        //                column: "UserId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentCategoryFees_CategoryId",
        //                table: "BillPaymentCategoryFees",
        //                column: "CategoryId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentCategoryFees_CountryCode",
        //                table: "BillPaymentCategoryFees",
        //                column: "CountryCode");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentCategoryProviders_ProviderId",
        //                table: "BillPaymentCategoryProviders",
        //                column: "ProviderId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentExternalTransactions_BillPaymentTransactionId",
        //                table: "BillPaymentExternalTransactions",
        //                column: "BillPaymentTransactionId",
        //                unique: true);

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentExternalTransactions_ExternalTransactionId",
        //                table: "BillPaymentExternalTransactions",
        //                column: "ExternalTransactionId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentProductIOs_ProductId",
        //                table: "BillPaymentProductIOs",
        //                column: "ProductId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentProducts_ProviderId",
        //                table: "BillPaymentProducts",
        //                column: "ProviderId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentProviderFees_ProviderId",
        //                table: "BillPaymentProviderFees",
        //                column: "ProviderId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentProviders_CountryCode",
        //                table: "BillPaymentProviders",
        //                column: "CountryCode");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentSubCategories_CategoryId",
        //                table: "BillPaymentSubCategories",
        //                column: "CategoryId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentTransactions_BillerId",
        //                table: "BillPaymentTransactions",
        //                column: "BillerId");

        //            migrationBuilder.CreateIndex(
        //                name: "IX_BillPaymentTransactions_ReferenceNumber",
        //                table: "BillPaymentTransactions",
        //                column: "ReferenceNumber",
        //                unique: true,
        //                filter: "[ReferenceNumber] IS NOT NULL");

        //            var end = @"  
        //  IF EXISTS (SELECT 1 
        //                               FROM INFORMATION_SCHEMA.TABLES 
        //                               WHERE TABLE_TYPE='BASE TABLE' 
        //                               AND TABLE_NAME='BillPaymentExternalTransactions_temp') 
        //                    BEGIN 

        //SET IDENTITY_INSERT [dbo].[BillPaymentCategories] ON 

        //INSERT INTO [dbo].[BillPaymentCategories]
        //           (Id, [Name]           ,[IconUrl]           ,[IsActive]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])

        //select  Id, [Name]           ,[IconUrl]           ,[IsActive]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //from [BillPaymentCategories_temp]
        //SET IDENTITY_INSERT [dbo].[BillPaymentCategories] OFF 



        //SET IDENTITY_INSERT [dbo].[BillPaymentSubCategories] ON 


        //INSERT INTO [dbo].[BillPaymentSubCategories]
        //           (Id, [Name]           ,[CategoryId]           ,[IsActive]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])

        //select  Id, [Name]           ,[CategoryId]           ,[IsActive]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentSubCategories_temp


        //SET IDENTITY_INSERT [dbo].[BillPaymentSubCategories] OFF 

        //SET IDENTITY_INSERT [dbo].[BillPaymentCategoryFees] ON 

        //INSERT INTO [dbo].[BillPaymentCategoryFees]
        //           (Id, [CountryCode]           ,[CategoryId]           ,[FxRatePercentage]           ,[MandatoryFee]
        //           ,[MandatoryFeeCurrency]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy])
        //select Id, [CountryCode]           ,[CategoryId]           ,[FxRatePercentage]           ,[MandatoryFee]
        //           ,[MandatoryFeeCurrency]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentCategoryFees_temp


        //SET IDENTITY_INSERT [dbo].[BillPaymentCategoryFees] OFF 

        //SET IDENTITY_INSERT [dbo].[BillPaymentProviders] ON 
        //INSERT INTO [dbo].[BillPaymentProviders]
        //           (Id, [Code]           ,[Name]           ,[Description]           ,[Type]           ,[CountryCode]
        //           ,[CatalogVersion]           ,[IconUrl]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy])
        //select   Id, [Code]           ,[Name]           ,[Description]           ,[Type]           ,[CountryCode]
        //           ,[CatalogVersion]           ,[IconUrl]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy] 
        //from    BillPaymentProviders_temp;


        //SET IDENTITY_INSERT [dbo].[BillPaymentProviders] OFF 

        //SET IDENTITY_INSERT [dbo].[BillPaymentProducts] ON 

        //INSERT INTO [dbo].[BillPaymentProducts]
        //           (Id, [ProviderId]           ,[Code]           ,[Description]           ,[Type]
        //           ,[Amount]           ,[MinAmount]           ,[MaxAmount]           ,[Currency]
        //           ,[DaysToPost]           ,[BusinessDays]           ,[InquiryAvailable]           ,[PastDuePaymentAllowed]
        //           ,[ExcessPaymentAllowed]           ,[PartialPaymentAllowed]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])

        //select   Id, [ProviderId]           ,[Code]           ,[Description]           ,[Type]
        //           ,[Amount]           ,[MinAmount]           ,[MaxAmount]           ,[Currency]
        //           ,[DaysToPost]           ,[BusinessDays]           ,[InquiryAvailable]           ,[PastDuePaymentAllowed]
        //           ,[ExcessPaymentAllowed]           ,[PartialPaymentAllowed]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentProducts_temp;

        //SET IDENTITY_INSERT [dbo].[BillPaymentProducts] OFF 

        //SET IDENTITY_INSERT [dbo].[BillPaymentProductIOs] ON 

        //INSERT INTO [dbo].[BillPaymentProductIOs]
        //           (Id, [ProductId]           ,[Name]           ,[Description]
        //           ,[Type]           ,[DataType]           ,[MinLength]           ,[MaxLength]
        //           ,[IOId]           ,[Operation]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])
        //select  Id, [ProductId]          ,[Name]           ,[Description]
        //           ,[Type]           ,[DataType]           ,[MinLength]           ,[MaxLength]
        //           ,[IOId]           ,[Operation]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //  from BillPaymentProductIOs_temp ;

        //SET IDENTITY_INSERT [dbo].[BillPaymentProductIOs] OFF


        //declare @value int;
        //select @value = Count(Id) from BillPaymentProductIOs;  
        //DBCC CHECKIDENT ('[BillPaymentProductIOs]', RESEED, @value);
        //select @value = Count(Id) from BillPaymentProducts;  
        //DBCC CHECKIDENT ('[BillPaymentProducts]', RESEED, @value); 
        //select @value = Count(Id) from BillPaymentProviders;  
        //DBCC CHECKIDENT ('[BillPaymentProviders]', RESEED, @value); 


        //INSERT INTO [dbo].[BillPaymentCategoryProviders]
        //           ([SubCategoryId]           ,[ProviderId]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])
        //select	[SubCategoryId]           ,[ProviderId]           ,[CreatedDate]           ,[UpdatedDate]
        //           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentCategoryProviders_temp


        //INSERT INTO [dbo].[BillPaymentProviderFees]
        //           ([ProviderId]           ,[ProductId]           ,[MaxUserFreeType]           ,[MandatoryFee]           ,[MaxUserFee]
        //           ,[MandatoryFeeType]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy])
        // select  [ProviderId]           ,[ProductId]           ,[MaxUserFreeType]           ,[MandatoryFee]           ,[MaxUserFee]
        //           ,[MandatoryFeeType]           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]
        //           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentProviderFees_temp


        //SET IDENTITY_INSERT [dbo].[BillPaymentDailyFxRates] ON 

        //INSERT INTO [dbo].[BillPaymentDailyFxRates]
        //           (Id, [FXDate]           ,[SettlementCurrency]           ,[BillerType]           ,[FxRate])

        //select Id, [FXDate]           ,[SettlementCurrency]           ,[BillerType]           ,[FxRate]
        //from [BillPaymentDailyFxRates_temp] 


        //SET IDENTITY_INSERT [dbo].[BillPaymentDailyFxRates] OFF 

        //select @value = Count(Id) from BillPaymentDailyFxRates;  
        //DBCC CHECKIDENT ('[BillPaymentDailyFxRates]', RESEED, @value); 


        //INSERT INTO [dbo].[BillPaymentBillers]
        //           ([Id]           ,[UserId]           ,[ProviderId]           ,[NickName]           ,[CreatedDate]
        //           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //           ,[CategoryId]) 
        //select    [Id]           ,[UserId]           ,[ProviderId]           ,[NickName]           ,[CreatedDate]
        //           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //           ,[CategoryId] from BillPaymentBillers_temp;

        //INSERT INTO [dbo].[BillPaymentBillerIOs]
        //           ([BillerId]           ,[ProductIOId]           ,[FieldValue]           ,[CreatedDate]
        //           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]) 
        //SELECT [BillerId]           ,[ProductIOId]           ,[FieldValue]           ,[CreatedDate]
        //           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy]
        //from BillPaymentBillerIOs_temp;

        //INSERT INTO [dbo].[BillPaymentTransactions]
        //           ([Id]           ,[BillerId]                  ,[ReferenceNumber]
        //           ,[BillAmount]           ,[BillAmountCurrency]           ,[ProviderCharge]           ,[ProviderChargeCurrency]
        //           ,[ConvertedBillAmount]           ,[ConvertedBillAmountCurrency]           ,[FeeAmount]
        //           ,[FeeAmountCurrency]           ,[FxRatePercent]           ,[ExternalFxRate]           ,[ModifiedFxRate]
        //           ,[TotalAmount]           ,[TotalAmountCurrency]           ,[DaysToPost]           ,[Status]
        //           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           ,[DeletedBy])
        //SELECT [Id]           ,[BillerId]                   ,[ReferenceNumber]
        //           ,[BillAmount]           ,[BillAmountCurrency]           ,[ProviderCharge]           ,[ProviderChargeCurrency]
        //           ,[ConvertedBillAmount]           ,[ConvertedBillAmountCurrency]           ,[FeeAmount]
        //           ,[FeeAmountCurrency]           ,[FxRatePercent]           ,[ExternalFxRate]           ,[ModifiedFxRate]
        //           ,[TotalAmount]           ,[TotalAmountCurrency]           ,[DaysToPost]           ,[Status]
        //           ,[CreatedDate]           ,[UpdatedDate]           ,[DeletedDate]           ,[IsDeleted]           
        //		   ,[DeletedBy]
        //FROM BillPaymentTransactions_temp;

        //INSERT INTO [dbo].[BillPaymentExternalTransactions]
        //            ([Id]           ,[BillPaymentTransactionId]           ,[ExternalTransactionId]
        //            ,[FXRate]           ,[ResponseCode]           ,[SettlementCurrency]
        //            ,[ResponseMessage]           ,[ResponseDateTime]          ,[ConfirmationNumber]
        //            ,[EntityTransactionId]           ,[BaseCurrency]           ,[TicketCaption]) 
        //SELECT [Id]           ,[BillPaymentTransactionId]           ,[ExternalTransactionId]
        //            ,[FXRate]           ,[ResponseCode]           ,[SettlementCurrency]
        //            ,[ResponseMessage]           ,[ResponseDateTime]          ,[ConfirmationNumber]
        //            ,[EntityTransactionId]           ,[BaseCurrency]           ,[TicketCaption]
        //from BillPaymentExternalTransactions_temp


        //					END

        //Update Countries set BillPaymentEnabled = 1 where Code in (select distinct CountryCode from BillPaymentProviders) 

        // IF EXISTS (SELECT 1 
        //                               FROM INFORMATION_SCHEMA.TABLES 
        //                               WHERE TABLE_TYPE='BASE TABLE' 
        //                               AND TABLE_NAME='BillPaymentExternalTransactions_temp') 
        //                    BEGIN  
        //                        drop table  BillPaymentExternalTransactions_temp ;
        //                        drop table  BillPaymentTransactions_temp; 
        //                        drop table  BillPaymentBillerIOs_temp ;
        //                        drop table  BillPaymentBillers_temp;
        //                        drop table  BillPaymentCategoryProviders_temp;
        //                        drop table  BillPaymentProviderFees_temp; 
        //                        drop table  BillPaymentProductIOs_temp;
        //                        drop table  BillPaymentProducts_temp;
        //                        drop table  BillPaymentProviders_temp;
        //                        drop table  BillPaymentCategoryFees_temp; 
        //                        drop table  BillPaymentSubCategories_temp; 
        //                        drop table  BillPaymentCategories_temp; 
        //                        drop table  BillPaymentDailyFxRates_temp;
        //                        drop table  BillPaymentFees_temp; 
        //                 END  

        //            ";
        //            migrationBuilder.Sql(end);


        //            var intialDataForCategories = @"

        //SET IDENTITY_INSERT [dbo].[BillPaymentCategories] ON 
        //GO

        //IF NOT EXISTS (SELECT 1 FROM [dbo].[BillPaymentCategories])
        //BEGIN

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (1, N'Electricity', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/electricity.png', 1, CAST(N'2021-12-07T10:50:25.3930000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (2, N'Gas', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/gas.png', 1, CAST(N'2021-12-07T10:50:57.8130000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (3, N'Water', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/water.png', 1, CAST(N'2021-12-07T10:51:21.1330000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (4, N'Transport', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/transport.png', 1, CAST(N'2021-12-07T10:51:52.3930000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (5, N'Television', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/television.png', 1, CAST(N'2021-12-07T10:52:22.8930000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (6, N'Internet', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/internet.png', 1, CAST(N'2021-12-07T10:52:55.6870000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (7, N'Telephone', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/telephone.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (8, N'Not Mapped', N'', 0, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (9, N'Education', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/education.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (10, N'Finance', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/finance.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (11, N'Government', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/government.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (12, N'Insurance', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/medicalinsurance.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (13, N'Shopping', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/shopping.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentCategories] ([Id], [Name], [IconUrl], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (14, N'Others', N'https://eaec3sharedsp.blob.core.windows.net/biller-categories/other.png', 1, CAST(N'2021-12-07T10:53:28.5000000' AS DateTime2), NULL, NULL, 0, NULL)


        //END

        //SET IDENTITY_INSERT [dbo].[BillPaymentCategories] OFF
        //GO

        //SET IDENTITY_INSERT [dbo].[BillPaymentSubCategories] ON 
        //GO
        //IF NOT EXISTS (SELECT 1 FROM [dbo].[BillPaymentSubCategories])
        //BEGIN

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (1, N'Electricity', 1, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (2, N'Water', 3, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (3, N'Gas', 2, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (4, N'Landline', 7, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (5, N'Internet', 6, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (6, N'Satellite TV', 5, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (7, N'Cable TV', 5, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (8, N'Natural gas', 2, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (9, N'Transportation', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (10, N'Tolls', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (11, N'Internet and TV', 6, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (12, N'Internet and TV', 5, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (13, N'Bottled water', 3, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (14, N'Digital TV', 5, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (15, N'Train', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (16, N'Travel', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (17, N'Collection Service', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (18, N'Transportation prepaid card', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (19, N'DTH', 5, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (20, N'Internet Prepaid', 6, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (21, N'Telephone', 7, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (22, N'Bus Tickets', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (23, N'Highway tolls', 4, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (24, N'Telecom', 7, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (25, N'Landline ', 7, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (26, N'Broadcasting', 6, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (27, N'Education', 9, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (28, N'School', 9, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (29, N'University', 9, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (30, N'Insurance', 12, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (31, N'Expedition of certificates', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (32, N'Government Agency', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (33, N'Government payments', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (34, N'Government', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (35, N'Housing and Loans', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (36, N'Mortgage payments', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (37, N'Taxes', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (38, N'Overseas Employment Certificate', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (39, N'Social Security', 11, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (40, N'Collection Service', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (41, N'Credit card', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (42, N'Department store card payments', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (43, N'Financial services', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (44, N'Installment', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (45, N'Loans', 10, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (46, N'Online shopping', 13, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (47, N'Shopping', 13, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (48, N'Courier and Parcel services', 14, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (49, N'Funeral services', 14, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //INSERT [dbo].[BillPaymentSubCategories] ([Id], [Name], [CategoryId], [IsActive], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (50, N'Memorial Plans', 14, 1, CAST(N'2021-12-07T10:58:13.8270000' AS DateTime2), NULL, NULL, 0, NULL)

        //END
        //SET IDENTITY_INSERT [dbo].[BillPaymentSubCategories] OFF
        //GO 
        //                        ";
        //            migrationBuilder.Sql(intialDataForCategories);

        //            var feeData = @"   
        //   SET IDENTITY_INSERT [dbo].[BillPaymentCategoryFees] ON 
        //   GO                 
        //                     IF NOT EXISTS (SELECT 1 FROM [dbo].[BillPaymentCategoryFees])
        //                     BEGIN
        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (44, N'IN', 1, CAST(4.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (45, N'IN', 2, CAST(4.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (46, N'IN', 3, CAST(2.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (47, N'IN', 4, CAST(1.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (48, N'IN', 5, CAST(1.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (49, N'IN', 6, CAST(1.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (50, N'IN', 7, CAST(1.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (51, N'IN', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (52, N'IN', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (54, N'IN', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (55, N'IN', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (56, N'IN', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (57, N'IN', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (58, N'PK', 1, CAST(3.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (59, N'PK', 2, CAST(2.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (60, N'PK', 3, CAST(3.00 AS Decimal(18, 2)), CAST(0.50 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (61, N'PK', 4, CAST(1.00 AS Decimal(18, 2)), CAST(0.50 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (62, N'PK', 5, CAST(1.00 AS Decimal(18, 2)), CAST(0.50 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (63, N'PK', 6, CAST(1.00 AS Decimal(18, 2)), CAST(0.50 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (64, N'PK', 7, CAST(1.00 AS Decimal(18, 2)), CAST(0.50 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (65, N'PK', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (66, N'PK', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (67, N'PK', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (68, N'PK', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (69, N'PK', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (70, N'PK', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (71, N'NP', 1, CAST(1.00 AS Decimal(18, 2)), CAST(1.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (72, N'NP', 2, CAST(4.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (73, N'NP', 3, CAST(3.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (74, N'NP', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (75, N'NP', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (76, N'NP', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (77, N'NP', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (78, N'NP', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (79, N'NP', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (80, N'NP', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (81, N'NP', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (82, N'NP', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (83, N'NP', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (84, N'PH', 1, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (85, N'PH', 2, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (86, N'PH', 3, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (87, N'PH', 4, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (88, N'PH', 5, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (89, N'PH', 6, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (90, N'PH', 7, CAST(1.00 AS Decimal(18, 2)), CAST(4.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (91, N'PH', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (92, N'PH', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (93, N'PH', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (94, N'PH', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (95, N'PH', 11, CAST(1.00 AS Decimal(18, 2)), CAST(18.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (96, N'PH', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:07:08.0900000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (97, N'AE', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (98, N'AE', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (99, N'AE', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (100, N'AE', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (101, N'AE', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (102, N'AE', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (103, N'AE', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (104, N'AE', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (105, N'AE', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (106, N'AE', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (107, N'AE', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (108, N'AE', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (109, N'AE', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (110, N'BD', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (111, N'BD', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (112, N'BD', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (113, N'BD', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (114, N'BD', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (115, N'BD', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (116, N'BD', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (117, N'BD', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (118, N'BD', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (119, N'BD', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (120, N'BD', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (121, N'BD', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (122, N'BD', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (123, N'BR', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (124, N'BR', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (125, N'BR', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (126, N'BR', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (127, N'BR', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (128, N'BR', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (129, N'BR', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (130, N'BR', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (131, N'BR', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (132, N'BR', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (133, N'BR', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (134, N'BR', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (135, N'BR', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (136, N'CO', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (137, N'CO', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (138, N'CO', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (139, N'CO', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (140, N'CO', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (141, N'CO', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (142, N'CO', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (143, N'CO', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (144, N'CO', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (145, N'CO', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (146, N'CO', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (147, N'CO', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (148, N'CO', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (149, N'CR', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (150, N'CR', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (151, N'CR', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (152, N'CR', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (153, N'CR', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (154, N'CR', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (155, N'CR', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (156, N'CR', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (157, N'CR', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (158, N'CR', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (159, N'CR', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (160, N'CR', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (161, N'CR', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (162, N'DO', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (163, N'DO', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (164, N'DO', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (165, N'DO', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (166, N'DO', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (167, N'DO', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (168, N'DO', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (169, N'DO', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (170, N'DO', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (171, N'DO', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (172, N'DO', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (173, N'DO', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (174, N'DO', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (175, N'EC', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (176, N'EC', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (177, N'EC', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (178, N'EC', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (179, N'EC', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (180, N'EC', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (181, N'EC', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (182, N'EC', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (183, N'EC', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (184, N'EC', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (185, N'EC', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (186, N'EC', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (187, N'EC', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (188, N'EG', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (189, N'EG', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (190, N'EG', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (191, N'EG', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (192, N'EG', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (193, N'EG', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (194, N'EG', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (195, N'EG', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (196, N'EG', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (197, N'EG', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (198, N'EG', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (199, N'EG', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (200, N'EG', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (201, N'GH', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (202, N'GH', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (203, N'GH', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (204, N'GH', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (205, N'GH', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (206, N'GH', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (207, N'GH', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (208, N'GH', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (209, N'GH', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (210, N'GH', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (211, N'GH', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (212, N'GH', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (213, N'GH', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (214, N'GT', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (215, N'GT', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (216, N'GT', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (217, N'GT', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (218, N'GT', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (219, N'GT', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (220, N'GT', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (221, N'GT', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (222, N'GT', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (223, N'GT', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (224, N'GT', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (225, N'GT', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (226, N'GT', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (227, N'HN', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (228, N'HN', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (229, N'HN', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (230, N'HN', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (231, N'HN', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (232, N'HN', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (233, N'HN', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (234, N'HN', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (235, N'HN', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (236, N'HN', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (237, N'HN', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (238, N'HN', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (239, N'HN', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (240, N'ID', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (241, N'ID', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (242, N'ID', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (243, N'ID', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (244, N'ID', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (245, N'ID', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (246, N'ID', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (247, N'ID', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (248, N'ID', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (249, N'ID', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (250, N'ID', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (251, N'ID', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (252, N'ID', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (253, N'JM', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (254, N'JM', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (255, N'JM', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (256, N'JM', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (257, N'JM', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (258, N'JM', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (259, N'JM', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (260, N'JM', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (261, N'JM', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (262, N'JM', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (263, N'JM', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (264, N'JM', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (265, N'JM', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (266, N'JO', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (267, N'JO', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (268, N'JO', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (269, N'JO', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (270, N'JO', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (271, N'JO', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (272, N'JO', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (273, N'JO', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (274, N'JO', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (275, N'JO', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (276, N'JO', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (277, N'JO', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (278, N'JO', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (279, N'LK', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (280, N'LK', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (281, N'LK', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (282, N'LK', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (283, N'LK', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (284, N'LK', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (285, N'LK', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (286, N'LK', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (287, N'LK', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (288, N'LK', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (289, N'LK', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (290, N'LK', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (291, N'LK', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (292, N'MX', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (293, N'MX', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (294, N'MX', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (295, N'MX', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (296, N'MX', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (297, N'MX', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (298, N'MX', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (299, N'MX', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (300, N'MX', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (301, N'MX', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (302, N'MX', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (303, N'MX', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (304, N'MX', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (305, N'NG', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (306, N'NG', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (307, N'NG', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (308, N'NG', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (309, N'NG', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (310, N'NG', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (311, N'NG', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (312, N'NG', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (313, N'NG', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (314, N'NG', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (315, N'NG', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (316, N'NG', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (317, N'NG', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (318, N'NI', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (319, N'NI', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (320, N'NI', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (321, N'NI', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (322, N'NI', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (323, N'NI', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (324, N'NI', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (325, N'NI', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (326, N'NI', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (327, N'NI', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (328, N'NI', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (329, N'NI', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (330, N'NI', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (331, N'PE', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (332, N'PE', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (333, N'PE', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (334, N'PE', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (335, N'PE', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (336, N'PE', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (337, N'PE', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (338, N'PE', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (339, N'PE', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (340, N'PE', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (341, N'PE', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (342, N'PE', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (343, N'PE', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (344, N'SV', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (345, N'SV', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (346, N'SV', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (347, N'SV', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (348, N'SV', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (349, N'SV', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (350, N'SV', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (351, N'SV', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (352, N'SV', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (353, N'SV', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (354, N'SV', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (355, N'SV', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (356, N'SV', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (357, N'US', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (358, N'US', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (359, N'US', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (360, N'US', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (361, N'US', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (362, N'US', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (363, N'US', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (364, N'US', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (365, N'US', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (366, N'US', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (367, N'US', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (368, N'US', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (369, N'US', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (370, N'VN', 1, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (371, N'VN', 2, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (372, N'VN', 3, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (373, N'VN', 4, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (374, N'VN', 5, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (375, N'VN', 6, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (376, N'VN', 7, CAST(2.00 AS Decimal(18, 2)), CAST(3.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (377, N'VN', 9, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (378, N'VN', 10, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (379, N'VN', 11, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (380, N'VN', 12, CAST(1.00 AS Decimal(18, 2)), CAST(6.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (381, N'VN', 13, CAST(1.00 AS Decimal(18, 2)), CAST(2.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     INSERT [dbo].[BillPaymentCategoryFees] ([Id], [CountryCode], [CategoryId], [FxRatePercentage], [MandatoryFee], [MandatoryFeeCurrency], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy]) VALUES (382, N'VN', 14, CAST(1.00 AS Decimal(18, 2)), CAST(5.00 AS Decimal(18, 2)), N'AED', CAST(N'2022-02-10T13:21:32.2530000' AS DateTime2), NULL, NULL, 0, NULL)

        //                     END
        //                     SET IDENTITY_INSERT [dbo].[BillPaymentCategoryFees] OFF
        //                      GO 

        //                     SET IDENTITY_INSERT [dbo].[BillPaymentFees] ON 
        //                     GO

        //                     IF NOT EXISTS (SELECT 1 FROM [dbo].[BillPaymentFees])
        //                     BEGIN
        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (1, CAST(2.50 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000020')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (2, CAST(2.50 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000013')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (3, CAST(2.50 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000016')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (4, CAST(2.50 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000012')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (5, CAST(1.00 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000011')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (6, CAST(1.00 AS Decimal(18, 2)), CAST(N'2022-02-10T16:35:48.8230000' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000014')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (7, CAST(1.00 AS Decimal(18, 2)), CAST(N'2022-01-19T12:55:09.5197235' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000017')

        //                     INSERT [dbo].[BillPaymentFees] ([Id], [Fee], [CreatedDate], [UpdatedDate], [DeletedDate], [IsDeleted], [DeletedBy], [FeeCurrency], [ProviderCode]) VALUES (8, CAST(1.00 AS Decimal(18, 2)), CAST(N'2022-01-19T12:55:09.5197235' AS DateTime2), NULL, NULL, 0, NULL, N'AED', N'784000000000018')

        //                     END
        //                     SET IDENTITY_INSERT [dbo].[BillPaymentFees] OFF
        //                      GO
        //";

        //            migrationBuilder.Sql(feeData);
        //        }
        protected override void Down(MigrationBuilder migrationBuilder)
        { }


        //protected override void Down(MigrationBuilder migrationBuilder)
        //{
        //    migrationBuilder.DropTable(
        //        name: "BillPaymentBillerIOs");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentCategoryFees");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentCategoryProviders");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentDailyFxRates");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentExternalTransactions");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentFees");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentProviderFees");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentProductIOs");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentSubCategories");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentTransactions");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentProducts");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentBillers");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentCategories");

        //    migrationBuilder.DropTable(
        //        name: "BillPaymentProviders");

        //    migrationBuilder.DropColumn(
        //        name: "BillPaymentEnabled",
        //        table: "Countries");
        //}
    }
}
