﻿using C3Pay.Core.Models;
using C3Pay.Core.Repositories;
using Edenred.Common.Data;

namespace C3Pay.Data.Repositories.C3Pay.MoneyTransfer
{
    public class MoneyTransferSuspiciousInformationRepository : Repository<MoneyTransferSuspiciousInformation>, IMoneyTransferSuspiciousInformationRepository
    {
        public MoneyTransferSuspiciousInformationRepository(C3PayContext context)
           : base(context)
        { }
    }
}
